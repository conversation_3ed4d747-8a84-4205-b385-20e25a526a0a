<?php

namespace App\Controller\LoyalApp;

use App\Entity\User;
use App\Reports\v2\WlaSubscriptionPackagesReport;
use App\Service\Report2\ReportService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

use function Sentry\captureMessage;

class LoyalAppSubscriptionPackagesController extends AbstractController
{
    #[Route(path: '/api/loyalapp/subscription-packages', methods: ['GET'])]
    public function getPackagesAction(
        Request $request,
        ReportService $reportService
    ): JsonResponse {
        if (!$this->isGranted('ROLE_SUBSCRIPTION_BASIC')) {
            captureMessage('No access, purchased subscription type is to weak.');
            return $this->json([], Response::HTTP_PAYMENT_REQUIRED);
        }
        /** @var User $user */
        $user = $this->getUser();

        /** @var User $user */
        $page = $request->get('page', 1);
        $perPage = $request->get('perPage', 25);

        $criterias = $request->query->all();

        $report = $reportService->getData(
            reportType: WlaSubscriptionPackagesReport::class,
            criteria:   $criterias,
            subscriber: $user->getSubscriber(),
            user:       $user,
            page:       $page,
            perPage:    $perPage,
        );
        return $this->json(data: $report, context: ['groups' => ['currency:basic', "report:data"]]);
    }
}

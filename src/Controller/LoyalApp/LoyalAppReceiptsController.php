<?php

namespace App\Controller\LoyalApp;

use App\Entity\User;
use App\Reports\v2\WlaReceiptsReport;
use App\Service\LoyalAppStatistics\LoyalAppStatistics;
use App\Service\Report2\ReportService;
use Exception;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

class LoyalAppReceiptsController extends AbstractController
{
    /**
     * Get receipts by source (WASHSTOP or BE_LOYAL)
     * @throws Exception
     */
    #[Route(path: '/api/loyalapp/receipts', name: 'cm_get_receipts_list', methods: ['GET'])]
    public function getInvoicesListBySource(
        Request $request,
        LoyalAppStatistics $appStatistics,
        ReportService $reportService,
    ): JsonResponse {
        $source = $request->get('app');
        $page = $request->get('page');
        $perPage = $request->get('perPage');
        /** @var User $user */
        $user = $this->getUser();
        if (!$appStatistics->getApp($source, $user)) {
            return new JsonResponse(["nie masz uprawnien do tej aplikacji"], JsonResponse::HTTP_FORBIDDEN);
        }

        $report = $reportService->getData(
            reportType: WlaReceiptsReport::class,
            criteria:   $request->query->all(),
            subscriber: $user->getSubscriber(),
            user:       $user,
            page:       $page,
            perPage:    $perPage,
        );

        return $this->json(data: $report, context: ['groups' => ['currency:basic', "report:data"]]);
    }

    /**
     * Download invoice by Id
     */
    #[Route(path: '/api/loyalapp/receipt/{receiptId}/download', requirements: ['receiptId' => '\d+'], name: 'cm_download_receipt', methods: ['GET'])]
    public function downloadInvoiceByPath(Request $request, LoyalAppStatistics $loyalAppStatistics, int $receiptId)
    {
        $source = $request->get('app');
        /** @var User $user */
        $user = $this->getUser();
        if (!$loyalAppStatistics->getApp($source, $user)) {
            return new JsonResponse(["nie masz uprawnien do tej aplikacji"], JsonResponse::HTTP_FORBIDDEN);
        }

        $imageBinaryData = $loyalAppStatistics->downloadReceipt($source, $receiptId);

        return new Response(
            $imageBinaryData,
            Response::HTTP_OK,
            [
                'Content-Type' => 'image/png',
                'Content-Disposition' => 'attachment; filename="receipt-' . $receiptId . '.png"',
            ]
        );
    }
}

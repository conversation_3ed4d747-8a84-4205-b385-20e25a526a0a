<?php

namespace App\Controller\LoyalApp;

use App\Entity\User;
use App\Service\LoyalAppStatistics\LoyalAppStatistics;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;

class LoyalAppPromotionalCodesController extends AbstractController
{
    public function __construct(
        private LoyalAppStatistics $loyalAppStatistics
    ) {
    }

    /**
     * @return JsonResponse
     */
    #[Route(path: '/api/loyalapp/promotionalcodes', name: 'cm_loyal_app_promotionalcode', methods: ['POST'])]
    public function newAction(Request $request)
    {
        $source = $request->get('app');
        /** @var User $user */
        $user = $this->getUser();
        if (!$this->loyalAppStatistics->getApp($source, $user)) {
            return new JsonResponse(["nie masz uprawnien do tej aplikacji"], JsonResponse::HTTP_FORBIDDEN);
        }

        $parameters = json_decode($request->getContent(), true);
        $data2 = $this->loyalAppStatistics->promotionalCodesNew(
            $parameters,
            $source
        );
        return new JsonResponse($data2, 200, [], true);
    }
    /**
     * @return JsonResponse
     */
    #[Route(path: '/api/loyalapp/promotionalcodes', name: 'cm_loyal_app_promotionalcodes', methods: ['GET'])]
    public function getPromotionalCodeListAction(Request $request)
    {
        $source = $request->get('app');
        /** @var User $user */
        $user = $this->getUser();
        if (!$this->loyalAppStatistics->getApp($source, $user)) {
            return new JsonResponse(["nie masz uprawnien do tej aplikacji"], JsonResponse::HTTP_FORBIDDEN);
        }

        $parameters = $request->query->all();
        $orderBy = $request->query->get('orderBy', null);
        if (null === $source) {
            return new JsonResponse([], 401);
        }
        if (isset($orderBy)) {
            $parameters['orderDescending'] = $request->query->get('orderDescending');
            $parameters['orderBy'] = $orderBy;
        }
        $data2 = $this->loyalAppStatistics->promotionalCodesList(
            $parameters,
            $source
        );
        return new JsonResponse($data2, 200, [], true);
    }
}

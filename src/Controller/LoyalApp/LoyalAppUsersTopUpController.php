<?php

namespace App\Controller\LoyalApp;

use App\Entity\User;
use App\Service\LoyalAppStatistics\LoyalAppStatistics;
use Exception;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use OpenApi\Annotations as OA;

class LoyalAppUsersTopUpController extends AbstractController
{
    public function __construct(
        private LoyalAppStatistics $loyalAppStatistics
    ) {
    }

    /**
     * @return JsonResponse
     */
    #[Route(path: '/api/loyalapp/user/{userId}/bonuses', methods: ['POST'])]
    public function userBonusAction(int $userId, Request $request)
    {
        $source = $request->get('app');
        /** @var User $user */
        $user = $this->getUser();
        if (!$this->loyalAppStatistics->getApp($source, $user)) {
            return new JsonResponse(["nie masz uprawnien do tej aplikacji"], JsonResponse::HTTP_FORBIDDEN);
        }

        $params = json_decode($request->getContent(), true);

        $params['value'] *= 100;
        $params['appName'] = 'CM';

        $data = $this->loyalAppStatistics->bonusUser(
            $params,
            $userId,
            $source
        );
        return new JsonResponse($data, 200, [], true);
    }

    /**
     * @return JsonResponse
     */
    #[Route(path: '/api/loyalapp/user/{userId}/transfers', methods: ['POST'])]
    public function userTransferAction(int $userId, Request $request)
    {
        $source = $request->get('app');
        /** @var User $user */
        $user = $this->getUser();
        if (!$this->loyalAppStatistics->getApp($source, $user)) {
            return new JsonResponse(["nie masz uprawnien do tej aplikacji"], JsonResponse::HTTP_FORBIDDEN);
        }

        $params = json_decode($request->getContent(), true);

        $params['value'] *= 100;
        $params['appName'] = 'CM';

        $data = $this->loyalAppStatistics->topUpUser(
            $params,
            $userId,
            $source
        );
        return new JsonResponse($data, 200, [], true);
    }
}

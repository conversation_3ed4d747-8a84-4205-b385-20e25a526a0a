<?php

namespace App\Controller\LoyalApp;

use App\Entity\User;
use App\Service\LoyalAppStatistics\LoyalAppStatistics;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

class LoyalAppController extends AbstractController
{
    #[Route('/api/loyalapp/apps', name: 'cm_loyal_app_list')]
    public function index(LoyalAppStatistics $loyalAppStatistics): JsonResponse
    {
        /** @var User $user */
        $user = $this->getUser();
        $apps = $loyalAppStatistics->getApps($user);
        return $this->json($apps, JsonResponse::HTTP_OK, [], ['groups' => ['manager:app']]);
    }
}

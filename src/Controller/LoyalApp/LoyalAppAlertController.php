<?php

namespace App\Controller\LoyalApp;

use App\Entity\User;
use App\Service\LoyalAppStatistics\LoyalAppStatistics;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;

class LoyalAppAlertController extends AbstractController
{
    #[Route(path: '/api/loyalapp/alerts', methods: ['GET'])]
    public function getLoyalappAlerts(Request $request, LoyalAppStatistics $loyalAppStatistics): JsonResponse
    {
        $source = $request->get('app');
        /** @var User $user */
        $user = $this->getUser();
        $app = $loyalAppStatistics->getApp($source, $user);
        if (!$app) {
            return new JsonResponse(["nie masz uprawnien do tej aplikacji"], JsonResponse::HTTP_FORBIDDEN);
        }

        $data = $loyalAppStatistics->alertsList(
            $request->query->all(),
            $source
        );
        return new JsonResponse($data, 200, [], true);
    }
}

<?php

namespace App\Controller\LoyalApp;

use App\Entity\User;
use App\Service\InvoiceApi\DataProviders\InvoiceListProvider;
use App\Service\InvoiceApi\DataProviders\InvoiceProvider;
use App\Service\InvoiceApi\InvoiceApi;
use App\Service\LoyalAppStatistics\LoyalAppStatistics;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

class LoyalAppSelfInvoiceController extends AbstractController
{
    #[Route(path: '/api/loyalapp/selfinvoices', methods: ['GET'])]
    public function getAllInvoices(InvoiceListProvider $invoiceListProvider, Request $request, LoyalAppStatistics $loyalAppStatistics): JsonResponse
    {
        $source = $request->get('app');
        /** @var User $user */
        $user = $this->getUser();
        $app = $loyalAppStatistics->getApp($source, $user);
        if (!$app) {
            return new JsonResponse(["nie masz uprawnien do tej aplikacji"], JsonResponse::HTTP_FORBIDDEN);
        }

        if (!$app->getSelfInvoice()) { // ta aplikacja nie obsługuje samofakturowania
            return $this->json(['data' => [], 'sums' => ['countTotal' => 0], "message" => "this app no provide self-invoicing"]);
        }

        $timezone = $user->getTimezone()->getLocation();
        $tz = new \DateTimeZone($timezone);

        $page = $request->query->get('page');
        $perPage = $request->query->get('perPage');
        $confirmed = $request->query->get('confirmed');
        if ($confirmed !== null) {
            $confirmed = $request->query->getBoolean('confirmed');
        }
        $data = $invoiceListProvider->getAllInvoiceList(
            $app->getSelfInvoice(),
            $tz,
            $page,
            $perPage,
            $confirmed,
            'loyalapp_seflinvoice_download'
        );

        $result = ['data' => $data['data'], 'sums' => ['countTotal' => $data['total']]];
        return $this->json($result);
    }

    #[Route(path: '/api/loyalapp/selfinvoices/{id}/download', methods: ['GET'], name: 'loyalapp_seflinvoice_download')]
    public function download(Request $request, LoyalAppStatistics $loyalAppStatistics, int $id, InvoiceApi $invoiceApi, InvoiceProvider $invoiceProvider): Response
    {
        $source = $request->get('app');
        /** @var User $user */
        $user = $this->getUser();
        $app = $loyalAppStatistics->getApp($source, $user);
        if (!$app) {
            return new JsonResponse(["nie masz uprawnien do tej aplikacji"], JsonResponse::HTTP_FORBIDDEN);
        }
        return $this->json(['not implemented yet'], JsonResponse::HTTP_NOT_IMPLEMENTED);
    }
}

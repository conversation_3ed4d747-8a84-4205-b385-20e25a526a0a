<?php

namespace App\Controller\LoyalApp;

use App\Entity\User;
use App\Service\LoyalAppStatistics\LoyalAppStatistics;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;

class LoyalAppPackageController extends AbstractController
{
    public function __construct(
        private LoyalAppStatistics $loyalAppStatistics
    ) {
    }

    /**
     * @return JsonResponse
     */
    #[Route(path: '/api/loyalapp/packages', methods: ['GET'])]
    public function listActions(Request $request)
    {
        $source = $request->get('app');
        /** @var User $user */
        $user = $this->getUser();
        if (!$this->loyalAppStatistics->getApp($source, $user)) {
            return new JsonResponse(["nie masz uprawnien do tej aplikacji"], JsonResponse::HTTP_FORBIDDEN);
        }

        $data2 = $this->loyalAppStatistics->packages(
            $source
        );
        return new JsonResponse($data2, 200, [], true);
    }

    #[Route(path: '/api/loyalapp/packages', methods: ['POST'])]
    public function createAction(Request $request)
    {
        $payload = $request->getPayload();
        $source = $payload->get('app');
        /** @var User $user */
        $user = $this->getUser();
        if (!$this->loyalAppStatistics->getApp($source, $user)) {
            return new JsonResponse(["nie masz uprawnien do tej aplikacji"], JsonResponse::HTTP_FORBIDDEN);
        }

        $parameters = json_decode($request->getContent(), true);
        $data2 = $this->loyalAppStatistics->packageNew(
            $parameters,
            $source
        );
        return new JsonResponse($data2, 200, [], true);
    }

    #[Route(path: '/api/loyalapp/package/{packageId}', methods: ['PUT'])]
    public function updateActions(int $packageId, Request $request)
    {
        $source = $request->get('app');
        /** @var User $user */
        $user = $this->getUser();
        if (!$this->loyalAppStatistics->getApp($source, $user)) {
            return new JsonResponse(["nie masz uprawnien do tej aplikacji"], JsonResponse::HTTP_FORBIDDEN);
        }

        $parameters = json_decode($request->getContent(), true);
        $data2 = $this->loyalAppStatistics->packageEdit(
            $parameters,
            $packageId,
            $source
        );
        return new JsonResponse($data2, 200, [], true);
    }

    #[Route(path: '/api/loyalapp/package/{packageId}', methods: ['DELETE'])]
    public function deleteAction(int $packageId, Request $request)
    {
        $source = $request->get('app');
        /** @var User $user */
        $user = $this->getUser();
        if (!$this->loyalAppStatistics->getApp($source, $user)) {
            return new JsonResponse(["nie masz uprawnien do tej aplikacji"], JsonResponse::HTTP_FORBIDDEN);
        }
        $data2 = $this->loyalAppStatistics->packageDelete(
            $packageId,
            $source
        );
        return new JsonResponse($data2, 200, [], true);
    }
}

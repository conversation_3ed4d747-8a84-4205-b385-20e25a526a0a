<?php

namespace App\Controller\LoyalApp;

use App\Entity\User;
use App\Service\LoyalAppStatistics\LoyalAppStatistics;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;

class LoyalAppPromotionalPackageController extends AbstractController
{
    public function __construct(
        private LoyalAppStatistics $loyalAppStatistics
    ) {
    }

    /**
     * @return JsonResponse
     */
    #[Route(path: '/api/loyalapp/promotionalpackages', name: 'cm_loyal_app_promotionalpackage', methods: ['POST'])]
    public function newAction(Request $request)
    {
        $source = $request->get('app');
        /** @var User $user */
        $user = $this->getUser();
        if (!$this->loyalAppStatistics->getApp($source, $user)) {
            return new JsonResponse(["nie masz uprawnien do tej aplikacji"], JsonResponse::HTTP_FORBIDDEN);
        }

        $parameters = json_decode($request->getContent(), true);
        $data2 = $this->loyalAppStatistics->addPromotionalPackage(
            $parameters,
            $source
        );
        return new JsonResponse($data2, 200, [], true);
    }
    /**
     * @return JsonResponse
     */
    #[Route(path: '/api/loyalapp/promotionalpackages', name: 'cm_loyal_app_promotionalpackages', methods: ['GET'])]
    public function getPromotionalPackageListAction(Request $request)
    {
        $source = $request->get('app');
        /** @var User $user */
        $user = $this->getUser();
        if (!$this->loyalAppStatistics->getApp($source, $user)) {
            return new JsonResponse(["nie masz uprawnien do tej aplikacji"], JsonResponse::HTTP_FORBIDDEN);
        }

        $data2 = $this->loyalAppStatistics->promotionalPackagesList(
            $request->get('search', null),
            $source
        );
        return new JsonResponse($data2, 200, [], true);
    }
    /**
     * @param  $packageId
     * @return JsonResponse
     */
    #[Route(path: '/api/loyalapp/promotionalpackage/{packageId}', name: 'cm_loyal_app_package_update', methods: ['PUT'])]
    public function updatePackageAction($packageId, Request $request)
    {
        $source = $request->get('app');
        /** @var User $user */
        $user = $this->getUser();
        if (!$this->loyalAppStatistics->getApp($source, $user)) {
            return new JsonResponse(["nie masz uprawnien do tej aplikacji"], JsonResponse::HTTP_FORBIDDEN);
        }

        $parameters = json_decode($request->getContent(), true);
        $data2 = $this->loyalAppStatistics->editPromotionalPackage(
            $parameters,
            $packageId,
            $source
        );
        return new JsonResponse($data2, 200, [], true);
    }
}

<?php

namespace App\Controller\LoyalApp;

use App\Entity\User;
use App\Reports\v2\WlaTransactionsReport;
use App\Service\LoyalAppStatistics\LoyalAppStatistics;
use App\Service\Report2\ReportService;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;

class LoyalAppTransactionsController extends AbstractController
{
    #[Route(path: '/api/loyalapp/transactions', methods: ['GET'])]
    public function indexAction(
        Request $request,
        LoyalAppStatistics $appStatistics,
        ReportService $reportService,
    ): JsonResponse {
        $page = $request->get('pageNumber', 1);
        $perPage = $request->get('itemsPerPage', 10);

        $appName = $request->get('app');
        /** @var User $user */
        $user = $this->getUser();
        if (!$appStatistics->getApp($appName, $user)) {
            return new JsonResponse(["nie masz uprawnien do tej aplikacji"], JsonResponse::HTTP_FORBIDDEN);
        }

        $report = $reportService->getData(
            reportType: WlaTransactionsReport::class,
            criteria:   $request->query->all(),
            subscriber: $user->getSubscriber(),
            user:       $user,
            page:       $page,
            perPage:    $perPage,
        );

        return $this->json(data: $report, context: ['groups' => ['currency:basic', "report:data"]]);
    }
}

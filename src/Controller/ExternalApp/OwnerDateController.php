<?php

namespace App\Controller\ExternalApp;

use App\Repository\SubscribersRepository;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

class OwnerDateController extends AbstractController
{
    #[Route(path: '/administration/owner_bkf/{ownerId}', methods: ['GET'])]
    public function administrationUserDetails(SubscribersRepository $subscribersRepository, int $ownerId): JsonResponse
    {
        $subscriber = $subscribersRepository->findOneBy(['ownerBkf' => $ownerId]);
        if ($subscriber === null) {
            return new JsonResponse([], Response::HTTP_NOT_FOUND);
        }
        return $this->json(
            data: $subscriber,
            context: [
                'circular_reference_limit' => 0,
                'circular_reference_handler' => function ($object) {
                    return $object->getId();
                },
                'groups' => [
                    'subscriber:details',
                    'country:basic',
                    'currency:basic'
                ],
            ]
        );
    }
}

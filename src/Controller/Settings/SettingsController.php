<?php

namespace App\Controller\Settings;

use App\Entity\Settings;
use App\Entity\User;
use App\Repository\SettingsRepository;
use Doctrine\ORM\EntityManagerInterface;
use PhpCsFixer\Console\Report\FixReport\JsonReporter;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Serializer\SerializerInterface;

class SettingsController extends AbstractController
{
    #[Route(path: '/api/settings/', methods: ['GET'])]
    public function getFilterSettings(SettingsRepository $settingsRepository): JsonResponse
    {
        /** @var User $user */
        $user = $this->getUser();
        $settings = $settingsRepository->findBy(['user' => $user]);

        return $this->json($settings, JsonResponse::HTTP_OK, [], ['groups' => ['settings_list']]);
    }

    #[Route(path: '/api/settings/', methods: ['PATCH'])]
    public function updateFilterSettings(Request $request, SettingsRepository $settingsRepository): JsonResponse
    {
        /** @var User $user */
        $user = $this->getUser();

        $data = json_decode($request->getContent(), true);

        $settings = $settingsRepository->findOneBy(
            [
                'user' => $user,
                'namespace' => $data['namespace'],
            ]
        );

        if ($settings === null) {
            $settings = new Settings();
            $settings->setNamespace($data['namespace'])
                ->setUser($user);
        }

        $settings->setValue(
            $data['value']
        );

        $settingsRepository->save($settings);
        return $this->json($settings, JsonResponse::HTTP_OK, [], ['groups' => ['settings_list']]);
    }
}

<?php

namespace App\Controller\Finance;

use App\Controller\ApiController\AbstractApiController;
use App\Entity\User;
use I2m\Connectors\Service\CwFiscalApi\FiscalApiService;
use I2m\StandardTypes\Enum\Country;
use I2m\StandardTypes\Enum\Currency;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\Routing\Annotation\Route;

class FiscalTransactionsApiController extends AbstractApiController
{
    #[Route(path: '/api/finance/fiscal/config', methods: ['GET'])]
    public function getConfigAction(Request $request, FiscalApiService $carwashApiFiscalService): JsonResponse
    {
        try {
            /** @var User $user */
            $user = $this->getUser();
            $snParam = $request->get('sn');

            $sns = [];
            if ($snParam) {
                // Jeśli podano parametr sn, podziel go na listę numerów seryjnych
                $requestedSns = array_map('intval', explode(',', $snParam));

                // Sprawdź dostęp do każdej myjni
                foreach ($requestedSns as $requestedSn) {
                    if (!$user->hasAccessToCarwashBySn($requestedSn)) {
                        throw new AccessDeniedHttpException("Brak dostępu do myjni o numerze seryjnym: $requestedSn");
                    }
                    $sns[] = $requestedSn;
                }
            } else {
                // Jeśli nie podano parametru sn, pobierz wszystkie myjnie użytkownika
                $carwashes = $user->getCarwashes();
                foreach ($carwashes as $carwash) {
                    $sns[] = $carwash->getSerialNumber();
                }
            }

            if (empty($sns)) {
                throw new NotFoundHttpException('Nie znaleziono żadnej myjni');
            }

            $fiscalConfigList = $carwashApiFiscalService->getFiscalConfig(
                ownerId: $user->getOwnerBkf(),
                sns: $sns
            );

            $result = [];
            foreach ($fiscalConfigList->getData() as $configItem) {
                $config = $configItem->getConfig();
                $configData = null;

                if ($config) {
                    $configData = [
                        'oib' => $config->getOib(),
                        'errorCounter' => $config->getErrorCounter(),
                        'isError' => $config->isError(),
                        'lastTime' => $config->getLastTime(),
                        'expiration' => $config->getExpiration(),
                        'validFrom' => $config->getValidFrom(),
                        'subject' => $config->getSubject(),
                    ];
                }

                $result[] = [
                    'sn' => $configItem->getSn(),
                    'name' => $configItem->getName(),
                    'config' => $configData,
                ];
            }

            return $this->json($result);
        } catch (HttpException $e) {
            return $this->json(['error' => $e->getMessage()], $e->getStatusCode() ?: JsonResponse::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    #[Route(path: '/api/finance/fiscal/certificate/{sn}', methods: ['POST'])]
    public function uploadCertificateAction(int $sn, Request $request, FiscalApiService $carwashApiFiscalService): JsonResponse
    {
        try {
            $payload = $request->getPayload();
            /** @var User $user */
            $user = $this->getUser();

            if (!$user->hasAccessToCarwashBySn($sn)) {
                throw new AccessDeniedHttpException("Brak dostępu do myjni o numerze seryjnym: $sn");
            }

            $certificateFile = $request->files->get('certificate');
            if (!$certificateFile instanceof UploadedFile) {
                throw new BadRequestHttpException('Nie przesłano pliku certyfikatu');
            }

            $extension = $certificateFile->getClientOriginalExtension();
            if ($extension !== 'p12') {
                throw new BadRequestHttpException('Nieprawidłowy format pliku. Dozwolony format: p12');
            }

            $oib = $payload->get('oib');
            if (empty($oib)) {
                throw new BadRequestHttpException('Parametr OIB jest wymagany');
            }

            // Walidacja OIB (numer identyfikacyjny w Chorwacji - 11 cyfr)
            if (strlen($oib) !== 11) {
                throw new BadRequestHttpException('Nieprawidłowy format OIB. OIB powinien składać się z 11 cyfr');
            }

            $certificate = $carwashApiFiscalService->addCertificate(
                ownerId: $user->getOwnerBkf(),
                sn: $sn,
                certificateFile: $certificateFile,
                oib: $oib
            );

            $result = [
                'success' => true,
                'message' => 'Certyfikat został pomyślnie wgrany',
                'certificate' => [
                    'oib' => $certificate->getOib(),
                    'certInfo' => $certificate->getCertInfo()
                ]
            ];

            return $this->json($result);
        } catch (HttpException $e) {
            return $this->json(['error' => $e->getMessage()], $e->getStatusCode());
        } catch (\Exception $e) {
            return $this->json(
                ['error' => 'Wystąpił błąd podczas wgrywania certyfikatu: ' . $e->getMessage()],
                JsonResponse::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }

    #[Route(path: '/api/finance/fiscal/transactions', methods: ['POST'])]
    public function storeAction(Request $request, FiscalApiService $carwashApiFiscalService): JsonResponse
    {
        /** @var User $user */
        $user = $this->getUser();
        $payload = $request->getPayload();
        $sn = $request->get('sn');

        if (!$user->hasAccessToCarwashBySn($sn)) {
            throw new AccessDeniedHttpException('Carwash not available for this user');
        }
        $mode = 'none';
        $status = 'transaction';
        $cr = null;
        if ($user->getCountry() == Country::HR) {
            $mode = 'croatia';
            $status = 'not_fiscalized';
            $cr = [

                "isu" => $payload->get('isu'),
                "location" => $payload->get('location'),
                'pw' => $payload->get('pw'),
                "tax" => 25,
                "test" => 0,
            ];
        }
        $transaction = $carwashApiFiscalService->newTransaction(
            ownerId: $user->getOwnerBkf(),
            sn:       $sn,
            value:    $payload->get('value'),
            currency: $user->getCurrency()->getCode(),
            type:     'CARWASH_MANAGER',
            mode:     $mode,
            source:   $payload->get('source'),
            status:   $status,
            date:     new \DateTime($payload->get('date'), $user->getTimezone()->getDateTimeZone()),
            bayId:    $payload->get('bayId'),
            cr:       $cr
        );

        if (in_array($transaction->getFiscal(), ['error','data_error'])) {
            $responseCode = JsonResponse::HTTP_BAD_REQUEST;
        } else {
            $responseCode = JsonResponse::HTTP_OK;
        }

        return $this->json(data: $transaction, status: $responseCode);
    }
}

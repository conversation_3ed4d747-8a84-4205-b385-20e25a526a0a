<?php

namespace App\Controller\Finance;

use App\Controller\ApiController\AbstractApiController;
use App\Entity\User;
use I2m\Connectors\Service\CwFiscalApi\FiscalApiService;
use I2m\StandardTypes\Enum\Country;
use I2m\StandardTypes\Enum\Currency;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException;
use Symfony\Component\Routing\Annotation\Route;

class FiscalProgramUsageApiController extends AbstractApiController
{
    #[Route(path: '/api/finance/fiscal/transactions', methods: ['POST'])]
    public function storeAction(Request $request, FiscalApiService $carwashApiFiscalService): JsonResponse
    {
        /** @var User $user */
        $user = $this->getUser();
        $payload = $request->getPayload();
        $sn = $request->get('sn');

        if (!$user->hasAccessToCarwashBySn($sn)) {
            throw new AccessDeniedHttpException('Carwash not available for this user');
        }
        $mode = 'none';
        $status = 'transaction';
        $cr = null;
        if ($user->getCountry() == Country::HR) {
            $mode = 'croatia';
            $status = 'not_fiscalized';
            $cr = [

                "isu" => $payload->get('isu'),
                "location" => $payload->get('location'),
                'pw' => $payload->get('pw'),
                "tax" => 25,
                "test" => 0,
            ];
        }
        $transaction = $carwashApiFiscalService->newTransaction(
            ownerId: $user->getOwnerBkf(),
            sn:       $sn,
            value:    $payload->get('value'),
            currency: $user->getCurrency()->getCode(),
            type:     'CARWASH_MANAGER',
            mode:     $mode,
            source:   $payload->get('source'),
            status:   $status,
            date:     new \DateTime($payload->get('date'), $user->getTimezone()->getDateTimeZone()),
            bayId:    $payload->get('bayId'),
            cr:       $cr
        );

        if (in_array($transaction->getFiscal(), ['error','data_error'])) {
            $responseCode = JsonResponse::HTTP_BAD_REQUEST;
        } else {
            $responseCode = JsonResponse::HTTP_OK;
        }

        return $this->json(data: $transaction, status: $responseCode);
    }
}

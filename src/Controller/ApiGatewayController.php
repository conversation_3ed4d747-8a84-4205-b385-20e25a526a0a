<?php

namespace App\Controller;

use App\Entity\User;
use App\Repository\LoyaltyAppRepository;
use App\Service\LoyalAppStatistics\LoyalAppStatistics;
use Bkf\Connector\Service\SerwisApi\SerwisProxyConnector;
use I2m\Connectors\Service\CwLoyaltyApi\LoyaltyProxyService;
use I2m\Connectors\Service\Wla\WlaApiProxy;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

use function Sentry\captureMessage;

class ApiGatewayController extends AbstractController
{
    /** Kontroler przekierowuje wszystkie requsty do beloyal-api */
    #[Route(path: '/api/gateway/wla-admin/{path}', requirements: ["path" => ".+"])]
    public function wlaAdmin(Request $request, string $path, WlaApiProxy $apiConnector, LoyalAppStatistics $loyalAppStatistics)
    {
        $source = $request->getPayload()->get('app');
        /** @var User $user */
        $user = $this->getUser();
        $app = $loyalAppStatistics->getApp($source, $user);
        if (!$app) {
            return new JsonResponse(["nie masz uprawnien do tej aplikacji"], JsonResponse::HTTP_FORBIDDEN);
        }
        $request->headers->set('X-User-Email', $user->getEmail());

        return $apiConnector
            ->proxy(
                $app,
                $request,
                "api/admin/" . $path
            );
    }

    #[Route(path: '/api/gateway/wla-owner/{path}', requirements: ["path" => ".+"])]
    public function wlaOwner(Request $request, string $path, WlaApiProxy $apiConnector, LoyaltyAppRepository $loyaltyAppRepository)
    {
        /** @var User $user */
        $user = $this->getUser();
        $app = $loyaltyAppRepository->findOneByName("beloyal24.com");
        // $app = $loyaltyAppRepository->findOneByName("test-local");
        $request->headers->set('X-User-Email', $user->getEmail());

        return $apiConnector
            ->proxy(
                $app,
                $request,
                "api/owner/{$user->getOwnerBkf()}/" . $path
            );
    }

    #[Route(path: '/api/gateway/bkfpay-owner/{path}', requirements: ["path" => ".+"])]
    public function bkfPayGateway(Request $request, string $path, LoyaltyProxyService $apiConnector)
    {
        if (!$this->isGranted('ROLE_SUBSCRIPTION_BASIC')) {
            captureMessage('No access, purchased subscription type is to weak.');
            return $this->json([], Response::HTTP_PAYMENT_REQUIRED);
        }
        /** @var User $user */
        $user = $this->getUser();
        $request->headers->set('X-User-Email', $user->getEmail());
        return $apiConnector
            ->proxy(
                $request,
                "api/owner/{$user->getOwnerBkf()}/" . $path
            );
    }

    /** Kontroler przekierowuje wszystkie requsty do serwis-api */
    #[Route(path: '/api/gateway/bkf-service/{path}', requirements: ["path" => ".+"])]
    #[Route(path: '/api/service/{path}', requirements: ["path" => ".+"])]
    public function bkfService(Request $request, string $path, SerwisProxyConnector $serwisApiConnector)
    {
        /** @var User $user */
        $user = $this->getUser();

        $request->headers->set('X-User-Email', $user->getEmail());
        return $serwisApiConnector
            ->proxy(
                $request,
                'api/admin/' . $path
            );
    }
}

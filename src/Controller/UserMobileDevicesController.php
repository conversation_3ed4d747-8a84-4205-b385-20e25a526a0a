<?php

namespace App\Controller;

use App\Entity\User;
use App\Service\Mobile\MobileNotificationService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;

class UserMobileDevicesController extends AbstractController
{
    #[Route(path: '/cm/user/mobile_devices', name: 'update_user_mobile_token', methods: ['PATCH', 'OPTIONS'])]
    public function patchUserMobileToken(Request $request, MobileNotificationService $notificationService): JsonResponse
    {
        $payload = $request->getPayload();
        /** @var User $user */
        $user = $this->getUser();
        $notificationService->storeUserMobileToken(
            $user,
            $payload->get('token'),
            $payload->get('device_info', '')
        );
        return $this->json([]);
    }

    #[Route(path: '/cm/user/mobile_devices', name: 'remove_user_mobile_token', methods: ['DELETE', 'OPTIONS'])]
    public function removeUserMobileToken(Request $request, MobileNotificationService $notificationService): JsonResponse
    {
        $payload = $request->getPayload();
        /** @var User $user */
        $user = $this->getUser();
        $notificationService->removeUserMobileToken(
            $user,
            $payload->get('token'),
        );
        return $this->json([]);
    }
}

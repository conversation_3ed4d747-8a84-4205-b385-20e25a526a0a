<?php

namespace App\Controller\Reports;

use App\Entity\Enum\ReportPeriod;
use App\Entity\ReportConfig;
use App\Entity\ReportFile;
use App\Entity\User;
use App\Repository\ReportConfigRepository;
use App\Service\Report2\ReportService;
use I2m\Reports\Enum\FileExtention;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Serializer\SerializerInterface;

use function Sentry\captureMessage;

class ReportsConfigController extends AbstractController
{
    #[Route(path: '/api/report_configs', methods: ['GET'])]
    public function getList(
        Request $request,
        ReportConfigRepository $repository,
    ): JsonResponse {
        /** @var User $user */
        $user = $this->getUser();
        if (!$this->isGranted('ROLE_SUBSCRIPTION_BASIC')) {
            captureMessage('No access, purchased subscription type is to weak.');
            return $this->json([], Response::HTTP_PAYMENT_REQUIRED);
        }

        $page = $request->query->getInt('page', 1);
        $perPage = $request->query->getInt('perPage', 10);

        $reports = $repository->getList($user, $page, $perPage);

        return $this->json(
            $reports,
            JsonResponse::HTTP_OK,
            [],
            [
                'groups' => [
                    'report:config:list',
                    'default:basic'
                ]
            ]
        );
    }

    #[Route(path: '/api/report_configs', methods: ['POST'])]
    public function create(Request $request, ReportService $reportService)
    {
        /** @var User $user */
        $user = $this->getUser();
        $criterias = $request->query->all();

        $reportName = 'App\Reports\\' . $criterias['report'];
        unset($criterias['report']);

        $ext = FileExtention::from($request->get('ext', 'pdf'));
        unset($criterias['ext']);

        $title = $criterias['title'] ?? null;
        unset($criterias['title']);

        $emails = $criterias['email'] ?? null ? explode(',', $criterias['email']) : null;
        unset($criterias['email']);

        $period = ReportPeriod::from($criterias['period']);
        unset($criterias['period']);

        $config = $reportService->createConfig(
            user: $user,
            reportType: $reportName,
            ext: $ext,
            period: $period,
            criterias: $criterias,
            emails: $emails,
            title: $title,
        );

        return $this->json(
            $config,
            JsonResponse::HTTP_OK,
            [],
            [
                'groups' => [
                    'report:config:list',
                    'default:basic'
                ]
            ]
        );
    }

    #[Route(path: '/api/report_config/{id}', methods: ['GET'])]
    public function getItem(
        int $id,
        ReportConfigRepository $repository,
    ): JsonResponse {
        /** @var User $user */
        $user = $this->getUser();
        if (!$this->isGranted('ROLE_SUBSCRIPTION_BASIC')) {
            captureMessage('No access, purchased subscription type is to weak.');
            return $this->json([], Response::HTTP_PAYMENT_REQUIRED);
        }

        $report = $repository->findOneBy(['id' => $id, 'user' => $user ]);

        return $this->json(
            $report,
            JsonResponse::HTTP_OK,
            [],
            [
                'groups' => [
                    'report:config:list',
                    'default:basic'
                ]
            ]
        );
    }

    #[Route(path: '/api/report_config/{id}', methods: ['PATCH'])]
    public function update(
        int $id,
        ReportConfigRepository $repository,
        SerializerInterface $serializer,
        Request $request
    ): JsonResponse {
        /** @var User $user */
        $user = $this->getUser();
        if (!$this->isGranted('ROLE_SUBSCRIPTION_BASIC')) {
            captureMessage('No access, purchased subscription type is to weak.');
            return $this->json([], Response::HTTP_PAYMENT_REQUIRED);
        }

        $report = $repository->findOneBy(['id' => $id, 'user' => $user ]);

        $serializer->deserialize(
            $request->getContent(),
            ReportConfig::class,
            'json',
            [
                'object_to_populate' => $report,
                'groups' => [
                    'report:config:user_write'
                ]
            ]
        );

        $repository->save($report);

        return $this->json(
            $report,
            JsonResponse::HTTP_OK,
            [],
            [
                'groups' => [
                    'report:config:list',
                    'default:basic'
                ]
            ]
        );
    }

    #[Route(path: '/api/report_config/{id}', methods: ['DELETE'])]
    public function delete(
        int $id,
        ReportConfigRepository $repository,
        SerializerInterface $serializer,
        Request $request
    ): JsonResponse {
        /** @var User $user */
        $user = $this->getUser();
        if (!$this->isGranted('ROLE_SUBSCRIPTION_BASIC')) {
            captureMessage('No access, purchased subscription type is to weak.');
            return $this->json([], Response::HTTP_PAYMENT_REQUIRED);
        }

        $report = $repository->findOneBy(['id' => $id, 'user' => $user ]);

        $report->setEnabled(false);
        $repository->save($report);

        return $this->json(
            $report,
            JsonResponse::HTTP_OK,
            [],
            [
                'groups' => [
                    'report:config:list',
                    'default:basic'
                ]
            ]
        );
    }

    #[Route(path: '/api/report_config/{id}/generate', methods: ['POST'])]
    public function generate(
        int $id,
        ReportConfigRepository $repository,
        ReportService $reportService
    ): JsonResponse {
        /** @var User $user */
        $user = $this->getUser();
        if (!$this->isGranted('ROLE_SUBSCRIPTION_BASIC')) {
            captureMessage('No access, purchased subscription type is to weak.');
            return $this->json([], Response::HTTP_PAYMENT_REQUIRED);
        }

        $config = $repository->findOneBy(['id' => $id, 'user' => $user ]);

        $dates = $reportService->prepareReportTimeInterval($config->getPeriod());
        $report = $reportService->generateAsync(
            subscriber: $user->getSubscriber(),
            reportType: $config->getType(),
            ext:        $config->getExt(),
            criterias:  array_merge($config->getCriterias(), $dates),
            emails:     $config->getEmail(),
            user:       $user,
            title:      $config->getTitle(),
        );

        return $this->json(
            $report,
            ReportFile::getReportStatus($report->getStatus()),
            [],
            [
                'groups' => [
                    'report:file:list',
                    'default:basic'
                ]
            ]
        );
    }
}

<?php

namespace App\Controller\Loyalty;

use App\Entity\User;
use App\Reports\v2\LoyaltyTopUps3Report;
use App\Service\Report2\ReportService;
use I2m\Connectors\Service\CwLoyaltyApi\LoyaltyTopUpService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;

class LoyaltyTopUpsController3 extends AbstractController
{
    #[Route(path: '/api/loyalty/v3/top_ups', methods: ['GET'])]
    public function getTopUps(
        Request $request,
        ReportService $reportService,
    ): JsonResponse {
        /** @var User $user */
        $user = $this->getUser();
        $ownerId = $user->getOwnerBkf();

        if (empty($ownerId)) {
            return new JsonResponse(["error" => "owner not found"], JsonResponse::HTTP_BAD_REQUEST);
        }
        $page = $request->query->getInt('page');
        $perPage = $request->query->getInt('perPage');

        $criterias = $request->query->all();

        $report = $reportService->getData(
            reportType: LoyaltyTopUps3Report::class,
            criteria:   $criterias,
            subscriber: $user->getSubscriber(),
            user:       $user,
            page: $page,
            perPage: $perPage
        );

        return $this->json(data: $report, context: ['groups' => ['currency:basic', "report:data"]]);
    }

    #[Route(path: '/api/loyalty/v3/top_up/{id}/invoice', methods: ['POST'])]
    public function generateInvoice2(
        Request $request,
        LoyaltyTopUpService $loyaltyTopUpService,
        int $id,
    ): JsonResponse {

        /** @var User $user */
        $user = $this->getUser();
        $payload = $request->getPayload();
        $paymentMethod = $payload->get('paymentMethod') ?: null;
        $paymentInterval = $payload->get('paymentTerm') ?: null;
        $description = $payload->get('description') ?: null;
        $ai = $description ? ['description' => $description] : null;

        $topUp = $loyaltyTopUpService->invoice($user->getOwnerBkf(), $id, $paymentMethod, $paymentInterval, $ai);
        return $this->json($topUp, JsonResponse::HTTP_OK);
    }

    #[Route(path: '/api/loyalty/v3/top_up/{token}/register', methods: ['POST'])]
    public function registerTopUp(
        LoyaltyTopUpService $loyaltyTopUpService,
        string $token,
    ): JsonResponse {

        /** @var User $user */
        $user = $this->getUser();

        $topUp = $loyaltyTopUpService->register($user->getOwnerBkf(), $token);
        return $this->json($topUp, JsonResponse::HTTP_OK);
    }
}

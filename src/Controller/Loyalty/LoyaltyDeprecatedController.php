<?php

namespace App\Controller\Loyalty;

use App\Classes\Exception\ClientSideException;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Routing\Annotation\Route;

class LoyaltyDeprecatedController extends AbstractController
{
    #[Route(path: '/api/loyalty/cards/stats', methods: ['GET'])] // do usuniecia, po migracji apki
    public function depracated(): JsonResponse
    {
        return $this->json(['error' => "use /api/loyalty/v3/... methods"]);
    }
}

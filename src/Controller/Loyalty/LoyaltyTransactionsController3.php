<?php

namespace App\Controller\Loyalty;

use App\Entity\User;
use App\Reports\v2\LoyaltyTransactions3Report;
use App\Service\Report2\ReportService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

use function Sentry\captureMessage;

class LoyaltyTransactionsController3 extends AbstractController
{
    #[Route(path: '/api/loyalty/v3/transactions', methods: ['GET'])]
    #[Route(path: '/api/loyalty/transactions', methods: ['GET'])] // <- nie u<PERSON>y<PERSON>, jakieś zew. api korzysta
    public function getData(Request $request, ReportService $reportService): JsonResponse
    {
        /** @var User $user */
        $user = $this->getUser();
        if (!$this->isGranted('ROLE_SUBSCRIPTION_BASIC')) {
            captureMessage('No access, purchased subscription type is to weak.');
            return $this->json([], Response::HTTP_PAYMENT_REQUIRED);
        }
        $ownerId = $user->getOwnerBkf();
        if (empty($ownerId)) {
            return new JsonResponse(["error" => "owner not found"], JsonResponse::HTTP_BAD_REQUEST);
        }
        $page = $request->query->getInt('page', 1);
        $perPage = $request->query->getInt('perPage', 50);

        $criterias = $request->query->all();

        $report = $reportService->getData(
            reportType: LoyaltyTransactions3Report::class,
            criteria:   $criterias,
            subscriber: $user->getSubscriber(),
            user:       $user,
            page: $page,
            perPage: $perPage
        );

        return $this->json(data: $report, context: ['groups' => ['currency:basic', "report:data"]]);
    }
}

<?php

namespace App\Controller\BkfService;

use App\Entity\User;
use App\Repository\CarwashesRepository;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use App\Service\CarwashIssues\CarwashIssuesService;

class CarwashIssuesController extends AbstractController
{
    public function __construct(
        private CarwashIssuesService $carwashIssuesService
    ) {
    }

    #[Route(path: '/administration/carwashissues', name: 'get_carwash_issues_grouped', methods: ['GET'])]
    public function getCarwashIssuesGrouped(Request $request): JsonResponse
    {
        /** @var User $user */
        $user = $this->getUser();

        $sn = $request->get('serial') ? explode(',', $request->get('serial')) : null;
        $data = $this->carwashIssuesService->getCarwashIssues(
            $sn,
            $user->getLanguage()->getLocale(),
            $user->getTimezone()->getLocation()
        );

        return $this->json(
            $data,
            Response::HTTP_OK,
        );
    }

    #[Route(path: '/administration/carwashissues/list', name: 'get_carwash_issues_list', methods: ['GET'])]
    public function getCarwashIssuesList(Request $request, CarwashesRepository $carwashesRepository): JsonResponse
    {

        /** @var User $user */
        $user = $this->getUser();
        $groups = $request->get('groups') ? explode(',', $request->get('groups')) : null;
        $sn = $request->get('serial') ? explode(',', $request->get('serial')) : null;
        $page = $request->get('page') ?? 1;
        $perPage = $request->get('perPage') ?? 999;
        $search = $request->get('search') ?? null;
        $sortByColumnName = $request->get('sortByColumnName') ?? 'id';
        $sortDirection = $request->get('sortDirection') ?? 'DESC';
        $dateFrom = $request->get('dateFrom') ?? null;
        $dateTo = $request->get('dateTo') ?? null;
        $isOpen = $request->get('isOpen') ?? null;


        $data = $this->carwashIssuesService->getCarwashIssuesList(
            $carwashesRepository,
            $groups,
            $sn,
            $user->getLanguage()->getLocale(),
            $user->getTimezone()->getLocation(),
            $page,
            $perPage,
            $sortByColumnName,
            $sortDirection,
            $search,
            null,
            $dateFrom,
            $dateTo,
            $isOpen
        );

        return $this->json(
            $data,
            Response::HTTP_OK,
        );
    }

    #[Route(path: '/administration/carwashissues/history', name: 'get_carwash_issues_history', methods: ['GET'])]
    public function getCarwashIssuesHistory(Request $request, CarwashesRepository $carwashesRepository): JsonResponse
    {

        /** @var User $user */
        $user = $this->getUser();
        $groups = $request->get('groups') ? explode(',', $request->get('groups')) : null;
        $sn = $request->get('serial') ? explode(',', $request->get('serial')) : null;
        $page = $request->get('page') ?? 1;
        $perPage = $request->get('perPage') ?? 999;
        $search = $request->get('search') ?? null;
        $sortByColumnName = $request->get('sortByColumnName') ?? 'id';
        $sortDirection = $request->get('sortDirection') ?? 'DESC';
        $dateFrom = $request->get('dateFrom') ?? null;
        $dateTo = $request->get('dateTo') ?? null;

        $data = $this->carwashIssuesService->getCarwashIssuesHistory(
            $groups,
            $sn,
            $user->getLanguage()->getLocale(),
            $user->getTimezone()->getLocation(),
            $page,
            $perPage,
            $sortByColumnName,
            $sortDirection,
            $search,
            $dateFrom,
            $dateTo,
        );

        return $this->json(
            $data,
            Response::HTTP_OK,
        );
    }

    #[Route(path: '/administration/carwashissues/history/stats', name: 'get_carwash_issues_history_stats', methods: ['GET'])]
    public function getCarwashIssuesHistoryStats(Request $request): JsonResponse
    {
        /** @var User $user */
        $user = $this->getUser();

        $dateFrom = $request->get('dateFrom') ?? null;
        $dateTo = $request->get('dateTo') ?? null;
        $timezone = $user->getTimezone()->getLocation();

        $data = $this->carwashIssuesService->getCarwashIssuesHistoryStats(
            $dateFrom,
            $dateTo,
            $timezone,
        );

        return $this->json(
            $data,
            Response::HTTP_OK,
        );
    }

    #[Route(path: '/administration/carwashissue/{id}', name: 'get_carwash_issues_details', methods: ['GET'])]
    public function getCarwashIssuesDetails(int $id): JsonResponse
    {
        /** @var User $user */
        $user = $this->getUser();

        $data = $this->carwashIssuesService->getCarwashIssuesDetails(
            $id,
            $user->getLanguage()->getLocale(),
            $user->getTimezone()->getLocation()
        );

        return $this->json(
            $data,
            Response::HTTP_OK,
        );
    }

    #[Route(path: '/administration/carwashissue/{id}/similar', name: 'get_carwash_issues_similar', methods: ['GET'])]
    public function getCarwashIssuesSimilar(int $id): JsonResponse
    {
        /** @var User $user */
        $user = $this->getUser();

        $data = $this->carwashIssuesService->getCarwashIssuesSimilar(
            $id,
            $user->getLanguage()->getLocale(),
            $user->getTimezone()->getLocation()
        );

        return $this->json(
            $data,
            Response::HTTP_OK,
        );
    }



    #[Route(path: '/administration/carwashissues/update', name: 'update_carwash_issues', methods: ['POST'])]
    public function updateCarwashIssues(Request $request): JsonResponse
    {
        /** @var User $user */
        $user = $this->getUser();

        $content = json_decode($request->getContent(), true);
        $payload = $request->getPayload();
        $issues = $content['issues'];
        $date = $payload->get('date');
        $explanation = $payload->get('explanation');
        $action = $payload->get('action');
        $priority = $payload->get('priority');

        $this->carwashIssuesService->updateCarwashIssues(
            $user,
            $issues,
            $date,
            $explanation,
            $action,
            $priority,
        );

        return $this->json(
            null,
            Response::HTTP_OK
        );
    }
}

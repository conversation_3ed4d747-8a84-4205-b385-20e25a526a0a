<?php

namespace App\Controller\ApiController;

use App\Classes\Exception\ClientSideException;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\HttpFoundation\JsonResponse;

class DeprecatedProgramsUsageApiController extends AbstractApiController
{
    /**
     * @deprecated
     */
    #[Route(path: '/cm/finance/programsusage_total_csv', methods: ['GET'])]
    #[Route(path: '/cm/finance/programsusage_total_excel', methods: ['GET'])]
    #[Route(path: '/cm_new/programsusage/', methods: ['GET'])]
    #[Route(path: '/cm_new/portal/programsusage/', methods: ['GET'])]
    public function indexAction(Request $request): JsonResponse
    {
        throw new ClientSideException(
            'Method is deprecated. Use /api/reports/data'
        );
    }
}

<?php

namespace App\Controller\ApiController;

use App\Entity\User;
use App\Repository\CarwashesRepository;
use App\Service\CarwashApi\Param\ParamApiService;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use OpenApi\Annotations as OA;
use Exception;

class ParamApiController extends AbstractApiController
{
    public function __construct(private ParamApiService $paramApiService)
    {
    }
    /**
     * @OA\Post(summary="Get params for carwashes")
     * @OA\Parameter(
     *     name="sn",
     *     in="query",
     *     required=true,
     *     description="Car wash serial numbers",
     *     @OA\Schema(type="string", example="10001,150")
     * )
     * @throws Exception
     */
    #[Route(path: '/cm_new/param', methods: ['GET'])]
    public function indexAction(Request $request, CarwashesRepository $carwashesRepository): JsonResponse
    {
        $sn = $request->get('sn');
        /** @var User $user */
        $user = $this->getUser();
        $carwash = $carwashesRepository->findOneBySerialNumber($sn);

        if (!$user->hasAccessToCarwashBySn($carwash->getSerialNumber())) {
            $this->checkPermision($user, $carwash->getSubscriber());
        }

        return $this->json(
            $this->paramApiService->getCarwashParams(
                $carwash->getSerialNumber(),
                strtolower($user->getLanguage()->getCode()),
                $user->getTimezone()->getLocation()
            )
        );
    }

    /**
     * @OA\Post(summary="Get params for carwashes")
     * @OA\Parameter(
     *     name="sn",
     *     in="query",
     *     required=true,
     *     description="Car wash serial numbers",
     *     @OA\Schema(type="string", example="10001,150")
     * )
     * @throws Exception
     */
    #[Route(path: '/cm_new/param-history', methods: ['POST'])]
    public function getParamValues(Request $request, CarwashesRepository $carwashesRepository): JsonResponse
    {
        $data = json_decode($request->getContent());
        $sn = $request->get('sn');
        /** @var User $user */
        $user = $this->getUser();
        $carwash = $carwashesRepository->findOneBySerialNumber($sn);

        if (!$user->hasAccessToCarwashBySn($carwash->getSerialNumber())) {
            $this->checkPermision($user, $carwash->getSubscriber());
        }

        $res = [];
        $params = json_decode($data->params, true);
        foreach ($params as $param) {
            array_push($res, $this->paramApiService->getCarwashParamsHistory(
                $carwash->getSerialNumber(),
                $param,
                $data->date_from,
                $data->date_to,
                $user->getTimezone()->getLocation(),
                strtolower($user->getLanguage()->getCode()),
            ));
        }
        return $this->json(
            $res
        );
    }
}

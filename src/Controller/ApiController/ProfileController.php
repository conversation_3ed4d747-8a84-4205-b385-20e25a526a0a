<?php

namespace App\Controller\ApiController;

use App\Entity\User;
use App\Entity\Subscribers;
use App\Entity\UserReports;
use App\Repository\LanguagesRepository;
use App\Repository\LoggerRepository;
use App\Repository\TimezonesRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Serializer\SerializerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;

class ProfileController extends AbstractController
{
    #[Route(path: '/api/profile', methods: 'GET')]
    public function getProfile(): JsonResponse
    {
        /** @var User $user */
        $user = $this->getUser();

        return $this->json(
            data: $user,
            context:
                  [
                      'circular_reference_limit' => 0,
                      'circular_reference_handler' => function ($object) {
                          return $object->getId();
                      },
                      'groups' =>
                          [
                          'alert:info',
                          "user:info",
                          "user:reports",
                          "user:details",
                          'subscriber:details',
                          "timezone:basic",
                          "country:basic",
                          "currency:basic",
                          "language:basic",
                          "carwash:basic"
                          ]

                  ]
        );
    }

    #[Route(path: '/api/profile', methods: 'PATCH')]
    public function updateProfile(
        SerializerInterface $serializer,
        Request $request,
        LanguagesRepository $languageRepository,
        TimezonesRepository $timezonesRepository,
        EntityManagerInterface $em,
        LoggerRepository $loggerRepository
    ): JsonResponse {
        /** @var User $user */
        $user = $this->getUser();
        $ip = $request->getClientIp();
        $client = $request->headers->get('User-Agent');
        $loggerRepository->debug($user->getSubscriber(), $user, null, 'updateProfile', "$client $ip: " . $request->getContent());
        $serializer->deserialize($request->getContent(), User::class, 'json', ['object_to_populate' => $user]);

        $user->setTimezone($user->getTimezone()->getId() ? $timezonesRepository->find($user->getTimezone()->getId()) : null);
        $user->setLanguage($user->getLanguage()->getLocale() ? $languageRepository->findOneByLocale($user->getLanguage()->getLocale()) : null);

        $em->persist($user);
        $em->flush();

        return $this->json(
            data: $user,
            context:
                  [
                      'circular_reference_limit' => 0,
                      'circular_reference_handler' => function ($object) {
                          return $object->getId();
                      },
                      'groups' =>
                          [
                              "user:reports",
                              "user:details",
                              'subscriber:details',
                              "timezone:basic",
                              "country:basic",
                              "currency:basic",
                              "language:basic",
                              "carwash:basic"
                          ],
                    ],
        );
    }

    #[Route(path: '/api/profile/reports', methods: 'GET')]
    public function getProfileReports(): JsonResponse
    {
        /** @var User $user */
        $user = $this->getUser();
        $reportsSettings = $user->getReportSettings();
        return $this->json(
            data: $reportsSettings,
            context: [
                'groups' =>
                [
                    "user:reports",
                ],
            ],
        );
    }

    #[Route(path: '/api/profile/reports', methods: 'PATCH')]
    public function updateProfileReports(
        Request $request,
        SerializerInterface $serializer,
        EntityManagerInterface $em,
    ): JsonResponse {
        /** @var User $user */
        $user = $this->getUser();
        $reportsSettings = $user->getReportSettings();
        $serializer->deserialize($request->getContent(), UserReports::class, 'json', ['object_to_populate' => $reportsSettings]);

        $em->persist($reportsSettings);
        $em->flush();

        return $this->json(
            data: $reportsSettings,
            context: [
                'groups' =>
                [
                    "user:reports",
                ],
            ],
        );
    }
}

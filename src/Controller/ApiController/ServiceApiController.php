<?php

namespace App\Controller\ApiController;

use App\Entity\User;
use App\Service\Connectors\BkfApi\BkfApiService;
use App\Service\Connectors\BkfApi\ServiceApiService;
use Symfony\Component\HttpFoundation\JsonResponse;
use OpenApi\Annotations as OA;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;

class ServiceApiController extends AbstractApiController
{
    private ServiceApiService $api;
    public function __construct(ServiceApiService $api)
    {
        $this->api = $api;
    }
    /**
    * @OA\Parameter(
    *     name="sn",
    *     in="query",
    *     description="Carwash serial number",
    *     @OA\Schema(type="string", example="1001,12990,5300")
    * )
    * @OA\Parameter(
    *     name="page",
    *     in="query",
    *     required=false,
    *     description="The collection page number",
    *     @OA\Schema(type="integer", example=1)
    * )
    * @OA\Parameter(
    *     name="limit",
    *     in="query",
    *     required=false,
    *     description="The number of items per page",
    *     @OA\Schema(type="integer", example=10)
    * )
    * @OA\Parameter(
    *     name="status",
    *     in="query",
    *     required=false,
    *     description="Status filter (open/completed)",
    *     @OA\Schema(type="string", example="open")
    * )
    * @OA\Parameter(
    *     name="dateFrom",
    *     in="query",
    *     required=false,
    *     description="Date From",
    *     @OA\Schema(type="string", example="2021-01-01")
    * )
    * @OA\Parameter(
    *     name="dateTo",
    *     in="query",
    *     required=false,
    *     description="Date to",
    *     @OA\Schema(type="string", example="2021-12-31")
    * )
    * @OA\Parameter(
    *     name="sortBy",
    *     in="query",
    *     required=false,
    *     description="Field to sort by [issue_id, issue_content, issue_title, type, isSurvey,
          status, issue_report_time, serialnumber, object_number, object_name]",
    *     @OA\Schema(type="string", example="issue_id")
    * )
    * @OA\Parameter(
    *     name="sortDesc",
    *     in="query",
    *     required=false,
    *     description="Sorts descending when set to true",
    *     @OA\Schema(type="bool", example="1")
    * )
     * @deprecated use /api/carwash_service
    */
    #[Route(path: '/cm_new/service/', methods: ['GET'])]
    public function index(Request $request): JsonResponse
    {
        /** @var User $user */
        $user = $this->getUser();
        $status = $request->get('status');
        $limit = $request->get('limit');
        $page = $request->get('page');
        $dateFrom = $request->get('dateFrom');
        $dateTo = $request->get('dateTo');
        $sortBy = $request->get('sortBy');
        $sortDesc = filter_var($request->get('sortDesc', true), FILTER_VALIDATE_BOOLEAN);
        $carwashes = $this->getCarwashSns($user, $request->get('sn'));

        $reporter = $request->get('reporter');
        $type = $request->get('type');
        $source = $request->get('source');
        if (null === $user->getOwnerBkf()) {
            return $this->json([
                'error' => 1,
                'message' => 'User\'s BKF Owner ID is not set',
            ], 400);
        }
        return $this->json($this->api->getServiceByOwner(
            $user->getOwnerBkf(),
            $carwashes,
            $dateFrom,
            $dateTo,
            $page,
            $limit,
            $status,
            $sortBy,
            $sortDesc,
            $reporter,
            $type,
            $source
        ));
    }

    #[Route(path: '/api/service_issues', methods: ['GET'])]
    public function index2(Request $request, BkfApiService $apiService): JsonResponse
    {
        /** @var User $user */
        $user = $this->getUser();
        $status = $request->get('status') ? explode(',', $request->get('status')) : null;
        $limit = $request->get('limit', 1);
        $page = $request->get('page', 1);
        $dateFrom = $request->get('dateFrom');
        $dateTo = $request->get('dateTo');
        $carwashes = $this->getCarwashSns($user, $request->get('sn')) ?? [];

        return $this->json($apiService->getIssues(
            serial: $carwashes,
            status: $status,
            limit: $limit,
            page: $page,
            orderDir: 'DESC'
        ));
    }
}

<?php

namespace App\Controller\ApiController;

use App\Entity\Subscribers;
use App\Service\InvoiceApi\DataProviders\InvoiceListProvider;
use App\Service\InvoiceApi\DataProviders\InvoiceProvider;
use App\Service\InvoiceApi\InvoiceApi;
use App\Service\InvoiceApi\InvoiceSenders\SelfInvoiceSender;
use App\Service\InvoiceApi\SelfInvoice\SelfInvoiceApiGenerator;
use App\Entity\User;
use DateTime;
use DateTimeZone;
use Doctrine\ORM\EntityManagerInterface;
use OpenApi\Annotations as OA;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Translation\Translator;
use Symfony\Contracts\Translation\TranslatorInterface;

class SelfInvoiceController extends AbstractApiController
{
    /**
     * @OA\Get(summary="Get a list of self invoices")
     * @OA\Parameter(
     *     name="page",
     *     in="query",
     *     required=false,
     *     description="The collection page number",
     *     @OA\Schema(type="integer", example=1)
     * )
     * @OA\Parameter(
     *     name="limit",
     *     in="query",
     *     required=false,
     *     description="The number of items per page",
     *     @OA\Schema(type="integer", example=10)
     * )
     * @OA\Parameter(
     *     name="order",
     *     in="query",
     *     required=false,
     *     description="Order field (by time) (ASC/DESC)",
     *     @OA\Schema(type="string", example="DESC")
     * )
     * @OA\Parameter(
     *     name="dateFrom",
     *     in="query",
     *     required=false,
     *     description="Date From",
     *     @OA\Schema(type="string", example="2021-01-01")
     * )
     * @OA\Parameter(
     *     name="dateTo",
     *     in="query",
     *     required=false,
     *     description="Date to",
     *     @OA\Schema(type="string", example="2021-12-31")
     * )
     * @OA\Response(
     *     response=200,
     *     description="Returns information about self invoices",
     *         content={
     *             @OA\MediaType(
     *                 mediaType="application/json",
     *                 @OA\Schema(example={
     *                              "data": {
     *                                 {
     *                                    "confirmationDate": null,
     *                                    "confirmedByUser": null,
     *                                    "id": 33012,
     *                                    "issuanceDate": "2022-02-01",
     *                                    "number": "BE-LOYAL-57/01/2022",
     *                                    "price": "1971.90",
     *                                    "currency": "zł",
     *                                    "period": "2022-01",
     *                                    "published": true,
     *                                    "fileId": 7030,
     *                                    "downloadUrl": "http://127.0.0.1:8000/cm_invoices/7030/download/**********"
     *                                  }
     *                                },
     *                                "sums": {
     *                                     "countTotal": "123"
     *                                     }
     *                                 })
     *              )
     *          }
     * )
     * @OA\Tag(name="SelfInvoice")
     */
    #[Route(path: '/cm_new/selfInvocice/list', methods: ['GET'])]
    public function listAction(InvoiceListProvider $invoiceListProvider): JsonResponse
    {
        /** @var User $user */
        $user = $this->getUser();
        $timezone = $user->getTimezone()->getLocation();
        $tz = new DateTimeZone($timezone);
        $data = $invoiceListProvider->getInvoiceList(
            InvoiceListProvider::INVOICE_TYPE_SELF_INVOICE,
            $user->getSubscriber()->getId(),
            $tz,
            'selfinvoice_invoice_download'
        );
        $result = ['data' => $data, 'sums' => ['countTotal' => 0]];
        return $this->json($result);
    }

    #[Route(path: '/cm_new/selfInvocice/{id}/download', methods: ['GET'], name: 'selfinvoice_invoice_download')]
    public function download(int $id, InvoiceApi $invoiceApi, InvoiceProvider $invoiceProvider): Response
    {
        /** @var User $user */
        $user = $this->getUser();

        $invoice = $invoiceProvider->getInvoiceData($id);


        if ($invoice == null) {
            return new Response('invoice not found', Response::HTTP_NOT_FOUND);
        }

        if ($invoice->getIssuer()->getExternalId() != $user->getSubscriber()->getId()) {
            return new Response('owners not match', Response::HTTP_FORBIDDEN);
        }

        $filename = $invoice->getFileName();
        return $invoiceApi->getInvoiceFileResponse(
            $filename,
            $id
        );
    }
    /**
     * @OA\Post(summary="Confirm self invoice")
     *
     * @OA\RequestBody(
     *      description= "Confirm self invoice",
     *      required= true,
     *      content={
     *             @OA\MediaType(
     *                 mediaType="application/json",
     *                 @OA\Schema(
     *                      example={
     *                          "invoiceId": 23423,
     *                          "ip": "127.0.0.1"
     *                      }
     *                  )
     *              )
     *       }
     * )
     * @OA\Response(
     *     response=200,
     *     description="Invoice confirmation succeed",
     *         content={
     *             @OA\MediaType(
     *                 mediaType="application/json",
     *                 @OA\Schema(
     *                   example={}
     *                 )
     *              )
     *          }
     * )
     * @OA\Tag(name="SelfInvoice")
     */
    #[Route(path: '/cm_new/selfInvocice/confirm', methods: ['POST'], name: 'self_invoice_confirm')]
    public function indexAction(
        Request $request,
        InvoiceProvider $invoiceProvider,
        InvoiceApi $invoiceApi,
        SelfInvoiceApiGenerator $selfInvoiceApiGenerator,
        SelfInvoiceSender $selfInvoiceSender,
        TranslatorInterface $translator
    ): JsonResponse {
        /** @var Translator $translator */
        $translator->setLocale('pl');

        /** @var User $user */
        $user = $this->getUser();
        $ip = $request->getClientIp();
        $payload = $request->getPayload();
        $invoiceId = $payload->get('invoiceId', null);

        $subscriber = $user->getSubscriber();
        if ($subscriber === null) {
            return $this->json(['Subscriber not found.'], 404);
        }
        $invoice = $invoiceProvider->getInvoiceData(
            $invoiceId
        );
        if ($invoice === null) {
            return $this->json(['Invoice not found.'], 404);
        }
        $invoiceConfirmation = $invoice->getConfirmation();
        if ($invoiceConfirmation !== null) {
            return $this->json(['Invoice already confirmed.'], 409);
        }
        $tzUtc = new DateTimeZone('UTC');
        $confirmDate = new DateTime();
        $confirmDate->setTimezone($tzUtc);
        $response = $invoiceApi->confirmSelfInvoice(
            $invoiceId,
            $user->getEmail(),
            $ip,
            $confirmDate
        );
        if ($response === null) {
            return $this->json(['Confirmation error.'], 404);
        }
        $selfInvoiceApiGenerator->confirmSelfInvoice(
            $invoice,
            $user->getEmail(),
            $confirmDate
        );
        $selfInvoiceSender->sendConfirmationEmail(
            $subscriber->getEmail(),
            $invoice,
            $user->getEmail(),
            $confirmDate
        );
        return $this->json(['Invoice confirmed']);
    }

    /**
     * @OA\Post(summary="Confirm self invoice by uuid")
     *
     * @OA\Parameter(
     *     name="uuid",
     *     in="query",
     *     required=false,
     *     description="Date to",
     *     @OA\Schema(type="string", example="2a299e4c61d47ead39225ed589eef2e612171a8e45bc410c0a53e056bb995f5db")
     * )
     *
     * @OA\Parameter(
     *     name="email",
     *     in="query",
     *     required=false,
     *     description="Date to",
     *     @OA\Schema(type="string", example="<EMAIL>")
     * )
     * @OA\Response(
     *     response=200,
     *     description="Confirmation succeed",
     *         content={
     *             @OA\MediaType(
     *                 mediaType="application/json",
     *                 @OA\Schema(
     *                   example={}
     *                 )
     *              )
     *          }
     * )
     */
    #[Route(path: '/cm/self_invoice/confirm_by_uuid/{uuid}/{email}', methods: ['GET'], name: 'self_invoice_confirm_by_uuid_and_email')]
    public function confirmByUUIDAction(
        Request $request,
        EntityManagerInterface $em,
        string $uuid,
        string $email,
        InvoiceProvider $invoiceProvider,
        SelfInvoiceApiGenerator $selfInvoiceApiGenerator,
        InvoiceApi $invoiceApi,
        SelfInvoiceSender $selfInvoiceSender,
        TranslatorInterface $translator
    ): Response {
        /** @var Translator $translator */
        $translator->setLocale('pl');
        $ip = $request->getClientIp();
        $invoice = $invoiceProvider->getInvoiceDataByUuid($uuid);
        if ($invoice === null) {
            return $this->render(
                'SelfInvoice/confirmation.html.twig',
                [
                    'responseType' => 'fail'
                ]
            );
        }

        $issuer = $invoice->getIssuer();
        $issuerId = $issuer->getExternalId();
        $subscriber = $em->getRepository(Subscribers::class)->find($issuerId);
        if ($subscriber === null) {
            return $this->render(
                'SelfInvoice/confirmation.html.twig',
                [
                    'responseType' => 'fail'
                ]
            );
        }
        $invoiceConfirmation = $invoice->getConfirmation();
        if ($invoiceConfirmation !== null) {
            return $this->render(
                'SelfInvoice/confirmation.html.twig',
                [
                    'invoiceNumber' => $invoice->getParsedInvoiceNumber(),
                    'responseType' => 'alreadyConfirmed'
                ]
            );
        }
        $tzUtc = new DateTimeZone('UTC');
        $confirmDate = new DateTime();
        $confirmDate->setTimezone($tzUtc);
        $response = $invoiceApi->confirmSelfInvoice(
            $invoice->getId(),
            $subscriber->getEmail(),
            $ip,
            $confirmDate
        );
        if ($response === null) {
            return $this->json(['Confirmation error.'], 404);
        }
        $selfInvoiceApiGenerator->confirmSelfInvoice(
            $invoice,
            $subscriber->getEmail(),
            $confirmDate
        );
        $selfInvoiceSender->sendConfirmationEmail(
            $subscriber->getEmail(),
            $invoice,
            $subscriber->getEmail(),
            $confirmDate
        );
        return $this->render(
            'SelfInvoice/confirmation.html.twig',
            [
                'invoiceNumber' => $invoice->getParsedInvoiceNumber(),
                'responseType' => 'success'
            ]
        );
    }

    /**
     * @OA\Post(summary="Confirm self invoice dialog")
     *
     * @OA\Parameter(
     *     name="uuid",
     *     in="query",
     *     required=false,
     *     description="Date to",
     *     @OA\Schema(type="string", example="2a299e4c61d47ead39225ed589eef2e612171a8e45bc410c0a53e056bb995f5db")
     * )
     *
     * @OA\Parameter(
     *     name="email",
     *     in="query",
     *     required=false,
     *     description="Date to",
     *     @OA\Schema(type="string", example="<EMAIL>")
     * )
     *
     * @OA\Response(
     *     response=200,
     *     description="Confirmation succeed",
     *         content={
     *             @OA\MediaType(
     *                 mediaType="application/json",
     *                 @OA\Schema(
     *                   example={}
     *                 )
     *              )
     *          }
     * )
     * @OA\Tag(name="SelfInvoice")
     */
    #[Route(path: '/cm/self_invoice/confirm_dialog/{uuid}', methods: ['GET'], name: 'self_invoice_confirm_dialog')]
    public function confirmDialogAction(
        EntityManagerInterface $em,
        string $uuid,
        InvoiceProvider $invoiceProvider,
        TranslatorInterface $translator
    ): Response {
        /** @var Translator $translator */
        $translator->setLocale('pl');
        $translator->setLocale('pl');
        $invoice = $invoiceProvider->getInvoiceDataByUuid($uuid);
        if ($invoice === null) {
            return $this->render(
                'SelfInvoice/confirmationDialog.html.twig',
                [
                    'responseType' => 'fail'
                ]
            );
        }

        $issuer = $invoice->getIssuer();
        $issuerId = $issuer->getExternalId();
        $user = $em->getRepository(Subscribers::class)->find($issuerId);
        if ($user === null) {
            return $this->render(
                'SelfInvoice/confirmationDialog.html.twig',
                [
                    'responseType' => 'fail'
                ]
            );
        }
        $invoiceConfirmation = $invoice->getConfirmation();
        if ($invoiceConfirmation === null) {
            return $this->render(
                'SelfInvoice/confirmationDialog.html.twig',
                [
                    'invoiceNumber' => $invoice->getParsedInvoiceNumber(),
                    'uuid' => $invoice->getUuid(),
                    'responseType' => 'toConfirm'
                ]
            );
        }
        return $this->render(
            'SelfInvoice/confirmationDialog.html.twig',
            [
                'invoiceNumber' => $invoice->getParsedInvoiceNumber(),
                'responseType' => 'alreadyConfirmed'
            ]
        );
    }
}

<?php

namespace App\Controller\ApiController;

use App\Entity\User;
use App\Service\CarwashApi\PortalProgramsUsage\PortalProgramsUsageApiService;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\HttpFoundation\JsonResponse;

#[Route(path: '/cm_new/portal/programsusage')]
class PortalProgramsUsageApiController extends AbstractApiController
{
    public function __construct(private PortalProgramsUsageApiService $portalProgramsUsageApiService)
    {
    }

    #[Route(path: '/over_time/daily', methods: ['GET'])]
    public function dailyOverTime(Request $request): JsonResponse
    {
        return $this->handleOverTimeRequest($request);
    }

    #[Route(path: '/over_time/monthly', methods: ['GET'])]
    public function monthlyOverTime(Request $request): JsonResponse
    {
        return $this->handleOverTimeRequest($request);
    }

    private function handleOverTimeRequest(Request $request): JsonResponse
    {
        $routePath = $request->getPathInfo();
        $routeParts = explode('/', $routePath);
        $type = end($routeParts);

        if ($type != 'daily' && $type != 'monthly') {
            throw new BadRequestHttpException('Usage of not supported type: ' . $type);
        }
        if (($serialNumbers = $request->get('serial_numbers')) === null) {
            throw new BadRequestHttpException("Field 'serial_numbers' is required.");
        }
        if (($dateFrom = $request->get('dateFrom')) === null) {
            throw new BadRequestHttpException("Field 'dateFrom' is required.");
        }
        if (($dateTo = $request->get('dateTo')) === null) {
            throw new BadRequestHttpException("Field 'dateTo' is required.");
        }

        /** @var User $user */
        $user = $this->getUser();

        return $this->json([
            'data' => $this->portalProgramsUsageApiService->getPortalUsageOverTime(
                $type,
                $serialNumbers,
                $dateFrom,
                $dateTo,
                $user->getTimezone()->getLocation(),
            )
        ]);
    }
}

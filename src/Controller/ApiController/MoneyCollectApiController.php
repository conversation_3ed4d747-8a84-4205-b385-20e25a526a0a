<?php

namespace App\Controller\ApiController;

use App\Entity\User;
use App\Reports\v2\FinanceMoneyCollects;
use App\Service\Report2\ReportService;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;

class MoneyCollectApiController extends AbstractApiController
{
    /** @deprecated używaj  /api/reports/data?page=1&perPage=25&report=v2%5CFinanceMoneyCollects*/
    #[Route(path: '/cm_new/moneycollect/', methods: ['GET'])]
    public function indexAction(Request $request, ReportService $reportService): JsonResponse
    {
        /** @var User $user */
        $user = $this->getUser();
        $page = $request->get('page', 1);
        $perPage = $request->get('limit', 5);
        $criteria = [
            'startDate' => $request->get('dateFrom'),
            'endDate' => $request->get('dateTo'),
            'sources' => $request->get('sources'),
            'serial' => $this->getSerialNumber($request->get('sn'))

        ];

        $report = $reportService->getData(
            reportType: FinanceMoneyCollects::class,
            criteria:   $criteria,
            subscriber: $user->getSubscriber(),
            user:       $user,
            page: $page,
            perPage: $perPage
        );
        return $this->json(data: $report, context: ['groups' => ['currency:basic', "report:data"]]);
    }

    // jesli na liscie jest więcej myjni
    // wtedy zakładam że wszystkie myjnie użytkownika
    // obejscie zrobione dla aplikacji 1.9
    public function getSerialNumber(?string $sns): ?int
    {
        if (is_null($sns)) {
            return null;
        }

        $snsA = explode(',', $sns);
        if (count($snsA) == 1) {
            return (int)$snsA[0];
        }

        return null;
    }
}

<?php

namespace App\Controller\ApiController;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Routing\Annotation\Route;

class DisabledEndpointsController extends AbstractController
{
    #[Route(path: '/api/loyalsystem/widget', methods: 'GET')]
    #[Route(path: '/api/user/carwashes', methods: 'GET')]
    public function notFound(): JsonResponse
    {
        // kontroler służy do przechwytywania, usuniętych endpointów
        // z niewspieranych aplikacji mobilnych i zewnętrznych API
        return $this->json(
            [],
            JsonResponse::HTTP_NOT_FOUND
        );
    }
}

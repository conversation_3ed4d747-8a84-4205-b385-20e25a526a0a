<?php

namespace App\Controller\ApiController;

use App\Entity\Languages;
use App\Repository\CountryRepository;
use App\Repository\CurrencyRepository;
use App\Repository\LanguagesRepository;
use App\Repository\TimezonesRepository;
use App\Repository\UserRepository;
use App\Repository\VatTaxRepository;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;

class ListsController extends AbstractController
{
    #[Route(path: '/api/lists/languages', methods: 'GET')]
    public function getLanguages(Request $request, LanguagesRepository $languagesRepository): JsonResponse
    {
        $page = (int)$request->get('page', 1);
        $perPage = (int)$request->get('perPage', 50);
        $search = $request->get('search');

        return $this->json(
            data: $languagesRepository->search($search, $page, $perPage),
            context: [
                'groups' =>
                    [
                        "language:basic"
                    ]
                  ]
        );
    }

    #[Route(path: '/api/lists/timezones', methods: 'GET')]
    public function getTimezones(Request $request, TimezonesRepository $timezonesRepository): JsonResponse
    {
        $page = (int)$request->get('page', 1);
        $perPage = (int)$request->get('perPage', 50);
        $search = $request->get('search');

        return $this->json(
            data: $timezonesRepository->search($search, $page, $perPage),
            context: [
                      'groups' =>
                          [
                              "timezone:basic"
                          ]
                  ]
        );
    }

    #[Route(path: '/api/lists/currencies', methods: 'GET')]
    public function getCurrencies(CurrencyRepository $timezonesRepository): JsonResponse
    {
        return $this->json(
            data: $timezonesRepository->findAll(),
            context: [
                'groups' =>
                    [
                        "currency:basic"
                    ]
            ]
        );
    }

    #[Route(path: '/api/lists/countries', methods: ['GET'])]
    public function getCountriesAction(CountryRepository $countriesRepository): JsonResponse
    {
        return $this->json(
            data: $countriesRepository->findAll(),
            context: [
                'groups' =>
                    [
                        "country:basic"
                    ]
            ]
        );
    }

    #[Route(path: '/api/lists/vat_tax', methods: ['GET'])]
    public function getVatAction(VatTaxRepository $vatTaxRepository): JsonResponse
    {
        return $this->json(
            data: $vatTaxRepository->findAll(),
            context: [
                      'groups' =>
                          [
                              "vat:read"
                          ]
                  ]
        );
    }
}

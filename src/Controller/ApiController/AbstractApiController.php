<?php

namespace App\Controller\ApiController;

use App\Entity\Subscribers;
use Carbon\Carbon;
use App\Entity\Carwashes;
use App\Entity\User;
use Closure;
use Exception;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException;
use Symfony\Component\Serializer\Encoder\CsvEncoder;
use Symfony\Component\Serializer\Normalizer\ObjectNormalizer;
use Symfony\Component\Serializer\Serializer;

abstract class AbstractApiController extends AbstractController
{
    /**
     * @deprecated  use getCarwashSns
     */
    protected function getUserCarwashesStringBySerialNumbers(
        string $requestedSerialNumbersString,
        ?int $length = null,
        ?Closure $filter = null,
        bool $rollover = false,
    ): string {
        /** @var User $user */
        $user = $this->getUser();
        if ($rollover && $user->hasRole('ROLE_SUPERADMIN')) {
            if ($requestedSerialNumbersString === '') {
                $em = $this->container->get('doctrine');
                $cRepo = $em->getRepository(Carwashes::class);
                $carwashes = $cRepo->findBy(
                    [
                        'product' => 'Portal'
                    ]
                );

                return implode(
                    ',',
                    array_map(function ($carwash) {
                        return $carwash->getSerialNumber();
                    }, $carwashes)
                );
            } else {
                return $requestedSerialNumbersString;
            }
        }

        $userCarwashes = $user->getUserStartedCarwashes();

        if (!empty($filter)) {
            $userCarwashes =  $userCarwashes->filter($filter);
        }

        if ($userCarwashes->isEmpty()) {
            throw new Exception("User has no active carwashes");
        }

        $userCarwashesSerialNumbers = $userCarwashes->map(
            fn(Carwashes $cw) => $cw->getSerialNumber()
        )->toArray();
        $requestedSerialNumbers = explode(",", $requestedSerialNumbersString);
        $requestedSerialNumbers = array_filter($requestedSerialNumbers);

        if (count($requestedSerialNumbers)) {
            $requestedSerialNumbers = array_map('intval', $requestedSerialNumbers);
            $userCarwashesSerialNumbers = array_intersect(
                $requestedSerialNumbers,
                $userCarwashesSerialNumbers
            );
        }

        if (empty($userCarwashesSerialNumbers)) {
            throw new Exception("User carwashes do not match the requested criteria");
        }

        return implode(",", array_slice($userCarwashesSerialNumbers, 0, $length));
    }

    protected function getCarwashSns(User $user, ?int $sn): ?array
    {

        if ($sn) {
            if ($user->hasAccessToCarwashBySn($sn)) {
                return [$sn];
            }
            return null;
        }

        return $user->getUserStartedCarwashes()->map(function ($entity) {
            return $entity->getSerialNumber();
        })->getValues();
    }

    protected function checkPermision(User $user, Subscribers $subscriber)
    {
        if ($user->isAdmin()) {
            return;
        }

        if (
            $user->isDealer() &&
            $subscriber->getDealer() === $user->getSubscriber()
        ) {
            return;
        }
        throw new AccessDeniedHttpException("user not allowed to see resource");
    }
}

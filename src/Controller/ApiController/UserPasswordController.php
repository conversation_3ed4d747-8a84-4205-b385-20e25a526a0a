<?php

declare(strict_types=1);

namespace App\Controller\ApiController;

use App\Entity\User;
use App\Repository\UserRepository;
use App\User\PasswordResetManagerInterface;
use Exception;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\PasswordHasher\Hasher\UserPasswordHasherInterface;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Core\User\UserInterface;
use Symfony\Component\Translation\Translator;
use Symfony\Contracts\Translation\TranslatorInterface;

class UserPasswordController extends AbstractController
{
    public function __construct(
        private readonly PasswordResetManagerInterface $passwordResetManager,
        private readonly UserRepository $userRepository,
        private readonly TranslatorInterface $translator,
    ) {
    }

    #[Route(path: 'api/users/password/reset', name: 'api_users_reset', methods: ['POST'])]
    public function reset(Request $request): Response
    {
        /** @var User $user */
        $user = $this->getEmailUser($request);

        if (!$this->passwordResetManager->isUserValid($user)) {
            return $this->json(null, Response::HTTP_NO_CONTENT);
        }

        /** @var Translator $translator */
        $translator = $this->translator;
        $translator->setLocale($user->getLanguage()->getLocale());

        try {
            $this->passwordResetManager->sendResetEmail($user);
        } catch (Exception) {
            return $this->json(['message' => 'Password Reset failed'], Response::HTTP_BAD_REQUEST);
        }

        return $this->json(null, Response::HTTP_CREATED);
    }

    #[Route(path: 'api/users/password', name: 'api_users_set_password', methods: ['PATCH'])]
    public function setPassword(Request $request, UserPasswordHasherInterface $passwordHasher): Response
    {
        $payload = $request->getPayload();
        $token = $payload->get('token');
        $password = $payload->get('password');

        if (!$this->passwordResetManager->isTokenValid($token)) {
            return $this->json(null, Response::HTTP_NOT_FOUND);
        }

        if ($password === '' || strlen($password) < 5) {
            return $this->json(['msg' => 'Password to short'], Response::HTTP_BAD_REQUEST);
        }

        $user = $this->userRepository->findOneBy(['passwordResetToken' => $token]);

        if (!$this->passwordResetManager->isUserValid($user)) {
            return $this->json(null, Response::HTTP_NO_CONTENT);
        }

        $hash = $passwordHasher->hashPassword($user, $password);

        $user
            ->setPassword($hash)
            ->setPasswordResetToken(null)
            ->setPasswordRequestedAt(null);

        $this->userRepository->updateUser();

        return $this->json(null, Response::HTTP_NO_CONTENT);
    }

    private function getEmailUser(Request $request): ?UserInterface
    {
        $payload = $request->getPayload();
        $userEmail = strtolower($payload->get('email'));
        return $this->userRepository->findOneBy(['email' => $userEmail]);
    }
}

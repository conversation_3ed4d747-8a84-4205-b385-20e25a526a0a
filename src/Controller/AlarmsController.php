<?php

namespace App\Controller;

use App\Controller\ApiController\AbstractApiController;
use App\Entity\User;
use App\Repository\CarwashesRepository;
use App\Service\Connectors\CarwashApi\CarwashApiAlarmService;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;

/**
 * User controller.
 */
class AlarmsController extends AbstractApiController
{
    public const ALARM_HISTORY_URI = '#/alarm-history';

    #[Route(path: '/cm/alarm/by_serials/{sn}', name: 'cm_alarm_by_serials', methods: ['GET'])]
    public function getAlarmBySerials(int $sn, CarwashApiAlarmService $alarmApiService, CarwashesRepository $carwashesRepository): JsonResponse
    {
        /** @var User $user */
        $user = $this->getUser();
        $carwash = $carwashesRepository->findOneBySerialNumber($sn);

        if (!$user->hasAccessToCarwashBySn($carwash->getSerialNumber())) {
            $this->checkPermision($user, $carwash->getSubscriber());
        }

        $data = $alarmApiService->getAlarmBySerials(
            [$sn],
            $user->getLanguage()->getLocale(),
            $user->getTimezone()->getLocation()
        );
        return new JsonResponse($data);
    }

    #[Route(path: '/cm/alarm/history/{sn}', name: 'cm_alarm_history', methods: ['GET'])]
    public function getAlarmHistory(
        int $sn,
        Request $request,
        CarwashApiAlarmService $alarmApiService,
        CarwashesRepository $carwashesRepository
    ): JsonResponse {
        /** @var User $user */
        $user = $this->getUser();
        $carwash = $carwashesRepository->findOneBySerialNumber($sn);

        if (!$user->hasAccessToCarwashBySn($carwash->getSerialNumber())) {
            $this->checkPermision($user, $carwash->getSubscriber());
        }

        $page = (int)$request->get('page');
        $perPage = $request->get('length') ? intval($request->get('length')) : null;

        $sortByColumnName = $request->get('sortByColumnName') == 'created' ? 'ct' : 'et';
        $sortDirection = $request->get('sortDirection');

        $startDate = $request->get('startDate');
        $endDate = $request->get('endDate');
        $alarmDefinitionId = $request->get('alarmDefinitionId') ?? '';
        $search = $request->get('search');

        $data = $alarmApiService->getHistory(
            [$sn],
            $user->getLanguage()->getLocale(),
            $user->getTimezone()->getLocation(),
            $startDate,
            $endDate,
            $page,
            $perPage,
            $sortByColumnName,
            $sortDirection,
            $search,
            explode(',', $alarmDefinitionId)
        );

        $possibleAlarmId = array_map(function ($item) {
            return [
                'alarm_def_id' => $item['alarm_def']['id'],
                'alarm_def_label' => "{$item['alarm_def']['id']} ({$item['alarm_count']}) {$item['alarm_def']['text']}",
                'alarm_count' => $item['alarm_count']
            ];
        }, $data['possibleAlarmId']);

        return new JsonResponse([
                                    'data' => $data['data'],
                                    'allRows' => $data['allRows'],
                                    'possibleAlarmId' => $possibleAlarmId

                                ]);
    }
}

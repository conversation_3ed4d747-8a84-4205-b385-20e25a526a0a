<?php

declare(strict_types=1);

namespace App\Controller\Security;

use App\Entity\User;
use App\TokenManager\ApiTokenManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Core\User\UserInterface;
use Symfony\Component\Security\Http\Attribute\CurrentUser;

class ApiLoginController extends AbstractController
{
    #[Route('/login_api', name: 'api_login')]
    public function index(
        #[CurrentUser] ?UserInterface $user,
        ApiTokenManagerInterface $apiTokenManager,
    ): Response {
        if (null === $user) {
            return $this->json([
                'message' => 'missing credentials',
            ], Response::HTTP_UNAUTHORIZED);
        }

        /** @var User $loggedInUser */
        $loggedInUser = $user;

        $token = $apiTokenManager->generateTokens($loggedInUser);

        return $this->json([
            'user' => $user->getUserIdentifier(),
            'token' => $token->getToken(),
            'valid' => $token->getExpiresAt()
        ]);
    }
}

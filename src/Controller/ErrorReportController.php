<?php

namespace App\Controller;

use App\Entity\User;
use App\Model\JobRouterApi\ErrorReport;
use App\Service\Connectors\BkfApi\BkfApiService;
use App\Service\Connectors\BkfApi\SupportApiService;
use App\Service\JrProcessService;
use Carbon\Carbon;
use Exception;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\Filesystem\Filesystem;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Contracts\Translation\TranslatorInterface;
use OpenApi\Annotations as OA;
use Throwable;

use function Sentry\captureException;

#[Route(path: '/cm/report_error')]
class ErrorReportController extends AbstractController
{
    public function __construct(
        private TranslatorInterface $translator,
        private BkfApiService $bkfApiService,
        private JrProcessService $jrProcessService,
        private Filesystem $filesystem,
        private SupportApiService $supportApiService,
    ) {
    }
    /**
     * Gets key usage index
     *
     * @return JsonResponse
     * @throws Exception
     */
    #[Route(path: '/reporter_data', name: 'cm_repoprt_error_reporter_data', methods: ['GET'])]
    public function reporterDataAction()
    {
        /** @var User $user */
        $user = $this->getUser();
        $data = [
            'owner_email' => $user->getSubscriber() !== null ? $user->getSubscriber()->getName() : null,
            'reporter_email' => $user->getEmail()
        ];
        return new JsonResponse($data);
    }
    /**
     * Get the list of user's error reports
     *
     * @OA\Parameter(
     *     name="page",
     *     in="query",
     *     required=false,
     *     description="The collection page number, default: 1",
     *     @OA\Schema(type="integer", example=1)
     * )
     * @OA\Parameter(
     *     name="limit",
     *     in="query",
     *     required=false,
     *     description="The number of items per page, default: 10",
     *     @OA\Schema(type="integer", example=10)
     * )
     * @OA\Parameter(
     *     name="status",
     *     in="query",
     *     required=false,
     *     description="Issue status filter",
     *     @OA\Schema(type="string", example="open")
     * )
     */
    #[Route(path: '/', name: 'cm_get_error_reports_list', methods: ['GET'])]
    public function getErrorReportsListAction(Request $request): JsonResponse
    {
        /** @var User $user */
        $user = $this->getUser();

        try {
            return new JsonResponse($this->supportApiService->getSupportByOwner(
                $user->getOwnerBkf(),
                $request->get('page', 1),
                $request->get('limit', 10),
                $request->get('status')
            ));
        } catch (Throwable $t) {
            return $this->json([
                'status' => true,
                'message' => $t->getMessage()
            ], 400);
        }
    }
    /**
     * Get the list of user's error reports
     *
     * @OA\Parameter(
     *     name="id",
     *     in="path",
     *     required=true,
     *     description="Issue ID",
     *     @OA\Schema(type="integer", example=2207)
     * )
     */
    #[Route(path: '/show/{id}', name: 'cm_get_error_issue', methods: ['GET'])]
    public function getIssueByIdAction(Request $request, int $id)
    {
        /** @var User $user */
        $user = $this->getUser();

        try {
            return new JsonResponse($this->supportApiService->getSupportIssueByOwnerAndId(
                $user->getOwnerBkf(),
                $id,
            ));
        } catch (Throwable $t) {
            return $this->json([
                'status' => true,
                'message' => $t->getMessage()
            ], 400);
        }
    }

    /**
     * New method for sending in error reports which makes sendErrorReportAction deprecated
     *
     * @OA\Post(summary="Send error report")
     * @OA\Parameter(
     *     name="id",
     *     in="query",
     *     description="Issue ID (for existing issue - reply)",
     *     @OA\Schema(type="integer", example="123")
     * )
     * @OA\Parameter(
     *     name="carwash",
     *     in="query",
     *     description="Carwash serial number (for new issue)",
     *     @OA\Schema(type="integer", example="10001")
     * )
     * @OA\Parameter(
     *     name="subject",
     *     in="query",
     *     description="Subject (for new issue)",
     *     @OA\Schema(type="string", example="Subject")
     * )
     * @OA\Parameter(
     *     name="description",
     *     in="query",
     *     description="Description (for new and existing issues)",
     *     @OA\Schema(type="string", example="Description")
     * )
     * @OA\Parameter(
     *     name="files[]",
     *     in="query",
     *     description="Attachments (for new and existing issues)",
     * )
     * @OA\Parameter(
     *     name="close",
     *     in="query",
     *     description="Issue closure request (for existing issue)",
     *     @OA\Schema(type="boolean", example="true")
     * )
     * @OA\Tag(name="ErrorReport")
     * @throws Exception
     */
    #[Route(path: '/post', name: 'cm_repoprt_error_post_report', methods: ['POST'])]
    public function postErrorReportAction(Request $request): JsonResponse
    {
        $payload = $request->getPayload();
        /** @var User $user */
        $user = $this->getUser();
        $params = [
            'id' => $payload->get('id'),
            'subject' => $payload->get('subject'),
            'description' => $payload->get('description'),
            'user' => $user,
            'carwash' => (int) $payload->get('carwash'),
            'close' => filter_var($payload->get('close'), FILTER_VALIDATE_BOOLEAN) ? true : null,
        ];
        /** @var UploadedFile[] $files */
        $files = $request->files->all();
        $savedFiles = $this->saveUploadedAttachments($files);
        $params['files'] = $savedFiles['info'];
        $response = new JsonResponse([
            'status' => false,
        ], 500);
        try {
            if (empty($params['id'])) {
                // New issue
                $response = $this->sendErrorReportToJr($params);
            } else {
                // Reply to an existing issue
                $response = $this->sendReplyToJr($params);
            }
        } catch (Throwable $t) {
            captureException($t);
            $response = new JsonResponse([
                'status' => false,
                'message' => $t->getMessage()
            ], 400);
        } finally {
            foreach ($savedFiles['saved'] as $file) {
                $this->filesystem->remove($file);
            }
            return $response;
        }
    }
    protected function sendErrorReportToJr(array $params): JsonResponse
    {
        $errorReport = ErrorReport::fromArray($params);
        $this->jrProcessService->startProcess(
            'SystemZgloszen',
            3,
            $errorReport->toApiRequestArray()
        );
        return $this->json([
            'status' => true,
            'message' => $this->translator->trans('success'),
            'report' => $errorReport->toArray()
        ], 400);
    }
    protected function saveUploadedAttachments(array $files): array
    {
        $fileInfos = [];
        $saved = [];

        if (false === empty($files['files'])) {
            foreach ($files['files'] as $key => $file) {
                $filename = Carbon::now()->format('YmdHis')
                    . $key
                    . "." .
                    $file->getClientOriginalExtension();
                $savedFile = $file->move(
                    $this->getParameter('kernel.project_dir')
                    . ''
                    . $this->getParameter('shared_dir'),
                    $filename
                );
                $savedPath = $savedFile->getRealPath();
                $fileInfos[] = [
                    'attach_description' => $file->getClientMimeType(),
                    'attachment' => fopen($savedPath, 'r'),
                ];
                $saved[] = $savedPath;
            }
        }
        return [
            'info' => $fileInfos,
            'saved' => $saved
        ];
    }
    protected function sendReplyToJr(array $params)
    {
        /** @var User $user */
        $user = $params['user'];

        $note = $this->jrProcessService->sendStep('SystemZgloszen', $params['id'], [
            'issue_new_info' => $params['description'],
            'issue_username' => $user->getUsername(),
            'issue_role' => 'client',
            'close_issue_op' => $params['close'],
        ]);

        $attachments = null;
        if (!empty($params['files'])) {
            $attachments = $this->bkfApiService->sendSupportReply(
                $params['id'],
                $user->getUsername(),
                '[Attachments]',
                'client',
                'message',
                $params['files']
            );
        }

        return new JsonResponse([
            'status' => true,
            'message' => $this->translator->trans('success'),
            'data' => [
                'note' => $note,
                'attachments' => $attachments,
            ],
        ]);
    }
}

<?php

namespace App\Controller\Messages;

use App\Service\Messages\NotificationInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Routing\Annotation\Route;

class MessagesController extends AbstractController
{
    public function __construct(
        private readonly NotificationInterface $notificationApi,
    ) {
    }

    #[Route(path: '/cm_new/messages', name: 'api_messages_list', methods: ['GET'])]
    public function getMessages(): JsonResponse
    {
        return new JsonResponse($this->notificationApi->getMessagesForUser(), 200, [], true);
    }

    #[Route(path: '/cm_new/messages/unread', name: 'api_unread_messages_list', methods: ['GET'])]
    public function getUnreadMessages(): JsonResponse
    {
        return new JsonResponse($this->notificationApi->getUnreadMessagesForUser(), 200, [], true);
    }

    #[Route(path: '/cm_new/messages/read', name: 'api_all_messages_mark_read', methods: ['PATCH'])]
    public function setAllMessagesRead(): JsonResponse
    {
        $this->notificationApi->markAllMessageRead();
        return new JsonResponse(null, 204, []);
    }

    #[Route(path: '/cm_new/messages/{id}/read', name: 'api_message_mark_read', methods: ['PATCH'])]
    public function markRead(int $id): JsonResponse
    {
        $this->notificationApi->markMessageRead($id);
        return new JsonResponse(null, 204, []);
    }
}

<?php

namespace App\Controller\Access;

use App\Entity\User;
use App\Repository\UserRepository;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Routing\Annotation\Route;

class AccessController extends AbstractController
{
    #[Route(path: '/api/access/info', methods: ['GET'])]
    public function getCarwashContactDetails(UserRepository $userRepository): JsonResponse
    {
        /** @var User $user */
        $user = $this->getUser();
        $user->setLastLogin(new \DateTimeImmutable());
        $userRepository->save($user);

        return new JsonResponse([
            'extras' => [
                'standsTopUps' => $this->isGranted('ROLE_SUBSCRIPTION_PREMIUM'),
            ],
            'loyalty' => [
                'clients' => $this->isGranted('ROLE_SUBSCRIPTION_PREMIUM')
                    && $this->isGranted('ROLE_CM_LOYALSYSTEM_KEYLIST_AND_CLIENTS'),
                'invoices' => $this->isGranted('ROLE_SUBSCRIPTION_PREMIUM')
                    && $this->isGranted('ROLE_CM_LOYALSYSTEM_KEYLIST_AND_CLIENTS'),
                'cards' => $this->isGranted('ROLE_SUBSCRIPTION_BASIC')
                    && $this->isGranted('ROLE_CM_LOYALSYSTEM_KEYLIST_AND_CLIENTS'),
                'transactions' => $this->isGranted('ROLE_SUBSCRIPTION_BASIC')
                    && $this->isGranted('ROLE_CM_LOYALSYSTEM_KEYLIST_AND_CLIENTS'),
                'cyclicTopUps' => $this->isGranted('ROLE_SUBSCRIPTION_PREMIUM')
                    && $this->isGranted('ROLE_CM_LOYALSYSTEM_KEYLIST_AND_CLIENTS'),
            ],
            'finance' => [
                'turnover' => $this->isGranted('ROLE_SUBSCRIPTION_BASIC')
                    && $this->isGranted('ROLE_CM_FINANCE'),
                'collections' => $this->isGranted('ROLE_SUBSCRIPTION_BASIC')
                    && $this->isGranted('ROLE_CM_FINANCE'),
            ],
            'process_data' => [
                "enable" => true,
            ],
            "users" => [
                "enable" => $this->isGranted('ROLE_SUBSCRIPTION_BASIC')
                    && $this->isGranted('ROLE_CM_OWNER'),
                "write" => true,
                "read" => true,
            ],
            "subscriber" => [
                "read" => true,
                "write" => $user->isOwner(),
            ],
            'administration' => [
                "enable" => $this->isGranted('ROLE_ADMIN'),
                "carwashes" => $this->isGranted('ROLE_ADMIN'),
                "users" => $this->isGranted('ROLE_ADMIN'),
                "subscribers" => $this->isGranted('ROLE_ADMIN'),
                "subscriptions" => $this->isGranted('ROLE_ADMIN'),
            ]
        ]);
    }
}

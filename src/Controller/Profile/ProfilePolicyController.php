<?php

namespace App\Controller\Profile;

use App\Entity\Logs;
use App\Entity\User;
use App\Repository\LoggerRepository;
use Doctrine\ORM\EntityManagerInterface;
use Exception;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use OpenApi\Annotations as OA;

/**
 * User controller.
 *
 */
class ProfilePolicyController extends AbstractController
{
    #[Route(path: '/api/profile/policy/accept', methods: ['POST'])]
    public function updateProfileSettings(LoggerRepository $logger, EntityManagerInterface $em): JsonResponse
    {
        /** @var User $user */
        $user = $this->getUser();

        $user->setPrivacyPolicyAccepted(true)
            ->setPrivacyPolicyAcceptedAt(new \DateTimeImmutable());

        $em->persist($user);

        $logger->log(
            subscriber: $user->getSubscriber(),
            user: $user,
            carwash: null,
            source: self::class,
            message: "Terms of use accepted by user {$user->getUserIdentifier()}",
            level: Logs::LEVEL_CHANGELOG
        );
        $em->flush();
        return new JsonResponse([], Response::HTTP_OK);
    }

    #[Route('/api/profile/policy/terms_of_use', methods: ['GET'])]
    public function getTherms(): Response
    {
        /** @var ?User $user */
        $user = $this->getUser();
        if ($user?->getLanguage()->getLocale() === 'pl') {
            return $this->getFile('/public/terms/pl/terms.html');
        }
        // na razie nie mam angielskiej wersji
        return $this->getFile('/public/terms/pl/terms.html');
    }
    public function getFile($file): Response
    {
        $filePath =
            $this->getParameter('kernel.project_dir') .
            $file;

        return new Response(file_get_contents($filePath), Response::HTTP_OK, [
            'Content-Type' => 'text/html; charset=UTF-8',
        ]);
    }
}

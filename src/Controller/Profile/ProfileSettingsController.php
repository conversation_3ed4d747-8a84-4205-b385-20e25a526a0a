<?php

namespace App\Controller\Profile;

use App\Entity\Currency;
use App\Entity\Languages;
use App\Entity\Timezones;
use App\Entity\User;
use Doctrine\ORM\EntityManagerInterface;
use Exception;
use OpenApi\Annotations as OA;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

/**
 * User controller.
 *
 */
class ProfileSettingsController extends AbstractController
{
    /**
     *
     * @OA\Get(summary="Get invoice settings")
     * @OA\Response(
     *     response=200,
     *     description="Returns information about mobile payments",
     *         content={
     *             @OA\MediaType(
     *                 mediaType="application/json",
     *                 @OA\Schema(example={
     *                   "email": "<EMAIL>",
     *                   "currency": "PLN",
     *                   "language": "pl",
     *                   "timezone": 257,
     *                   "firstname": "Jan",
     *                   "lastname": "<PERSON>walski",
     *                   "phone": null
     *                 })
     *              )
     *          }
     * )
     * @OA\Tag(name="ProfileSettings")
     *
     * @throws Exception
     */
    #[Route(path: '/cm/profile_settings/', name: 'cm_profile_settings_get', methods: ['GET'])]
    public function getProfileSettings(Request $request): JsonResponse
    {
        /** @var User $user */
        $user = $this->getUser();
        return new JsonResponse([
            'email' => $user->getEmail(),
            'currency' => $user->getCurrency()->getCode(),
            'language' => $user->getLanguage()->getLocale(),
            'timezone' => $user->getTimezone()->getId(),
            'firstName' => $user->getFirstname(),
            'lastName' => $user->getLastname(),
            'phone' => $user->getPhone(),
        ]);
    }

    /**
     *
     * @OA\Post(summary="Set invoice settings")
     * @OA\RequestBody(
     *      description= "Set invoice settings",
     *      required= true,
     *      content={
     *             @OA\MediaType(
     *                 mediaType="application/json",
     *                 @OA\Schema(
     *                      example={
     *                          "paymentTermType": "D",
     *                          "paymentTermValue": "0",
     *                          "numeratorFormat": "FS-10TKA\\/{n}\\/{Y}\\/{m}",
     *                          "vatTaxId": 3,
     *                          "logo": "data:image/png;base64,iVBORw0KGg ... iQxZXcC2IiI"
     *                      }
     *                  )
     *              )
     *       }
     * )
     * @OA\Tag(name="ProfileSettings")
     *
     * @throws Exception
     */
    #[Route(path: '/cm/profile_settings/update', name: 'cm_profile_settings_update', methods: ['POST'])]
    public function updateProfileSettings(Request $request, EntityManagerInterface $em): JsonResponse
    {
        /** @var User $user */
        $user = $this->getUser();
        $inputData = json_decode($request->getContent(), true);
        $timezone = $em->getRepository(Timezones::class)->findOneBy(['id' => $inputData['timezone']]);
        if ($timezone === null) {
            return new JsonResponse(['message' => 'Timezone not found'], Response::HTTP_BAD_REQUEST);
        }
        $currency = $em->getRepository(Currency::class)->findOneBy(['code' => $inputData['currency']]);
        if ($currency === null) {
            return new JsonResponse(['message' => 'Currency not found'], Response::HTTP_BAD_REQUEST);
        }
        $language = $em->getRepository(Languages::class)->findOneBy(['locale' => $inputData['language']]);
        if ($language === null) {
            return new JsonResponse(['message' => 'Language not found'], Response::HTTP_BAD_REQUEST);
        }
        $user
            ->setFirstname($inputData['firstName'])
            ->setLastname($inputData['lastName'])
            ->setPhone($inputData['phone'])
            ->setLanguage($language)
            ->setTimezone($timezone);
        $em->flush();
        return new JsonResponse([], Response::HTTP_OK);
    }
}

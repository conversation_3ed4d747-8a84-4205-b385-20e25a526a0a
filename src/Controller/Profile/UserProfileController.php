<?php

namespace App\Controller\Profile;

use App\Entity\User;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Routing\Annotation\Route;

class UserProfileController extends AbstractController
{
    #[Route(path: '/cm/profile/invoice_data_check', name: 'cm_user_profile_invoice_data_check', methods: ['GET'])]
    public function actionCheckInvoiceData(): JsonResponse
    {
        /** @var User $user */
        $user = $this->getUser();

        $subscriber = $user->getSubscriber();
        return new JsonResponse(
            [
                "isDataCompleted" => $subscriber !== null && $subscriber->isInvoiceDataFilled(),
            ]
        );
    }
}

<?php

namespace App\Controller\Administration;

use App\Entity\Enum\SubscriptionStatus;
use App\Entity\ReportFile;
use App\Entity\Subscription\OwnerSubscriptionPayment;
use App\Entity\User;
use App\Reports\v2\SubscriptionReport;
use App\Repository\SubscribersRepository;
use App\Repository\Subscription\OwnerSubscriptionPaymentRepository;
use App\Service\Report2\ReportService;
use App\Service\Subscription\OwnerSubscriptionService;
use Carbon\Carbon;
use DateTime;
use I2m\Reports\Enum\FileExtention;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\Routing\Annotation\Route;

class SubscriptionController extends AdminAbstractController
{
    #[Route(path: '/administration/subscription/{subscriptionId}', name: 'cancel_owner_subscription', methods: ['DELETE'], requirements: ['subscriptionId' => '\d+'])]
    public function cancelSubscription(OwnerSubscriptionPaymentRepository $ospRepository, int $subscriptionId): JsonResponse
    {
        /** @var User $user */
        $user = $this->getUser();
        if (!$user->isAdmin()) {
            throw new AccessDeniedHttpException("only admin can do this");
        }

        $ownerSubscriptionPayment = $ospRepository->find($subscriptionId);
        if ($ownerSubscriptionPayment === null) {
            return new JsonResponse([], Response::HTTP_NOT_FOUND);
        }
        $ownerSubscriptionPayment
            ->setMtime(Carbon::now())
            ->setStatus(SubscriptionStatus::MANUALLY_CANCELED);
        $ospRepository->save($ownerSubscriptionPayment);

        return $this->json($ownerSubscriptionPayment, JsonResponse::HTTP_OK, [], ['groups' => ['subscription:read']]);
    }

    #[Route(path: '/administration/subscription/{subscriptionId}', methods: ['GET'], requirements: ['subscriptionId' => '\d+'])]
    public function getSubscriptionDetials(OwnerSubscriptionPaymentRepository $ospRepository, int $subscriptionId): JsonResponse
    {
        /** @var User $user */
        $user = $this->getUser();
        $ownerSubscriptionPayment = $ospRepository->find($subscriptionId);
        if ($ownerSubscriptionPayment === null) {
            throw new NotFoundHttpException();
        }

        $this->checkPermision($user, $ownerSubscriptionPayment->getSubscriber());

        return $this->json(
            $ownerSubscriptionPayment,
            JsonResponse::HTTP_OK,
            [],
            [
                'groups' =>
                    [
                        'subscription:list',
                        'subscriber:basic',
                        'subscription:read',
                        'vat:read',
                        'subscription:admin',
                        'currency:basic'
                    ]
            ]
        );
    }

    #[Route(path: '/administration/subscription/{subscriptionId}/confirm', methods: ['POST'], requirements: ['subscriptionId' => '\d+'])]
    public function confirmSubscription(OwnerSubscriptionPayment $subscription, Request $request, OwnerSubscriptionService $ownerSubscriptionService): JsonResponse
    {
        /** @var User $user */
        $user = $this->getUser();
        if (!$user->isAdmin()) {
            throw new AccessDeniedHttpException("only admin can do this");
        }

        $payload = $request->getPayload();

        $comment = $payload->get('comment');
        $startDate = $payload->get('startDate') ? new DateTime($request->get('startDate')) : null;
        $invoiceType = $payload->get('document', 'none');

        $ownerSubscriptionPayment = $ownerSubscriptionService->confirm($subscription, $comment, $startDate, $invoiceType);
        if ($ownerSubscriptionPayment === null) {
            return $this->json([], Response::HTTP_NOT_FOUND);
        }

        return $this->json(
            $ownerSubscriptionPayment,
            JsonResponse::HTTP_OK,
            [],
            [
            'groups' =>
                [
                    'subscription:list',
                    'subscriber:basic',
                    'subscription:read',
                    'vat:read',
                    'subscription:admin',
                    'currency:basic'
                ]
            ]
        );
    }

    #[Route(path: '/administration/subscriptions', methods: ['GET'])]
    public function history2(
        Request $request,
        OwnerSubscriptionPaymentRepository $osPaymentRepo,
        SubscribersRepository $subscribersRepository
    ) {
        /** @var User $loggedUser */
        $loggedUser = $this->getUser();
        $dealerId = $request->get('dealer');
        $itemsPerPage = $request->get('itemsPerPage', 10);
        $pageNumber = $request->get('pageNumber', 1);
        $status = $request->get('status') ? explode(',', $request->get('status')) : null;
        $from = $request->get('dateFrom') ? new \DateTime($request->get('dateFrom')) : null;
        $to = $request->get('dateTo') ?  new \DateTime($request->get('dateTo')) : null;
        $search = $request->get('search') ?? null;

        $dealer = $dealerId ? $subscribersRepository->find($dealerId) : null;
        $dealer = $loggedUser->isAdmin() ?  $dealer : $loggedUser->getSubscriber();

        $paymentsHistory = $osPaymentRepo->findSubscriptions(
            null,
            $dealer,
            $from,
            $to,
            $status,
            $pageNumber,
            $itemsPerPage,
            search: $search
        );

        return $this->json(
            $paymentsHistory,
            JsonResponse::HTTP_OK,
            [],
            [
                               'groups' =>
                               [
                                   'subscription:list',
                                   'subscriber:basic',
                                   'vat:read',
                                   'subscription:admin',
                                   'currency:basic'
                               ]
            ]
        );
    }

    #[Route('/administration/subscription/{subscriptionId}/report', methods: ['POST'])]
    public function sendReportAction(
        OwnerSubscriptionPaymentRepository $ospRepo,
        $subscriptionId,
        ReportService $reportService,
        Request $request,
    ): JsonResponse {
        /** @var User $user */
        $user = $this->getUser();
        $subscription = $ospRepo->findOneBy(['id' => $subscriptionId]);

        $report = $reportService->generateAsync(
            subscriber: $subscription->getSubscriber(),
            reportType: SubscriptionReport::class,
            ext: FileExtention::PDF,
            criterias: [
                'id' => $subscription->getId(),
            ],
            emails: $request->get('email') ? explode(',', $request->get('email')) : null,
            user: $user,
            title: $request->get('title'),
        );

        return $this->json(
            $report,
            ReportFile::getReportStatus($report->getStatus()),
            [],
            [
                'groups' => [
                    'report:file:list',
                    'default:basic'
                ]
            ]
        );
    }
}

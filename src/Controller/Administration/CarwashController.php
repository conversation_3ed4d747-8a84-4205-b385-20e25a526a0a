<?php

namespace App\Controller\Administration;

use App\Entity\Carwashes;
use App\Entity\User;
use App\Repository\CarwashesRepository;
use App\Repository\SubscribersRepository;
use Doctrine\ORM\EntityManagerInterface;
use Exception;
use OpenApi\Annotations as OA;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException;
use Symfony\Component\Routing\Annotation\Route;

class CarwashController extends AdminAbstractController
{
    #[Route(path: '/administration/carwashes', methods: ['GET'])]
    public function administrationCarwashes2List(
        Request $request,
        CarwashesRepository $carwashesRepository,
        SubscribersRepository $subscribersRepository
    ): JsonResponse {
        /** @var User $user */
        $user = $this->getUser();

        $page = (int)$request->get('page', 1);
        $perPage = (int)$request->get('perPage', 50);
        $search = $request->get('search');
        $dealerId = $request->get('dealer');
        $dealer = $dealerId ? $subscribersRepository->find($dealerId) : null;

        $dealer = $user->isAdmin() ? $dealer :  $user->getSubscriber();

        $carwashes = $carwashesRepository->searchCarwashes($search, null, $dealer, $page, $perPage);
        return $this->json($carwashes, JsonResponse::HTTP_OK, [], ['groups' => ["carwash:list", 'subscriber:basic']]);
    }

    /**
     *
     * @OA\Get(summary="Get details of carwash")
     * @OA\Response(
     *     response=200,
     *     description="Returns information about carwashes",
     *         content={
     *             @OA\MediaType(
     *                 mediaType="application/json",
     *                 @OA\Schema(example={
     *                   "sn":1363,
     *                   "startDate":{
     *                     "date":"2012-04-23 00:00:00.000000",
     *                     "timezone_type":3,
     *                     "timezone":"UTC"
     *                   },
     *                   "warrantyVoided":{
     *                     "date":"2028-12-22 00:00:00.000000",
     *                     "timezone_type":3,
     *                     "timezone":"UTC"
     *                   },
     *                   "owner":{
     *                     "id":57,
     *                     "email":"<EMAIL>",
     *                     "bkfId":7280
     *                   },
     *                   "isRollover":false,
     *                   "name":"Dziesi\u0105tka Gda\u0144ska",
     *                   "product":"CARWASH"
     *                 })
     *              )
     *          }
     * )
     * @OA\Tag(name="Administration:Carwash")
     *
     * @throws Exception
     */
    #[Route(path: '/administration/carwash/{sn}', methods: ['GET'])]
    public function administrationCarwashDetailsList(CarwashesRepository $carwashesRepository, int $sn): JsonResponse
    {
        /** @var User $user */
        $user = $this->getUser();
        $carwash = $carwashesRepository->findOneBySerialNumber($sn);

        $this->checkPermision($user, $carwash->getSubscriber());

        return $this->json($carwash, JsonResponse::HTTP_OK, [], ['groups' => [
            "carwash:basic",
            "carwash:list",
            'subscriber:basic'
        ]]);
    }
}

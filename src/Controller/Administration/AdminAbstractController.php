<?php

namespace App\Controller\Administration;

use App\Entity\Subscribers;
use App\Entity\User;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException;

class AdminAbstractController extends AbstractController
{
    public function checkPermision(User $user, ?Subscribers $subscriber): void
    {
        if ($user->isAdmin()) {
            return;
        }

        if (
            $user->isDealer() &&
            $subscriber->getDealer() === $user->getSubscriber()
        ) {
            return;
        }
        throw new AccessDeniedHttpException("user not allowed to see this subscriber");
    }
}

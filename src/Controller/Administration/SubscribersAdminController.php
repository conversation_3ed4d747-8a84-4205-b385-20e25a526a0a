<?php

namespace App\Controller\Administration;

use App\Entity\Enum\SubscriptionPayer;
use App\Entity\Enum\SubscriptionStatus;
use I2m\StandardTypes\Enum\CMSubscription;
use App\Entity\Subscribers;
use App\Entity\Subscription\OwnerSubscriptionPayment;
use App\Entity\User;
use App\Repository\CurrencyRepository;
use App\Repository\LanguagesRepository;
use App\Repository\SubscribersRepository;
use App\Repository\Subscription\OwnerSubscriptionPaymentRepository;
use App\Repository\TimezonesRepository;
use App\Service\Admin\UserManager;
use App\Service\Report2\ReportService;
use App\Service\Subscription\OwnerSubscriptionService;
use App\Service\Subscription\SubscriptionInvoiceService;
use DateTime;
use Doctrine\ORM\EntityManagerInterface;
use I2m\StandardTypes\Enum\Country;
use I2m\StandardTypes\Enum\Currency;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Serializer\SerializerInterface;

class SubscribersAdminController extends AdminAbstractController
{
    public function __construct(
        private SubscriptionInvoiceService $invoice2Service
    ) {
    }

    #[Route(path: '/administration/subscriber/{subscriberId}/users', methods: ['GET'])]
    public function getSubscriberUser(
        SubscribersRepository $subscribersRepository,
        ?int $subscriberId
    ): JsonResponse {
        /** @var User $user */
        $user = $this->getUser();

        $subscriber = $subscribersRepository->find($subscriberId);

        $this->checkPermision($user, $subscriber);

        return $this->json(
            data: $subscriber->getUsers(),
            context: [
                'circular_reference_handler' => function ($object) {
                    return $object->getId();
                },
                'groups' => [
                    "user:list",
                    "user:details",
                    "user:admin"
                ]
            ]
        );
    }

    #[Route(path: '/administration/subscriber/{subscriberId}/users', methods: ['POST'])]
    public function addUser(
        SubscribersRepository $subscribersRepository,
        ?int $subscriberId,
        UserManager $userManager,
        Request $request
    ): JsonResponse {
        /** @var User $user */
        $user = $this->getUser();

        $subscriber = $subscribersRepository->find($subscriberId);

        $this->checkPermision($user, $subscriber);

        $data = json_decode($request->getContent(), true);

        if ($userManager->checkIfExist($data['email'])) {
            return $this->json(['message' => 'email alerady exist'], JsonResponse::HTTP_CONFLICT);
        }

        $user = $userManager->createSimpleUser(
            email: $data['email'],
            subscriber: $subscriber,
            comment: $data['comment'],
            firstName: $data['first_name'],
            lastName: $data['last_name'],
            creator: $user
        );

        return $this->json(
            data: $user,
            context: [
                      'circular_reference_handler' => function ($object) {
                          return $object->getId();
                      },
                      'groups' => [
                          "user:list",
                          "user:details",
                          "user:admin"
                      ]
                  ]
        );
    }

    #[Route(path: '/administration/subscriber/{subscriberId}/carwashes', methods: ['GET'])]
    public function getCarwashes(
        SubscribersRepository $subscribersRepository,
        ?int $subscriberId
    ): JsonResponse {
        /** @var User $user */
        $user = $this->getUser();

        $subscriber = $subscribersRepository->find($subscriberId);

        $this->checkPermision($user, $subscriber);

        return $this->json(
            data: $subscriber->getCarwashes(),
            context: [
                      'circular_reference_handler' => function ($object) {
                          return $object->getId();
                      },
                      'groups' => [
                          "carwash:list"
                      ]
                  ]
        );
    }

    #[Route(path: '/administration/subscriber/{subscriberId}/subscriptions', methods: ['GET'])]
    public function getSubscriptions(
        OwnerSubscriptionPaymentRepository $subscriptionPaymentRepository,
        SubscribersRepository $subscribersRepository,
        ?int $subscriberId
    ): JsonResponse {
        /** @var User $user */
        $user = $this->getUser();

        $subscriber = $subscribersRepository->find($subscriberId);

        $this->checkPermision($user, $subscriber);
        $subscriptions = $subscriptionPaymentRepository->findSubscriptions(subscriber: $subscriber);

        return $this->json(
            data: $subscriptions,
            context: [
                      'circular_reference_handler' => function ($object) {
                          return $object->getId();
                      },
                      'groups' => [
                          'subscription:list',
                          'subscription:admin',
                          'vat:read'
                      ]
                  ]
        );
    }
    #[Route(path: '/administration/subscriber/{subscriberId}/calculate', methods: ['POST'])]
    public function calculateAction(
        SubscribersRepository $subscribersRepository,
        Request $request,
        int $subscriberId,
        OwnerSubscriptionService $ownerSubscriptionService,
    ): JsonResponse {
        $subscriber = $subscribersRepository->find($subscriberId);

        /** @var User $adder */
        $adder = $this->getUser();
        $this->checkPermision($adder, $subscriber);

        $payload = $request->getPayload();
        $subscriptionPayment = $ownerSubscriptionService->generate(
            new DateTime($payload->get('from')),
            new DateTime($payload->get('to')),
            Currency::from($payload->get('currency')),
            CMSubscription::from($payload->get('package')),
            $subscriber,
            $adder,
            SubscriptionPayer::from($payload->get('payer'))
        );
        return $this->json($subscriptionPayment, JsonResponse::HTTP_OK, [], ['groups' => ['subscription:admin','subscription:read']]);
    }

    #[Route(path: '/administration/subscriber/{subscriberId}/subscriptions', methods: ['POST'])]
    public function orderAction(
        SubscribersRepository $subscribersRepository,
        Request $request,
        int $subscriberId,
        OwnerSubscriptionService $ownerSubscriptionService
    ): JsonResponse {
        $subscriber = $subscribersRepository->find($subscriberId);

        /** @var User $user */
        $user = $this->getUser();
        if (!$user->isAdmin()) {
            return $this->json(['message' => "only admin can do this"], JsonResponse::HTTP_FORBIDDEN);
        }

        $payload = $request->getPayload();
        /** @var User $adder */
        $adder = $this->getUser();
        $status = SubscriptionStatus::from($payload->get('status', SubscriptionStatus::PAID->value));
        $payer = SubscriptionPayer::from($payload->get('payer'));
        $invoiceType = $payload->get('document');
        $subscriptionPayment = $ownerSubscriptionService->generate(
            new DateTime($payload->get('from')),
            (new DateTime($payload->get('to')))->setTime(23, 59, 59, 999),
            Currency::from($payload->get('currency')),
            CMSubscription::from($payload->get('package')),
            $subscriber,
            $adder,
            SubscriptionPayer::from($payload->get('payer')),
            $status,
            $payload->get('comment'),
            null,
            true
        );


        $this->saveInvoice($invoiceType, $payer, $subscriptionPayment);


        return $this->json($subscriptionPayment, JsonResponse::HTTP_OK, [], [
            'circular_reference_limit' => 1,
            'circular_reference_handler' => function ($object) {
                return $object->getId();
            },
            'groups' => [
            'subscription:admin',
            'subscription:read',
            'default:basic'
            ]]);
    }


    #[Route(path: '/administration/subscriber/{subscriberId}', methods: ['GET'])]
    public function getDetails(
        SubscribersRepository $subscribersRepository,
        ?int $subscriberId,
    ): JsonResponse {
        /** @var User $user */
        $user = $this->getUser();

        $subscriber = $subscribersRepository->find($subscriberId);
        $this->checkPermision($user, $subscriber);
        if (!$subscriber) {
            return $this->json([], Response::HTTP_NOT_FOUND);
        }

        return $this->json(
            data: $subscriber,
            context: [
                'circular_reference_limit' => 1,
                'circular_reference_handler' => function ($object) {
                    return $object->getId();
                },
                'language' => $user->getLanguage()->getLocale(),
                'groups' => [
                    'alert:info',
                    'subscriber:info',
                    'subscriber:admin',
                    'subscriber:details',
                    'country:basic',
                    'currency:basic',
                    'language:basic',
                    'timezone:basic',
                    'subscriber:logo',
                  ],
            ]
        );
    }

    #[Route(path: '/administration/subscriber/{subscriberId}/reports/data', methods: ['GET'])]
    public function getData(
        Request $request,
        ReportService $reportService,
        SubscribersRepository $subscribersRepository,
        ?int $subscriberId,
    ): JsonResponse {
        /** @var User $user */
        $user = $this->getUser();

        $subscriber = $subscribersRepository->find($subscriberId);
        $this->checkPermision($user, $subscriber);
        if (!$subscriber) {
            return $this->json([], Response::HTTP_NOT_FOUND);
        }
        $reportName = 'App\Reports\\' . $request->get('report');
        $page = $request->query->getInt('page');
        $perPage = $request->query->getInt('perPage');

        $report = $reportService->getData(
            reportType: $reportName,
            criteria:   $request->query->all(),
            subscriber: $subscriber,
            user:       $user,
            page:       $page,
            perPage:    $perPage
        );

        return $this->json(data: $report, context: ['groups' => ['currency:basic', "report:data"]]);
    }
    #[Route(path: '/administration/subscribers', methods: ['GET'])]
    public function getList(Request $request, SubscribersRepository $subscribersRepository): JsonResponse
    {
        /** @var User $user */
        $user = $this->getUser();

        $country = $request->get('country') ? Country::from($request->get('country')) : null;
        $search = $request->get('search');
        $dealerId = $request->get('dealer');
        $isDealer = $request->get('isDealer');
        $page = $request->get('page');
        $perPage = $request->get('perPage');
        $ownerBkf = $request->get('ownerBkf');
        $dealer = $dealerId ? $subscribersRepository->find($dealerId) : null;

        // dealer może wyświetlić tylko swoje firmy
        $dealer = $user->isAdmin() ?  $dealer : $user->getSubscriber();

        $subscriberList = $subscribersRepository->search(
            $search,
            $country,
            $dealer,
            $isDealer,
            $page,
            $perPage,
            $ownerBkf
        );

        return $this->json(
            data: $subscriberList,
            context: [
                  'circular_reference_limit' => 1,
                  'circular_reference_handler' => function ($object) {
                      return $object->getId();
                  },
                'groups' => [
                    'subscriber:admin',
                    'subscriber:details',
                    'subscriber:list',
                    'subscriber:extend',
                    "carwash:basic",
                    "country:basic",
                    "currency:basic",
                    "timezone:basic"
                  ],
            ]
        );
    }

    #[Route(path: '/administration/subscriber/{subscriberId}', methods: ['PATCH'])]
    public function update(
        Request $request,
        SerializerInterface $serializer,
        EntityManagerInterface $em,
        CurrencyRepository $currencyRepository,
        SubscribersRepository $subscribersRepository,
        TimezonesRepository $timezonesRepository,
        LanguagesRepository $languagesRepository,
        ?int $subscriberId
    ): JsonResponse {
        /** @var User $user */
        $user = $this->getUser();
        if (!$user->isAdmin()) {
            return $this->json(['message' => "only admin can do this"], JsonResponse::HTTP_FORBIDDEN);
        }

        /** @var Subscribers $subscriber */
        $subscriber = $subscribersRepository->find($subscriberId);

        $serializer->deserialize(
            $request->getContent(),
            Subscribers::class,
            'json',
            [
                'object_to_populate' => $subscriber,
            ]
        );


        $subscriber->setDealer($subscriber->getDealer()?->getId() ? $subscribersRepository->find($subscriber->getDealer()->getId()) : null);
        $subscriber->setCurrency($subscriber->getCurrency()?->getId() ? $currencyRepository->find($subscriber->getCurrency()->getId()) : null);
        $subscriber->setLanguage($subscriber->getLanguage()->getId() ? $languagesRepository->find($subscriber->getLanguage()->getId()) : null);
        $subscriber->setTimezone($subscriber->getTimezone()->getId() ? $timezonesRepository->find($subscriber->getTimezone()->getId()) : null);

        $em->persist($subscriber);
        $em->flush();

        return $this->json(
            data: $subscriber,
            context: [
                'groups' => [
                    'subscriber:admin',
                    'subscriber:details',
                    'country:basic',
                    'currency:basic',
                    'language:basic',
                'timezone:basic',
                    'subscriber:logo',
                ],
                'circular_reference_limit' => 1,
                'circular_reference_handler' => function ($object) {
                    return $object->getId();
                },
            ]
        );
    }

    private function saveInvoice(
        string $type,
        SubscriptionPayer $payer,
        OwnerSubscriptionPayment $subscriptionPayment
    ): void {
        if ($payer !== SubscriptionPayer::CLIENT) {
            return;
        }

        switch ($type) {
            case 'none':
                break;
            case 'issue':
                $this->invoice2Service->generateForClient($subscriptionPayment, false);
                break;

            case 'send':
                $this->invoice2Service->generateForClient($subscriptionPayment, true);
                break;
        }
    }
}

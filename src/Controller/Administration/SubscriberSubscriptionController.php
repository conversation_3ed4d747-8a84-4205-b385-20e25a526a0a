<?php

namespace App\Controller\Administration;

use App\Entity\Subscribers;
use App\Entity\Subscription\OwnerSubscriptionPackages;
use App\Entity\User;
use App\Service\Subscription\SubscriptionFinder;
use App\Service\Subscription\SubscriptionService;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Bridge\Doctrine\Attribute\MapEntity;

class SubscriberSubscriptionController extends AdminAbstractController
{
    #[Route(path: "/administration/subscriber/{id}/subscriptions/plans", methods: ['GET'])]
    public function getPlans(
        Subscribers $subscriber,
        SubscriptionFinder $subscriptionFinder
    ): JsonResponse {
        /** @var User $user */
        $user = $this->getUser();

        $this->checkPermision($user, $subscriber);
        $plans = $subscriptionFinder->getPlanByOwner($user->getSubscriber());
        return $this->json($plans, JsonResponse::HTTP_OK, [], ['groups' => ['plans:read']]);
    }

    #[Route(path: "/administration/subscriber/{subscriberId}/subscriptions/package/{packageId}/calculate", methods: ['GET'])]
    public function calculate(
        #[MapEntity(id: 'subscriberId')] Subscribers $subscriber,
        #[MapEntity(id: 'packageId')] OwnerSubscriptionPackages $package,
        SubscriptionService $subscriptionService,
    ): JsonResponse {
        /** @var User $user */
        $user = $this->getUser();

        $this->checkPermision($user, $subscriber);
        $subscription = $subscriptionService->subscriptonFromPackage(
            package: $package,
            subscriber: $user->getSubscriber(),
            addedBy: $user
        );
        return $this->json($subscription, JsonResponse::HTTP_OK, [], ['groups' => ['subscription:owner',"default:basic"]]);
    }

    #[Route(path: "/administration/subscriber/{subscriberId}/subscriptions/package/{packageId}/add", methods: ['POST'])]
    public function add(
        #[MapEntity(id: 'subscriberId')] Subscribers $subscriber,
        #[MapEntity(id: 'packageId')] OwnerSubscriptionPackages $package,
        SubscriptionService $subscriptionService,
    ): JsonResponse {
        /** @var User $user */
        $user = $this->getUser();

        $this->checkPermision($user, $subscriber);
        $subscription = $subscriptionService->subscriptonFromPackage(
            package: $package,
            subscriber: $user->getSubscriber(),
            addedBy: $user
        );
        $subscriptionService->add($subscription);
        return $this->json($subscription, JsonResponse::HTTP_OK, [], ['groups' => ['subscription:owner',"default:basic"]]);
    }

    #[Route(path: "/administration/subscriber/{subscriberId}/subscriptions/package/{packageId}/save", methods: ['POST'])]
    public function save(
        #[MapEntity(id: 'subscriberId')] Subscribers $subscriber,
        #[MapEntity(id: 'packageId')] SubscriptionService $subscriptionService,
        OwnerSubscriptionPackages $package,
    ): JsonResponse {
        /** @var User $user */
        $user = $this->getUser();

        $this->checkPermision($user, $subscriber);
        $subscription = $subscriptionService->subscriptonFromPackage(
            package: $package,
            subscriber: $user->getSubscriber(),
            addedBy: $user
        );
        $subscriptionService->add($subscription);
        return $this->json($subscription, JsonResponse::HTTP_OK, [], ['groups' => ['subscription:owner',"default:basic"]]);
    }
}

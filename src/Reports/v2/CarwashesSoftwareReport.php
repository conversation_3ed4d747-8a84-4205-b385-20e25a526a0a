<?php

namespace App\Reports\v2;

use I2m\StandardTypes\Enum\CMSubscription;
use App\Repository\CarwashesRepository;
use App\Repository\LoggerRepository;
use App\Service\Report2\Data\ReportDataAbstract;
use App\Service\Report2\Model\Data;
use App\Service\UserGrantedChecker;
use I2m\Connectors\Service\CarwashApi\CarwashApiCarwashService;
use I2m\Reports\Model\Column;
use I2m\Reports\Model\Table;
use Symfony\Contracts\Translation\TranslatorInterface;

class CarwashesSoftwareReport extends ReportDataAbstract
{
    public function __construct(
        CarwashesRepository $carwashesRepository,
        TranslatorInterface $translator,
        private CarwashApiCarwashService $carwashApiCarwashService,
        UserGrantedChecker $userGrantedChecker,
        LoggerRepository $loggerRepository
    ) {
        parent::__construct($translator, $carwashesRepository, $userGrantedChecker, $loggerRepository);
    }

    public function getTitle(): string
    {
        return 'carwash_software_report';
    }

    public function getEmailText(): ?string
    {
        return $this->getTitle();
    }

    public function getData(?int $page = null, ?int $perPage = null): Data
    {
        if (!$this->checkPermision(CMSubscription::BASIC, ['ROLE_SUPERADMIN'])) {
            return new Data();
        }

        $sns = $this->getCarwashCriteriaSns(
            $this->getCriteria()['serial'] ?? null
        );

        if (empty($sns)) {
            return new Data();
        }


        $softwareInfoList = $this->carwashApiCarwashService->getSoftwareInfo($sns);
        $data = [];

        foreach ($softwareInfoList->getData() as $softwareInfo) {
            $carwash = $this->getCarwash($softwareInfo->getSn());

            $data[] = [
                'carwashName' => $carwash->getLongName(),
                'serialNumber' => $softwareInfo->getSn(),
                'software' => $softwareInfo->getSoftware(),
                'requiredSoftware' => $softwareInfo->getRequiredSoftware(),
                'plcSn' => $softwareInfo->getPlcSn(),
                'plcMac' => $softwareInfo->getPlcMac(),
                'ip' => $softwareInfo->getIp(),
                'ipCountry' => $softwareInfo->getIpCountry(),
                'ipCity' => $softwareInfo->getIpCity(),
                'mobilePort' => $softwareInfo->getMobilePort(),
                'mobileType' => $softwareInfo->getMobileType(),
                'lastContact' => $softwareInfo->getLastContact(),
                'warrantyTo' => $softwareInfo->getWarrantyTo(),
                'startDate' => $softwareInfo->getStartDate(),
                'softwareUpdateVersion' => $softwareInfo->getSoftwareUpdate()?->getVersion(),
            ];
        }

        return new Data(
            data: $data,
            total: count($data)
        );
    }

    public function getTables(): array
    {
        $data = $this->getData();
        if (!$data->isGenerated()) {
            return [];
        }

        $columns = [
            new Column('carwashName', 'carwash_name', class: 'align-left'),
            new Column('serialNumber', 'serial_number', class: 'align-left'),
            new Column('software', 'software_version', class: 'align-left'),
            new Column('requiredSoftware', 'required_software', class: 'align-left'),
            new Column('plcSn', 'plc_sn', class: 'align-left'),
            new Column('plcMac', 'plc_mac', class: 'align-left'),
            new Column('ip', 'ip_address', class: 'align-left'),
            new Column('ipCountry', 'ip_country', class: 'align-left'),
            new Column('ipCity', 'ip_city', class: 'align-left'),
            new Column('mobilePort', 'mobile_port', class: 'align-left'),
            new Column('mobileType', 'mobile_type', class: 'align-left'),
            new Column('lastContact', 'last_contact', class: 'align-left'),
            new Column('warrantyTo', 'warranty_to', class: 'align-left'),
            new Column('startDate', 'start_date', class: 'align-left'),
            new Column('softwareUpdateId', 'software_update_id', class: 'align-left'),
            new Column('softwareUpdateVersion', 'software_update_version', class: 'align-left'),
        ];

        return [
            new Table(
                name: 'carwash_software_report',
                columns: $columns,
                items: $data->getData(),
                summary: null
            )
        ];
    }
}

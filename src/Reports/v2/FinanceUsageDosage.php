<?php

namespace App\Reports\v2;

use I2m\StandardTypes\Enum\CMSubscription;
use App\Repository\CarwashesRepository;
use App\Repository\LoggerRepository;
use App\Service\Report2\Data\ReportDataAbstract;
use App\Service\Report2\Model\Data;
use App\Service\UserGrantedChecker;
use I2m\Connectors\Service\CarwashApi\CarwashApiChemDosageService;
use I2m\Connectors\Model\CarwashApi\ChemDosage\ChemDosage;
use I2m\Reports\Model\Column;
use I2m\Reports\Model\Table;
use Symfony\Contracts\Translation\TranslatorInterface;

class FinanceUsageDosage extends ReportDataAbstract
{
    private static string $DOSAGE_UNIT = '%';

    public function __construct(
        CarwashesRepository $carwashesRepository,
        TranslatorInterface $translator,
        private readonly CarwashApiChemDosageService $chemDosageService,
        UserGrantedChecker $userGrantedChecker,
        LoggerRepository $loggerRepository
    ) {
        parent::__construct($translator, $carwashesRepository, $userGrantedChecker, $loggerRepository);
    }




    public function getTitle(): string
    {
        return 'finance_dosage';
    }

    public function getEmailText(): ?string
    {
        return $this->getTitle();
    }

    public function getData(?int $page = null, ?int $perPage = null): Data
    {
        if (!$this->checkPermision(CMSubscription::PREMIUM, ['ROLE_CM_FINANCE'])) {
            return new Data();
        }

        $serialNumbers = $this->getCarwashCriteriaSns(
            $this->getCriteria()['serial'] ?? null
        );

        if (empty($serialNumbers)) {
            return new Data();
        }

        $ownerId = $this->getSubscriber()->getOwnerBkf();
        $response = $this->chemDosageService->getList($serialNumbers, $ownerId);

        $data = array_map(function (ChemDosage $item) {
            return [
                'carwashName' => $this->getCarwash($item->getCarwash()->getSn())->getLongName(),
                'prewash' => $item->getPrewash(),
                'mainwash' => $item->getMainwash(),
                'wasxing' => $item->getWasxing(),
                'glossing' => $item->getGlossing(),
                'rims' => $item->getRims(),
                'brush' => $item->getBrush(),
                'foam' => $item->getFoam(),
                'unit' => self::$DOSAGE_UNIT,
            ];
        }, $response->getData());

        return new Data(
            data: $data,
            total: $response->getTotal(),
        );
    }
    public function getTables(): array
    {
        $data = $this->getData();
        if (!$data->isGenerated()) {
            return [];
        }

        $data = array_map(function ($item) {
            foreach ($item as $key => $value) {
                if (is_numeric($value)) {
                    $item[$key] = "{$value}{$item['unit']}";
                }
            }
            return $item;
        }, $data->getData());

        return [
            new Table(
                name: '',
                columns: [
                    new Column("carwashName", "table.carwash", class: "align-left"),
                    new Column("prewash", "programs.prewash", class: "align-right"),
                    new Column("mainwash", "programs.mainwash", class: "align-right"),
                    new Column("foam", "programs.foam", class: "align-right"),
                    new Column("wasxing", "programs.wasxing", class: "align-right"),
                    new Column("glossing", "programs.glossing", class: "align-right"),
                    new Column("rims", "programs.rims", class: "align-right"),
                    new Column("brush", "programs.brush", class: "align-right"),
                ],
                items: $data,
            )
        ];
    }
}

<?php

namespace App\Reports\v2;

use I2m\StandardTypes\Enum\CMSubscription;
use App\Repository\CarwashesRepository;
use App\Repository\LoggerRepository;
use App\Service\Report2\Data\ReportDataAbstract;
use App\Service\Report2\Model\Data;
use App\Service\UserGrantedChecker;
use I2m\Connectors\Service\CwFiscalApi\FiscalApiService;
use I2m\Connectors\Model\CwFiscalApi\GroupType;
use I2m\Reports\Model\Column;
use I2m\Reports\Model\Table;
use Symfony\Contracts\Translation\TranslatorInterface;

class FinanceFiscalByDay extends ReportDataAbstract
{
    public function __construct(
        CarwashesRepository $carwashesRepository,
        TranslatorInterface $translator,
        private FiscalApiService $carwashApiFiscalService,
        UserGrantedChecker $userGrantedChecker,
        LoggerRepository $loggerRepository
    ) {
        parent::__construct($translator, $carwashesRepository, $userGrantedChecker, $loggerRepository);
    }



    public function getTables(): array
    {
        $data = $this->getData();
        if (!$data->isGenerated()) {
            return [];
        }

        $currency = $this->getSubscriber()->getCurrency()?->getSymbol();
        $columns = [
            new Column('time', "table.date", null, "align-left"),
            new Column('carwashName', "table.carwash", null, "align-left"),
            new Column('method', "fiscal_transactions.table.type", null),
            new Column('fiscal', "fiscal.type.status", null),
            new Column('payments', "table.value", $currency),
        ];

        return  [new Table("summary", $columns, $data->getData(), $data->getPageSum())];
    }



    public function getTitle(): string
    {
        return $this->trans('fiscal_report_summary_title');
    }

    public function getEmailText(): ?string
    {
        return $this->getTitle();
    }
    public function getData(?int $page = null, ?int $perPage = null): Data
    {
        if (!$this->checkPermision(CMSubscription::PREMIUM, ['ROLE_CM_FINANCE'])) {
            return  new Data();
        }

        $sns = $this->getCarwashCriteriaSns(
            $this->getCriteria()['serial'] ?? null
        );
        if (empty($sns)) {
            return  new Data();
        }
        $timezone = $this->getTimezone();

        $dateTo = $this->getDateCriteria('endDate', $timezone)->modify("+1 day")->modify("-1 second");

        $response = $this->carwashApiFiscalService->getStatsFiscalByDay(
            ownerId: $this->getSubscriber()->getOwnerBkf(),
            sns: $sns,
            dateFrom: $this->getDateCriteria('startDate', $timezone),
            dateTo: $dateTo,
            fiscal: $this->getCriteria()['fiscal'] ?? null,
            grouped:  GroupType::from($this->getCriteria()['type']),
            timezone: $timezone->getName()
        );

        $items = [];

        $pageSum['sum'] = 0;
        foreach ($response->data as $data) {
            $items[] = [
                'time' => $data->getTime(),
                'sn' => $data->getSn(),
                'carwashName' => $this->getCarwash($data->getSn())->getLongName(),
                'fiscal' => $data->getFiscal(),
                'method' => $data->getType(),
                'payments' => $data->getValue(),
            ];

            $pageSum['sum'] += $data->getValue();
        }

        return new Data(
            data:    $items,
            currency: $this->getSubscriber()->getCurrency(),
            pageSum:  $pageSum,
        );
    }
}

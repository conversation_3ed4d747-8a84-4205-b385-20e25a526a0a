<?php

namespace App\Reports\v2;

use I2m\StandardTypes\Enum\CMSubscription;
use App\Repository\CarwashesRepository;
use App\Repository\LoggerRepository;
use App\Service\CarwashApi\PortalProgramsUsage\PortalProgramsUsageApiService;
use App\Service\Report2\Data\ReportDataAbstract;
use App\Service\Report2\Model\Data;
use App\Service\UserGrantedChecker;
use I2m\Reports\Model\Column;
use I2m\Reports\Model\Table;
use Symfony\Contracts\Translation\TranslatorInterface;

class FinancePortalProgramsUsageDaily extends ReportDataAbstract
{
    public function __construct(
        CarwashesRepository $carwashesRepository,
        TranslatorInterface $translator,
        UserGrantedChecker $userGrantedChecker,
        LoggerRepository $loggerRepository,
        private PortalProgramsUsageApiService $programsPortalUsageApiService,
    ) {
        parent::__construct($translator, $carwashesRepository, $userGrantedChecker, $loggerRepository);
    }

    public function getTitle(): string
    {
        return 'finance.portal-programsusage-daily';
    }

    public function getEmailText(): ?string
    {
        return $this->getTitle();
    }



    public function getData(?int $page = null, ?int $perPage = null): Data
    {
        if (!$this->checkPermision(CMSubscription::BASIC, ['ROLE_CM_FINANCE'])) {
            return  new Data();
        }

        $params = $this->getCriteria();
        $serialNumbers = $this->getCarwashCriteriaSns(
            $this->getCriteria()['serial'] ?? null
        );

        if (empty($serialNumbers)) {
            return new Data();
        }

        sort($serialNumbers);
        $sns = implode(',', $serialNumbers);

        $totalData = $this->programsPortalUsageApiService->getPortalUsageTotal(
            $sns,
            $this->getDateCriteria('startDate')->format('Y-m-d'),
            $this->getDateCriteria('endDate')->format('Y-m-d'),
            $this->getUser()->getTimezone()->getLocation(),
        );
        $dailyData = $this->programsPortalUsageApiService->getPortalUsageDaily(
            $sns,
            $this->getDateCriteria('startDate')->format('Y-m-d'),
            $this->getDateCriteria('endDate')->format('Y-m-d'),
            [1, 2, 3, 4, 5, 6, 7],
            $this->getUser()->getTimezone()->getLocation(),
        );

        $data = new Data(
            data: [
                'total' => $totalData,
                'daily' => $dailyData,
            ],
        );

        return $data;
    }

    public function getTables(): array
    {
        $data = $this->getData();
        if (!$data->isGenerated()) {
            return [];
        }

        $columns = [
            new Column(
                'hour',
                'table.hour',
                class: "align-left"
            ),
            new Column(
                'monday',
                'js.monday',
                class: "align-right",
                numberFormat: 0
            ),
            new Column(
                'tuesday',
                'js.tuesday',
                class: "align-right",
                numberFormat: 0
            ),
            new Column(
                'wednesday',
                'js.wednesday',
                class: "align-right",
                numberFormat:
                0
            ),
            new Column(
                'thursday',
                'js.thursday',
                class: "align-right",
                numberFormat: 0
            ),
            new Column(
                'friday',
                'js.friday',
                class: "align-right",
                numberFormat: 0
            ),
            new Column(
                'saturday',
                'js.saturday',
                class: "align-right",
                numberFormat: 0
            ),
            new Column(
                'sunday',
                'js.sunday',
                class: "align-right",
                numberFormat: 0
            ),
            new Column(
                'total',
                'table.sum',
                class: "align-right",
                numberFormat: 0
            ),
        ];

        return [
            new Table(
                name: "",
                columns: $columns,
                items: $data->getData()['daily'],
                summary: $data->getTotalSum(),
            )
        ];
    }
}

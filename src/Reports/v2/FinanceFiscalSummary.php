<?php

namespace App\Reports\v2;

use I2m\StandardTypes\Enum\CMSubscription;
use App\Repository\CarwashesRepository;
use App\Repository\LoggerRepository;
use App\Service\Report2\Data\ReportDataAbstract;
use App\Service\Report2\Model\Data;
use App\Service\UserGrantedChecker;
use I2m\Connectors\Service\CwFiscalApi\FiscalApiService;
use I2m\Reports\Model\Column;
use I2m\Reports\Model\Table;
use Symfony\Contracts\Translation\TranslatorInterface;

class FinanceFiscalSummary extends ReportDataAbstract
{
    public function __construct(
        CarwashesRepository $carwashesRepository,
        TranslatorInterface $translator,
        private FiscalApiService $carwashApiFiscalService,
        UserGrantedChecker $userGrantedChecker,
        LoggerRepository $loggerRepository
    ) {
        parent::__construct($translator, $carwashesRepository, $userGrantedChecker, $loggerRepository);
    }





    public function getTitle(): string
    {
        return 'finance.fiscal-transactions';
    }

    public function getEmailText(): ?string
    {
        return $this->getTitle();
    }
    public function getData(?int $page = null, ?int $perPage = null): Data
    {
        if (!$this->checkPermision(CMSubscription::PREMIUM, ['ROLE_CM_FINANCE'])) {
            return  new Data();
        }

        $sns = $this->getCarwashCriteriaSns(
            $this->getCriteria()['serial'] ?? null
        );
        if (empty($sns)) {
            return  new Data();
        }
        $carwashes = [];
        $pageSum = [
            "vat" => 0,
            "net" => 0,
            "value" => 0,
        ];

        // inicjalizuje tablice
        foreach ($sns as $sn) {
            $carwashes[$sn] = [
                'carwashName' => $this->getCarwash($sn)->getLongName(),
                'serialNumber' => $sn,
                "vat" => 0,
                "net" => 0,
                "value" => 0,
            ];
        }
        $timezone = $this->getTimezone();
        $summary = $this->carwashApiFiscalService->getStatsBays(
            ownerId: $this->getSubscriber()->getOwnerBkf(),
            sns:      $sns,
            dateFrom: $this->getDateCriteria('startDate', $timezone),
            dateTo:  $this->getDateCriteria('endDate', $timezone)->modify("+1 day")->modify("-1 second"),
            fiscal:   $this->getCriteria()['fiscal'] ?? null,
            timezone: $timezone->getName()
        );

        foreach ($summary->data as $item) {
            $sn = $item->getSn();

            $stand = [
                "source" => $item->getSource(),
                "bayId" => $item->getBayId(),
                "deviceName" => $this->getDeviceName($item->getSource(), $item->getBayId()),
                "fiscalDevice" => $item->getFiscalDevice(),
                "last" => $item->getLast()->setTimezone($timezone)->format("Y-m-d H:i:s"),
                "count" => $item->getCount(),
                "vat" => $item->getVat(),
                "net" => $item->getNet(),
                "value" => $item->getValue(),
            ];

            $carwashes[$sn]['stands'][] = $stand;
            $carwashes[$sn]['net'] += $stand['net'];
            $carwashes[$sn]['vat'] += $stand['vat'];
            $carwashes[$sn]['value'] += $stand['value'];

            $pageSum['net'] += $stand['net'];
            $pageSum['vat'] += $stand['vat'];
            $pageSum['value'] += $stand['value'];
        }

        foreach ($carwashes as &$carwash) {
            $carwash['vat'] = round($carwash['vat'], 2);
            $carwash['net'] = round($carwash['net'], 2);
            $carwash['value'] = round($carwash['value'], 2);
        }

        $pageSum['vat'] = round($pageSum['vat'], 2);
        $pageSum['net'] = round($pageSum['net'], 2);
        $pageSum['value'] = round($pageSum['value'], 2);

        return new Data(
            data:     array_values($carwashes),
            currency: $this->getSubscriber()->getCurrency(),
            pageSum:  $pageSum,
            filters:  $summary->filters
        );
    }
    public function getTables(): array
    {
        $data = $this->getData();
        if (!$data->isGenerated()) {
            return [];
        }
        $currency = $data->getCurrency()->getSymbol();
        $columns = [
            new Column("carwashName", "table.carwash", class: "align-left"),
            new Column("net", "fiscal.net", class: "align-right", unit: $currency, numberFormat: 2),
            new Column("vat", "table.vat", class: "align-right", unit: $currency, numberFormat: 2),
            new Column("value", "table.value", class: "align-right", unit: $currency, numberFormat: 2),
        ];

        $table[] =
            new Table(
                columns: $columns,
                name: "summary",
                items: $data->getData(),
                summary:$data->getPageSum()
            )
        ;

        $columnsStand = [
            new Column("deviceName", "table.source", class: "align-left"),
            new Column("fiscalDevice", "table.fiscal_device", class: "align-left"),
            new Column("net", "fiscal.net", class: "align-right", unit: $currency, numberFormat: 2),
            new Column("vat", "table.vat", class: "align-right", unit: $currency, numberFormat: 2),
            new Column("value", "table.value", class: "align-right", unit: $currency, numberFormat: 2),
        ];

        foreach ($data->getData() as $carwash) {
            $cwSum = [
                'net' => $carwash['net'],
                'vat' => $carwash['vat'],
                'value' => $carwash['value'],
            ];

            $table[] =
                new Table(
                    columns: $columnsStand,
                    name: $carwash['carwashName'],
                    items: $carwash['stands'] ?? [],
                    summary: $cwSum
                )
            ;
        }


        return $table;
    }
}

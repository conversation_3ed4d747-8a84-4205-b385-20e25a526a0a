<?php

namespace App\Reports\v2;

use I2m\StandardTypes\Enum\CMSubscription;
use App\Repository\CarwashesRepository;
use App\Repository\LoggerRepository;
use App\Service\Report2\Data\ReportDataAbstract;
use App\Service\Report2\Model\Data;
use App\Service\UserGrantedChecker;
use I2m\Connectors\Service\CwLoyaltyApi\LoyaltyPackagesService;
use I2m\Reports\Model\Column;
use I2m\Reports\Model\Table;
use Symfony\Contracts\Translation\TranslatorInterface;

class LoyaltyPackagesReport extends ReportDataAbstract
{
    public function __construct(
        CarwashesRepository $carwashesRepository,
        TranslatorInterface $translator,
        private LoyaltyPackagesService $loyaltyPackagesService,
        UserGrantedChecker $userGrantedChecker,
        LoggerRepository $loggerRepository
    ) {
        parent::__construct($translator, $carwashesRepository, $userGrantedChecker, $loggerRepository);
    }

    public function getTitle(): string
    {
        return 'loyalSystem_promotionalPackages';
    }

    public function getEmailText(): ?string
    {
        return $this->getTitle();
    }



    public function getData(?int $page = null, ?int $perPage = null): Data
    {
        if (!$this->checkPermision(CMSubscription::BASIC, ['ROLE_CM_LOYALSYSTEM_KEYLIST_AND_CLIENTS'])) {
            return  new Data();
        }

        $items = [];
        $total = 0;
        $params = $this->getCriteria();
        $params['perPage'] = $perPage ?? 100;
        $params['showTotal'] = true;

        $page2 = $page ?? 1;
        do {
            $params['page'] = $page2;

            $list = $this->loyaltyPackagesService->getList(
                ownerId: $this->getSubscriber()->getOwnerBkf(),
                search: $params,
            );

            if ($list->total !== null) {
                $total = $list->total;
                $params['showTotal'] = false;
            }

            foreach ($list->data as $package) {
                $items[] = [
                    'id' => $package->id,
                    'description' => $package->description,
                    'discount' => $package->discount,
                    'title' => $package->title,
                    'value' => $package->value,
                    'currencySymbol' => $package->currency['symbol'] ?? null,
                ];
            }
            $page2++;
        } while (!empty($list->data) && is_null($page)); // jesli nie podajemy strony wtedy pobieramy wszystkie

        $data = new Data(
            data: $items,
            total: $total,
        );

        return $data;
    }

    public function getTables(): array
    {
        $data = $this->getData();
        if (!$data->isGenerated()) {
            return [];
        }

        $columns = [
            new Column("id", "common_id", class: "align-left"),
            new Column("description", "common_tableDescription", class: "align-left"),
            new Column("title", "loyalApp_title", class: "align-left"),
            new Column("value", "loyalApp_packageValue", class: "align-left"),
            new Column("discount", "common_discount", class: "align-left"),
        ];

        return [
            new Table(
                name: "",
                columns: $columns,
                items: $data->getData(),
                summary: $data->getPageSum()
            )
        ];
    }
}

<?php

namespace App\Reports\v2;

use I2m\StandardTypes\Enum\CMSubscription;
use App\Entity\User;
use App\Repository\CarwashesRepository;
use App\Repository\LoggerRepository;
use App\Repository\UserRepository;
use App\Service\Report2\Data\ReportDataAbstract;
use App\Service\Report2\Model\Data;
use App\Service\UserGrantedChecker;
use I2m\Reports\Model\Column;
use I2m\Reports\Model\Table;
use Symfony\Contracts\Translation\TranslatorInterface;

class UserReport extends ReportDataAbstract
{
    public function __construct(
        CarwashesRepository $carwashesRepository,
        TranslatorInterface $translator,
        private UserRepository $userRepository,
        UserGrantedChecker $userGrantedChecker,
        LoggerRepository $loggerRepository
    ) {
        parent::__construct($translator, $carwashesRepository, $userGrantedChecker, $loggerRepository);
    }

    public function getTitle(): string
    {
        return 'common_users';
    }

    public function getEmailText(): ?string
    {
        return $this->getTitle();
    }

    public function getData(?int $page = null, ?int $perPage = null): Data
    {
        if (!$this->checkPermision(CMSubscription::BASIC, ['ROLE_CM_LOYALSYSTEM_KEYLIST_AND_CLIENTS'])) {
            return  new Data();
        }

        $items = [];
        $total = 0;
        $params = $this->getCriteria();
        $params['perPage'] = $perPage ?? 100;
        $params['showTotal'] = true;

        $page2 = $page ?? 1;
        do {
            $params['page'] = $page2;

            $list = $this->userRepository->searchUsers(
                search: $params['search'] ?? null,
                isOwner: false,
                page: $params['page'],
                perPage: $params['perPage'],
                subscriber: $this->getSubscriber(),
                showTotal: $params['showTotal'],
            );

            if ($list['count'] !== null) {
                $total = $list['count'];
                $params['showTotal'] = false;
            }

            foreach ($list['data'] as $user) {
                /** @var User $user */
                $items[] = [
                    'id' => $user->getId(),
                    'firstname' => $user->getFirstname(),
                    'lastname' => $user->getLastname(),
                    'email' => $user->getEmail(),
                    'phone' => $user->getPhone(),
                    'isOwner' => $user->isOwner() ? 'true' : 'false',
                    'lastLogin' => $user->getLastLogin()?->format('Y-m-d H:i'),
                    'carwashes' => array_map(function ($carwash) {
                        return $carwash->getLongName();
                    }, $user->getCarwashes()->toArray()),
                    'roles' => $user->getRoles()
                ];
            }
            $page2++;
        } while (!empty($list['data']) && is_null($page)); // jesli nie podajemy strony wtedy pobieramy wszystkie

        $data = new Data(
            data: $items,
            total: $total,
        );

        return $data;
    }

    public function getTables(): array
    {
        $data = $this->getData();
        if (!$data->isGenerated()) {
            return [];
        }

        $columns = [
            new Column("email", "common_email", class: "align-left"),
            new Column("firstname", "common_firstName", class: "align-left"),
            new Column("lastname", "common_lastName", class: "align-left"),
            new Column("isOwner", "admin_isOwner", class: "align-left"),
            new Column("lastLogin", "common_lastLogin", class: "align-left"),
        ];

        return [
            new Table(
                name: "",
                columns: $columns,
                items: $data->getData(),
            )
        ];
    }
}

<?php

namespace App\Reports\v2;

use I2m\StandardTypes\Enum\CMSubscription;
use App\Repository\CarwashesRepository;
use App\Repository\LoggerRepository;
use App\Service\CarwashApi\ProgramsUsage\ProgramsUsageApiService;
use App\Service\Report2\Data\ReportDataAbstract;
use App\Service\Report2\Model\Data;
use App\Service\UserGrantedChecker;
use I2m\Reports\Model\Column;
use I2m\Reports\Model\Table;
use Symfony\Contracts\Translation\TranslatorInterface;

class FinanceProgramsUsageDaily extends ReportDataAbstract
{
    public function __construct(
        CarwashesRepository $carwashesRepository,
        TranslatorInterface $translator,
        UserGrantedChecker $userGrantedChecker,
        LoggerRepository $loggerRepository,
        private ProgramsUsageApiService $programsUsageApiService
    ) {
        parent::__construct($translator, $carwashesRepository, $userGrantedChecker, $loggerRepository);
    }

    public function getTitle(): string
    {
        return 'menu.finance-programsusage-daily';
    }

    public function getEmailText(): ?string
    {
        return $this->getTitle();
    }



    public function getData(?int $page = null, ?int $perPage = null): Data
    {
        if (!$this->checkPermision(CMSubscription::BASIC, ['ROLE_CM_FINANCE'])) {
            return  new Data();
        }

        $serialNumbers = $this->getCarwashCriteriaSns(
            $this->getCriteria()['serial'] ?? null
        );

        sort($serialNumbers);
        $sns = implode(',', $serialNumbers);

        if (empty($serialNumbers)) {
            return new Data();
        }

        $dailyData = $this->programsUsageApiService->getWorkTimeDaily(
            $sns,
            $this->getDateCriteria('startDate')->format('Y-m-d'),
            $this->getDateCriteria('endDate')->format('Y-m-d'),
            [1, 2, 3, 4, 5, 6, 7],
            new \DateTimeZone($this->getUser()->getTimezone()->getLocation())
        );

        $items = [];

        $sum = [
            'monday' => 0,
            'tuesday' => 0,
            'wednesday' => 0,
            'thursday' => 0,
            'friday' => 0,
            'saturday' => 0,
            'sunday' => 0,
            'total' => 0,
        ];

        foreach ($dailyData['sums']['hours2'] as $row) {
            $tmp = [
                'hour' => $row['hour'],
                'monday' => floor($row['monday'] / 60),
                'tuesday' => floor($row['tuesday'] / 60),
                'wednesday' => floor($row['wednesday'] / 60),
                'thursday' => floor($row['thursday'] / 60),
                'friday' => floor($row['friday'] / 60),
                'saturday' => floor($row['saturday'] / 60),
                'sunday' => floor($row['sunday'] / 60),
                'total' => floor($row['total'] / 60),
            ];

            $items[] = $tmp;

            $sum['monday'] += $tmp['monday'];
            $sum['tuesday'] += $tmp['tuesday'];
            $sum['wednesday'] += $tmp['wednesday'];
            $sum['thursday'] += $tmp['thursday'];
            $sum['friday'] += $tmp['friday'];
            $sum['saturday'] += $tmp['saturday'];
            $sum['sunday'] += $tmp['sunday'];
            $sum['total'] += $tmp['total'];
        }

        $data = new Data(
            data: $items,
            totalSum: $sum,
        );

        return $data;
    }

    public function getTables(): array
    {
        $data = $this->getData();
        if (!$data->isGenerated()) {
            return [];
        }

        $columns = [
            new Column('hour', 'table.hour', class: "align-left"),
            new Column('monday', 'js.monday', unit: 'min', class: "align-right", numberFormat: 0),
            new Column('tuesday', 'js.tuesday', unit: 'min', class: "align-right", numberFormat: 0),
            new Column('wednesday', 'js.wednesday', unit: 'min', class: "align-right", numberFormat: 0),
            new Column('thursday', 'js.thursday', unit: 'min', class: "align-right", numberFormat: 0),
            new Column('friday', 'js.friday', unit: 'min', class: "align-right", numberFormat: 0),
            new Column('saturday', 'js.saturday', unit: 'min', class: "align-right", numberFormat: 0),
            new Column('sunday', 'js.sunday', unit: 'min', class: "align-right", numberFormat: 0),
            new Column('total', 'table.sum', unit: 'min', class: "align-right", numberFormat: 0),
        ];

        return [
            new Table(
                name: "",
                columns: $columns,
                items: $data->getData(),
                summary: $data->getTotalSum(),
            )
        ];
    }
}

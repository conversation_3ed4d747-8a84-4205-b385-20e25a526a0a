<?php

namespace App\Reports\v2;

use I2m\StandardTypes\Enum\CMSubscription;
use App\Repository\CarwashesRepository;
use App\Repository\LoggerRepository;
use App\Service\Report2\Data\ReportDataAbstract;
use App\Service\Report2\Model\Data;
use App\Service\UserGrantedChecker;
use I2m\Connectors\Service\CwLoyaltyApi\LoyaltyClientsService;
use I2m\Connectors\Model\CwLoyaltyApi\Alert;
use I2m\Connectors\Model\CwLoyaltyApi\Client;
use I2m\Reports\Model\Column;
use I2m\Reports\Model\Table;
use Symfony\Contracts\Translation\TranslatorInterface;

class LoyaltyClients3Report extends ReportDataAbstract
{
    public function __construct(
        CarwashesRepository $carwashesRepository,
        TranslatorInterface $translator,
        readonly private LoyaltyClientsService $loyaltyClientsService,
        UserGrantedChecker $userGrantedChe<PERSON>,
        LoggerRepository $loggerRepository
    ) {
        parent::__construct($translator, $carwashesRepository, $userGrantedChecker, $loggerRepository);
    }

    public function getTitle(): string
    {
        return 'table.clients';
    }

    public function getEmailText(): ?string
    {
        return $this->getTitle();
    }



    public function getData(?int $page = null, ?int $perPage = null): Data
    {
        if (!$this->checkPermision(CMSubscription::BASIC, ['ROLE_CM_LOYALSYSTEM_KEYLIST_AND_CLIENTS'])) {
            return  new Data();
        }

        $items = [];
        $total = 0;
        $params = $this->getCriteria();
        $params['perPage'] = $perPage ?? 100;
        $params['showTotal'] = true;

        $page2 = $page ?? 1;
        do {
            $params['page'] = $page2;

            $list = $this->loyaltyClientsService->getList(
                ownerId: $this->getSubscriber()->getOwnerBkf(),
                search: $params,
            );
            $total = $list->total;

            foreach ($list->data as $client) {
                /** @var Client $client */
                $items[] = [
                    'id' => $client->getId(),
                    'companyName' => $client->getCompanyName(),
                    'taxNumber' => $client->getTaxNumber(),
                    'city' => $client->getCity(),
                    'address' => $client->getAddress(),
                    'invoiceStrategy' => $client->getInvoiceStrategy(),
                    'invoiceStrategyTrans' => $this->trans("client-modal.invoice.strategies.{$client->getInvoiceStrategy()}"),
                    'alertLevel' => Alert::getMaxAlertLevel($client->getAlerts()),
                    'description' => $client->getDescription(),
                ];
            }
            $page2++;
        } while (!empty($list->data) && is_null($page)); // jesli nie podajemy strony wtedy pobieramy wszystkie

        $data = new Data(
            data:     $items,
            currency: $this->getSubscriber()->getCurrency(),
            total: $total,
        );

        return $data;
    }

    public function getTables(): array
    {
        $data = $this->getData();
        if (!$data->isGenerated()) {
            return [];
        }

        $columns = [
            new Column("companyName", "name", class: "align-left"),
            new Column("taxNumber", "table.tax_number", class: "align-left"),
            new Column("city", "common_city", class: "align-left"),
            new Column("address", "common_formAddress", class: "align-left"),
            new Column("invoiceStrategyTrans", "loyaltyCards_generateStrategy", class: "align-left"),
        ];

        return [
            new Table(
                name: "",
                columns: $columns,
                items: $data->getData(),
                summary: $data->getPageSum()
            )
        ];
    }
}

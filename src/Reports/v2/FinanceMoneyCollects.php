<?php

namespace App\Reports\v2;

use I2m\StandardTypes\Enum\CMSubscription;
use App\Repository\CarwashesRepository;
use App\Repository\LoggerRepository;
use App\Service\CarwashApi\Model\MoneyCollect\MoneyCollect;
use App\Service\Connectors\CarwashApi\CarwashApiMoneyCollectService;
use App\Service\Report2\Data\ReportDataAbstract;
use App\Service\Report2\Model\Data;
use App\Service\UserGrantedChecker;
use I2m\Reports\Model\Column;
use I2m\Reports\Model\Table;
use I2m\StandardTypes\Enum\Source;
use Symfony\Contracts\Translation\TranslatorInterface;

use function Sentry\captureMessage;

class FinanceMoneyCollects extends ReportDataAbstract
{
    public function __construct(
        CarwashesRepository $carwashesRepository,
        TranslatorInterface $translator,
        private CarwashApiMoneyCollectService $carwashApiMoneyCollectService,
        UserGrantedChecker $userGrantedChe<PERSON>,
        LoggerRepository $loggerRepository
    ) {
        parent::__construct($translator, $carwashesRepository, $userGrantedChecker, $loggerRepository);
    }



    public function getTitle(): string
    {
        $title = $this->trans('email.report-title-moneycollect');
        if ($this->getCriteria()['serial'] ?? null) {
            $title .= ": " . $this->getCarwash($this->getCriteria()['serial'])->getLongName();
        }
        return $title;
    }

    public function getEmailText(): ?string
    {
        return $this->getTitle();
    }



    public function getData(?int $page = null, ?int $perPage = null): Data
    {
        return ( $this->getCriteria()['id'] ?? null) ?
            $this->generateSingle($this->getCriteria()['id']) :
            $this->generate($page, $perPage);
    }

    private function generate(?int $page = null, ?int $perPage = null): Data
    {
        $items = [];

        if (!$this->checkPermision(CMSubscription::BASIC, ['ROLE_CM_FINANCE'])) {
            return  new Data();
        }

        $sns = $this->getCarwashCriteriaSns(
            $this->getCriteria()['serial'] ?? null,
        );
        if (empty($sns)) {
            return  new Data();
        }
        $page2 = $page ?? 1;
        $showTotal = true;
        $dateFrom = $this->getDateCriteria('startDate');
        $dateTo = $this->getDateCriteria('endDate')?->modify("+1 day")->modify("-1 second");

        $pageSums = [];
        $sources = $this->getCriteria()['sources'] ?? null;
        $sources = $sources ? explode(',', $sources) : null;

        do {
            $list = $this->carwashApiMoneyCollectService->getMoneyCollectHistory(
                sns:      $sns,
                currency: $this->getSubscriber()->getCurrency()->getCode(),
                timezone: $this->getSubscriber()->getTimezone()->getLocation(),
                dateFrom: $dateFrom,
                dateTo:   $dateTo,
                sources:  $sources,
                details:  true,
                page:     $page2,
                limit:    $perPage ?? 10000,
                showTotal: $showTotal
            );

            foreach ($list->data as $item) {
                $source = $item->getSource()->getName();
                $mc = $this->parseMc($item);

                $items[] = $mc;
                if (!array_key_exists($source, $pageSums)) {
                    $pageSums[$source] = $mc['sums'];
                } else {
                    $pageSums[$source] = $this->calculateSum($pageSums[$source], $mc['sums']);
                }
            }

            if ($showTotal) {
                $total = $list->total;
                $showTotal = false; // pobieram tylko raz
            }
            $page2++;
        } while (!empty($list->data) && is_null($page)); // jesli nie podajemy strony wtedy pobieramy wszystkie

        $data = new Data(
            data:     $items,
            currency: $this->getSubscriber()->getCurrency(),
            pageSum:  $pageSums,
            total:    $total
        );

        return $data;
    }

    private function generateSingle(int $id)
    {
        $mc = $this->carwashApiMoneyCollectService->getMoneyCollect(
            id: $id,
            currency: $this->getSubscriber()->getCurrency()->getCode(),
            timezone: $this->getSubscriber()->getTimezone()->getLocation(),
        );

        // sprawdzam czy ten subskryber ma dostęp do myjni
        $this->getCarwash($mc->getCarwash()->getSn());

        $data = new Data(
            data:     [$this->parseMc($mc)],
            currency: $this->getSubscriber()->getCurrency(),
        );

        return $data;
    }

    public function getTables(): array
    {
        $data = $this->getData();
        if (!$data->isGenerated()) {
            return [];
        }

        $currency = $this->getData()->getCurrency()->getSymbol();
        $columnsChanger = [
            new Column("resetTime", "table.date", class: "align-left"),
            new Column("bill", "table.bill", class: "align-right", unit: $currency, numberFormat: 2),
            new Column("coin", "table.coins", class: "align-right", unit: $currency, numberFormat: 2),
            new Column("bankCard", "table.bank_cards", class: "align-right", unit: $currency, numberFormat: 2),
            new Column("hopperA", "table.hopperA", class: "align-right", unit: $currency, numberFormat: 2),
            new Column("hopperB", "table.hopperB", class: "align-right", unit: $currency, numberFormat: 2),
            new Column("bkfCardRevalue", "table.bkfKeyRecharge", class: "align-right", unit: $currency, numberFormat: 2),
            new Column("bkfCardSoldValue", "table.bkfCardSale", class: "align-right", unit: $currency, numberFormat: 2),
            new Column("bkfCardSoldCount", "table.bkfCardSoldCount", class: "align-right", unit: $currency, numberFormat: 2),
            new Column("carwashRecharge", "table.carwashRecharge", class: "align-right", unit: $currency, numberFormat: 2),

        ];

        $columnsCarwash = [
            new Column("resetTime", "table.date", class: "align-left"),
            new Column("deviceName", "table.device", class: "align-left"),
            new Column("bills", "table.bill", class: "align-right", unit: $currency, numberFormat: 2),
            new Column("coins", "table.coins", class: "align-right", unit: $currency, numberFormat: 2),
            new Column("tokens", "table.tokens", class: "align-right", unit: $currency, numberFormat: 2),
        ];

        $columnsYeti = [
            new Column("resetTime", "table.date", class: "align-left"),
            new Column("coin", "table.coins", class: "align-left"),
            new Column("token", "table.tokens", class: "align-left"),
            new Column("emergencyDrops", "report.number_of_emergency_discharges_to_safe", class: "align-left"),
            new Column("unrecognized", "report.unrecognized_coins_number", class: "align-left"),
            new Column("safe", "table.safe", class: "align-right", unit: $currency),
            new Column("hopperA", "table.hopperA", class: "align-right", unit: $currency),
            new Column("hopperB", "table.hopperB", class: "align-right", unit: $currency),
        ];



        $tables = [];

        foreach ($data->getData() as $mc) {
            switch (Source::from($mc['source'])) {
                case Source::CAR_WASH:
                    $columns = $columnsCarwash;
                    break;
                case Source::MONEY_CHANGER:
                case Source::TERMINAL:
                    $columns = $columnsChanger;
                    break;

                case Source::YETI:
                    $columns = $columnsYeti;
                    break;
                default:
                    captureMessage("nieznany rodzaj inkasacji");
                    $columns = null;
                    break;
            }

            if (is_null($columns)) {
                continue;
            }

            $tables[] =
                new Table(
                    name: $this->getDeviceName($mc['source']) . ": " . $mc['carwash'] . ' - ' . $mc['at'],
                    columns: $columns,
                    items: $mc['content'],
                    summary: $mc['sums']
                );
        }

        return $tables;
    }

    private function parseCarWash(array $content): array
    {
        $sums = [
            'bills' => 0,
            'coins' => 0,
            'tokens' => 0,
            'all' => 0
        ];

        $newContent = [];
        foreach ($content as $item) {
            $newContent[] = [
                "deviceName" => $this->getDeviceName($item['source']['name'], $item['bayId']),
                'standId' => $item['bayId'],
                'bills' => $item['bill'],
                'coins' => $item['coin'],
                'tokens' => $item['token'],
                'resetTime' => $item['time'],
                'deviceType' => $item['source']['name'],
                'sumRow' => $item['sum'],
            ];
            $sums['bills'] += $item['bill'];
            $sums['coins'] += $item['coin'];
            $sums['tokens'] += $item['token'];
            $sums['all'] += $item['sum'];
        }

        return ['content' => $newContent, 'sums' => $sums ];
    }
    private function parseMoneyChanger(array $content): array
    {
        $sums = [
            'outcome' => 0,
            'income' => 0,
            'all' => 0,
            'cash' => 0,
            'cashLess' => 0,
            'hoppers' => 0,
            'sale' => 0,
            'balance' => 0
        ];
        $newContent = [];
        foreach ($content as $item) {
            $newContent[] = [
                'bill' => $item['bills'],
                'coin' => $item['coins'],
                'bankCard' => $item['bankCards'],
                'carwashRecharge' => $item['carwashRecharge'],
                'hopperA' => $item['hopperA'],
                'hopperB' => $item['hopperB'],
                'bkfCardRevalue' => $item['bkfCardRevalue'],
                'bkfCardSoldValue' => $item['bkfCardSoldValue'],
                'bkfCardSoldCount' => $item['bkfCardSoldCount'],
                'resetTime' => $item['time'],
                'deviceType' => $item['source']['name'],
            ];
            $sale = $item['carwashRecharge'] + $item['bkfCardRevalue'] + $item['bkfCardSoldValue'];
            $hoppers = $item['hopperA'] + $item['hopperB'];
            $cash = $item['bills'] + $item['coins'];
            $income = $cash + $item['bankCards'];
            $outcome = $hoppers + $sale;

            $sums['cash'] += $cash;
            $sums['cashLess'] += $item['bankCards'];
            $sums['hoppers'] += $hoppers;
            $sums['sale'] += $sale;
            $sums['outcome'] += $outcome;
            $sums['income'] += $income;
            $sums['all'] += $item['sum'];
            $sums['balance'] += $income - $outcome;
        }

        return ['content' => $newContent, 'sums' => $sums ];
    }

    private function parseYeti(array $content): array
    {
        $sums = [
            'all' => 0
        ];

        $newContent = [];
        foreach ($content as $item) {
            $tmp = [

                'coin' => $item['coins'],
                'token' => $item['tokens'],

                'emergencyDrops' => $item['emergencyDrops'],
                'unrecognized' => $item['unrecognized'],
                'safe' => $item['safe'],

                'hopperA' => $item['hopperA'],
                'hopperB' => $item['hopperB'],

                'resetTime' => $item['time'],
                'sumRow' => $item['sum'],
            ];

            $tmp['sucked'] = $item['coins'];
            $tmp['sorted'] = $item['hopperA'] + $item['hopperB'] + $item['safe'];
            $tmp['balance'] = $tmp['sucked'] - $tmp['sorted'];
            $newContent[] = $tmp;
            $sums['all'] += $item['sum'];
        }
        return ['content' => $newContent, 'sums' => $sums ];
    }
    private function getContent(Source $src, $content): array
    {
        switch ($src) {
            case Source::CAR_WASH:
                return self::parseCarWash($content);

            case Source::MONEY_CHANGER:
            case Source::TERMINAL:
                return self::parseMoneyChanger($content);

            case Source::YETI:
                return self::parseYeti($content);
        }
        throw new \Exception("Unknown source type");
    }

    private function calculateSum(array $sums, array $item)
    {
        foreach ($sums as $key => &$sum) {
            $sum += $item[$key];
        }
        return $sums;
    }

    private function parseMc(MoneyCollect $item): array
    {
        $content = $this->getContent(Source::from($item->getSource()->getName()), $item->getContent());
        $mc = [
            "id" => $item->getId(),
            "at" => $item->getTime()->format('Y-m-d H:i:s'),
            "source" => $item->getSource()->getName(),
            "sn" => $item->getCarwash()->getSn(),
            'carwash' => $this->getCarwash($item->getCarwash()->getSn())->getLongName(),
            'content' => $content['content'],
            'sums' => $content['sums'],
        ];
        return $mc;
    }
}

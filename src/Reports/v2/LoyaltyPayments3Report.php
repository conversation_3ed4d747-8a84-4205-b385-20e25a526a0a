<?php

namespace App\Reports\v2;

use I2m\StandardTypes\Enum\CMSubscription;
use App\Repository\CarwashesRepository;
use App\Repository\LoggerRepository;
use App\Service\Report2\Data\ReportDataAbstract;
use App\Service\Report2\Model\Data;
use App\Service\UserGrantedChecker;
use I2m\Connectors\Service\CwLoyaltyApi\LoyaltyExternalPaymentsService;
use I2m\Connectors\Model\CwLoyaltyApi\ExternalPayment;
use I2m\Reports\Model\Column;
use I2m\Reports\Model\Table;
use Symfony\Contracts\Translation\TranslatorInterface;

class LoyaltyPayments3Report extends ReportDataAbstract
{
    public function __construct(
        CarwashesRepository $carwashesRepository,
        TranslatorInterface $translator,
        UserGrantedChecker $userGrantedChecker,
        private LoyaltyExternalPaymentsService $loyaltyExternalPaymentsService,
        LoggerRepository $loggerRepository
    ) {
        parent::__construct($translator, $carwashesRepository, $userGrantedChecker, $loggerRepository);
    }

    public function getTitle(): string
    {
        return 'loyalty_PaymentReportTitle';
    }

    public function getEmailText(): ?string
    {
        return $this->getTitle();
    }

    public function getTwigTemplate(): string
    {
        return 'Reports2/default.html.twig';
    }

    public function getData(?int $page = null, ?int $perPage = null): Data
    {
        if (!$this->checkPermision(CMSubscription::BASIC, ['ROLE_CM_LOYALSYSTEM_KEYLIST_AND_CLIENTS'])) {
            return  new Data();
        }

        $items = [];
        $params = $this->getCriteria();
        $params['itemsPerPage'] = $perPage ?? 10;
        $params['showTotal'] = true;

        $page2 = $page ?? 1;
        do {
            $params['page'] = $page2;
            $list = $this->loyaltyExternalPaymentsService->getList(
                ownerId: $this->getSubscriber()->getOwnerBkf(),
                search: $params,
                startDate:  $this->getDateCriteria('startDate'),
                endDate: $this->getDateCriteria('endDate'),
            );
            $total = $list->total;

            foreach ($list->data as $payment) {
                /** @var ExternalPayment $payment  */
                $items[] = [
                    'id' => $payment->getId(),
                    'externalId' => $payment->getExternalId(),
                    'value' => $payment->getValue(),
                    'userEmail' => $payment->getUser()['email'],
                    'status' => $payment->getStatus(),
                    'time' => $payment->getInitiatedTimestamp()->format('Y-m-d H:i:s'),
                    'currency' => $payment->getCurrency(),
                    'type' => $payment->getType(),
                ];
            }
            $page2++;
        } while (!empty($list->data) && is_null($page)); // jesli nie podajemy strony wtedy pobieramy wszystkie

        $data = new Data(
            data: $items,
            total: $total,
            filters: [
                'type' => $list->type,
                'status' => $list->status,
            ],
        );

        return $data;
    }

    public function getTables(): array
    {
        $data = $this->getData();
        if (!$data->isGenerated()) {
            return [];
        }

        $columns = [
            new Column("id", "common_id", class: "align-left"),
            new Column("time", "common_tableDate", class: "align-left"),
            new Column("status", "common_state", class: "align-left"),
            new Column("userEmail", "common_user", class: "align-left"),
            new Column("type", "common_type", class: "align-left"),
            new Column("externalId", "common_type", class: "align-left"),
            new Column("value", "common_value", null, class: "align-right", numberFormat: 2),
            new Column("currency", "common_currency", null, class: "align-left"),
        ];

        return [
            new Table(
                name: "",
                columns: $columns,
                items: $data->getData(),
                summary: null
            )
        ];
    }
}

<?php

namespace App\Reports\v2;

use I2m\StandardTypes\Enum\CMSubscription;
use App\Repository\CarwashesRepository;
use App\Repository\LoggerRepository;
use App\Service\Report2\Data\ReportDataAbstract;
use App\Service\Report2\Model\Data;
use App\Service\UserGrantedChecker;
use I2m\Connectors\Service\CwFinanceApi\FinanceApiFinanceService;
use I2m\Reports\Model\Column;
use I2m\Reports\Model\Table;
use Symfony\Contracts\Translation\TranslatorInterface;

class FinanceTurnoverNetGross extends ReportDataAbstract
{
    public function __construct(
        CarwashesRepository $carwashesRepository,
        TranslatorInterface $translator,
        UserGrantedChecker $userGrantedChecker,
        LoggerRepository $loggerRepository,
        private FinanceApiFinanceService $carwashApiFinanceService,
    ) {
        parent::__construct($translator, $carwashesRepository, $userGrantedChecker, $loggerRepository);
    }

    public function getTitle(): string
    {
        return $this->trans('finance_turnover_reportPeriodTitle');
    }

    public function getEmailText(): ?string
    {
        return $this->getTitle();
    }



    public function getData(?int $page = null, ?int $perPage = null): Data
    {
        if (!$this->checkPermision(CMSubscription::BASIC, ['ROLE_CM_FINANCE'])) {
            return  new Data();
        }

        $items = [];
        $sums = [
            'cashGross' => 0,
            'cashNet' => 0,
            'cashVat' => 0,
            'bankCardsGross' => 0,
            'bankCardsNet' => 0,
            'bankCardsVAT' => 0,
            'sumGross' => 0,
            'sumNet' => 0,
            'sumVat' => 0,
        ];

        $list = $this->carwashApiFinanceService->getCarwashPeriod(
            $this->getSubscriber()->getOwnerBkf(),
            $this->getCarwashCriteriaSns(),
            startDate: $this->getDateCriteria('startDate', $this->getTimezone()),
            endDate: $this->getDateCriteria('endDate', $this->getTimezone())->setTime(23, 59, 59),
        );

        foreach ($list->getData() as $stats) {
            $carwash = $this->carwashesRepository->findOneBy(['serialNumber' => $stats->getSn()]);
            $cash = $stats->getBill() + $stats->getCoin();
            $bankCard = $stats->getBankCard();

            $items[] = [
                'carwashName' => $carwash->getLongName(),
                'sap' => $carwash->getUserDefinedNameProperty('sap'),
                'mpk' => $carwash->getUserDefinedNameProperty('mpk'),
                'cashGross' => $cash,
                'cashNet' => $this->gross2Net($cash),
                'cashVat' => $this->gross2Vat($cash),
                'bankCardsGross' => $bankCard,
                'bankCardsNet' => $this->gross2Net($bankCard),
                'bankCardsVAT' => $this->gross2Vat($bankCard),
                'sumGross' => $bankCard + $cash,
                'sumNet' => $this->gross2Net($bankCard + $cash),
                'sumVat' => $this->gross2Vat($bankCard + $cash),
            ];

            $sums['cashGross'] += $cash;
            $sums['cashNet'] += $this->gross2Net($cash);
            $sums['cashVat'] += $this->gross2Vat($cash);
            $sums['bankCardsGross'] += $bankCard;
            $sums['bankCardsNet'] += $this->gross2Net($bankCard);
            $sums['bankCardsVAT'] += $this->gross2Vat($bankCard);
            $sums['sumGross'] += ($bankCard + $cash);
            $sums['sumNet'] += $this->gross2Net($bankCard + $cash);
            $sums['sumVat'] += $this->gross2Vat($bankCard + $cash);
        }

        $data = new Data(
            data: $items,
            totalSum: $sums,
            total: count($items),
        );

        return $data;
    }

    public function getTables(): array
    {
        $data = $this->getData();
        if (!$data->isGenerated()) {
            return [];
        }

        $columns = [
            new Column("carwashName", 'table.carwash', class: "align-left"),
            new Column("sap", "SAP", class: "align-left"),
            new Column("mpk", "MPK", class: "align-left"),
            new Column("cashGross", "table.cash", unit: null, class: "align-right", numberFormat: 2),
            new Column(
                "cashNet",
                name: ($this->trans('table.cash') . ' ' . $this->trans('fiscal_transactions.table.net')),
                unit: null,
                class: "align-right",
                numberFormat: 2
            ),
            new Column(
                "cashVat",
                name: ($this->trans('table.cash') . ' ' . $this->trans('fiscal_transactions.table.vat')),
                unit: null,
                class: "align-right",
                numberFormat: 2
            ),
            new Column("bankCardsGross", "table.bank_cards", unit: null, class: "align-right", numberFormat: 2),
            new Column(
                "bankCardsNet",
                name: ($this->trans('table.bank_cards') . ' ' . $this->trans('fiscal_transactions.table.net')),
                unit: null,
                class: "align-right",
                numberFormat: 2
            ),
            new Column(
                "bankCardsVAT",
                name: ($this->trans('table.bank_cards') . ' ' . $this->trans('fiscal_transactions.table.vat')),
                unit: null,
                class: "align-right",
                numberFormat: 2
            ),
            new Column("sumGross", "table.sum", null, class: "align-left", numberFormat: 2),
            new Column(
                "sumNet",
                name: ($this->trans('table.sum') . ' ' . $this->trans('fiscal_transactions.table.net')),
                unit: null,
                class: "align-right",
                numberFormat: 2
            ),
            new Column(
                "sumVat",
                name: ($this->trans('table.sum') . ' ' . $this->trans('fiscal_transactions.table.vat')),
                unit: null,
                class: "align-right",
                numberFormat: 2
            ),
        ];

        return [
            new Table(
                name: "",
                columns: $columns,
                items: $data->getData(),
                summary: $data->getTotalSum()
            )
        ];
    }

    /**
     * @param $value
     */
    private function gross2Net($valueGross)
    {
        return $valueGross / (1 + 0.23);
    }

    /**
     * @param $value
     */
    private function gross2Vat($valueGross)
    {
        return $valueGross * 0.23 / (1 + 0.23);
    }
}

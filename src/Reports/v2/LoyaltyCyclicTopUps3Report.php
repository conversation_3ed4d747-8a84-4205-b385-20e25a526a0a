<?php

namespace App\Reports\v2;

use I2m\StandardTypes\Enum\CMSubscription;
use App\Repository\CarwashesRepository;
use App\Repository\LoggerRepository;
use App\Service\Report2\Data\ReportDataAbstract;
use App\Service\Report2\Model\Data;
use App\Service\UserGrantedChecker;
use I2m\Connectors\Service\CwLoyaltyApi\LoyaltyCyclicTopUpService;
use I2m\Connectors\Model\CwLoyaltyApi\CyclicTopUp;
use I2m\Connectors\Model\CwLoyaltyApi\List\CyclicTopUpList;
use I2m\Reports\Model\Column;
use I2m\Reports\Model\Table;
use Symfony\Contracts\Translation\TranslatorInterface;

class LoyaltyCyclicTopUps3Report extends ReportDataAbstract
{
    public function __construct(
        CarwashesRepository $carwashesRepository,
        TranslatorInterface $translator,
        readonly private LoyaltyCyclicTopUpService $loyaltyCyclicTopUpService,
        UserGrantedChecker $userGrantedChecker,
        LoggerRepository $loggerRepository
    ) {
        parent::__construct($translator, $carwashesRepository, $userGrantedChecker, $loggerRepository);
    }

    public function getTitle(): string
    {
        return 'common_cyclicTopUpsHeading';
    }

    public function getEmailText(): ?string
    {
        return $this->getTitle();
    }



    public function getData(?int $page = null, ?int $perPage = null): Data
    {
        if (!$this->checkPermision(CMSubscription::BASIC, ['ROLE_CM_LOYALSYSTEM_KEYLIST_AND_CLIENTS'])) {
            return  new Data();
        }
        $items = [];
        $params['search'] = $this->getCriteria()['search'] ?? null;
        $params['itemsPerPage'] = $perPage ?? 50;
        $params['showTotal'] = true;
        $params['isActive'] = $this->getCriteria()['isActive'] ?? null;

        $total = null;

        $page2 = $page ?? 1;
        do {
            $params['page'] = $page2;

            /** @var CyclicTopUpList $list */
            $list = $this->loyaltyCyclicTopUpService->getList(
                ownerId: $this->getSubscriber()->getOwnerBkf(),
                search: $params,
            );
            $total = $list->total;
            foreach ($list->data as $topUp) {
                /** @var CyclicTopUp $topUp */
                $items[] = [
                    'id' => $topUp->getId(),
                    'cardNumber' => $topUp->getCard()->getNumber(),
                    'cardAlias' => $topUp->getCard()->getAlias(),
                    'cardClientName' => $topUp->getCard()->getClient()?->getCompanyName(),
                    'comment' => $topUp->getComment(),
                    'startTime' => $topUp->getStartTime()->format('Y-m-d H:i:s'),
                    'endTime' => $topUp->getEndTime()->format('Y-m-d H:i:s'),
                    'isActive' => $topUp->getIsActive(),
                    'type' => $topUp->getType(),
                    'lastPeriod' => ($topUp->getLastPeriod() !== null) ? $topUp->getLastPeriod()->format('Y-m-d H:i:s') : null,
                    'value' => $topUp->getValue(),
                    'cardCurrencySymbol' => $topUp->getCard()->getCurrency()?->getSymbol(),
                ];
            }
            $page2++;
        } while (!empty($list->data) && is_null($page)); // jesli nie podajemy strony wtedy pobieramy wszystkie

        $data = new Data(
            data: $items,
            total: $total,
        );

        return $data;
    }

    public function getTables(): array
    {
        $data = $this->getData();
        if (!$data->isGenerated()) {
            return [];
        }

        $columns = [
            new Column("cardNumber", "loyaltyCards_card", class: "align-left"),
            new Column("cardAlias", "loyaltyCards_name", class: "align-left"),
            new Column("cardClientName", "common_client", class: "align-left"),
            new Column("comment", "common_comment", class: "align-left"),
            new Column("startTime", "loyaltyCards_startTime", class: "align-left"),
            new Column("endTime", "loyaltyCards_endTime", class: "align-left"),
            new Column("isActive", "loyaltyCards_state", class: "align-left"),
            new Column("type", "loyaltyCards_type", class: "align-left"),
            new Column("lastPeriod", "loyaltyCards_lastCal", class: "align-left"),
            new Column("value", "loyaltyCards_value", class: "align-left"),
            new Column("cardCurrencySymbol", "common_currency", class: "align-left"),
        ];

        return [
            new Table(
                name: "",
                columns: $columns,
                items: $data->getData(),
                summary: null
            )
        ];
    }
}

<?php

namespace App\Reports\v2;

use App\Entity\Carwashes;
use I2m\StandardTypes\Enum\CMSubscription;
use App\Repository\CarwashesRepository;
use App\Repository\LoggerRepository;
use App\Service\Report2\Data\ReportDataAbstract;
use App\Service\Report2\Model\Data;
use App\Service\UserGrantedChecker;
use I2m\Connectors\Service\CwFinanceApi\FinanceApiWorkTimeService;
use I2m\Reports\Model\Column;
use I2m\Reports\Model\Table;
use Symfony\Contracts\Translation\TranslatorInterface;

class FinanceProgramsTotal extends ReportDataAbstract
{
    public function __construct(
        CarwashesRepository $carwashesRepository,
        TranslatorInterface $translator,
        UserGrantedChecker $userGrantedChecker,
        LoggerRepository $loggerRepository,
        private FinanceApiWorkTimeService $workTimeService
    ) {
        parent::__construct($translator, $carwashesRepository, $userGrantedChecker, $loggerRepository);
    }

    public function getTitle(): string
    {
        return 'finance.program-usage';
    }

    public function getEmailText(): ?string
    {
        return $this->getTitle();
    }



    public function getData(?int $page = null, ?int $perPage = null): Data
    {
        if (!$this->checkPermision(CMSubscription::BASIC, ['ROLE_CM_FINANCE'])) {
            return  new Data();
        }

        $sns = $this->getCarwashCriteriaSns(
            $this->getCriteria()['serial'] ?? null
        );

        if (empty($sns)) {
            return  new Data();
        }

        $startDate = $this->getDateCriteria('startDate', $this->getTimezone());
        $endDate = $this->getDateCriteria('endDate', $this->getTimezone())->setTime(23, 59, 59);

        $cwData = $this->workTimeService->getCarwashPeriod(
            $this->getSubscriber()->getOwnerBkf(),
            $sns,
            startDate: $startDate,
            endDate: $endDate,
            timezone: new \DateTimeZone($this->getUser()->getTimezone()->getLocation())
        );

        $items = [];

        foreach ($cwData->getData() as $stats) {
            /** @var Carwashes $carwash */
            $carwash = $this->carwashesRepository->findOneBy(['serialNumber' => $stats->getSn()]);
            $totalDays = $this->getFullDaysDifference(max($startDate, $carwash->getStartDate()), $endDate);
            $lostDays = $stats->getDays() - $totalDays;

            $items[] = [
                'carwashName' => $carwash->getLongName(),
                'prewash' => round($stats->getPrewash() / 60),
                'mainwash' => round($stats->getMainwash() / 60),
                'rinsing' => round($stats->getRinsing() / 60),
                'wasxing' => round($stats->getWasxing() / 60),
                'glossing' => round($stats->getGlossing() / 60),
                'rims' => round($stats->getRims() / 60),
                'brush' => round($stats->getBrush() / 60),
                'foam' => round($stats->getFoam() / 60),
                'degreaser' => round($stats->getDegreser() / 60),
                'water' => $stats->getWater() ? $stats->getWater() / 1000 : null,
                'water_avg' => $stats->getWater() ? $stats->getWater() / $stats->getTotal() * 60 : null,
                'working_days' => $stats->getDays(),
                'lost_days' => $lostDays,
                'total_days' => $totalDays,
                'availability' => $totalDays ? $stats->getDays() / $totalDays * 100 : null,
                'total' => round($stats->getTotal() / 60),
            ];
        }



        $summary = $this->calculateSum($items);


        return new Data(
            data: $items,
            totalSum: $summary
        );
    }

    private function calculateSum(array $items)
    {
        $sums = [
            'prewash' => 0,
            'mainwash' => 0,
            'rinsing' => 0,
            'wasxing' => 0,
            'glossing' => 0,
            'rims' => 0,
            'brush' => 0,
            'foam' => 0,
            'degreaser' => 0,
            'water' => 0,
            'total' => 0,
            'total_days' => 0,
            'working_days' => 0,
            'lost_days' => 0
        ];
        $waterMin = 0;
        $totalDays = 0;
        foreach ($items as $item) {
            foreach ($sums as $key => &$sum) {
                $sum += $item[$key];
            }

            // do sredniego zuzycia wchodza tylko myjnie ktore maja liczniki wody
            if ($item['water']) {
                $waterMin += $item['total'];
            }

            $totalDays += $item['total_days'];
        }

        $sums['water_avg'] = $waterMin ? $sums['water'] / $waterMin * 1000 : null;
        $sums['availability'] = $totalDays ?  $sums['working_days'] / $totalDays * 100 : null;
        return $sums;
    }

    public function getTables(): array
    {
        $data = $this->getData();
        if (!$data->isGenerated()) {
            return [];
        }

        $columns = [
            new Column('carwashName', 'finance_carwash', class: "align-left"),
            new Column('prewash', 'programs.prewash', class: "align-right", numberFormat: 0, unit: "min"),
            new Column('mainwash', 'programs.mainwash', class: "align-right", numberFormat: 0, unit: "min"),
            new Column('rinsing', 'programs.rinsing', class: "align-right", numberFormat: 0, unit: "min"),
            new Column('wasxing', 'programs.wasxing', class: "align-right", numberFormat: 0, unit: "min"),
            new Column('glossing', 'programs.glossing', class: "align-right", numberFormat: 0, unit: "min"),
            new Column('rims', 'programs.rims', class: "align-right", numberFormat: 0, unit: "min"),
            new Column('brush', 'programs.brush', class: "align-right", numberFormat: 0, unit: "min"),
            new Column('foam', 'programs.foam', class: "align-right", numberFormat: 0, unit: "min"),
            new Column('degreaser', 'programs.degreaser', class: "align-right", numberFormat: 0, unit: "min"),
            new Column('total', 'programsusage.table.sum', class: "align-right", numberFormat: 0, unit: "min"),
            new Column('water', 'programsusage.table.water-usage', class: "align-right", numberFormat: 2, unit: "m³"),
            new Column('water_avg', 'programsusage.table.water-average', class: "align-right", numberFormat: 2, unit: "l/min"),
            new Column('availability', 'programsusage.table.availability', class: "align-right", numberFormat: 0, unit: "%")
        ];

        return [
            new Table(
                name: "",
                columns: $columns,
                items: $data->getData(),
                summary: $data->getTotalSum(),
            )
        ];
    }

    private function getFullDaysDifference(\DateTimeInterface $startDate, \DateTimeInterface $endDate)
    {

        // Różnica w sekundach
        $diffInSeconds = abs($endDate->getTimestamp() - $startDate->getTimestamp());

        // Obliczenie liczby dni, zaokrąglając w górę
        $fullDays = ceil($diffInSeconds / 86400);

        // Upewnienie się, że zawsze zwracamy co najmniej 1 dzień
        return $fullDays > 0 ? $fullDays : 1;
    }

    public function getPdfOptions(): array
    {
        return [
            'orientation' => 'Landscape',
        ];
    }
}

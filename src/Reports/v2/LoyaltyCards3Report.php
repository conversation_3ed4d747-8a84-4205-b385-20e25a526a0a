<?php

namespace App\Reports\v2;

use I2m\StandardTypes\Enum\CMSubscription;
use App\Repository\CarwashesRepository;
use App\Repository\LoggerRepository;
use App\Service\Report2\Data\ReportDataAbstract;
use App\Service\Report2\Model\Data;
use App\Service\UserGrantedChecker;
use I2m\Connectors\Service\CwLoyaltyApi\LoyaltyCardsService;
use I2m\Reports\Model\Column;
use I2m\Reports\Model\Table;
use Symfony\Contracts\Translation\TranslatorInterface;

class LoyaltyCards3Report extends ReportDataAbstract
{
    public function __construct(
        CarwashesRepository $carwashesRepository,
        TranslatorInterface $translator,
        private LoyaltyCardsService $loyaltyCardsService,
        UserGrantedChecker $userGrantedChecker,
        LoggerRepository $loggerRepository
    ) {
        parent::__construct($translator, $carwashesRepository, $userGrantedChecker, $loggerRepository);
    }

    public function getTitle(): string
    {
        return 'card_client_report.card_report_email_header';
    }

    public function getEmailText(): ?string
    {
        return $this->getTitle();
    }



    public function getData(?int $page = null, ?int $perPage = null): Data
    {
        if (!$this->checkPermision(CMSubscription::BASIC, ['ROLE_CM_LOYALSYSTEM_KEYLIST_AND_CLIENTS'])) {
            return  new Data();
        }

        $items = [];
        $total = 0;
        $params = $this->getCriteria();
        $params['perPage'] = $perPage ?? 100;
        $params['showTotal'] = true;

        $dateFrom = $this->getDateCriteria('startDate');
        $dateTo = $this->getDateCriteria('endDate');

        $page2 = $page ?? 1;
        do {
            $params['page'] = $page2;

            $list = $this->loyaltyCardsService->getList(
                ownerId: $this->getSubscriber()->getOwnerBkf(),
                search: $params,
                dateFrom: $dateFrom,
                dateTo: $dateTo,
                timezone: $this->getSubscriber()->getTimezone()->getLocation()
            );

            if ($list->total !== null) {
                $total = $list->total;
                $params['showTotal'] = false;
            }

            foreach ($list->data as $card) {
                $items[] = [
                    'number' => $card->getNumber(),
                    'alias' => $card->getAlias(),
                    'email' => $card->getEmail(),
                    'clientId' => $card->getClient()?->getId(),
                    'type' => $card->getType(),
                    'status' => $card->getStatus()->value,
                    'clientName' => $card->getClient()?->getCompanyName(),
                    'lastContact' => $card->getLastContact(),
                    'balance' => $card->getBalance(),
                    'topUps' => ($card->getStats()['topUps'] ?? 0) + ($card->getStats()['promotions'] ?? 0),
                    'payments' => -($card->getStats()['payments'] ?? null),
                    'toSend' => $card->getStats()['toSend'] ?? null,
                    'currencySymbol' => $card->getCurrency()->getSymbol(),
                    'cardToken' => $card->getCardToken(),
                    'additInfo' => $card->getAdditInfo(),
                    'fullComment' => $card->getFullComment(),
                ];
            }
            $page2++;
        } while (!empty($list->data) && is_null($page)); // jesli nie podajemy strony wtedy pobieramy wszystkie

        $data = new Data(
            data:     $items,
            total: $total,
        );

        return $data;
    }

    public function getTables(): array
    {
        $data = $this->getData();
        if (!$data->isGenerated()) {
            return [];
        }

        $columns = [
            new Column("number", "card_client_report.card", class: "align-left"),
            new Column("alias", "cards_topups_report.card_name", class: "align-left"),
            new Column("status", "table.status", class: "align-left"),
            new Column("clientName", "keys.client", class: "align-left"),
            new Column("topUps", "card_client_report.transactions.topups", unit: null, class: "align-right", numberFormat: 2),
            new Column("payments", "card_client_report.transactions.payments", unit: null, class: "align-right", numberFormat: 2),
            new Column("toSend", "card_client_report.topUpsToSent", unit: null, class: "align-right", numberFormat: 2),
            new Column("balance", "card_client_report.card_funds", unit: null, class: "align-right", numberFormat: 2),
            new Column("currencySymbol", "form.currency", null, class: "align-left"),
        ];

        return [
            new Table(
                name: "",
                columns: $columns,
                items: $data->getData(),
                summary: $data->getPageSum()
            )
        ];
    }
}

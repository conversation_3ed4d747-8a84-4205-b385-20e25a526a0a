<?php

namespace App\Reports\v2;

use App\Entity\Carwashes;
use I2m\StandardTypes\Enum\CMSubscription;
use App\Repository\CarwashesRepository;
use App\Repository\LoggerRepository;
use App\Service\CarwashApi\PortalProgramsUsage\PortalProgramsUsageApiService;
use App\Service\Report2\Data\ReportDataAbstract;
use App\Service\Report2\Model\Data;
use App\Service\UserGrantedChecker;
use I2m\Reports\Model\Column;
use I2m\Reports\Model\Table;
use Symfony\Contracts\Translation\TranslatorInterface;

class FinancePortalProgramsUsageTotal extends ReportDataAbstract
{
    public function __construct(
        CarwashesRepository $carwashesRepository,
        TranslatorInterface $translator,
        UserGrantedChecker $userGrantedChecker,
        LoggerRepository $loggerRepository,
        private PortalProgramsUsageApiService $programsPortalUsageApiService,
    ) {
        parent::__construct($translator, $carwashesRepository, $userGrantedChecker, $loggerRepository);
    }

    public function getTitle(): string
    {
        return 'finance.portal-programsusage-total';
    }

    public function getEmailText(): ?string
    {
        return $this->getTitle();
    }


    public function getData(?int $page = null, ?int $perPage = null): Data
    {
        if (!$this->checkPermision(CMSubscription::BASIC, ['ROLE_CM_FINANCE'])) {
            return  new Data();
        }

        $serialNumbers = $this->getCarwashCriteriaSns(
            $this->getCriteria()['serial'] ?? null
        );

        if (empty($serialNumbers)) {
            return new Data();
        }

        sort($serialNumbers);
        $sns = implode(',', $serialNumbers);

        $totalData = $this->programsPortalUsageApiService->getPortalUsageTotal(
            $sns,
            $this->getDateCriteria('startDate')->format('Y-m-d'),
            $this->getDateCriteria('endDate')->format('Y-m-d'),
            $this->getUser()->getTimezone()->getLocation(),
        );

        $totalItems = [];

        $totalSums = [
            'p1' => 0,
            'p2' => 0,
            'p3' => 0,
            'p4' => 0,
            'p5' => 0,
            'p6' => 0,
        ];

        foreach ($totalData as $row) {
            /** @var Carwashes $carwash */
            $carwash = $this->carwashesRepository->findOneBy(['serialNumber' => $row['sn']]);

            $totalItems[] = [
                'carwashName' => $carwash->getLongName(),
                'sn' => $row['sn'],
                'p1' => $row['programs']['p1'],
                'p2' => $row['programs']['p2'],
                'p3' => $row['programs']['p3'],
                'p4' => $row['programs']['p4'],
                'p5' => $row['programs']['p5'],
            ];

            $totalSums['p1'] += $row['programs']['p1'];
            $totalSums['p2'] += $row['programs']['p2'];
            $totalSums['p3'] += $row['programs']['p3'];
            $totalSums['p4'] += $row['programs']['p4'];
            $totalSums['p5'] += $row['programs']['p5'];
        }

        $data = new Data(
            data: $totalItems,
            totalSum: $totalSums,
        );

        return $data;
    }

    public function getTables(): array
    {
        $data = $this->getData();
        if (!$data->isGenerated()) {
            return [];
        }

        $columns = [
            new Column('carwashName', 'finance_carwash', class: "align-left"),
            new Column(
                'p1',
                "{$this->trans('programsusage.table.program')} 1",
                class: "align-left",
                numberFormat: 0
            ),
            new Column(
                'p1',
                "{$this->trans('programsusage.table.program')} 1",
                class: "align-left",
                numberFormat: 0
            ),
            new Column(
                'p2',
                "{$this->trans('programsusage.table.program')} 2",
                class: "align-left",
                numberFormat: 0
            ),
            new Column(
                'p3',
                "{$this->trans('programsusage.table.program')} 3",
                class: "align-left",
                numberFormat: 0
            ),
            new Column(
                'p4',
                "{$this->trans('programsusage.table.program')} 4",
                class: "align-left",
                numberFormat: 0
            ),
            new Column(
                'p5',
                "{$this->trans('programsusage.table.program')} 5",
                class: "align-left",
                numberFormat: 0
            ),
        ];

        return [
            new Table(
                name: "",
                columns: $columns,
                items: $data->getData(),
                summary: $data->getTotalSum(),
            )
        ];
    }
}

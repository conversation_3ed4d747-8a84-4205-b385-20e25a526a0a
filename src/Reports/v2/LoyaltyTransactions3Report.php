<?php

namespace App\Reports\v2;

use I2m\StandardTypes\Enum\CMSubscription;
use App\Repository\CarwashesRepository;
use App\Repository\LoggerRepository;
use App\Service\Report2\Data\ReportDataAbstract;
use App\Service\Report2\Model\Data;
use App\Service\UserGrantedChecker;
use I2m\Connectors\Service\CwLoyaltyApi\LoyaltyTransactionService;
use I2m\Connectors\Model\CwLoyaltyApi\Transaction;
use I2m\Reports\Model\Column;
use I2m\Reports\Model\Table;
use Symfony\Contracts\Translation\TranslatorInterface;

class LoyaltyTransactions3Report extends ReportDataAbstract
{
    public function __construct(
        CarwashesRepository $carwashesRepository,
        TranslatorInterface $translator,
        UserGrantedChecker $userGrantedChecker,
        private LoyaltyTransactionService $loyaltyTransactionService,
        LoggerRepository $loggerRepository
    ) {
        parent::__construct($translator, $carwashesRepository, $userGrantedChecker, $loggerRepository);
    }

    public function getTitle(): string
    {
        return 'card_client_report.cards_transactions';
    }

    public function getEmailText(): ?string
    {
        return $this->getTitle();
    }



    public function getData(?int $page = null, ?int $perPage = null): Data
    {
        if (!$this->checkPermision(CMSubscription::BASIC, ['ROLE_CM_LOYALSYSTEM_KEYLIST_AND_CLIENTS'])) {
            return  new Data();
        }

        $items = [];
        $params = $this->getCriteria();
        $params['perPage'] = $perPage ?? 1000;
        $params['showTotal'] = true;

        $total = null;

        $page2 = $page ?? 1;
        do {
            $params['page'] = $page2;
            $list = $this->loyaltyTransactionService->getList(
                ownerId: $this->getSubscriber()->getOwnerBkf(),
                search: $params,
                dateFrom:  $this->getDateCriteria('startDate'),
                dateTo: $this->getDateCriteria('endDate'),
                timezone: $this->getSubscriber()->getTimezone()->getLocation()
            );

            if ($list->total !== null) {
                $total = $list->total;
                $params['showTotal'] = false;
            }

            foreach ($list->data as $transaction) {
                $sn = $transaction->getCarwash()?->getSn();
                /** @var Transaction $transaction */
                $items[] = [
                    'time' => $transaction->getTime()->format('Y-m-d H:i:s'),
                    'carwashName' => $sn ? $this->getCarwash($sn, true)->getLongName() : 'none',
                    "deviceName" => $this->getDeviceName($transaction->getSource()->value, $transaction->getBayId()),
                    'source' => $transaction->getSource()->value,
                    'bayId' => $transaction->getBayId(),
                    'cardNumber' => $transaction->getCard()->getNumber(),
                    'cardAlias' => $transaction->getCard()->getAlias(),
                    'cardType' => $transaction->getCard()->getType()->value,
                    'type' => $transaction->getType()->value,
                    'value' => $transaction->getValue(),
                    'balance' => $transaction->getBalance(),
                    'currencySymbol' => $transaction->getCurrency()?->getSymbol(),
                ];
            }
            $page2++;
        } while (!empty($list->data) && is_null($page)); // jesli nie podajemy strony wtedy pobieramy wszystkie

        $data = new Data(
            data: $items,
            total: $total,
            filters: [
                'type' => $list->type,
                'source' => $list->source,
            ],
        );

        return $data;
    }

    public function getTables(): array
    {
        $data = $this->getData();
        if (!$data->isGenerated()) {
            return [];
        }
        $columns = [
            new Column("time", "common_tableDate", class: "align-left"),
            new Column("carwashName", "table.carwash", class: "align-left"),
            new Column("deviceName", "table.device", class: "align-left"),

            new Column("cardNumber", "card_client_report.card", class: "align-left"),
            new Column("cardAlias", "cards_topups_report.card_name", class: "align-left"),
            //new Column("card.client", "keys.client", class: "align-left"),
            new Column("type", "common_type", class: "align-left"),


            new Column("value", "table.value", null, class: "align-right", numberFormat: 2),
            new Column("balance", "card_client_report.value_after_transaction", null, class: "align-right", numberFormat: 2),
            new Column("currencySymbol", "form.currency", null, class: "align-left"),
        ];

        return [
            new Table(
                name: "",
                columns: $columns,
                items: $data->getData(),
                summary: null
            )
        ];
    }
}

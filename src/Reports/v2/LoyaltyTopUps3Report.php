<?php

namespace App\Reports\v2;

use I2m\StandardTypes\Enum\CMSubscription;
use App\Repository\CarwashesRepository;
use App\Repository\LoggerRepository;
use App\Service\Report2\Data\ReportDataAbstract;
use App\Service\Report2\Model\Data;
use App\Service\UserGrantedChecker;
use I2m\Connectors\Service\CwLoyaltyApi\LoyaltyTopUpService;
use I2m\Connectors\Model\CwLoyaltyApi\List\TopUpList;
use I2m\Connectors\Model\CwLoyaltyApi\TopUp;
use I2m\Reports\Model\Column;
use I2m\Reports\Model\Table;
use Symfony\Contracts\Translation\TranslatorInterface;

class LoyaltyTopUps3Report extends ReportDataAbstract
{
    public function __construct(
        CarwashesRepository $carwashesRepository,
        TranslatorInterface $translator,
        readonly private LoyaltyTopUpService $loyaltyTopUpService,
        UserGrantedChecker $userGranted<PERSON>he<PERSON>,
        LoggerRepository $loggerRepository
    ) {
        parent::__construct($translator, $carwashesRepository, $userGrantedChecker, $loggerRepository);
    }

    public function getTitle(): string
    {
        return 'card_client_report.transactions.topups';
    }

    public function getEmailText(): ?string
    {
        return $this->getTitle();
    }



    public function getData(?int $page = null, ?int $perPage = null): Data
    {
        if (!$this->checkPermision(CMSubscription::BASIC, ['ROLE_CM_LOYALSYSTEM_KEYLIST_AND_CLIENTS'])) {
            return  new Data();
        }
        $items = [];
        $params = $this->getCriteria();
        $params['perPage'] = $perPage ?? 1000;
        $params['showTotal'] = true;

        $total = null;

        $page2 = $page ?? 1;
        do {
            $params['page'] = $page2;

            /** @var TopUpList $list */
            $list = $this->loyaltyTopUpService->getList(
                ownerId: $this->getSubscriber()->getOwnerBkf(),
                search: $params,
                dateFrom: $this->getDateCriteria('startDate'),
                dateTo:  $this->getDateCriteria('endDate'),
                timezone: $this->getSubscriber()->getTimezone()->getLocation()
            );

            if ($list->total !== null) {
                $total = $list->total;
                $params['showTotal'] = false;
            }

            foreach ($list->data as $topUp) {
                /** @var TopUp $topUp */
                $items[] = [
                    'id' => $topUp->getId(),
                    'ctime' => $topUp->getCtime()->format('Y-m-d H:i:s'),
                    'cardNumber' => $topUp->getCard()?->getNumber() ?? '-',
                    'cardAlias' => $topUp->getCard()?->getAlias() ?? '-',
                    'cardClientId' => $topUp->getCard()?->getClient()?->getId(),
                    'cardClientName' => $topUp->getCard()?->getClient()?->getCompanyName(),
                    'type' => $topUp->getType()->value,
                    'source' => $topUp->getSource()->value,
                    'progress' => $topUp->getProgress(),
                    'addedBy' => $topUp->getAddedBy(),
                    'topUpSent' => $topUp->getTopUpSent(),
                    'topUpToSend' => $topUp->getTopUpToSend(),
                    'topUpValue' => $topUp->getTopUpValue(),
                    'currencySymbol' => $topUp->getCurrency()->getSymbol(),
                    'invoice' => $topUp->getInvoice(),
                    'invoiceGenerate' => $topUp->isInvoiceGenerate(),
                    'invoiceNumber' => $topUp->getInvoice2()?->getNumber(),
                ];
            }
            $page2++;
        } while (!empty($list->data) && is_null($page)); // jesli nie podajemy strony wtedy pobieramy wszystkie

        $data = new Data(
            data: $items,
            total: $total,
            filters: [
                'type' => $list->type,
                'source' => $list->source,
            ],
        );

        return $data;
    }

    public function getTables(): array
    {
        $data = $this->getData();
        if (!$data->isGenerated()) {
            return [];
        }

        $columns = [
            new Column("ctime", "common_tableDate", class: "align-left"),
            new Column("cardNumber", "card_client_report.card", class: "align-left"),
            new Column("cardAlias", "cards_topups_report.card_name", class: "align-left"),
            new Column("cardClientName", "keys.client", class: "align-left"),
            new Column("type", "fiscal_transactions.table.type", class: "align-left"),
            new Column("source", "cards_topups_report.source", class: "align-left"),
            new Column("progress", "cards_topups_report.status", class: "align-left"),
            new Column("topUpSent", "cards_topups_report.topup-send", null, class: "align-right", numberFormat: 2),
            new Column("topUpToSend", "cards_topups_report.topUpsToSent", null, class: "align-right", numberFormat: 2),
            new Column("topUpValue", "cards_topups_report.topup_value", null, class: "align-right", numberFormat: 2),
            new Column("currencySymbol", "form.currency", null, class: "align-left"),
            new Column("addedBy", "table.added-by", class: "align-left"),
        ];

        return [
            new Table(
                name: "",
                columns: $columns,
                items: $data->getData(),
                summary: null
            )
        ];
    }
}

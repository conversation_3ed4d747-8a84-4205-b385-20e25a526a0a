<?php

namespace App\Reports\v2;

use I2m\StandardTypes\Enum\CMSubscription;
use App\Repository\CarwashesRepository;
use App\Repository\LoggerRepository;
use App\Service\Report2\Data\ReportDataAbstract;
use App\Service\Report2\Model\Data;
use App\Service\UserGrantedChecker;
use I2m\Connectors\Service\CwFinanceApi\FinanceApiFinanceService;
use I2m\Connectors\Model\CwFinanceApi\Finance\FinanceSerieType;
use I2m\Reports\Model\Column;
use I2m\Reports\Model\Table;
use Symfony\Contracts\Translation\TranslatorInterface;

class FinanceTurnoverDetailed extends ReportDataAbstract
{
    public function __construct(
        CarwashesRepository $carwashesRepository,
        TranslatorInterface $translator,
        private FinanceApiFinanceService $carwashApiFinanceService,
        UserGrantedChecker $userGrantedChecker,
        LoggerRepository $loggerRepository
    ) {
        parent::__construct($translator, $carwashesRepository, $userGrantedChecker, $loggerRepository);
    }



    public function getTables(): array
    {
        $data = $this->getData();
        if (!$data->isGenerated()) {
            return [];
        }

        $currency = $this->getSubscriber()->getCurrency()?->getSymbol();
        $columns = [
            new Column('date', "table.date", null, "align-left"),
            new Column('coin', "table.coins", $currency),
            new Column('bill', "table.bill", $currency),
            new Column('mobile', "table.mobile-payments", $currency),
            new Column('bank_card', "table.bank_cards", $currency),
            new Column('changer_revalue', "table.POST", $currency),
            new Column('token', "table.tokens", $currency),
            new Column('bkf_card', "keyusage.payment-transaction", $currency),
            new Column('promotion', "table.promotion", $currency),
            new Column('service', "table.service", $currency),
            new Column('client', "table.clients", null),
        ];

        return  [new Table("summary", $columns, $data->getData(), $data->getPageSum())];
    }



    public function getTitle(): string
    {
        return $this->trans('finance_turnover_reportPeriodTitle');
    }

    public function getEmailText(): ?string
    {
        return $this->getTitle();
    }

    public function getData(?int $page = null, ?int $perPage = null): Data
    {
        if (!$this->checkPermision(CMSubscription::BASIC, ['ROLE_CM_FINANCE'])) {
            return  new Data();
        }

        $sns = $this->getCarwashCriteriaSns(
            $this->getCriteria()['serial'] ?? null
        );
        if (empty($sns)) {
            return  new Data();
        }
        $days = $this->carwashApiFinanceService->getSerie(
            $this->getSubscriber()->getOwnerBkf(),
            sns:      $sns,
            startDate: $this->getDateCriteria('startDate', $this->getTimezone()),
            endDate: $this->getDateCriteria('endDate', $this->getTimezone())->setTime(23, 59, 59),
            timezone:     $this->getSubscriber()->getTimezone()->getLocation(),
            type:    FinanceSerieType::from($this->getCriteria()['type']),
        );

        $data = [];
        $totalSums = [
            "bank_card" => 0,
            "bill" => 0,
            "bkf_card" => 0,
            "changer_revalue" =>  0,
            "coin" => 0,
            "client" => 0,
            "mobile" => 0,
            "promotion" => 0,
            "service" => 0,
            "token" => 0,
            "summary" => 0,
        ];

        foreach ($days->getData() as $day) {
            $item = [
                "date" => $day->getDate(),
                "bank_card" => $day->getBankCard(),
                "bill" => $day->getBill(),
                "bkf_card" => $day->getBkfCard(),
                "changer_revalue" =>  $day->getChangerRevalue(),
                "coin" => $day->getCoin(),
                "client" => $day->getClient(),
                "mobile" => $day->getMobile(),
                "promotion" => $day->getPromotion(),
                "service" => $day->getService(),
                "token" => $day->getToken(),
                "summary" => $day->getSum(),
            ];
            $data[] = $item;
            foreach ($totalSums as $key => &$sum) {
                $sum += $item[$key];
            }
        }

        $data = new Data(
            data:    $data,
            currency: $this->getSubscriber()->getCurrency(),
            pageSum:  $totalSums,
        );


        return $data;
    }
}

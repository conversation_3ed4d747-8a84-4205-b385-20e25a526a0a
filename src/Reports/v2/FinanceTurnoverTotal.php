<?php

namespace App\Reports\v2;

use I2m\StandardTypes\Enum\CMSubscription;
use App\Repository\CarwashesRepository;
use App\Repository\LoggerRepository;
use App\Service\CarwashApi\PortalFinance\PortalFinanceStatsApiService;
use App\Service\Report2\Data\ReportDataAbstract;
use App\Service\Report2\Model\Data;
use App\Service\UserGrantedChecker;
use I2m\Connectors\Service\CwFinanceApi\FinanceApiFinanceService;
use I2m\Connectors\Model\CwFinanceApi\Finance\FinanceBayList;
use I2m\Reports\Model\Column;
use I2m\Reports\Model\Table;
use I2m\StandardTypes\Enum\Source;
use Symfony\Contracts\Translation\TranslatorInterface;

class FinanceTurnoverTotal extends ReportDataAbstract
{
    public function __construct(
        CarwashesRepository $carwashesRepository,
        TranslatorInterface $translator,
        private FinanceApiFinanceService $carwashApiFinanceService2,
        private PortalFinanceStatsApiService $portalFinanceStatsApiService,
        UserGrantedChecker $userGrantedChecker,
        LoggerRepository $loggerRepository
    ) {
        parent::__construct($translator, $carwashesRepository, $userGrantedChecker, $loggerRepository);
    }



    public function getTables(): array
    {
        $data = $this->getData();
        if (!$data->isGenerated()) {
            return [];
        }

        $currency = $this->getData()->getCurrency()->getSymbol();
        // strona podsumowania
        $items = [];
        foreach ($data->getData() as $carwash) {
            $items[] = [
                'name' => $carwash['carwashName'],
                'cash' => $carwash['summary']['cash'],
                'cashless' => $carwash['summary']['cashless'],
                'selling' => $carwash['summary']['selling'],
                'exchanging' => $carwash['summary']['exchanging'],
                'promotion' => $carwash['summary']['promotion'],
                'client' => $carwash['summary']['client'],
                'prepaid' => $carwash['summary']['prepaid'],
            ];
        }
        $carwashesColumns = [
            new Column('name', "table.hardware", null, "align-left"),
            new Column('cash', "turnover.cash", $currency),
            new Column('cashless', "turnover.cashless", $currency),
            new Column('selling', "turnover.exchangerSale", $currency),
            new Column('prepaid', "turnover.prepaid", $currency),
            new Column('exchanging', "turnover.exchangerHoppers", $currency),
            new Column('promotion', "table.promotion", $currency),
            new Column('client', "table.client", null),

        ];

        $summaryText = $this->trans('table.in-total');

        $sheets[] = new Table($summaryText, $carwashesColumns, $items, $this->getData()->getPageSum());

        $standsColumns =
                 [
                    new Column('deviceName', "table.hardware", null, "align-left no-wrap"),
                    new Column('coin', "table.coins", $currency),
                    new Column('bill', "table.bill", $currency),
                    new Column('mobile', "turnover.table.mobilePayments", $currency),
                    new Column('bank_card', "table.bank_cards", $currency),
                    new Column('changer_revalue', "table.POST", $currency),
                    new Column('token', "table.tokens", $currency),
                    new Column('bkf_card', "keyusage.payment-transaction", $currency),
                    new Column('promotion', "table.promotion", $currency),
                    new Column('service', "table.service", $currency),
                    new Column('client', "table.clients", null),
                 ];

        foreach ($data->getData() as $carwash) {
            $sheets[] = new Table($carwash['carwashName'], $standsColumns, $carwash['stands'] ?? [], $carwash['standsSum']);
        }

        return $sheets;
    }



    public function getTitle(): string
    {
        if ($this->isLastCollection()) {
            return $this->trans('finance_turnover_reportSinceLastMCTitle');
        } else {
            return $this->trans('finance_turnover_reportPeriodTitle') . " {$this->getCriteria()['startDate']} - {$this->getCriteria()['endDate']}";
        }
    }

    public function getEmailText(): ?string
    {
        return $this->getTitle();
    }

    private function getChangerData($serialNumbersArray)
    {
        if ($this->isLastCollection()) {
            return $this->carwashApiFinanceService2->getChangerLast(
                $this->getSubscriber()->getOwnerBkf(),
                $serialNumbersArray
            );
        } else {
            return $this->carwashApiFinanceService2->getChangerPeriod(
                $this->getSubscriber()->getOwnerBkf(),
                $serialNumbersArray,
                startDate: $this->getDateCriteria('startDate', $this->getTimezone()),
                endDate: $this->getDateCriteria('endDate', $this->getTimezone())->setTime(23, 59, 59),
            );
        }
    }
    private function getBaysData($serialNumbersArray): FinanceBayList
    {

        if ($this->isLastCollection()) {
            return $this->carwashApiFinanceService2->getBayLast(
                $this->getSubscriber()->getOwnerBkf(),
                $serialNumbersArray
            );
        } else {
            return $this->carwashApiFinanceService2->getBayPeriod(
                $this->getSubscriber()->getOwnerBkf(),
                $serialNumbersArray,
                startDate: $this->getDateCriteria('startDate', $this->getTimezone()),
                endDate: $this->getDateCriteria('endDate', $this->getTimezone())->setTime(23, 59, 59),
            );
        }
    }

    private function getPortalStats($serialNumbersArray)
    {
        if ($this->isLastCollection()) {
            return $this->portalFinanceStatsApiService->getStatsForLastCollection(
                serialNumbers: $serialNumbersArray,
                currency: $this->getSubscriber()->getCurrency()->getCode(),
            );
        } else {
            return $this->portalFinanceStatsApiService->getStatsInRange(
                serialNumbers: $serialNumbersArray,
                dateFrom:      $this->getCriteria()['startDate'],
                dateTo:        $this->getCriteria()['endDate'],
                timezone:      $this->getSubscriber()->getTimezone()->getLocation(),
                currency:      $this->getSubscriber()->getCurrency()->getCode(),
            );
        }
    }
    private function isLastCollection(): bool
    {
        return is_null($this->getCriteria()['startDate'] ?? null) ||
                is_null($this->getCriteria()['endDate'] ?? null) ;
    }

    private function calculateSum(array $sums, array $item)
    {
        foreach ($sums as $key => &$sum) {
            $sum += $item[$key];
        }
        return $sums;
    }

    public function getData(?int $page = null, ?int $perPage = null): Data
    {

        if (!$this->checkPermision(CMSubscription::BASIC, ['ROLE_CM_FINANCE'])) {
            return  new Data();
        }

        $sns = $this->getCarwashCriteriaSns(
            $this->getCriteria()['serial'] ?? null
        );

        if (empty($sns)) {
            return  new Data();
        }
        $carwashes = [];

        // inicjalizuje tablice
        foreach ($sns as $sn) {
            $carwashes[$sn] = [
                'carwashName' => $this->getCarwash($sn)->getLongName(),
                'serialNumber' => $sn,
                'standsSum' =>
                    [
                        "bank_card" => 0,
                        "bill" => 0,
                        "bkf_card" => 0,
                        "changer_revalue" =>  0,
                        "client" => 0,
                        "coin" => 0,
                        "counter" => 0,
                        "mobile" => 0,
                        "promotion" => 0,
                        "service" => 0,
                        "token" => 0,

                        "cash" => 0,
                        "cashless" => 0,
                        "prepaid" => 0,
                        "sum" => 0,
                    ],
                'summary' =>
                    [
                        "cash" => 0,
                        "coin" => 0,
                        "bill" => 0,

                        "cashless" => 0,
                        "bank_card" => 0,
                        "mobile" => 0,

                        "selling" => 0,
                        "bkf_card_sold" => 0,
                        "bkf_card_revalue" => 0,
                        "bay_charge" => 0,

                        "sum" => 0,

                        "prepaid" =>  0,
                        "bkf_card" => 0,
                        "token" => 0,

                        "exchanging" =>  0,

                        "others" => 0,
                        "service" => 0,
                        "promotion" =>  0,

                        "client" => 0,
                    ]

            ];
        }

        foreach ($this->getBaysData($sns)->getData() as $item) {
            $sn = $item->getSn();

            $stand =  [
                "deviceName" => $this->getDeviceName($item->getSource()->value, $item->getBayId()),
                "source" => $item->getSource()->value,
                "stand" => $item->getBayId(),
                "bank_card" => $item->getBankCard(),
                "bill" => $item->getBill(),
                "bkf_card" => $item->getBkfCard(),
                "changer_revalue" =>  $item->getChangerRevalue(),
                "client" => $item->getClient(),
                "coin" => $item->getCoin(),
                "counter" => $item->getCounter(),
                "mobile" => $item->getMobile(),
                "promotion" => $item->getPromotion(),
                "service" => $item->getService(),
                "token" => $item->getToken(),
                "sum" => $item->getSum(),
            ];
            $stand['cash'] = $stand['coin'] + $stand['bill'];
            $stand['cashless'] = $stand['bank_card'] + $stand['mobile'];
            $stand['prepaid'] = $stand['bkf_card'] + $stand['token'];
            $stand['others'] = $stand['promotion'] + $stand['service'];

            $carwashes[$sn]['stands'][] = $stand;
            $carwashes[$sn]['standsSum'] = $this->calculateSum($carwashes[$sn]['standsSum'], $stand);

            $carwashes[$sn]['summary']['cash'] += $stand['cash'];
            $carwashes[$sn]['summary']['bill'] += $stand['bill'];
            $carwashes[$sn]['summary']['coin'] += $stand['coin'];

            $carwashes[$sn]['summary']['cashless'] += $stand['cashless'];
            $carwashes[$sn]['summary']['bank_card'] += $stand['bank_card'];
            $carwashes[$sn]['summary']['mobile']    += $stand['mobile'];

            $carwashes[$sn]['summary']['prepaid'] += $stand['prepaid'];
            $carwashes[$sn]['summary']['bkf_card'] += $stand['bkf_card'];
            $carwashes[$sn]['summary']['token'] += $stand['token'];
            $carwashes[$sn]['summary']['client'] += $stand['client'];

            $carwashes[$sn]['summary']['others'] += $stand['others'];
            $carwashes[$sn]['summary']['promotion'] += $stand['promotion'];
            $carwashes[$sn]['summary']['service'] += $stand['service'];
        }

        foreach ($this->getChangerData($sns)->getData() as $changerData) {
            $sn = $changerData->getSn();

            $exchanger =   [
               'hopper_a' => $changerData->getHopperA(),
               'hopper_b' => $changerData->getHopperB(),
               'note' => $changerData->getBill(),
               'mobile' => $changerData->getMobile(),
               'coin' => $changerData->getCoin(),
               'bay_charge' => $changerData->getBayCharge(),
               'bank_card' => $changerData->getBankCard(),
               'bkf_card_sold' => $changerData->getBkfCardSold(),
               'bkf_card_pay' => $changerData->getBkfCardPay(),
               'bkf_card_revalue' => $changerData->getBkfCardRevalue(),
               'bkf_card_promo' => $changerData->getBkfCardPromo(),
               'income' => $changerData->getBill() + $changerData->getCoin() + $changerData->getBankCard() + $changerData->getBkfCardPay() + $changerData->getMobile(),
               'outcome' => $changerData->getHopperA() + $changerData->getHopperB(),
               'selling' => $changerData->getBkfCardSold() + $changerData->getBkfCardRevalue() + $changerData->getBayCharge(),
               "promotion" => $changerData->getBkfCardPromo(),
            ];

            $exchanger['balance'] = $exchanger['income'] - $exchanger['outcome'] - $exchanger['selling'];
            $source = ($changerData->getSource() == Source::MONEY_CHANGER) ? "exchanger" : "terminal";
            $carwashes[$sn][$source] = $exchanger;
            $carwashes[$sn]['summary']['selling'] += $exchanger['selling'];
            $carwashes[$sn]['summary']['bkf_card_sold'] += $exchanger['bkf_card_sold'];
            $carwashes[$sn]['summary']['bkf_card_revalue'] += $exchanger['bkf_card_revalue'];
            $carwashes[$sn]['summary']['bay_charge'] += $exchanger['bay_charge'];

            $carwashes[$sn]['summary']['exchanging'] += $exchanger['hopper_a'] + $exchanger['hopper_b'];
            $carwashes[$sn]['summary']['promotion'] += $exchanger['promotion'];
        }

        foreach ($this->getPortalStats($sns)['data'] as $item) {
            $sn = $item['sn'];
            $portal = $item['data'];
            $carwashes[$sn]['portal'] = $portal;
        }

        $totalSums = [
            "cash" => 0,
            "coin" => 0,
            "bill" => 0,

            "cashless" => 0,
            "bank_card" => 0,
            "mobile" => 0,

            "selling" => 0,
            "bkf_card_sold" => 0,
            "bkf_card_revalue" => 0,
            "bay_charge" => 0,

            "sum" => 0,

            "prepaid" =>  0,
            "bkf_card" => 0,
            "token" => 0,

            "exchanging" =>  0,

            "others" => 0,
            "service" => 0,
            "promotion" =>  0,

            "client" => 0,
        ];

        foreach ($carwashes as &$carwash) {
            $carwash['summary']['sum'] = $carwash['summary']['cash'] + $carwash['summary']['cashless'] + $carwash['summary']['selling'];
            $totalSums = $this->calculateSum($totalSums, $carwash['summary']);
        }

        $data = new Data(
            data:    array_values($carwashes),
            currency: $this->getSubscriber()->getCurrency(),
            pageSum:  $totalSums,
        );


        return $data;
    }
}

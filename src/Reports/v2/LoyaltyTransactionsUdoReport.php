<?php

namespace App\Reports\v2;

use I2m\StandardTypes\Enum\CMSubscription;
use App\Repository\CarwashesRepository;
use App\Repository\LoggerRepository;
use App\Service\Report2\Data\ReportDataAbstract;
use App\Service\Report2\Model\Data;
use App\Service\UserGrantedChecker;
use I2m\Connectors\Service\CwLoyaltyApi\LoyaltyTransactionService;
use I2m\Connectors\Model\CwLoyaltyApi\Type;
use I2m\Reports\Model\Column;
use I2m\Reports\Model\Table;
use I2m\StandardTypes\Enum\Source;
use Symfony\Contracts\Translation\TranslatorInterface;

// raport przygotowany na prośbę udo
// https://gitlab.bkf.pl/bkf/dij/wsparcie-cm/-/issues/1020#
class LoyaltyTransactionsUdoReport extends ReportDataAbstract
{
    public function __construct(
        CarwashesRepository $carwashesRepository,
        TranslatorInterface $translator,
        UserGrantedChecker $userGrantedChecker,
        LoggerRepository $loggerRepository,
        readonly protected LoyaltyTransactionService $loyaltyService,
    ) {
        parent::__construct($translator, $carwashesRepository, $userGrantedChecker, $loggerRepository);
    }

    public function getTitle(): string
    {
        return 'card_client_report.cards_transactions';
    }

    public function getEmailText(): ?string
    {
        return $this->getTitle();
    }

    public function getData(?int $page = null, ?int $perPage = null): Data
    {

        if (!$this->checkPermision(CMSubscription::PREMIUM, ['ROLE_CM_LOYALSYSTEM_KEYLIST_AND_CLIENTS'])) {
            return new Data();
        }

        $serialNumbers = $this->getCarwashCriteriaSns(
            $this->getCriteria()['serial'] ?? null
        );

        if (empty($serialNumbers)) {
            return new Data();
        }

        $subscriber = $this->getSubscriber();
        $params = $this->getCriteria();

        $params['perPage'] = 2500;
        $params['showTotal'] = false;

        $result = [];
        $page = 0;
        do {
            $page++;
            $params['page'] = $page;
            $transactionsList = $this->loyaltyService->getList(
                ownerId: $subscriber->getOwnerBkf(),
                search: $params,
                dateFrom:  new \DateTime($this->getCriteria()['startDate']),
                dateTo: new \DateTime($this->getCriteria()['endDate']),
                timezone: $subscriber->getTimezone()->getLocation()
            );

            foreach ($transactionsList->data as $transaction) {
                $type = $transaction->getType();
                $source = $transaction->getSource();

                $data = [
                    'cardNumber' => $transaction->getCard()->getNumber(),
                    'cardAlias' => $transaction->getCard()->getAlias(),
                    'time' => $transaction->getTime()->format('Y-m-d H:i:s'),
                    'topUp' => ($type == Type::ADDITION) ? $transaction->getValue() : 0,
                    'topUpBonus' => ($type == Type::PROMOTION) ? $transaction->getValue() : 0,
                    'payment' => ($type == Type::SUBTRACTION) ? abs($transaction->getValue()) : 0,
                    'balance' => $transaction->getBalance(),
                    'changerTopUp' => ($type == Type::ADDITION) & ($source == Source::MONEY_CHANGER) ? $transaction->getValue() : 0,
                    'internetTopUp' => ($type == Type::ADDITION) & ($source == Source::INTERNET) ? $transaction->getValue() : 0,
                    'adjustment' => ($type == Type::ALIGNMENT) ? $transaction->getValue() : 0,
                    'type' => $type->value . "/" . $source->value,
                    'carwashName' => $transaction->getCarwash()?->getSn() ?? "none" . " / " . $transaction->getBayId()
                ];
                $data['startBalance'] = $data['balance'] + $data['payment'] - $data['topUp'] - $data['topUpBonus'] - $data['adjustment'];

                $result[] = $data;
            }
        } while (count($transactionsList->data));

        return new Data(
            data: $result,
        );
    }
    public function getTables(): array
    {
        $data = $this->getData();
        if (!$data->isGenerated()) {
            return [];
        }

        return [
            new Table(
                name: '',
                columns: [
                    new Column("time", 'card_client_report.transaction_date', class: "align-right"),
                    new Column("cardNumber", 'card_client_report.card', class: "align-left"),
                    new Column("cardAlias", 'cards_topups_report.card_name', class: "align-right"),
                    new Column("startBalance", 'keyusage.start-balance', class: "align-right", numberFormat: 2),
                    new Column("topUp", 'card_client_report.transactions.topup', class: "align-right", numberFormat: 2),
                    new Column("topUpBonus", 'keyusage.bonus-for-topup', class: "align-right", numberFormat: 2),
                    new Column("payment", 'card_client_report.transactions.payment', class: "align-right", numberFormat: 2),
                    new Column("balance", 'card_client_report.value_after_transaction', class: "align-right", numberFormat: 2),
                    new Column("changerTopUp", 'keyusage.changer-topup', class: "align-right", numberFormat: 2),
                    new Column("internetTopUp", 'keyusage.internet-topup-form-carwash', class: "align-right", numberFormat: 2),
                    new Column("adjustment", 'keyusage.balance_adjustment', class: "align-right", numberFormat: 2),
                    new Column("carwashName", 'table.carwash', class: "align-right"),
                ],
                items: $data->getData(),
            )
        ];
    }
}

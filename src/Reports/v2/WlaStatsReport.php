<?php

namespace App\Reports\v2;

use I2m\StandardTypes\Enum\CMSubscription;
use App\Repository\CarwashesRepository;
use App\Repository\CurrencyRepository;
use App\Repository\LoggerRepository;
use App\Repository\LoyaltyAppRepository;
use App\Service\Report2\Data\ReportDataAbstract;
use App\Service\Report2\Model\Data;
use App\Service\Report2\ReportException;
use App\Service\UserGrantedChecker;
use I2m\Connectors\Service\Wla\WlaApiStatsService;
use I2m\Reports\Model\Column;
use I2m\Reports\Model\Table;
use Symfony\Contracts\Translation\TranslatorInterface;

class WlaStatsReport extends ReportDataAbstract
{
    public function __construct(
        CarwashesRepository $carwashesRepository,
        TranslatorInterface $translator,
        private WlaApiStatsService $wlaApiStatsService,
        private LoyaltyAppRepository $loyaltyAppRepository,
        UserGrantedChecker $userGrantedChecker,
        LoggerRepository $loggerRepository,
        private CurrencyRepository $currencyRepository
    ) {
        parent::__construct($translator, $carwashesRepository, $userGrantedChecker, $loggerRepository);
    }

    public function getTitle(): string
    {
        return 'loyalApp_statsReport';
    }

    public function getEmailText(): ?string
    {
        return $this->getTitle();
    }

    public function getData(?int $page = null, ?int $perPage = null): Data
    {
        if (!$this->checkPermision(CMSubscription::BASIC, ['ROLE_CM_LOYALSYSTEM_KEYLIST_AND_CLIENTS'])) {
            return new Data();
        }

        $params = $this->getCriteria();
        $appConfig = $this->loyaltyAppRepository->findOneByName($params['app']);

        if (is_null($appConfig)) {
            return new Data();
        }

        $dateFrom = $this->getDateCriteria('startDate');
        $dateTo = $this->getDateCriteria('endDate');

        $query = [];
        if ($dateFrom) {
            $query['dateFrom'] = $dateFrom->format('Y-m-d');
        }
        if ($dateTo) {
            $query['dateTo'] = $dateTo->format('Y-m-d');
        }
        if (isset($params['timezone'])) {
            $query['timezone'] = $params['timezone'];
        }
        if (isset($params['type'])) {
            // front domyślnie uzywa formatu daily/monthly, a WLA api oczekuje day/month
            if ($params['type'] === 'daily') {
                $query['period'] = 'day';
            } elseif ($params['type'] === 'monthly') {
                $query['period'] = 'month';
            } else {
                throw new ReportException('Nieprawidłowa wartość parametru type. Dozwolone wartości to: "daily" lub "monthly"');
            }
        }

        $trendPaymentData = $this->wlaApiStatsService->getTrendPayment($appConfig, $query);
        $trendBalanceData = $this->wlaApiStatsService->getTrendBalance($appConfig, $query);
        $balanceData = $this->wlaApiStatsService->getBalance($appConfig, $query);

        $paymentItems = array_map(function ($row) {
 // nie serializuje obiektu
            $row1['user'] = $row->getUser();
            $row1['period'] = $row->getPeriod();
            $row1['carwash'] = $row->getCarwash();
            $row1['bonus'] = $row->getBonus();
            $row1['top_up'] = $row->getTopUp();
            $row1['direct_payment'] = $row->getDirectPayment();
            $row1['payment'] = $row->getPayment();


            return $row1;
        }, $trendPaymentData->getData());

        $balanceItems = array_map(function ($row) {
 // nie serializuje obiektu
            $row1['balance'] = $row->getBalance();
            $row1['period'] = $row->getPeriod();
            return $row1;
        }, $trendBalanceData->getData());

        $statsItems = array_map(function ($row) {
 // nie serializuje obiektu
            $row1['balance'] = $row->getBalance();
            $row1['issuer'] = $row->getIssuer();
            return $row1;
        }, $balanceData->getData());

        $currency = $this->currencyRepository->findOneByCode($trendPaymentData->getCurrency()->value);

        $data = new Data(
            data: [
                'payments' => $paymentItems,
                'balance' => $balanceItems,
                'stats' => $statsItems,
            ],
            currency: $currency,
            total: count($paymentItems) + count($balanceItems) + count($statsItems),
        );

        return $data;
    }

    public function getTables(): array
    {
        $data = $this->getData();
        if (!$data->isGenerated()) {
            return [];
        }

        $tables = [];

        $statsData = $data->getData()['stats'] ?? [];
        if (!empty($statsData)) {
            $statsColumns = [
                new Column("issuer", "loyalApp_issuer", class: "align-left"),
                new Column("value", "loyalApp_value", class: "align-right", numberFormat: 2),
            ];

            $tables[] = new Table(
                name: "loyalApp_generalStats",
                columns: $statsColumns,
                items: $statsData
            );
        }

        $paymentItems = $data->getData()['payments'] ?? [];
        if (!empty($paymentItems)) {
            $paymentColumns = [
                new Column("period", "common_period", class: "align-left"),
                new Column("user", "loyalApp_userCount", class: "align-right"),
                new Column("carwash", "loyalApp_carwashCount", class: "align-right"),
                new Column("payment", "loyalApp_payment", class: "align-right", numberFormat: 2),
                new Column("top_up", "loyalApp_topUp", class: "align-right", numberFormat: 2),
                new Column("bonus", "loyalApp_bonus", class: "align-right", numberFormat: 2),
            ];

            $tables[] = new Table(
                name: "loyalApp_paymentTrends",
                columns: $paymentColumns,
                items: $paymentItems
            );
        }

        $balanceItems = $data->getData()['balance'] ?? [];
        if (!empty($balanceItems)) {
            $balanceColumns = [
                new Column("period", "common_period", class: "align-left"),
                new Column("balance", "loyalApp_balance", class: "align-right", numberFormat: 2),
            ];

            $tables[] = new Table(
                name: "loyalApp_balanceTrends",
                columns: $balanceColumns,
                items: $balanceItems
            );
        }

        return $tables;
    }
}

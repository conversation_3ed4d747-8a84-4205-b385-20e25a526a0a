<?php

namespace App\Reports\v2;

use I2m\StandardTypes\Enum\CMSubscription;
use App\Repository\CarwashesRepository;
use App\Repository\LoggerRepository;
use App\Service\Connectors\CarwashApi\CarwashApiFiscalService;
use App\Service\Report2\Data\ReportDataAbstract;
use App\Service\Report2\Model\Data;
use App\Service\UserGrantedChecker;
use I2m\Connectors\Service\CwFiscalApi\FiscalApiService;
use I2m\Reports\Model\Column;
use I2m\Reports\Model\Table;
use Symfony\Contracts\Translation\TranslatorInterface;

class FinanceFiscalTransactions extends ReportDataAbstract
{
    public function __construct(
        CarwashesRepository $carwashesRepository,
        TranslatorInterface $translator,
        private FiscalApiService $carwashApiFiscalService,
        UserGrantedChecker $userGrantedChecker,
        LoggerRepository $loggerRepository
    ) {
        parent::__construct($translator, $carwashesRepository, $userGrantedChecker, $loggerRepository);
    }



    public function getTitle(): string
    {
        return 'finance.fiscal-transactions';
    }

    public function getEmailText(): ?string
    {
        return $this->getTitle();
    }

    public function getData(?int $page = null, ?int $perPage = null): Data
    {
        $items = [];
        if (!$this->checkPermision(CMSubscription::PREMIUM, ['ROLE_CM_FINANCE'])) {
            return  new Data();
        }

        $sns = $this->getCarwashCriteriaSns(
            $this->getCriteria()['serial'] ?? null
        );
        if (empty($sns)) {
            return  new Data();
        }
        $page2 = $page ?? 1;

        $pageSum = [
            'vat' => 0,
            'net' => 0,
            'value' => 0,
        ];
        $showTotal = ($perPage) ? true : false;// pobieram tylko jesli te dane są potrzebne
        $totalSum = null;
        $total = null;
        $filters = null;

        $timezone = $this->getTimezone();
        do {
            $list = $this->carwashApiFiscalService->getList(
                ownerId: $this->getSubscriber()->getOwnerBkf(),
                sns: $sns,
                dateFrom: $this->getDateCriteria('startDate', $timezone),
                dateTo:  $this->getDateCriteria('endDate', $timezone)->modify("+1 day")->modify("-1 second"),
                limit: $perPage ?? 2500,
                page: $page2,
                timezone: $timezone->getName(),
                fiscal: $this->getCriteria()['fiscal'] ?? null,
                search: $this->getCriteria()['search'] ?? null,
                showTotal: $showTotal
            );
            foreach ($list->data as $item) {
                $items[] = [
                    'carwashName' => $this->getCarwash($item->getCarwash()->getSn())->getLongName(),
                    "deviceName" => $this->getDeviceName($item->getSource()->value, $item->getBayId()),
                    "time" => $item->getTime()->setTimezone($timezone)->format('Y-m-d H:i:s'),
                    "sn" => $item->getCarwash()->getSn(),
                    "source" => $item->getSource()->value,
                    "bayId" => $item->getBayId(),
                    "type" =>  $item->getType()->value,
                    "fiscal" => $item->getFiscal(),
                    "value" => $item->getValue(),
                    "details" => $item->getDetails(),
                    "net" => $item->getNet(),
                    "vat" => $item->getVat(),
                    "id" => $item->getId(),
                ];

                $pageSum['vat'] += $item->getVat() ?? 0;
                $pageSum['net'] += $item->getNet() ?? 0;
                $pageSum['value'] += $item->getValue();
            }
            if ($showTotal) {
                $total = $list->total;
                $totalSum = $list->totalSum;
                $filters = $list->filters;
                $showTotal = false; // pobieram tylko raz
            }
            $page2++;
        } while (!empty($list->data) && is_null($page)); // jesli nie podajemy strony wtedy pobieramy wszystkie



        $pageSum['vat'] = round($pageSum['vat'], 2);
        $pageSum['net'] = round($pageSum['net'], 2);
        $pageSum['value'] = round($pageSum['value'], 2);

        $data = new Data(
            data:    $items,
            currency: $this->getSubscriber()->getCurrency(),
            pageSum:  $pageSum,
            totalSum:  $totalSum,
            total: $total,
            filters: $filters
        );

        return $data;
    }

    public function getTables(): array
    {
        $data = $this->getData();
        if (!$data->isGenerated()) {
            return [];
        }

        $currency = $this->getData()->getCurrency()->getSymbol();
        $columns = [
            new Column("time", "table.date", class: "align-left"),
            new Column("carwashName", "table.carwash", class: "align-left"),
            new Column("deviceName", "table.device", class: "align-left"),
            new Column("type", "table.date", class: "align-left"),
            new Column("fiscal", "fiscal.type.status", class: "align-left"),
            new Column("net", "fiscal.net", class: "align-right", unit: $currency),
            new Column("vat", "table.vat", class: "align-right", unit: $currency),
            new Column("value", "table.value", class: "align-right", unit: $currency),
        ];

        return [
            new Table(
                name: "summary",
                columns: $columns,
                items: $data->getData(),
                summary: $data->getPageSum()
            )
        ];
    }
}

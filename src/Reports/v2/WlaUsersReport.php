<?php

namespace App\Reports\v2;

use App\Repository\CarwashesRepository;
use App\Repository\LoggerRepository;
use App\Repository\LoyaltyAppRepository;
use App\Service\LoyalAppStatistics\LoyalAppStatistics;
use App\Service\Report2\Data\ReportDataAbstract;
use App\Service\Report2\Model\Data;
use App\Service\UserGrantedChecker;
use I2m\Connectors\Service\Wla\WlaApiUserService;
use I2m\Reports\Model\Column;
use I2m\Reports\Model\Table;
use Symfony\Contracts\Translation\TranslatorInterface;

class WlaUsersReport extends ReportDataAbstract
{
    public function __construct(
        CarwashesRepository $carwashesRepository,
        TranslatorInterface $translator,
        private WlaApiUserService $wlaApiUserService,
        private LoyaltyAppRepository $loyaltyAppRepository,
        UserGrantedChecker $userGrantedChecker,
        LoggerRepository $loggerRepository,
        private LoyalAppStatistics $appStatistics,
    ) {
        parent::__construct($translator, $carwashesRepository, $userGrantedChecker, $loggerRepository);
    }

    public function getTitle(): string
    {
        return 'loyalApp_usersListHeading';
    }

    public function getEmailText(): ?string
    {
        return $this->getTitle();
    }

    public function getData(?int $page = null, ?int $perPage = null): Data
    {


        $items = [];
        $total = 0;
        $params = $this->getCriteria();
        $params['perPage'] = $perPage ?? 20;
        $params['showTotal'] = true;

        $dateFrom = $this->getDateCriteria('startDate');
        $dateTo = $this->getDateCriteria('endDate');

        $params['startDate'] = $dateFrom;
        $params['endDate'] = $dateTo;
        $params['showStats'] = false;
        $params['showInfo'] = false;

        if (!$this->appStatistics->getApp($params['app'], $this->getUser())) {
            return new Data();
        }

        $appConfig = $this->loyaltyAppRepository->findOneByName($params['app']);

        $page2 = $page ?? 1;
        do {
            $params['page'] = $page2;

            $list = $this->wlaApiUserService->getUsersList(
                $appConfig,
                query: $params,
            );

            if ($list->getTotalItems() !== null) {
                $total = $list->getTotalItems();
                $params['showTotal'] = false;
            }

            foreach ($list->getItems() as $user) {
                $items[] = [
                    'id' => $user->getId(),
                    'email' => $user->getEmail(),
                    'managerEmail' => $user->getManagerEmail(),
                    'ctime' => $user->getCtime() ? (new \DateTime($user->getCtime()))->format('Y-m-d H:i:s') : null,
                    'lastUsage' => $user->getLastUsage() ? (new \DateTime($user->getLastUsage()))->format('Y-m-d H:i:s') : null,
                    'fleetManager' => $user->isFleetManager(),
                    'trustedPartner' => $user->isTrustedPartner(),
                    'balance' => $user->getBalance(),
                    'currencyCode' => $user->getCurrency()->getCode(),
                    'currencySymbol' => $user->getCurrency()->getSymbol(),
                    'invoiceDataFilled' => $user->isInvoiceDataFilled(),
                    'fleetMember' => $user->isFleetMember(),
                    'blocked' => $user->isBlocked(),
                    'notificable' => $user->isNotificable(),
                ];
            }
            $page2++;
        } while (!empty($list->getItems()) && is_null($page)); // jesli nie podajemy strony wtedy pobieramy wszystkie

        $data = new Data(
            data: $items,
            total: $total,
        );

        return $data;
    }

    public function getTables(): array
    {
        $data = $this->getData();
        if (!$data->isGenerated()) {
            return [];
        }


        $columns = [
            new Column("id", "common_id", class: "align-left"),
            new Column("email", "common_email", class: "align-left"),
            new Column("managerEmail", "loyalApp_managerEmail", class: "align-left"),
            new Column("ctime", "common_tableCreateDate", class: "align-left"),
            new Column("lastUsage", "common_lastUsage", class: "align-left"),
            new Column("fleetManager", "loyalApp_fleetManager", class: "align-left"),
            new Column("trustedPartner", "loyalApp_tableTrustedPartner", class: "align-left"),
            new Column("balance", "loyalApp_balance", unit: null, class: "align-right", numberFormat: 2),
            new Column("currencySymbol", "common_currency", null, class: "align-left"),
        ];

        return [
            new Table(
                name: "",
                columns: $columns,
                items: $data->getData(),
                summary: $data->getPageSum()
            )
        ];
    }
}

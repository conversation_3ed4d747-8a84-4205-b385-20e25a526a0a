<?php

namespace App\Reports\v2;

use I2m\StandardTypes\Enum\CMSubscription;
use App\Repository\CarwashesRepository;
use App\Repository\LoggerRepository;
use App\Service\Report2\Data\ReportDataAbstract;
use App\Service\Report2\Model\Data;
use App\Service\UserGrantedChecker;
use I2m\Connectors\Service\CwFiscalApi\FiscalApiService;
use I2m\Reports\Model\Column;
use I2m\Reports\Model\Table;
use Symfony\Contracts\Translation\TranslatorInterface;

class FinanceFiscalDevicesStatus extends ReportDataAbstract
{
    public function __construct(
        CarwashesRepository $carwashesRepository,
        TranslatorInterface $translator,
        private FiscalApiService $carwashApiFiscalService,
        UserGrantedChecker $userGrantedChecker,
        LoggerRepository $loggerRepository
    ) {
        parent::__construct($translator, $carwashesRepository, $userGrantedChecker, $loggerRepository);
    }




    public function getTitle(): string
    {
        return 'fiscal_transactions.fiscalDevicesDetails';
    }

    public function getEmailText(): ?string
    {
        return $this->getTitle();
    }
    public function getData(?int $page = null, ?int $perPage = null): Data
    {
        if (!$this->checkPermision(CMSubscription::PREMIUM, ['ROLE_CM_FINANCE'])) {
            return  new Data();
        }

        $sns = $this->getCarwashCriteriaSns(
            $this->getCriteria()['serial'] ?? null
        );

        if (empty($sns)) {
            return  new Data();
        }

        $devicesInfo = $this->carwashApiFiscalService->getFiscalDevicesStatus(
            ownerId: $this->getSubscriber()->getOwnerBkf(),
            sns: $sns,
        )->data;

        $data = array_map(function ($item) {
            return [
                'carwashName' => $this->getCarwash($item->getSn())->getLongName(),
                'serialNumber' => $item->getSn(),
                'lastContact' => $item->getLastContact(),
                'standInfo' => $item->getStandInfo(),
            ];
        }, $devicesInfo);

        return new Data(
            data: $data,
        );
    }
    public function getTables(): array
    {
        $data = $this->getData();
        if (!$data->isGenerated()) {
            return [];
        }

        $data = $this->getData();
        $tables = [];

        foreach ($data->getData() as $carwash) {
            $standInfo = $carwash['standInfo'];

            if (is_null($standInfo)) {
                continue;
            }

            foreach ($standInfo as $index => $stand) {
                $standInfo[$index]['deviceName'] = $this->getDeviceName($stand['source'], $stand['bayId']);

                unset($standInfo[$index]['source']);
                unset($standInfo[$index]['bayId']);
            }

            $columns = [
                new Column("deviceName", "table.source", class: "align-left"),
            ];

            usort($standInfo, function ($a, $b) {
                return $a['deviceName'] <=> $b['deviceName'];
            });

            $firstStand = reset($standInfo);
            $standKeys = array_keys($firstStand);
            foreach ($standKeys as $standKey) {
                if ($standKey !== 'deviceName') {
                    $columns[] = new Column($standKey, $standKey, class: "align-left");
                }
            }

            $tables[] = new Table(
                columns: $columns,
                name: $carwash['carwashName'],
                items: $standInfo,
            );
        }

        return $tables;
    }
}

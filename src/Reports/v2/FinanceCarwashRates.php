<?php

namespace App\Reports\v2;

use I2m\StandardTypes\Enum\CMSubscription;
use App\Repository\CarwashesRepository;
use App\Repository\LoggerRepository;
use App\Service\Report2\Data\ReportDataAbstract;
use App\Service\Report2\Model\Data;
use App\Service\UserGrantedChecker;
use I2m\Connectors\Service\CarwashApi\CarwashApiRatesService;
use I2m\Connectors\Model\CarwashApi\CarwashRates\CarwashRates;
use I2m\Reports\Model\Column;
use I2m\Reports\Model\Table;
use Symfony\Contracts\Translation\TranslatorInterface;

class FinanceCarwashRates extends ReportDataAbstract
{
    private const TIME_NUMERATOR = 60;
    private const LITERS_NUMERATOR = 1;




    public function __construct(
        CarwashesRepository $carwashesRepository,
        TranslatorInterface $translator,
        private readonly CarwashApiRatesService $carwashRatesService,
        UserGrantedChecker $userGrantedChe<PERSON>,
        LoggerRepository $loggerRepository,
    ) {
        parent::__construct($translator, $carwashesRepository, $userGrantedChecker, $loggerRepository);
    }

    public function getTitle(): string
    {
        return 'common_financeCarwashRates';
    }

    public function getEmailText(): ?string
    {
        return $this->getTitle();
    }
    public function getData(?int $page = null, ?int $perPage = null): Data
    {
        if (!$this->checkPermision(CMSubscription::PREMIUM, ['ROLE_CM_FINANCE'])) {
            return new Data();
        }

        $user = $this->getUser();
        $serialNumbers = $this->getCarwashCriteriaSns(
            $this->getCriteria()['serial'] ?? null
        );

        if (empty($serialNumbers)) {
            return new Data();
        }

        $ownerId = $this->getSubscriber()->getOwnerBkf();
        $timezone = $this->getTimezone();
        $isUsingTimeUnit = filter_var($this->getCriteria()['useTimeUnit'] ?? false, FILTER_VALIDATE_BOOLEAN);

        $carwashRatesResponse = $this->carwashRatesService->getList($serialNumbers, $ownerId);

        $data = array_map(function (CarwashRates $item) use ($timezone, $isUsingTimeUnit) {
            $syncTime = $item->getSyncTime()->setTimezone($timezone);
            $currencySymbol = $item->getCurrency()->getSymbol();

            return [
                'carwashName' => $this->getCarwash($item->getCarwash()->getSn())->getLongName(),
                'syncTime' => $syncTime->format('Y-m-d H:i:s'),
                'syncWarning' => $syncTime < (new \DateTime('now', $timezone))->modify('-6 hours'),
                'prewash' => $this->formatServiceRate($item->getPrewash(), $isUsingTimeUnit, $currencySymbol),
                'mainwash' => $this->formatServiceRate($item->getMainwash(), $isUsingTimeUnit, $currencySymbol),
                'rinsing' => $this->formatServiceRate($item->getRinsing(), $isUsingTimeUnit, $currencySymbol),
                'wasxing' => $this->formatServiceRate($item->getWasxing(), $isUsingTimeUnit, $currencySymbol),
                'glossing' => $this->formatServiceRate($item->getGlossing(), $isUsingTimeUnit, $currencySymbol),
                'rims' => $this->formatServiceRate($item->getRims(), $isUsingTimeUnit, $currencySymbol),
                'brush' => $this->formatServiceRate($item->getBrush(), $isUsingTimeUnit, $currencySymbol),
                'foam' => $this->formatServiceRate($item->getFoam(), $isUsingTimeUnit, $currencySymbol),
                'degreser' => $this->formatServiceRate($item->getDegreser(), $isUsingTimeUnit, $currencySymbol),
                'pause' => $this->formatServiceRate($item->getPause(), $isUsingTimeUnit, $currencySymbol),
                // 'cleaner' domyślnie w s/zł, zmieniamy na zł/min
                'cleaner' => $this->formatServiceRate($this->convertTime($item->getCleaner()), $isUsingTimeUnit, $currencySymbol),
                'distributor' => $this->formatDistributorServiceRate($item->getDistributor(), $isUsingTimeUnit, $currencySymbol),

            ];
        }, $carwashRatesResponse->getData());

        return new Data(
            data: $data,
            total: $carwashRatesResponse->getTotal(),
            currency: $user->getCurrency()
        );
    }

    public function getTables(): array
    {
        $data = $this->getData();
        if (!$data->isGenerated()) {
            return [];
        }

        $data = array_map(function ($item) {
            foreach ($item as $key => $entry) {
                if (is_array($entry) && array_key_exists('value', $entry) && array_key_exists('unit', $entry)) {
                    $value = $entry['value'];
                    $unit = $entry['unit'];

                    $item[$key] = number_format($value, 2) . " {$unit}";
                }
            }

            return $item;
        }, $data->getData());

        return [
            new Table(
                name: '',
                columns: [
                    new Column("carwashName", "table.carwash", class: "align-left"),
                    new Column("prewash", "programs.prewash", class: "align-center"),
                    new Column("mainwash", "programs.mainwash", class: "align-center"),
                    new Column("rinsing", "programs.rinsing", class: "align-center"),
                    new Column("wasxing", "programs.wasxing", class: "align-center"),
                    new Column("glossing", "programs.glossing", class: "align-center"),
                    new Column("rims", "programs.rims", class: "align-center"),
                    new Column("brush", "programs.brush", class: "align-center"),
                    new Column("foam", "programs.foam", class: "align-center"),
                    new Column("degreser", "programs.degreaser", class: "align-center"),
                    new Column("pause", "finance_pause", class: "align-center"),
                    new Column("cleaner", "fiscal.source.VACUUM_CLEANER", class: "align-center"),
                    new Column("distributor", "fiscal.source.DISTRIBUTOR", class: "align-center"),
                ],
                items: $data,
            )
        ];
    }

    private function formatServiceRate(?float $serviceValue, bool $isUsingTimeUnit, string $currencySymbol): array
    {
        $unit = $this->trans($isUsingTimeUnit ? 'unit_seconds_abbr' : 'unit_minutes_abbr');

        return [
            'value' => $isUsingTimeUnit ? $this->convertTime($serviceValue) : $serviceValue,
            'unit' => $isUsingTimeUnit ? "{$unit}/{$currencySymbol}" : "{$currencySymbol}/{$unit}",
        ];
    }

    private function formatDistributorServiceRate(?float $serviceValue, bool $isUsingTimeUnit, string $currencySymbol): array
    {
        $unit = $this->trans('unit_liters_abbr');

        return [
            'value' => round($isUsingTimeUnit ? $this->convertLiters($serviceValue) : $serviceValue, 2),
            'unit' => $isUsingTimeUnit ? "{$unit}/{$currencySymbol}" : "{$currencySymbol}/{$unit}",
        ];
    }

    private function convertTime(?float $value): ?float
    {
        // zł/min <-> s/zł
        return $value > 0 ? (self::TIME_NUMERATOR / $value) : null;
    }

    private function convertLiters(?float $value): ?float
    {
        // l/min <-> l/zł
        return $value > 0 ? (self::LITERS_NUMERATOR / $value) : null;
    }
}

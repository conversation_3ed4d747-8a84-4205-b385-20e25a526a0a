<?php

namespace App\Reports\v2;

use I2m\StandardTypes\Enum\CMSubscription;
use App\Repository\CarwashesRepository;
use App\Repository\LoggerRepository;
use App\Service\Report2\Data\ReportDataAbstract;
use App\Service\Report2\Model\Data;
use App\Service\UserGrantedChecker;
use I2m\Connectors\Service\CwLoyaltyApi\LoyaltyInvoicesService;
use I2m\Reports\Model\Column;
use I2m\Reports\Model\Table;
use Symfony\Contracts\Translation\TranslatorInterface;

class LoyaltyInvoices3Report extends ReportDataAbstract
{
    public function __construct(
        CarwashesRepository $carwashesRepository,
        TranslatorInterface $translator,
        readonly protected LoyaltyInvoicesService $invoiceListProvider,
        UserGrantedChecker $userGrantedChecker,
        LoggerRepository $loggerRepository
    ) {
        parent::__construct($translator, $carwashesRepository, $userGrantedChecker, $loggerRepository);
    }

    public function getTitle(): string
    {
        return 'cmuser.role_client_invoices_list';
    }

    public function getEmailText(): ?string
    {
        return $this->getTitle();
    }



    public function getData(?int $page = null, ?int $perPage = null): Data
    {
        if (!$this->checkPermision(CMSubscription::PREMIUM, ['ROLE_CM_LOYALSYSTEM_KEYLIST_AND_CLIENTS'])) {
            return  new Data();
        }

        $params = $this->getCriteria();

        $invoiceList = $this->invoiceListProvider->getList(
            ownerId: $this->getSubscriber()->getOwnerBkf(),
            search: $params['search'] ?? null,
            page: $page,
            perPage: $perPage,
            dateFrom: $this->getDateCriteria('startDate'),
            dateTo: $this->getDateCriteria('endDate'),
            clientId: $params['clientId'] ?? null,
        );

        $result = [];

        foreach ($invoiceList->data as $invoice) {
            $result[] = [
                'id' => $invoice->getId(),
                'createdAt' => $invoice->getCreatedAt()->format('Y-m-d'),
                'sendDate' => $invoice->getSendDate()?->format('Y-m-d H:i'),
                'serviceDate' => $invoice->getServiceDate()->format('Y-m-d'),
                'number' => $invoice->getNumber(),
                'companyName' => $invoice->getClient()->getCompanyName(),
                'taxNumber' => $invoice->getClient()->getTaxNumber(),
                'clientId' => $invoice->getClient()->getId(),
                'totalNet' => $invoice->getTotalNet(),
                'total' => $invoice->getTotalGross(),
                'currency' => $invoice->getCurrency()
            ];
        }

        return new Data(data: $result, total: $invoiceList->total);
    }

    public function getTables(): array
    {
        $data = $this->getData();
        if (!$data->isGenerated()) {
            return [];
        }

        $currency = $this->getSubscriber()->getCurrency()->getSymbol();
        $columns = [
            new Column("createdAt", "invoices.issuance-date", class: "align-left"),
            new Column("serviceDate", "fiscal.timestamp", class: "align-left"),
            new Column("sendDate", "invoices.send-date", class: "align-left"),
            new Column("number", "invoices.invoice-number", class: "align-left"),
            new Column("companyName", "table.client", class: "align-left"),
            new Column("taxNumber", "table.tax_number", class: "align-left"),


            new Column("totalNet", "table.net-value", unit: $currency, class: "align-right", numberFormat: 2),
            new Column("total", "table.gross-value", unit: $currency, class: "align-right", numberFormat: 2),

        ];

        return [
            new Table(
                name: "",
                columns: $columns,
                items: $data->getData(),
                summary: $data->getPageSum()
            )
        ];
    }
}

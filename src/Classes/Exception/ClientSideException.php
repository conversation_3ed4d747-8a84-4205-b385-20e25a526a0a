<?php

namespace App\Classes\Exception;

use Symfony\Component\HttpFoundation\Response;

class ClientSideException extends \Exception
{
    protected $clientCode;

    protected $httpCode;

    /**
     * ClientSideException constructor.
     *
     * @param string $message
     * @param int $code
     * @param \Throwable|null $previous
     * @param string $clientCode
     */
    public function __construct(
        $message = "",
        $code = 0,
        \Throwable $previous = null,
        $clientCode = "",
        $httpCode = Response::HTTP_BAD_REQUEST
    ) {
        parent::__construct($message, $code, $previous);
        $this->clientCode = $clientCode;
        $this->httpCode = $httpCode;
    }

    /**
     * @return mixed
     */
    public function getClientCode()
    {
        return $this->clientCode;
    }

    /**
     * @return mixed
     */
    public function getHttpCode()
    {
        return $this->httpCode;
    }
}

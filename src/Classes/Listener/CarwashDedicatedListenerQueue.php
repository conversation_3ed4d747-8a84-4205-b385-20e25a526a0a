<?php

namespace App\Classes\Listener;

use App\Classes\Listener\Commands\InvokeMoneyCollectInCM;
use App\Classes\Listener\Commands\InvokeNewAlarmInCM;
use App\Entity\Carwashes;
use I2m\IIot\Model\Param\ParamList;

class CarwashDedicatedListenerQueue
{
    public function __construct(
        private InvokeMoneyCollectInCM $moneyCollect,
        private InvokeNewAlarmInCM $newAlarmEmailParse
    ) {
    }

    public function executeActions(
        Carwashes $carwashes,
        array $data
    ) {
        switch ($data['p_id']) {
            case ParamList::CW_API_NEW_ALARM:
                $this->newAlarmEmailParse->parse($carwashes, $data);
                break;
            case ParamList::CW_API_NEW_MONEY_COLLECT:
                $this->moneyCollect->moneyCollectFromCarwashApi($carwashes, $data);
                break;
        }
    }
}

<?php

namespace App\Classes\Listener\Commands;

use App\Entity\Carwashes;
use I2m\StandardTypes\Enum\CMSubscription;
use App\Entity\User;
use App\Reports\v2\FinanceMoneyCollects;
use App\Repository\LoggerRepository;
use App\Service\Report2\ReportService;
use I2m\Reports\Enum\FileExtention;

class InvokeMoneyCollectInCM
{
    public function __construct(
        private ReportService $reportService,
        private LoggerRepository $loggerRepository
    ) {
    }

    public function moneyCollectFromCarwashApi(
        Carwashes $carwash,
        array $data
    ) {
        $ai = json_decode($data['dt'][0]['ai'], true);
        $id = $ai['id'];
        $this->generateReport($carwash, $id);
    }

    public function generateReport(
        Carwashes $carwash,
        int $mcId
    ) {

        $subscriber = $carwash->getSubscriber();
        if (is_null($subscriber)) {
            return;
        }

        if (!$subscriber->getSubscription()->isGreaterEqualThan(CMSubscription::BASIC)) {
            return;
        }

        $emails = $subscriber->getUsers()->filter(
            function (User $user) use ($carwash) {
                return $user->getReportSettings()->getMoneyCollectAlarms() &&
                    $user->hasAccessToCarwashBySn($carwash->getSerialNumber()) &&
                    $user->hasRole('ROLE_CM_FINANCE')
                    ;
            }
        )->map(fn(User $user) => $user->getEmail())
            ->getValues();
        if (empty($emails)) {
            return;
        }

        $emailsList = implode(',', $emails);
        $this->loggerRepository->debug(
            $subscriber,
            null,
            $carwash,
            self::class,
            "Email notification " .
            "with money collect {$mcId} for users: $emailsList"
        );

        $criteria = [
            'id' => $mcId,
            'serial' => $carwash->getSerialNumber()
        ];

        $this->reportService->generateAsync(
            subscriber: $subscriber,
            reportType: FinanceMoneyCollects::class,
            ext:        FileExtention::PDF,
            criterias:  $criteria,
            emails:     $emails
        );
    }
}

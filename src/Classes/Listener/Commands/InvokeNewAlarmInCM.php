<?php

namespace App\Classes\Listener\Commands;

use App\Controller\AlarmsController;
use App\Entity\Carwashes;
use I2m\StandardTypes\Enum\CMSubscription;
use App\Entity\User;
use App\Repository\LoggerRepository;
use App\Service\Connectors\CarwashApi\CarwashApiAlarmService;
use App\Service\Email\EmailService;
use App\Service\Mobile\MobileNotificationService;
use Symfony\Bundle\FrameworkBundle\Translation\Translator;
use Symfony\Contracts\Translation\TranslatorInterface;
use Twig\Environment;

use function Sentry\captureException;

class InvokeNewAlarmInCM
{
    const LEVEL_PRIORITY = [
        'unspecified' => 20, // jesli alarm nie zostal zdefiniowany, wtedy traktuje jako informacje
        'information' => 20,
        'warning' => 30,
        "error" => 40,
        'fatal' => 50,
        'service' => 100
    ];

    public function __construct(
        private TranslatorInterface $translator,
        private Environment $templating,
        private CarwashApiAlarmService $alarmApiService,
        private LoggerRepository $loggerRepository,
        private EmailService $emailService,
        private MobileNotificationService $mobileNotificationService,
    ) {
    }

    public function parse(
        Carwashes $carwash,
        array $data
    ) {
        $subscriber = $carwash->getSubscriber();
        if (is_null($subscriber)) {
            return;
        }

        foreach ($data['dt'] as $data) {
            $ai = json_decode($data['ai'], true);
            $this->sendNewAlarm($carwash, $ai['id']);
        }
    }

    public function sendNewAlarm(Carwashes $carwash, int $id): void
    {
        $subscriber = $carwash->getSubscriber();
        if (is_null($subscriber)) {
            return;
        }

        if (!$subscriber->getSubscription()->isGreaterEqualThan(CMSubscription::BASIC)) {
            return;
        }

        $usersToSend = $subscriber->getUsers()->filter(
            function (User $user) use ($carwash) {
                return $user->hasAccessToCarwashBySn($carwash->getSerialNumber()) &&
                    $user->hasRole('ROLE_CM_ALARMS_AND_TECHNICAL_DATA') &&
                    (
                        !is_null($user->getReportSettings()->getAlarmsSettings()) ||
                        !is_null($user->getReportSettings()->getAlarmsMobileSettings())
                    )
                    ;
            }
        );
        if ($usersToSend->isEmpty()) {
            return;
        }

        $alarmData = $this->getAlarmData($id, $subscriber->getLanguage()->getLocale(), $subscriber->getTimezone()->getLocation());
        $level = $alarmData['level'];
        if (in_array($level, ['service', 'ignore'])) {
            return;
        }

        foreach ($usersToSend as $user) {
            /** @var User $user */
            $alarmDataUser = null;
            if (
                $this->checkLevel($user->getReportSettings()->getAlarmsSettings(), $level)
            ) {
                $alarmDataUser = $this->getAlarmData($id, $user->getLanguage()->getLocale(), $user->getTimezone()->getLocation());
                $this->sendEmail($user, $carwash, $alarmDataUser);
            }

            if (
                $this->checkLevel($user->getReportSettings()->getAlarmsMobileSettings(), $level) &&
                !$user->getMobileTokens()->isEmpty()
            ) {
                $alarmDataUser = $alarmDataUser ?? $this->getAlarmData($id, $user->getLanguage()->getLocale(), $user->getTimezone()->getLocation());
                $this->sendMobile($user, $carwash, $alarmDataUser);
            }
        }
    }


    public function sendEmail(User $user, Carwashes $carwash, array $data)
    {
        /** @var Translator $translator */
        $translator = $this->translator;
        $translator->setLocale(
            ($user->getLanguage()->getLocale())
        );

        $time = new \DateTime($data['createTime']);

        $body = $this->templating->render(
            'Reports/reportAlarmEmail.html.twig',
            [
                'createTime' => $time,
                'occuredAlarm' => $data,
                'carwashLongName' => $carwash->getLongName(),
                'timezone' => $user->getTimezone()->getLocation(),
                'alarmHistoryURI' => AlarmsController::ALARM_HISTORY_URI,
            ]
        );
        try {
            $this->newNotificationLog($carwash, $user, $data, "email");

            $this->sendReportEmailWithoutAttachment(
                $user,
                $translator->trans('reports.email-alarms-title')
                . ' ' . $carwash->getLongName(),
                $body
            );
        } catch (\Throwable $e) {
            $this->loggerRepository->error(
                $carwash->getSubscriber(),
                $user,
                $carwash,
                self::class,
                "Error during email notification: " . $e->getMessage()
            );
            captureException($e);
        }
    }

    private function sendMobile(User $user, Carwashes $carwash, array $data): void
    {
        try {
            $this->newNotificationLog($carwash, $user, $data, "mobile");

            /** @var Translator $translator */
            $translator = $this->translator;
            $translator->setLocale($user->getLanguage()->getLocale());

            $title = $translator->trans('reports.email-alarms-title') . ' ' . $carwash->getName();
            $body = $data['description'];

            $this->mobileNotificationService->sendNotification(
                $user->getId(),
                $title,
                $body,
                [
                    'carwash_serial' => (string)$carwash->getSerialNumber(),
                ]
            );
        } catch (\Throwable $e) {
            $this->loggerRepository->error(
                $carwash->getSubscriber(),
                $user,
                $carwash,
                self::class,
                "Error during mobile notification: " . $e->getMessage()
            );
            captureException($e);
        }
    }

    private function sendReportEmailWithoutAttachment(User $user, $title, $body = '')
    {
        $this->emailService->sendEmail(
            template: 'Email/report.html.twig',
            data: [
                'body' => $body . "<br/>",
            ],
            title: $title,
            emails: [$user->getEmail()],
            language: $user->getLanguage()->getLocale(),
        );
    }

    private function getAlarmData(int $id, string $locale, string $timezone): array
    {
        $alarmData = $this->alarmApiService->getSpecificAlarms(
            $id,
            $locale,
            $timezone
        );

        return [
            'level' => $alarmData["alarmDef"]['level']['name'],
            'createTime' => $alarmData['ct'],
            'description' => $alarmData['text'],
            'docUrl' => $alarmData["alarmDef"]['docUrl'],
            'alarmId' => $alarmData["alarmDef"]['id'],
            'priority' => $alarmData["alarmDef"]['level']['priority']
        ];
    }

    private function checkLevel(?string $userLevel, string $alarmLevel): bool
    {
        if (is_null($userLevel)) {
            return false;
        }

        $userPriority = self::LEVEL_PRIORITY[$userLevel];
        $alarmPriority = self::LEVEL_PRIORITY[$alarmLevel];

        if ($alarmPriority >= $userPriority) {
            return true;
        }
        return false;
    }

    private function newNotificationLog(Carwashes $carwash, User $user, array $data, string $type): void
    {
        $this->loggerRepository->debug(
            $carwash->getSubscriber(),
            $user,
            $carwash,
            self::class,
            "new alarm notification ($type) : {$data['createTime']} {$data['alarmId']} ({$data['level']}) - {$data['description']}"
        );
    }
}

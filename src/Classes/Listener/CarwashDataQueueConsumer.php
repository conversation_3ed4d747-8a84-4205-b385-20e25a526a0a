<?php

namespace App\Classes\Listener;

use Exception;
use PhpAmqpLib\Channel\AMQPChannel;
use PhpAmqpLib\Connection\AMQPStreamConnection;
use Symfony\Component\Console\Output\OutputInterface;

class CarwashDataQueueConsumer
{
    private const CM_QUEUE_PORT = 5672;
    private const CM_QUEUE_USER = "cm";
    private const CM_QUEUE_PASS = "f(6$'bLHmx6$8u*{";
    private const CM_QUEUE_QUEUE = 'monitor_protocol';
    private const CM_QUEUE_IP = '*********';

    private $queue;
    private $channel;
    private $connection;

    /**
     * @throws Exception
     */
    public function shutdown(
        AMQPChannel $channel,
        AMQPStreamConnection $connection
    ) {
        $channel->close();
        $connection->close();
    }

    private function initQueueConnection()
    {
        if (!is_null($this->queue)) {
            return;
        }

        $this->queue = 1;

        $connection = new AMQPStreamConnection(
            self::CM_QUEUE_IP,
            self::CM_QUEUE_PORT,
            self::CM_QUEUE_USER,
            self::CM_QUEUE_PASS
        );

        $channel = $connection->channel();
        $channel->queue_declare(
            self::CM_QUEUE_QUEUE,
            false,
            true,
            false,
            false
        );

        $this->channel = $channel;
        $this->connection = $connection;

        register_shutdown_function(
            [$this, 'shutdown'],
            $this->channel,
            $this->connection
        );
    }

    /**
     * @throws Exception|\Doctrine\DBAL\Driver\Exception
     */
    public function processQueueMessages(
        Listener $listener,
        OutputInterface $output,
        ?string $json
    ): int {
        if ($json === null) {
            $output->writeln("Probuje polaczyc sie z kolejka...");
            $this->initQueueConnection();
            $output->writeln("Udane polaczenie z kolejka");
        }

        $startTime = microtime(true);
        $processedPackageCounter = 0;

        while ($msg = $this->getMessage($json)) {
            $processedPackageCounter++;
            $sn = $msg['data'][0]['sn'];
            $output->writeln("Przetwarzam dane z myjni: " . $sn);

            $listener->insertData($msg['data']);

            $executionTime = microtime(true) - $startTime;
            if ($executionTime > 10 * 60 || $processedPackageCounter > 1000) {
                /* exit after some time or processed package number */
                break;
            }
        }

        return $processedPackageCounter;
    }

    /**
     * Get message from queue
     *
     *
     */
    private function getMessage(?string $json = null): ?array
    {
        if ($json !== null) {
            return unserialize($json);
        }

        while ($msg = $this->channel->basic_get(self::CM_QUEUE_QUEUE, true)) {
            if (!isset($msg->body)) {
                continue;
            }

            return unserialize($msg->body);
        }

        return null;
    }
}

<?php

namespace App\Classes\Listener;

use App\Entity\Carwashes;
use App\Repository\CarwashesRepository;
use Doctrine\ORM\EntityManagerInterface;
use Exception;

use function Sentry\captureMessage;

class Listener
{
    public function __construct(
        private EntityManagerInterface $em,
        private CarwashDedicatedListenerQueue $carwashDedicatedListenerQueue,
        private CarwashesRepository $carwashesRepository
    ) {
    }

    /**
     * @throws Exception
     */
    public function insertData(array $data)
    {
        foreach ($data as $inputData) {
            $serialNumber = $inputData['sn'] ?? null;
            $carwash = $this->carwashesRepository->findOneBySerialNumber($serialNumber);
            if (is_null($carwash)) {
                captureMessage("Nie znaleziono myjni $serialNumber, podczas analizy paramertu z kolejki");
                return;
            }
            foreach ($inputData['sensors'] as $sensorData) {
                if (isset($sensorData['p'])) {
                    $this->parseCarwashData($carwash, $sensorData['p']);
                }
            }
        }

        $this->em->flush();
    }

    private function parseCarwashData(Carwashes $carwash, $parameters)
    {
        foreach ($parameters as $parameter) {
            if (
                isset($parameter['p_id'])
                && $parameter['p_id'] > 0
            ) {
                $this->carwashDedicatedListenerQueue->executeActions(
                    $carwash,
                    $parameter
                );
            }
        }
    }
}

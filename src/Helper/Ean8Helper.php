<?php

namespace App\Helper;

class Ean8Helper
{
    /**
     * calculate check sum for EAN8.
     */
    public static function checkSumEAN8($in)
    {
        $digits = (string) $in;
        $sum = 0;

        if (strlen($digits) < 7) {
            return -1;
        }

        for ($i = 0; $i < 7; $i += 1) {
            if ($i % 2) {
                $sum += (int)$digits[$i] * 1;
            } else {
                $sum += (int)$digits[$i] * 3;
            }
        }

        return (10 - ($sum % 10)) % 10;
    }
}

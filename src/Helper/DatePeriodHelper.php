<?php

namespace App\Helper;

use Carbon\Carbon;
use DateTime;
use DateInterval;

class DatePeriodHelper
{
    public static function getDateFromPeriodString(string $period): array
    {
        switch ($period) {
            case 'now':
                $startDateObject = new DateTime('now');
                return [
                    'endDate' => $startDateObject->setTime(23, 59, 59)->format("Y-m-d H:i:s"),
                    'startDate' => $startDateObject->setTime(0, 0, 0)->format("Y-m-d H:i:s"),
                ];
            case 'yesterday':
                $startDateObject = (new DateTime('now'))->sub(new DateInterval('P1D'));
                return [
                    'endDate' => $startDateObject->setTime(23, 59, 59)->format("Y-m-d H:i:s"),
                    'startDate' => $startDateObject->setTime(0, 0, 0)->format("Y-m-d H:i:s"),
                ];
            case 'previous_month':
                $startDateObject = Carbon::now()->subMonthsNoOverflow(1);
                return [
                    'endDate' => $startDateObject->modify('last day of this month')->setTime(23, 59, 59)->format("Y-m-d H:i:s"),
                    'startDate' => $startDateObject->setTime(0, 0, 0)->format("Y-m-01 H:i:s"),
                ];
            case 'since_month_start':
                $endDate = new DateTime('now');
                $startDateObject = clone($endDate);
                $startDateObject->setDate(
                    (int)$endDate->format("Y"),
                    (int)$endDate->format("m"),
                    1
                );
                return [
                    'endDate' => $endDate->setTime(23, 59, 59)->format("Y-m-d H:i:s"),
                    'startDate' => $startDateObject->setTime(0, 0, 0)->format("Y-m-d H:i:s"),
                ];
        }

        if (!preg_match("/^P(\d+)D$/", $period)) {
            $startDateObject = new DateTime('now');
            return [
                'endDate' => $startDateObject->setTime(23, 59, 59)->format("Y-m-d H:i:s"),
                'startDate' => $startDateObject->setTime(0, 0, 0)->format("Y-m-d H:i:s"),
            ];
        }

        $startDateObject = ((new DateTime())->sub(new DateInterval($period)))->setTime(0, 0, 0)
            ->add(new DateInterval('P1D'));
        $endDate = (new DateTime())->setTime(23, 59, 59)->format("Y-m-d H:i:s");

        return [
            'startDate' => $startDateObject->format("Y-m-d H:i:s"),
            'endDate' => $endDate,
        ];
    }
}

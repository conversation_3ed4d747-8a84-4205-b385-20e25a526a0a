<?php

namespace App\Command;

use App\Entity\Subscribers;
use App\Service\Email\EmailService;
use App\Service\InvoiceApi\DataProviders\InvoiceConfirmedListProvider;
use App\Service\InvoiceApi\InvoiceApi;
use Carbon\Carbon;
use Doctrine\ORM\EntityManagerInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Input\InputDefinition;
use Symfony\Component\Console\Output\OutputInterface;
use DateTime;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\Translation\Translator;
use Symfony\Contracts\Translation\TranslatorInterface;

class SelfInvoiceNotificationCommand extends Command
{
    protected static $defaultName = 'bp:invoice:self:notificaiton';
    private string $cmApiUrl;

    public function __construct(
        private InvoiceApi $invoiceApi,
        protected LoggerInterface $logger,
        private EntityManagerInterface $em,
        readonly private InvoiceConfirmedListProvider $dataProvider,
        private TranslatorInterface $translator,
        ParameterBagInterface $parameterBag,
        private EmailService $emailService,
    ) {
        $this->cmApiUrl = $parameterBag->get('web_url');
        parent::__construct();
    }

    protected function configure()
    {
        $this
            ->setName('bp:invoice:self:notificaiton')
            ->setDescription('Generate email notification for BE LOYAL')
            ->setHelp('This command send self-invoices notification to confirm BeLoyal invoice')
            ->setDefinition(
                new InputDefinition([
                    new InputOption('checkDate', 'cd', InputOption::VALUE_OPTIONAL),
                    new InputOption('owner', 'o', InputOption::VALUE_OPTIONAL),
                ])
            );
    }

    /**
     * Send email notification when subscription is going to an end.
     * Change subscription to free, when no payment for subscription has recorede.
     * @throws \Exception
     */
    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $checkDate = $input->getOption('checkDate');

        if (is_null($checkDate)) {
            $checkDate = new DateTime();
        }

        $output->writeln(
            '<comment>Checking self invoice confirmations at ' . $checkDate->format(
                'Y-m-d H:i:s'
            ) . ' ... </comment>'
        );

        $invoicesToNotify = $this->dataProvider->getInvoiceConfirmedList(
            InvoiceConfirmedListProvider::INVOICE_TYPE_SELF_INVOICE,
        );

        $languageLocale = 'pl';
        foreach ($invoicesToNotify as $invoiceToNotify) {
            $subscriber = $this->em
                ->getRepository(Subscribers::class)
                ->find($invoiceToNotify['issuer']['externalId']);
            /** @var Translator $translator */
            $translator = $this->translator;
            $translator->setLocale($languageLocale);
            $email = $subscriber->getEmail();

            $filename = str_replace('/', '-', $invoiceToNotify['parsedInvoiceNumber'] . '.pdf');
            $attachments = $this->prepareAttachments($invoiceToNotify['id'], $filename);

            $this->emailService->sendEmail(
                template: 'Email/self_invoice_reminder/mail_content.html.twig',
                data: [
                    'invoiceDate' => Carbon::parse($invoiceToNotify['invoiceDate'])->format('Y-m-d'),
                    'invoiceNumber' => $invoiceToNotify['parsedInvoiceNumber'],
                    'confirmUrl' => "{$this->cmApiUrl}/cm/self_invoice/confirm_dialog/{$invoiceToNotify['uuid']}"
                ],
                title: $this->translator->trans('self-invoice-reminder.email-title', locale: $languageLocale),
                emails: [$email, '<EMAIL>'],
                language: $languageLocale,
                attachments: $attachments,
            );
        }

        return 0;
    }

    private function prepareAttachments(int $invoiceId, string $fileName): array
    {
        $invoiceTmpFilePath = tempnam(sys_get_temp_dir(), 'cmSelfInvoice') . '.pdf';

        $fileStream = $this->invoiceApi->getInvoiceFileStream($invoiceId);

        $invoiceFileResourceTo = fopen($invoiceTmpFilePath, 'w');
        stream_copy_to_stream($fileStream, $invoiceFileResourceTo);
        fclose($fileStream);

        return [$fileName => $invoiceTmpFilePath];
    }
}

<?php

namespace App\Command;

use Symfony\Component\Routing\RouterInterface;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;

class I2mAnalizeApiCommand extends Command
{
    protected static $defaultName = 'i2m:analize:api';
    protected static $defaultDescription = 'Analyze API logs and compare with routing';

    private RouterInterface $router;

    public function __construct(RouterInterface $router)
    {
        parent::__construct();
        $this->router = $router;
    }

    protected function configure(): void
    {
        $this
            ->setDescription(self::$defaultDescription)
            ->addArgument('logs', InputArgument::OPTIONAL, 'Path to the log directory', 'logi')
            ->addOption('option1', null, InputOption::VALUE_NONE, 'Option description');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);
        $logDirectory = $input->getArgument('logs');

        if (!is_dir($logDirectory)) {
            $output->writeln('<error>Specified path is not a directory!</error>');
            return 1;
        }

        $logFiles = array_merge(glob($logDirectory . '/*.log'), glob($logDirectory . '/*.log.1'));
        if (empty($logFiles)) {
            $output->writeln('<error>No log files found in the specified directory.</error>');
            return 1;
        }

        $requestCounts = [];

        foreach ($logFiles as $logFile) {
            $file = fopen($logFile, 'r');
            if ($file === false) {
                $output->writeln("<error>Failed to open log file: $logFile</error>");
                continue;
            }

            while (($line = fgets($file)) !== false) {
                // Step 1: Find the start of the URL (method and path)
                if (preg_match('/"(GET|POST|PUT|PATCH|DELETE)\s([^\s?]+)/', $line, $matches)) {
                    $method = $matches[1];
                    $url = $matches[2];

                    // Step 2: Replace numeric IDs in the URL with {id}
                    $normalizedUrl = preg_replace('/\/\d+/', '/{id}', $url);

                    // Step 3: Remove everything after ? or space
                    $finalUrl = preg_replace('/[\?\s].*/', '', $normalizedUrl);

                    $key = "$method $finalUrl";
                    if (isset($requestCounts[$key])) {
                        $requestCounts[$key]++;
                    } else {
                        $requestCounts[$key] = 1;
                    }
                }
            }

            fclose($file);
        }

        arsort($requestCounts);

        $usedEndpointsFile = 'used_endpoints.txt';
        $unusedEndpointsFile = 'unused_endpoints.txt';

        $output->writeln('<info>Writing used endpoints to file...</info>');
        file_put_contents($usedEndpointsFile, "Used Endpoints:\n");
        foreach ($requestCounts as $url => $count) {
            file_put_contents($usedEndpointsFile, "$url - $count requests\n", FILE_APPEND);
        }

        // Compare with routing
        $output->writeln('<info>Comparing with application routing...</info>');
        $routes = $this->router->getRouteCollection();
        $usedRoutes = array_keys($requestCounts);
        $unusedRoutes = [];

        foreach ($routes as $name => $route) {
            $methods = $route->getMethods();
            $path = $route->getPath();

            // Normalize route paths
            $normalizedPath = preg_replace('/\{[^\/]+\}/', '{id}', $path);
            $normalizedPath = preg_replace('/\/\d+/', '/{id}', $normalizedPath);

            foreach ($methods as $method) {
                $routeKey = "$method $normalizedPath";
                if (!in_array($routeKey, $usedRoutes, true)) {
                    $unusedRoutes[] = $routeKey;
                }
            }
        }

        // Sort unused routes by endpoint only
        usort($unusedRoutes, function ($a, $b) {
            return strcmp(explode(' ', $a)[1], explode(' ', $b)[1]);
        });

        $output->writeln('<info>Writing unused endpoints to file...</info>');
        file_put_contents($unusedEndpointsFile, "Unused Endpoints:\n");
        foreach ($unusedRoutes as $unusedRoute) {
            file_put_contents($unusedEndpointsFile, "$unusedRoute\n", FILE_APPEND);
        }

        $output->writeln("<info>Used endpoints written to: $usedEndpointsFile</info>");
        $output->writeln("<info>Unused endpoints written to: $unusedEndpointsFile</info>");

        return 0;
    }
}

<?php

namespace App\Command;

use App\Entity\ReportFile;
use App\Entity\User;
use App\Reports\v2\FinanceFiscalSummary;
use App\Reports\v2\FinanceProgramsTotal;
use App\Reports\v2\FinanceTurnoverTotal;
use App\Repository\UserRepository;
use App\Service\Report2\ReportService;
use Carbon\Carbon;
use I2m\Reports\Enum\FileExtention;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Twig\Error\Error;

class ReportTestCommand extends Command
{
    private const DATE_FORMAT = 'Y-m-d';

    public function __construct(
        private readonly UserRepository $userRepository,
        private ReportService $reportService,
    ) {
        parent::__construct();
    }

    public function generateReport(array $criterias, User $user, string $type): void
    {
        $reportFile = (new ReportFile())
            ->setType($type)
            ->setCriterias($criterias)
            ->setSubscriber($user->getSubscriber())
            ->setUser($user)
            ->setExt(FileExtention::HTML);

        $data = $this->reportService->getFile($reportFile);
        dump($data);
    }

    protected function configure(): void
    {
        $this->setName('cm:report:test')
            ->addOption(
                'interval',
                null,
                InputOption::VALUE_REQUIRED,
                'Settings interval to send: daily, weekly, monthly',
                'daily'
            );
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $output->writeln('<comment>Invoke reports sending...</comment>');

        //$carwash = $this->carwashesRepository->findOneBySerialNumber(150);
        //$this->invokeNewAlarmInCM->sendNewAlarm($carwash, 27975165);

        $criterias = $this->prepareReportTimeInterval($input->getOption('interval'));
        //$user = $this->userRepository->findOneByEmail('<EMAIL>');
        $user = $this->userRepository->findOneByEmail('<EMAIL>');


        $this->generateReport($criterias, $user, FinanceProgramsTotal::class);
        #$this->generateReport($criterias, $user, FinanceTurnoverTotal::class);
        #$this->generateReport($criterias, $user, FinanceFiscalSummary::class);
        #$this->generateReport($criterias, $user, FinanceProgramsUsage::class);


        return 0;
    }


    private function prepareReportTimeInterval(string $interval): array
    {
        $dateStart = new Carbon('now');
        $dateEnd = new Carbon('now');

        switch ($interval) {
            case 'weekly':
                return [
                    'startDate' => $dateStart->subWeek()->startOfDay()->format(self::DATE_FORMAT),
                    'endDate' => $dateEnd->addDay()->format(self::DATE_FORMAT)
                ];
            case 'monthly':
                return [
                    'startDate' => $dateStart->subMonth()->startOfDay()->format(self::DATE_FORMAT),
                    'endDate' => $dateEnd->addDay()->endOfDay()->format(self::DATE_FORMAT)
                ];
            case 'daily':
            default:
                return [
                    'startDate' => $dateStart->subDay()->startOfDay()->format(self::DATE_FORMAT),
                    'endDate' => $dateEnd->addDay()->endOfDay()->format(self::DATE_FORMAT)
                ];
        }
    }
}

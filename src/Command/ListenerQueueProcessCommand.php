<?php

namespace App\Command;

use App\Classes\Listener\CarwashDataQueueConsumer;
use App\Classes\Listener\Listener;
use DateTime;
use Doctrine\DBAL\Driver\Exception;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Command\LockableTrait;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class ListenerQueueProcessCommand extends Command
{
    use LockableTrait;


    public function __construct(
        private Listener $listener,
        private CarwashDataQueueConsumer $cmDataQueue
    ) {

        parent::__construct();
    }

    protected function configure(): void
    {
        $this->setName('bkf:listener:queue:process')
            ->setDescription('Consume carwash data from listener queue')
            ->addArgument('json', InputArgument::OPTIONAL);
    }

    /**
     *
     * @throws Exception
     */
    protected function execute(
        InputInterface $input,
        OutputInterface $output
    ): int {
        $commandStartDate = new DateTime();
        $output->writeln(
            $commandStartDate->format('Y-m-d H:i:s') . " Start komendy sciagajacej dane z kolejki danych CM"
        );

        if (!$this->lock()) {
            return 0;
        }

        $packagesProcessed = $this->cmDataQueue->processQueueMessages(
            $this->listener,
            $output,
            $input->getArgument('json')
        );
        $output->writeln('Ilosc przetworzonych wiadomosci:  ' . $packagesProcessed);

        return 0;
    }
}

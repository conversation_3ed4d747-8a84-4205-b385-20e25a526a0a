<?php

namespace App\Command;

use App\Service\Connectors\BkfApi\Model\Device;
use App\Service\BkfSynchroDeviceResolverService;
use App\Service\Connectors\BkfApi\BkfApiService;
use Doctrine\ORM\EntityManagerInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Command\LockableTrait;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use Symfony\Component\Serializer\Exception\ExceptionInterface;

use function Sentry\captureMessage;

class BkfSynchroDevicesCommand extends Command
{
    use LockableTrait;

    /** @var string|null $defaultName */
    protected static $defaultName = 'bkf:synchro-devices';
    private BkfApiService $bkfApi;
    private EntityManagerInterface $em;
    private LoggerInterface $logger;
    private BkfSynchroDeviceResolverService $bkfSynchroResolver;

    public function __construct(
        BkfApiService $bkfApi,
        EntityManagerInterface $em,
        LoggerInterface $logger,
        BkfSynchroDeviceResolverService $bkfSynchroResolver,
    ) {
        $this->bkfApi = $bkfApi;
        $this->em = $em;
        $this->logger = $logger;
        $this->bkfSynchroResolver = $bkfSynchroResolver;

        parent::__construct();
    }

    protected function configure()
    {
        $this
            ->setName(self::$defaultName)
            ->setDescription('Synchro devices bkf_devices - carwash-socket');
    }

    /**
     * @throws ExceptionInterface
     */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);
        $qty = 0;

        if (!$this->lock($this->getName() . "-" . getenv('APP_ENV'))) {
            captureMessage("Nie udało się uruchomić synchronizacji bazy myjni");
            $this->logger->warning('Trwa juz inna komenda: ' . $this->getName());
            return 0;
        }
        $this->logger->notice(self::$defaultName . " - rozpoczynam pobieranie myjni");

        $page = 0;

        do {
            $devices = $this->getDevices(++$page);

            foreach ($devices as $device) {
                $qty++;
                /** @var Device $device*/
                $this->logger->info("Myjnia {$device->getSerialNumber()}, właściciel {$device->getNetwork()?->getCmEmailAccount()}");
                $this->bkfSynchroResolver->resolveCarwashAndOwner($device);
                $this->em->flush();
                $this->em->clear();
            }
        } while (count($devices));


        $this->logger->notice(
            "Razem myjni $qty"
        );

        return 0;
    }

    protected function getDevices(int $page): array
    {
        $devices = $this->bkfApi->getDevicesByGroup(['MYJNIA','PORTAL'], $page, 100);
        $this->logger->notice(self::$defaultName . " - pobranych myjni: " . count($devices) . "/$page") ;
        return $devices;
    }
}

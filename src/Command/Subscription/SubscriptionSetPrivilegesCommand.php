<?php

namespace App\Command\Subscription;

use App\Entity\Carwashes;
use App\Entity\Enum\SubscriptionCarwashType;
use App\Entity\Enum\SubscriptionPayer;
use App\Entity\Enum\SubscriptionStatus;
use I2m\StandardTypes\Enum\CMSubscription;
use App\Entity\Subscribers;
use App\Entity\Subscription\OwnerSubscriptionPayment;
use App\Repository\SubscribersRepository;
use App\Repository\Subscription\OwnerSubscriptionCarwashRepository;
use App\Repository\Subscription\OwnerSubscriptionPaymentRepository;
use App\Repository\UserRepository;
use App\Service\Subscription\OwnerSubscriptionService;
use DateTime;
use Doctrine\ORM\EntityManagerInterface;
use I2m\StandardTypes\Enum\Currency;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Command\LockableTrait;
use Symfony\Component\Console\Input\InputDefinition;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

use function Sentry\captureMessage;

class SubscriptionSetPrivilegesCommand extends Command
{
    use LockableTrait;

    protected static $defaultName = 'cm:subscriptions:set-privileges';

    public function __construct(
        private OwnerSubscriptionPaymentRepository $osPaymentRepo,
        private OwnerSubscriptionCarwashRepository $osCarwashRepo,
        private SubscribersRepository $subscribersRepository,
        private UserRepository $userRepository,
        private OwnerSubscriptionService $ownerSubscriberService,
        private EntityManagerInterface $em
    ) {

        parent::__construct();
    }

    protected function configure()
    {
        $this->setName('cm:subscriptions:set-privileges')
            ->setDescription('Set cm users privileges based on paid subscriptions')
            ->setHelp('This command changes cm privileges based on paid subscription')
            ->setDefinition(
                new InputDefinition(
                    [
                        new InputOption('checkDate', 'cd', InputOption::VALUE_OPTIONAL),
                    ]
                )
            );
    }

    /**
     * Send email notification when subscription is going to an end.
     * Change subscription to free, when no payment for subscription has recorede.
     *
     *
     * @throws \Exception
     */
    protected function execute(
        InputInterface $input,
        OutputInterface $output
    ) {
        $checkDate = $input->getOption('checkDate');
        $checkDate = (is_null($checkDate)) ? new DateTime() : new DateTime($checkDate);

        if (!$this->lock($this->getName() . "-" . getenv('APP_ENV'))) {
            captureMessage("Nie udało się uruchomić weryfikacji abonamentów");
            return 0;
        }

        $subscribers = $this->subscribersRepository->findAll();
        //$subscribers = $this->subscribersRepository->findBy(["id" => 1477]);


        foreach ($subscribers as $subscriber) {
            /** @var Subscribers $subscriber */

            $output->writeln("subscription for user: {$subscriber->getName()} ({$subscriber->getId()})");
            $lastSubscription = $this->osPaymentRepo->getSubscriptionEnd($subscriber);

            // sprawdzam czy mogę wygenerować lub przedłużyć subskrypcje
            if (
                $subscriber->getCarwashes()->count() &&
                (is_null($lastSubscription) || ($lastSubscription < new DateTime('+2 week')))
            ) {
                $this->autoRenew($subscriber, $lastSubscription, $checkDate, $output);
            }


            $activeSubscription = $this->ownerSubscriberService->getActiveSubscritpion($subscriber, $checkDate);
            if ($activeSubscription) { //
                $this->verifySubscription($activeSubscription, $output, $subscriber, $checkDate);
            }

            $package = $activeSubscription?->getOwnerSubscriptionPackage();
            //$output->writeln("\tset subscription $sName for user: {$owner->getEmail()} ({$owner->getId()})");
            $subscriber->setSubscription($package?->getCode());
            $this->em->flush();
        }
        $output->writeln('Done');

        return 0;
    }

    private function notify(OutputInterface $output, string $message, bool $sentry = null): void
    {
        $output->writeln($message);
        if ($sentry) {
            captureMessage("subscription: " . $message);
        }
    }

    private function autoRenew(
        Subscribers $subscriber,
        ?\DateTimeInterface $lastSubscription,
        DateTime $checkDate,
        OutputInterface $output
    ) {
        $comment = null;
        $startSubscription = $lastSubscription ?? (clone $checkDate)->setTime(0, 0);

        if ($subscriber->isAutoRenew()) { // ale klient ma autoodsiewazanie
            $endSubscription = (new DateTime('last day of next month'))
                ->setTime(23, 59, 59);
            $comment = 'auto renew';
        } else {
            // sprawdzam czy wszystkie myjnie są na gwarancji
            /** @var ?DateTime $warranty */
            $warranty = null;
            foreach ($subscriber->getActiveCarwashes() as $carwash) {
                /** @var Carwashes $carwash */
                $warranty = min($warranty ?? $carwash->getWarrantyVoided(), $carwash->getWarrantyVoided());
            }

            $endSubscription = $warranty ? (DateTime::createFromInterface($warranty))->setTime(23, 59, 59) : null;
            $comment = 'auto warranty';
        }


        if ($endSubscription > $startSubscription) {
            $this->notify(
                $output,
                "\tNew subscription for user: {$subscriber->getName()} to {$endSubscription->format('Y-m-d')}",
                true
            );

            $this->ownerSubscriberService->generate(
                from:             $startSubscription,
                to:               $endSubscription,
                currency:         Currency::PLN,
                CMSubscription: CMSubscription::PREMIUM,
                subscriber:       $subscriber,
                whoAdded:         $this->userRepository->findOneByEmail('<EMAIL>'),
                payerType: SubscriptionPayer::DEALER,
                status:           SubscriptionStatus::PAID,
                comment:          $comment,
                save:             true
            );
        }
    }

    private function verifySubscription(
        ?OwnerSubscriptionPayment $activeSubscription,
        OutputInterface $output,
        Subscribers $subscriber,
        DateTime $checkDate
    ): void {
        // spradzam czy wszystkie myjnie właściciela są w subskrypcji
            $output->writeln("\tchecking subscription {$activeSubscription->getId()} for carwashes");
        foreach ($subscriber->getCarwashes() as $carwash) {
            /** @var Carwashes $carwash */
            $cwSubscription = $this->osCarwashRepo->findBy(
                ['subscription' => $activeSubscription, 'carwash' => $carwash]
            );
            //dump($cwSubscription);
            //$output->writeln("\t\tchecking carwash {$carwash->getSerialNumber()}");
            if (empty($cwSubscription)) {
                // jeśli myjnia jeszcze nie wystartowana wtedy ignoruje, brak subksrypcji
                if (is_null($carwash->getStartDate()) || $carwash->isUnsubscribed()) {
                    continue;
                }

                if (
                    ($carwash->getWarrantyVoided() > $activeSubscription->getEndDate()) || // doszla myjnia gwarancyjne
                    ($carwash->getSubscriber()->isAutoRenew())  // automatycznie dodawane subskrycpje dla wybranych klientów
                ) {
                    $this->notify(
                        $output,
                        "add carwash {$carwash->getSerialNumber()} to subscription {$activeSubscription?->getId()} for owner {$subscriber->getEmail()} ({$subscriber->getId()})",
                        true
                    );

                    $this->osPaymentRepo->createCarwashItem(
                        $carwash,
                        $checkDate,
                        $activeSubscription->getEndDate(),
                        SubscriptionCarwashType::WARRANTY,
                        $activeSubscription,
                        SubscriptionPayer::BKF,
                        true
                    );
                    continue;
                }

                if ($activeSubscription->getId() < 57491) {
                    // od tego momentu dodane zostaly myjnie do subskrypcji
                    continue;
                }

                $this->notify(
                    $output,
                    "subscription for user: {$subscriber->getName()} ({$subscriber->getId()}) - no subsription for carwash {$carwash->getSerialNumber()}",
                    true
                );
            }
        }
    }
}

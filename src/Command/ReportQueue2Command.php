<?php

namespace App\Command;

use App\Repository\ReportFileRepository;
use App\Service\Report2\ReportService;
use Psr\Log\LoggerInterface;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Command\LockableTrait;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

use function Sentry\captureMessage;

#[AsCommand(
    name: 'cm:report:queue2',
    description: 'Add a short description for your command',
)]
class ReportQueue2Command extends Command
{
    use LockableTrait;

    public function __construct(
        private ReportService $reportService,
        private ReportFileRepository $reportFileRepository,
        private LoggerInterface $logger,
    ) {
        parent::__construct();
    }


    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $output->writeln('<comment>Report execution ...</comment>');
        // generuje raporty
        $reports = $this->reportFileRepository->findBy(['status' => 'NEW2']);

        if (!$this->lock($this->getName() . "-" . getenv('APP_ENV'))) {
            if ($reports) {
                captureMessage("Nie udało się uruchomić generowania raportów");
                $this->logger->warning('Trwa juz inna komenda: ' . $this->getName());
            }
            return 0;
        }

        foreach ($reports as $reportFile) {
            $this->reportService->generate($reportFile);
        }

        return Command::SUCCESS;
    }
}

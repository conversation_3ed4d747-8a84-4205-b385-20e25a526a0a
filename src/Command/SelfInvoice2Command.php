<?php

namespace App\Command;

use App\Repository\SubscribersRepository;
use App\Service\InvoiceApi\Exceptions\NoMobilePaymentsToGenerateInvoiceException;
use App\Service\InvoiceApi\SelfInvoice\SelfInvoiceApiGenerator;
use App\Service\LoyalAppStatistics\LoyalAppStatistics;
use DateTime;
use DateTimeZone;
use Doctrine\ORM\EntityManagerInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;

#[AsCommand(
    name: 'bp:invoice:self2',
    description: 'Add a short description for your command',
)]
class SelfInvoice2Command extends Command
{
    public function __construct(
        private LoyalAppStatistics $loyalAppStatistics,
        private SubscribersRepository $subscribersRepository,
        private SelfInvoiceApiGenerator $selfInvoiceApiGenerator
    ) {
        parent::__construct();
    }
    protected function configure(): void
    {
        $this
            ->addArgument('arg1', InputArgument::OPTIONAL, 'Argument description')
            ->addOption('option1', null, InputOption::VALUE_NONE, 'Option description')
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);

        $appName = 'beloyal24.com';
        $owners = json_decode($this->loyalAppStatistics->getOwners($appName), true);

        $from = new DateTime('first day of previous month', new DateTimeZone('Europe/Warsaw'));
        $to = new DateTime('last day of previous month', new DateTimeZone('Europe/Warsaw'));

        $from->setTime(0, 0);
        $to->setTime(23, 59, 59, 999999);

        $from->setTimezone(new DateTimeZone('UTC'));
        $to->setTimezone(new DateTimeZone('UTC'));

        $reciver = $this->subscribersRepository->findOneBy(['ownerBkf' => 43141]); // I2M

        foreach ($owners as $owner) {
            $subscriber = $this->subscribersRepository->findOneBy(['ownerBkf' => $owner['id']]);
            // właściciele testowi
            if (in_array($subscriber->getOwnerBkf(), [16679, 36814])) {
                $io->info("Skip generation for testing owner {$subscriber->getName()}");
                continue;
            }

            $io->info("Start generation selfInvoice for bkf owner {$subscriber->getName()}");



            try {
                $this->selfInvoiceApiGenerator->generateInvoice($reciver, $subscriber, $appName, $from, $to);
            } catch (NoMobilePaymentsToGenerateInvoiceException $e) {
                $io->warning('Self-invoice for owner: ' . $owner['id'] . ' NO PAYMENTS FOUND IN PERIOD.');
            }
            $io->success("End generation selfInvoice for bkf owner {$owner['id']}");
        }

        $io->success('You have a new command! Now make it your own! Pass --help to see your options.');

        return Command::SUCCESS;
    }
}

<?php

namespace App\Command;

use App\Entity\Enum\ReportPeriod;
use I2m\StandardTypes\Enum\CMSubscription;
use App\Repository\ReportConfigRepository;
use App\Service\Report2\ReportService;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Command\LockableTrait;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

use function Sentry\captureMessage;

#[AsCommand(name: 'cm:report:interval3')]
class ReportInterval3Command extends Command
{
    use LockableTrait;

    public function __construct(
        private ReportService $reportService,
        private ReportConfigRepository $reportConfigRepository,
    ) {
        parent::__construct();
    }

    protected function configure(): void
    {
        $this
            ->addOption(
                'period',
                null,
                InputOption::VALUE_REQUIRED,
                'Settings interval to send: daily, weekly, monthly',
                'daily'
            );
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $output->writeln('<comment>Report execution ...</comment>');

        $period = ReportPeriod::from($input->getOption('period'));
        $configs = $this->reportConfigRepository->findByPeriod($period);


        if (!$this->lock($this->getName() . "-" . getenv('APP_ENV'))) {
            captureMessage("Nie udało się uruchomić generowania raportów: {$period->value}");
            return 0;
        }

        $dates = $this->reportService->prepareReportTimeInterval($period);
        $output->writeln("generating for dates: " . json_encode($dates));
        foreach ($configs as $config) {
            $user = $config->getUser();
            $subscriber = $user->getSubscriber();

            if ($subscriber->getSubscription() == CMSubscription::FREE) {
                continue;
            }

            $output->writeln("generate report {$config->getId()} {$config->getTitle()} for user {$user->getEmail()}");
            $this->reportService->generateAsync(
                subscriber: $subscriber,
                reportType: $config->getType(),
                ext:        $config->getExt(),
                criterias:  array_merge($config->getCriterias(), $dates),
                emails:     $config->getEmail(),
                user:       $user,
                info:       "generated for config: {$config->getId()}"
            );
        }

        $output->writeln('Done');

        return Command::SUCCESS;
    }
}

<?php

declare(strict_types=1);

namespace App\Model\Admin;

use Symfony\Component\Validator\Constraints as Assert;

class UserDTO
{
    #[Assert\Email]
    private ?string $email = null;
    private ?string $firstName = null;
    private ?string $lastName = null;
    private ?string $phone = null;
    private ?int $ownerBkf = null;
    #[Assert\PositiveOrZero]
    #[Assert\LessThan(value: 100)]
    private ?int $discount = null;
    #[Assert\NotBlank]
    private ?string $currency = null;
    #[Assert\NotBlank]
    private ?string $language = null;
    #[Assert\NotBlank]
    private ?int $timezone = null;
    private ?string $comment = null;

    public function getEmail(): ?string
    {
        return $this->email;
    }

    public function setEmail(?string $email): UserDTO
    {
        $this->email = $email;
        return $this;
    }

    public function getPhone(): ?string
    {
        return $this->phone;
    }

    public function setPhone(?string $phone): UserDTO
    {
        $this->phone = $phone;
        return $this;
    }

    public function getFirstName(): ?string
    {
        return $this->firstName;
    }

    public function setFirstName(?string $firstName): UserDTO
    {
        $this->firstName = $firstName;
        return $this;
    }

    public function getLastName(): ?string
    {
        return $this->lastName;
    }

    public function setLastName(?string $lastName): UserDTO
    {
        $this->lastName = $lastName;
        return $this;
    }

    public function getOwnerBkf(): ?int
    {
        return $this->ownerBkf;
    }

    public function setOwnerBkf(?int $ownerBkf): UserDTO
    {
        $this->ownerBkf = $ownerBkf;
        return $this;
    }

    public function getDiscount(): ?int
    {
        return $this->discount;
    }

    public function setDiscount(?int $discount): UserDTO
    {
        $this->discount = $discount;
        return $this;
    }

    public function getCurrency(): ?string
    {
        return $this->currency;
    }

    public function setCurrency(?string $currency): UserDTO
    {
        $this->currency = $currency;
        return $this;
    }

    public function getLanguage(): ?string
    {
        return $this->language;
    }

    public function setLanguage(?string $language): UserDTO
    {
        $this->language = $language;
        return $this;
    }

    public function getTimezone(): ?int
    {
        return $this->timezone;
    }

    public function setTimezone(?int $timezone): UserDTO
    {
        $this->timezone = $timezone;
        return $this;
    }

    public function getComment(): ?string
    {
        return $this->comment;
    }

    public function setComment(?string $comment): UserDTO
    {
        $this->comment = $comment;
        return $this;
    }
}

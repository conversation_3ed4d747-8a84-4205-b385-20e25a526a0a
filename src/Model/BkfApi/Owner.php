<?php

namespace App\Model\BkfApi;

class Owner
{
    private ?int $id;
    private ?string $cmEmailAccount;
    private ?string $clientAcronym;
    private ?string $fullNip = null;
    private ?string $country = null;
    private ?string $organizationName = null;

    public function getOrganizationName(): ?string
    {
        return $this->organizationName;
    }

    public function setOrganizationName(?string $organizationName): Owner
    {
        $this->organizationName = $organizationName;
        return $this;
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(?int $id): Owner
    {
        $this->id = $id;
        return $this;
    }

    public function getCmEmailAccount(): ?string
    {

        return $this->cmEmailAccount ? strtolower($this->cmEmailAccount) : null;
    }

    public function setCmEmailAccount(?string $cmEmailAccount): Owner
    {
        $this->cmEmailAccount = $cmEmailAccount;
        return $this;
    }

    public function getClientAcronym(): ?string
    {
        return $this->clientAcronym;
    }

    public function setClientAcronym(?string $clientAcronym): Owner
    {
        $this->clientAcronym = $clientAcronym;
        return $this;
    }

    public function getFullNip(): ?string
    {
        return $this->fullNip;
    }

    public function setFullNip(?string $fullNip): Owner
    {
        // zamieniam    PL664-185-32-19 na PL6641853219
        $this->fullNip = str_replace('-', '', $fullNip);
        ;
        return $this;
    }

    public function getCountry(): ?string
    {
        return $this->country;
    }

    public function setCountry(?string $country): Owner
    {
        $this->country = $country;
        return $this;
    }

    public function companyDataOk(): bool
    {
        return
            !empty($this->getFullNip())
        && !empty($this->getCountry())
        && !empty($this->getOrganizationName())
            ;
    }
}

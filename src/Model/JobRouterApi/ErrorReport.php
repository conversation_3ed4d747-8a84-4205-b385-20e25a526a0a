<?php

namespace App\Model\JobRouterApi;

use Carbon\Carbon;
use App\Entity\User;
use Exception;

class ErrorReport
{
    public const LOYALTY_CARDS = 'loyalty_cards';
    public const FINANCE_DATA = 'finance_data';
    public const CLIENTS_ADD = 'client_add';
    public const SUBSCRIPTION = 'subscirption';
    public const OTHER = 'other';

    public const ERRORTYPES = [
        self::LOYALTY_CARDS => "Problem z kartami lojalnościowymi",
        self::FINANCE_DATA => "Problem z danymi finansowymi",
        self::CLIENTS_ADD => "Problem z dodaniem klienta",
        self::SUBSCRIPTION => "Problem z abonamentem",
        self::OTHER => "Inny problem",
    ];

    private ?int $id;
    private string $subject;
    private string $description;
    private ?Carbon $createdAt;
    private ?Carbon $modifiedAt = null;
    private ?Carbon $closedAt = null;
    private ?int $status;
    private User $user;
    private ?array $files;
    private ?int $carwashSn;

    // TODO: Adjust statuses according to API docs
    public const STATUS_OPEN = 1;
    public const STATUS_CLOSED = 2;

    public function __construct(
        ?int $id,
        string $subject,
        string $content,
        User $user,
        ?Carbon $createdAt = null,
        ?Carbon $modifiedAt = null,
        ?Carbon $closedAt = null,
        ?int $status = self::STATUS_OPEN,
        ?int $carwashSn = null,
        ?array $files = null
    ) {
        $this->id = $id;
        $this->subject = $subject;
        $this->description = $content;
        $this->createdAt = $createdAt;
        $this->modifiedAt = $modifiedAt;
        $this->closedAt = $closedAt;
        $this->status = $status;
        $this->user = $user;
        $this->carwashSn = $carwashSn;
        $this->files = $files;
    }

    // TODO: Remove unused and/or unwanted getters and setters

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(?int $id): void
    {
        $this->id = $id;
    }

    public function getSubject(): string
    {
        return $this->subject;
    }

    public function setSubject(string $subject): void
    {
        $this->subject = $subject;
    }

    public function getDescription(): string
    {
        return $this->description;
    }

    public function setDescription(string $description): void
    {
        $this->description = $description;
    }

    public function getCreatedAt(): ?Carbon
    {
        return $this->createdAt;
    }

    public function setCreatedAt(?Carbon $createdAt): void
    {
        $this->createdAt = $createdAt;
    }

    public function getModifiedAt(): ?Carbon
    {
        return $this->modifiedAt;
    }

    public function setModifiedAt(?Carbon $modifiedAt): void
    {
        $this->modifiedAt = $modifiedAt;
    }

    public function getClosedAt(): ?Carbon
    {
        return $this->closedAt;
    }

    public function setClosedAt(?Carbon $closedAt): void
    {
        $this->closedAt = $closedAt;
    }

    public function getStatus(): ?int
    {
        return $this->status;
    }

    public function setStatus(?int $status): void
    {
        $this->status = $status;
    }

    public function getUser(): User
    {
        return $this->user;
    }

    public function setUser(User $user): void
    {
        $this->user = $user;
    }

    public function toArray(): array
    {
        return [
            "id" => $this->id,
            "subject" => $this->subject,
            "description" => $this->description,
            "createdAt" => $this->createdAt->toDateTimeString(),
            "modifiedAt" => is_null($this->modifiedAt)
                ? null
                : $this->modifiedAt->toDateTimeString(),
            "closedAt" => is_null($this->closedAt)
                ? null
                : $this->closedAt->toDateTimeString(),
            "status" => $this->status,
            "user" => $this->user->getEmail(),
            "carwash" => $this->carwashSn,
            "files" => count($this->files),
        ];
    }

    public function toApiRequestArray(): array
    {
        /** @var User $user */
        $user = $this->getUser();
        $requestArray = [
            'issue_subject' => $this->getSubject(),
            'issue_content' => $this->getDescription(),
            'device_serial_number' => $this->getCarwashSn(),
            'initiator_mail' => $this->getUser()->getEmail(),
            'issue_initiator' => 'CMrobot',
            'initiator_department' => 'CM',
            'owner' => $user->getOwnerBkf(),
        ];

        if (!empty($this->getFiles())) {
            $requestArray['ST_JRHELPDESK_ATTACH'] = $this->getFiles();
        }

        if ($id = $this->getId()) {
            $requestArray['id'] = $id;
        }

        return $requestArray;
    }

    public static function fromArray(array $data): self
    {
        return new self(
            isset($data['id']) ? (int)$data['id'] : null,
            isset($data['subject']) ? (string)$data['subject'] : null,
            (string)$data['description'],
            $data['user'],
            $data['createdAt'] ?? Carbon::now(),
            $data['modifiedAt'] ?? null,
            $data['closedAt'] ?? null,
            $data['status'] ?? null,
            $data['carwash'] ?? null,
            $data['files'] ?? null
        );
    }

    public function getFiles(): ?array
    {
        return $this->files;
    }

    public function getCarwashSn(): ?int
    {
        return $this->carwashSn;
    }
}

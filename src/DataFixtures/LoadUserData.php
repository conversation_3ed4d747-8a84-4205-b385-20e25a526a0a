<?php

namespace App\DataFixtures;

use App\Entity\Languages;
use App\Entity\Subscribers;
use App\Entity\Timezones;
use App\Entity\User;
use DateTime;
use Doctrine\Common\DataFixtures\AbstractFixture;
use Doctrine\Common\DataFixtures\FixtureInterface;
use Doctrine\Common\DataFixtures\OrderedFixtureInterface;
use Doctrine\Persistence\ObjectManager;
use Carbon\Carbon;
use Doctrine\Bundle\FixturesBundle\Fixture;

/**
 * Load user data to table bkfpay_users to enable login to bkfpay for functional testing
 * and other purposes
 */
class LoadUserData extends Fixture implements OrderedFixtureInterface
{
    /**
     * Load data fixtures with the passed EntityManager
     *
     * @param ObjectManager $manager
     */
    public function load($manager): void
    {
        $language = $this->getReference('language-pl', Languages::class);

        $subscriber = $this->getReference('first-user-subscriber', Subscribers::class);
        $users = [];

        $timezone = $this->getReference('timezone', Timezones::class);

        $users[] = (new User())
            ->setFirstname('Mariusz')
            ->setLastname('Andrysiewicz')
            ->setEmail('<EMAIL>')
            ->setSubscriber($subscriber)
            ->setRoles(['ROLE_CM'])
            ->setStatus('a')

            ->setLanguage($language)
            ->setTimezone($timezone);

        $users[] = (new User())
            ->setFirstname('Mariusz')
            ->setLastname('Andrysiewicz')
            ->setEmail('<EMAIL>')
            ->setSubscriber($subscriber)
            ->setRoles(['ROLE_CM'])
            ->setStatus('a')
            ->setLanguage($language)
            ->setTimezone($timezone);

        $users[] = (new User())
            ->setFirstname('Mariusz')
            ->setLastname('Andrysiewicz')
            ->setEmail('<EMAIL>')
            ->setSubscriber($subscriber)
            ->setRoles(['ROLE_CM'])
            ->setStatus('a')
            ->setLanguage($language)
            ->setTimezone($timezone);

        // na potrzeby obliczenia subskrypcji
        $users[] = (new User())
            ->setFirstname('Piotr')
            ->setLastname('Andrzejewski')
            ->setEmail('<EMAIL>')
            ->setRoles(['ROLE_CM', 'ROLE_CM_ADMINISTRATION'])
            ->setStatus('a')
            ->setLanguage($language)
            ->setTimezone($timezone)
            ->setIsAdmin(true)
        ;

        $users[] = (new User())
            ->setFirstname('support')
            ->setLastname('i2m')
            ->setEmail('<EMAIL>')
            ->setRoles(['ROLE_CM', 'ROLE_CM_ADMINISTRATION','ROLE_SUPERADMIN'])
            ->setStatus('a')
            ->setLanguage($language)
            ->setTimezone($timezone)
            ->setIsAdmin(true)
        ;

        foreach ($users as $item) {
            $manager->persist($item);
        }
        $manager->flush();
    }

    /**
     * Get the order of this fixture
     *
     * @return integer
     */
    public function getOrder(): int
    {
        return 4;
    }
}

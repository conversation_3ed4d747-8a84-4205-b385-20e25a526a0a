<?php

namespace App\DataFixtures;

use App\Entity\VatTax;
use Doctrine\Bundle\FixturesBundle\Fixture;
use Doctrine\Persistence\ObjectManager;

class LoadVatTaxData extends Fixture
{
    public function load(ObjectManager $manager): void
    {
        // $product = new Product();
        // $manager->persist($product);

        $vat[] = (new VatTax())
            ->setTaxKey('23')
            ->setTaxValue(23)
        ;
        $vat[] = (new VatTax())
            ->setTaxKey('NP')
            ->setTaxValue(0)
        ;

        foreach ($vat as $item) {
            $manager->persist($item);
        }

        $manager->flush();
    }
}

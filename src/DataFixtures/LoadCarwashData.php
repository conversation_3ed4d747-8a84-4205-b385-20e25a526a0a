<?php

namespace App\DataFixtures;

use App\Entity\Carwashes;
use App\Entity\Subscribers;
use App\Entity\User;
use Doctrine\Bundle\FixturesBundle\Fixture;
use Doctrine\Common\DataFixtures\OrderedFixtureInterface;
use Doctrine\Persistence\ObjectManager;
use Carbon\Carbon;

class LoadCarwashData extends Fixture implements OrderedFixtureInterface
{
    public function load(ObjectManager $manager): void
    {
        $user = $this->getReference('first-user', User::class);
        $subscriber = $this->getReference('first-user-subscriber', Subscribers::class);
        $carwash1 = new Carwashes();
        $carwash1->setStatus('a');
        $carwash1->setSerialNumber('10001');
        $carwash1->setStartDate(Carbon::now()->modify("-12 months"));
        $carwash1->setWarrantyVoided(Carbon::now()->modify("+36 months"));
        $carwash1->setSubscriber($subscriber);
        $carwash1->setProduct("Myjnia");
        $manager->persist($carwash1);
        $manager->flush();


        // myjnia calkowicie po gwarancji
        $carwash2 = new Carwashes();
        $carwash2->setStatus('a');
        $carwash2->setSerialNumber('10002');
        $carwash2->setStartDate(Carbon::now()->modify("-12 months"));
        $carwash2->setWarrantyVoided(Carbon::yesterday());
        $carwash2->setProduct("Myjnia");
        $carwash2->setSubscriber($subscriber);
        $manager->persist($carwash2);
        $manager->flush();

        // myjnia czesciowo po gwarancji w tym roku

        $carwash3 = new Carwashes();
        $carwash3->setStatus('a');
        $carwash3->setSerialNumber('10003');
        $carwash3->setStartDate(Carbon::now()->modify("-12 months"));
        $carwash3->setWarrantyVoided(Carbon::now()->modify("+6 months"));
        $carwash3->setSubscriber($subscriber);
        $carwash3->setProduct("Myjnia");
        $manager->persist($carwash3);
        $manager->flush();


        $user->setAssignedCarwashes([$carwash1->getId()]);
        $user->setSubscriber($this->getReference('first-user-subscriber', Subscribers::class));

        $manager->persist($user);
        $manager->flush();


        // inny właścicicel
        $subscriber2 = $this->getReference('cm-user-2-subscriber', Subscribers::class);
        $carwash4 = new Carwashes();
        $carwash4->setStatus('a');
        $carwash4->setSerialNumber('10021');
        $carwash4->setStartDate(Carbon::now()->modify("-12 months"));
        $carwash4->setWarrantyVoided(Carbon::yesterday());
        $carwash4->setProduct("Myjnia");
        $carwash4->setSubscriber($subscriber2);
        $manager->persist($carwash4);
        $manager->flush();

        $carwash5 = new Carwashes();
        $carwash5->setStatus('a');
        $carwash5->setSerialNumber('150');
        $carwash5->setStartDate((new \DateTime())->modify("-12 month"));
        $carwash5->setWarrantyVoided((new \DateTime())->modify("+12 month"));
        $carwash5->setProduct("Myjnia");
        $carwash5->setSubscriber($subscriber2);
        $manager->persist($carwash5);
        $manager->flush();

        $carwash6 = new Carwashes();
        $carwash6->setStatus('a');
        $carwash6->setSerialNumber('144');
        $carwash6->setStartDate(Carbon::now()->modify("-12 months"));
        $carwash6->setWarrantyVoided(Carbon::now()->modify("+12 months"));
        $carwash6->setProduct("Myjnia");
        $carwash6->setSubscriber($subscriber2);
        $manager->persist($carwash6);
        $manager->flush();
    }

    public function getOrder(): int
    {
        return 3;
    }
}

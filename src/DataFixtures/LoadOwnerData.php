<?php

namespace App\DataFixtures;

use App\Entity\Currency;
use I2m\StandardTypes\Enum\CMSubscription;
use App\Entity\Languages;
use App\Entity\Subscribers;
use App\Entity\Timezones;
use App\Entity\User;
use Doctrine\Bundle\FixturesBundle\Fixture;
use Doctrine\Common\DataFixtures\OrderedFixtureInterface;
use Doctrine\Persistence\ObjectManager;
use I2m\StandardTypes\Enum\Country;

class LoadOwnerData extends Fixture implements OrderedFixtureInterface
{
    public function load(ObjectManager $manager): void
    {
        $timezone = $this->getReference('timezone', Timezones::class);


        // owner z polski
        $user = new User();
        $user->setEmail('<EMAIL>');
        $user->setRoles(['ROLE_ADMIN', 'ROLE_CM_OWNER', 'ROLE_CM', 'ROLE_SUBSCRIPTION_PREMIUM','ROLE_CM_FINANCE']);
        $user->setStatus('a');
        $user->setLanguage($this->getReference('language-pl', Languages::class));
        $user->setTimezone($timezone);


        $subscriber = new Subscribers();
        $subscriber
            ->setTaxNumber('888-11-11-11')
            ->setName('Subscriber ' . $user->getEmail())
            ->setCountry(Country::PL)
            ->setCurrency($this->getReference('currency-pln', Currency::class))
            ->setLanguage($this->getReference('language-pl', Languages::class))
            ->setTimezone($timezone)
            ->setOwnerBkf(1)
        ;
        $user->setSubscriber($subscriber);

        $manager->persist($subscriber);
        $manager->persist($user);
        $this->addReference('first-user', $user);
        $this->addReference('first-user-subscriber', $subscriber);

        //
        $user2 = (new User())
            ->setFirstname('Marek')
            ->setLastname('Kowalski')
            ->setEmail('<EMAIL>')
            ->setRoles(['ROLE_ADMIN', 'ROLE_CM_OWNER', 'ROLE_CM'])
            ->setStatus('a')
            ->setLanguage($this->getReference('language-pl', Languages::class))
            ->setTimezone($timezone)
        ;
        $subscriber2 = (new Subscribers())

                ->setTaxNumber('888-11-11-12')
                ->setName('Subscriber ' . $user2->getEmail())
                ->setCountry(Country::SK)
                ->setCurrency($this->getReference('currency-eur', Currency::class))
                ->setOwnerBkf(36814)
                ->setSubscription(CMSubscription::PREMIUM)
                ->setLanguage($this->getReference('language-pl', Languages::class))
                ->setTimezone($timezone)
            ;

        $manager->persist($subscriber2);
        $user2->setSubscriber($subscriber2);
        $manager->persist($user2);
        $this->addReference('cm-user-2', $user2);
        $this->addReference('cm-user-2-subscriber', $subscriber2);

        //
        $user3 = new User();
        $user3
            ->setFirstname('Mariusz')
            ->setLastname('Andrysiewicz')
            ->setEmail('<EMAIL>')
            ->setRoles(['ROLE_ADMIN', 'ROLE_CM_OWNER', 'ROLE_CM', 'ROLE_CM_LOYAL_APP_WASHSTOP'])
            ->setStatus('a')
            ->setLanguage($this->getReference('language-pl', Languages::class))
            ->setTimezone($timezone);
        $this->addReference('cm-user-3', $user3);
        $manager->persist($user3);

        //
        $user4 = new User();
        $user4
            ->setFirstname('Mariusz')
            ->setLastname('Andrysiewicz')
            ->setEmail('<EMAIL>')
            ->setRoles(['ROLE_ADMIN', 'ROLE_CM_OWNER', 'ROLE_CM'])
            ->setStatus('a')
            ->setLanguage($this->getReference('language-pl', Languages::class))
            ->setTimezone($timezone);
        $this->addReference('cm-user-4', $user4);
        $manager->persist($user4);

        $manager->flush();
    }

    /**
     * Get the order of this fixture
     *
     * @return integer
     */
    public function getOrder(): int
    {
        return 2;
    }
}

<?php

namespace App\DataFixtures;

use App\Entity\Subscription\ExternalPayment;
use DateTime;
use Doctrine\Bundle\FixturesBundle\Fixture;
use Doctrine\Persistence\ObjectManager;
use I2m\Payment\Enum\Status;
use I2m\StandardTypes\Enum\Currency;

/**
 * Load external payment data to table external_payments for functional testing
 */
class LoadExternalPaymentData extends Fixture
{
    public function load(ObjectManager $manager): void
    {
        $externalPayment = (new ExternalPayment())
            ->setInitiatedTimestamp(new DateTime())
            ->setStatus(Status::INITIATED)
            ->setValue((string)1.01)
            ->setCurrency(Currency::PLN)
            ->setAdditionalData('{"error":"0","redirect":"...trnRequest\/ABCDEF"}');

        $manager->persist($externalPayment);
        $manager->flush();
    }
}

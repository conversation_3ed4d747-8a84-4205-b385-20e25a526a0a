<?php

namespace App\DataFixtures;

use App\Entity\Timezones;
use Doctrine\Common\DataFixtures\AbstractFixture;
use Doctrine\Common\DataFixtures\FixtureInterface;
use Doctrine\Persistence\ObjectManager;
use Doctrine\Bundle\FixturesBundle\Fixture;

class LoadTimezonesData extends Fixture
{
    public function load(ObjectManager $manager): void
    {
        $timezone = new Timezones();
        $timezone->setStatus('a');
        $timezone->setLocation('Europe/Warsaw');
        $timezone->setGmt('UTC+01:00');
        $timezone->setTimeOffset(2);
        $this->addReference('timezone', $timezone);
        $manager->persist($timezone);

        $timezone = new Timezones();
        $timezone->setStatus('a');
        $timezone->setLocation('Europe/London');
        $timezone->setGmt('UTC');
        $timezone->setTimeOffset(1);

        $manager->persist($timezone);

        $manager->flush();
    }

    public function getOrder(): int
    {
        return 1;
    }
}

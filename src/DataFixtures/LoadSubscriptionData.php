<?php

namespace App\DataFixtures;

use I2m\StandardTypes\Enum\CMSubscription;
use App\Entity\Subscription\OwnerSubscriptionPackages;
use Doctrine\Bundle\FixturesBundle\Fixture;
use Doctrine\Common\DataFixtures\OrderedFixtureInterface;
use Doctrine\Persistence\ObjectManager;
use I2m\StandardTypes\Enum\Currency;

class LoadSubscriptionData extends Fixture implements OrderedFixtureInterface
{
    public function load(ObjectManager $manager): void
    {
        // $product = new Product();
        // $manager->persist($product);

        $package = [];

        // free pln
        $package[] = (new OwnerSubscriptionPackages())
            ->setMonthsLength(1)
            ->setCurrency(Currency::PLN)
            ->setValue(0)
            ->setCode(CMSubscription::FREE);

        $package[] = (new OwnerSubscriptionPackages())
            ->setMonthsLength(3)
            ->setCurrency(Currency::PLN)
            ->setValue(0)
            ->setCode(CMSubscription::FREE);

        $package[] = (new OwnerSubscriptionPackages())
            ->setMonthsLength(6)
            ->setCurrency(Currency::PLN)
            ->setValue(0)
            ->setCode(CMSubscription::FREE);

        $package[] = (new OwnerSubscriptionPackages())
            ->setMonthsLength(12)
            ->setCurrency(Currency::PLN)
            ->setValue(0)
            ->setCode(CMSubscription::FREE);


        // basic pln
        $package[] = (new OwnerSubscriptionPackages())
            ->setMonthsLength(1)
            ->setCurrency(Currency::PLN)
            ->setCode(CMSubscription::BASIC)
            ->setValue(119);


        $package[] = (new OwnerSubscriptionPackages())
            ->setMonthsLength(3)
            ->setCurrency(Currency::PLN)
            ->setCode(CMSubscription::BASIC)
            ->setValue(114);

        $package[] = (new OwnerSubscriptionPackages())
            ->setMonthsLength(6)
            ->setCurrency(Currency::PLN)
            ->setCode(CMSubscription::BASIC)
            ->setValue(109);

        $package[] = (new OwnerSubscriptionPackages())
            ->setMonthsLength(12)
            ->setCurrency(Currency::PLN)
            ->setCode(CMSubscription::BASIC)
            ->setValue(104);

        // premium pln
        $package[] = (new OwnerSubscriptionPackages())
            ->setMonthsLength(1)
            ->setCurrency(Currency::PLN)
            ->setCode(CMSubscription::PREMIUM)
            ->setValue(169);


        $package[] = (new OwnerSubscriptionPackages())
            ->setMonthsLength(3)
            ->setCurrency(Currency::PLN)
            ->setCode(CMSubscription::PREMIUM)
            ->setValue(164);

        $package[] = (new OwnerSubscriptionPackages())
            ->setMonthsLength(6)
            ->setCurrency(Currency::PLN)
            ->setCode(CMSubscription::PREMIUM)
            ->setValue(159);

        $package[] = (new OwnerSubscriptionPackages())
            ->setMonthsLength(12)
            ->setCurrency(Currency::PLN)
            ->setCode(CMSubscription::PREMIUM)
            ->setValue(154);


        // free eur
        $package[] = (new OwnerSubscriptionPackages())
            ->setMonthsLength(1)
            ->setCurrency(Currency::EUR)
            ->setValue(0)
            ->setCode(CMSubscription::FREE);

        $package[] = (new OwnerSubscriptionPackages())
            ->setMonthsLength(3)
            ->setCurrency(Currency::EUR)
            ->setValue(0)
            ->setCode(CMSubscription::FREE);

        $package[] = (new OwnerSubscriptionPackages())
            ->setMonthsLength(6)
            ->setCurrency(Currency::EUR)
            ->setValue(0)
            ->setCode(CMSubscription::FREE);

        $package[] = (new OwnerSubscriptionPackages())
            ->setMonthsLength(12)
            ->setCurrency(Currency::EUR)
            ->setValue(0)
            ->setCode(CMSubscription::FREE);


        // basic eur
        $package[] = (new OwnerSubscriptionPackages())
            ->setMonthsLength(1)
            ->setCurrency(Currency::EUR)
            ->setCode(CMSubscription::BASIC)
            ->setValue(25);


        $package[] = (new OwnerSubscriptionPackages())
            ->setMonthsLength(3)
            ->setCurrency(Currency::EUR)
            ->setCode(CMSubscription::BASIC)
            ->setValue(24);

        $package[] = (new OwnerSubscriptionPackages())
            ->setMonthsLength(6)
            ->setCurrency(Currency::EUR)
            ->setCode(CMSubscription::BASIC)
            ->setValue(22);

        $package[] = (new OwnerSubscriptionPackages())
            ->setMonthsLength(12)
            ->setCurrency(Currency::EUR)
            ->setCode(CMSubscription::BASIC)
            ->setValue(20);

        // premium eur
        $package[] = (new OwnerSubscriptionPackages())
            ->setMonthsLength(1)
            ->setCurrency(Currency::EUR)
            ->setCode(CMSubscription::PREMIUM)
            ->setValue(39);


        $package[] = (new OwnerSubscriptionPackages())
            ->setMonthsLength(3)
            ->setCurrency(Currency::EUR)
            ->setCode(CMSubscription::PREMIUM)
            ->setValue(37);

        $package[] = (new OwnerSubscriptionPackages())
            ->setMonthsLength(6)
            ->setCurrency(Currency::EUR)
            ->setCode(CMSubscription::PREMIUM)
            ->setValue(35);

        $package[] = (new OwnerSubscriptionPackages())
            ->setMonthsLength(12)
            ->setCurrency(Currency::EUR)
            ->setCode(CMSubscription::PREMIUM)
            ->setValue(32);

        foreach ($package as $pack) {
            $manager->persist($pack);
        }

        $manager->flush();
    }

    public function getOrder(): int
    {
        return 2;
    }
}

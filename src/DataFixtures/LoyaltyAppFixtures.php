<?php

namespace App\DataFixtures;

use App\Entity\LoyaltyApp;
use App\Entity\User;
use Doctrine\Bundle\FixturesBundle\Fixture;
use Doctrine\Common\DataFixtures\OrderedFixtureInterface;
use Doctrine\Persistence\ObjectManager;

class LoyaltyAppFixtures extends Fixture implements OrderedFixtureInterface
{
    public function load(ObjectManager $manager): void
    {
        // $product = new Product();
        // $manager->persist($product);

        $loyaltyapp[] = (new LoyaltyApp())
            ->setName('BE_LOYAL')
            ->setToken('869d5b7373393d0b48d14da554e68bb17e24c679')
            ->setUrl('https://api-beloyal.i2m.pl')
            ->setRole('ROLE_CM_LOYAL_APP_BE_LOYAL');

        $loyaltyapp[] = (new LoyaltyApp())
            ->setName('WASHSTOP')
            ->setToken('DcjjeYbG66t7n3iK5zJpFwkv1IrCrpGEixmwMOqZlozFQ04NgSWGFWmVEEmmYXor')
            ->setUrl('https://api-washstop.i2m.pl')
            ->setRole('ROLE_CM_LOYAL_APP_WASHSTOP');

        foreach ($loyaltyapp as $app) {
            $manager->persist($app);
        }

        /** @var User $user */
        $user = $this->getReference('cm-user-2', User::class);
        $user->setLoyaltyApp([$loyaltyapp[0]->getId()]);

        $user = $this->getReference('cm-user-3', User::class);
        $user->setLoyaltyApp([$loyaltyapp[1]->getId()]);

        $manager->flush();
    }

    public function getOrder(): int
    {
        return 4;
    }
}

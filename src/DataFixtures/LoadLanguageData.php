<?php

namespace App\DataFixtures;

use App\Entity\Languages;
use Doctrine\Persistence\ObjectManager;
use Doctrine\Bundle\FixturesBundle\Fixture;

class LoadLanguageData extends Fixture
{
    /**
     * Load data fixtures with the passed EntityManager
     *
     * @param ObjectManager $manager
     */
    public function load(ObjectManager $manager): void
    {
        $languagePL = new Languages();
        $languagePL
            ->setLocale('pl')
            ->setName('Polski')
            ->setStatus('a')
            ->setCode('pl_PL');

        $languageEN = new Languages();
        $languageEN
            ->setLocale('en')
            ->setName('English')
            ->setStatus('a')
            ->setCode('en_US');

        $manager->persist($languagePL);
        $manager->persist($languageEN);

        $manager->flush();

        $this->addReference('language-pl', $languagePL);
        $this->addReference('language-en', $languageEN);
    }
}

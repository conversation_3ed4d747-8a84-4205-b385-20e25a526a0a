<?php

namespace App\DataFixtures;

use App\Entity\Currency;
use Doctrine\Persistence\ObjectManager;
use Doctrine\Bundle\FixturesBundle\Fixture;
use I2m\StandardTypes\Enum\Currency as CurrencyEnum;

class LoadCurrencyCmData extends Fixture
{
    /**
     * Load data fixtures with the passed EntityManager
     */
    public function load(ObjectManager $manager): void
    {

        $eur = new Currency();
        $eur->setSymbol('€')
            ->setCode(CurrencyEnum::EUR);

        $pln = new Currency();
        $pln->setSymbol('zł')
            ->setCode(CurrencyEnum::PLN);

        $manager->persist($eur);
        $manager->persist($pln);

        $manager->flush();

        $this->addReference('currency-eur', $eur);
        $this->addReference('currency-pln', $pln);
    }
}

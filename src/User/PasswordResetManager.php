<?php

declare(strict_types=1);

namespace App\User;

use App\Entity\User;
use App\Repository\UserRepository;
use App\Service\Email\EmailService;
use DateTimeImmutable;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Contracts\Translation\TranslatorInterface;

class PasswordResetManager implements PasswordResetManagerInterface
{
    private const RESET_PATH = '/#/resetting';

    public function __construct(
        private readonly UserRepository $userRepository,
        private readonly TranslatorInterface $translator,
        private readonly ParameterBagInterface $parameterBag,
        private readonly EmailService $emailService,
    ) {
    }

    public function sendResetEmail(User $user): void
    {
        if (!$this->isUserValid($user)) {
            return;
        }

        $token = $this->generateToken();
        $user
            ->setPasswordResetToken($token)
            ->setPasswordRequestedAt(new DateTimeImmutable());

        $this->userRepository->updateUser();

        $this->emailService->sendEmail(
            template: 'Email/reset_password/template.html.twig',
            data: [
                'reset_link' => $this->getResetUrl($token),
            ],
            title: $this->translator->trans('email.reset-password'),
            emails: [
                $user->getEmail(),
            ],
            language: $user->getLanguage()->getCode(),
        );
    }

    public function isUserValid(User $user = null): bool
    {
        return $user !== null;
    }

    public function isTokenValid(string $token = null): bool
    {
        if ($token === null) {
            return false;
        }

        return $this->userRepository->findOneBy(['passwordResetToken' => $token]) !== null;
    }

    public function getResetUrl(string $token): string
    {
        $frontUrl = $this->parameterBag->get('front_url');
        return $frontUrl . self::RESET_PATH . "?token=$token";
    }

    private function generateToken(): string
    {
        return rtrim(strtr(base64_encode(random_bytes(32)), '+/', '-_'), '=');
    }
}

<?php

namespace App\Repository;

use App\Entity\ApiAccessToken;
use DateTimeImmutable;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<ApiAccessToken>
 *
 * @method ApiAccessToken|null find($id, $lockMode = null, $lockVersion = null)
 * @method ApiAccessToken|null findOneBy(array $criteria, array $orderBy = null)
 * @method ApiAccessToken[]    findAll()
 * @method ApiAccessToken[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class ApiAccessTokenRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, ApiAccessToken::class);
    }

    public function save(ApiAccessToken $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(ApiAccessToken $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

//    /**
//     * @return ApiAccessToken[] Returns an array of ApiAccessToken objects
//     */
//    public function findByExampleField($value): array
//    {
//        return $this->createQueryBuilder('a')
//            ->andWhere('a.exampleField = :val')
//            ->setParameter('val', $value)
//            ->orderBy('a.id', 'ASC')
//            ->setMaxResults(10)
//            ->getQuery()
//            ->getResult()
//        ;
//    }

//    public function findOneBySomeField($value): ?ApiAccessToken
//    {
//        return $this->createQueryBuilder('a')
//            ->andWhere('a.exampleField = :val')
//            ->setParameter('val', $value)
//            ->getQuery()
//            ->getOneOrNullResult()
//        ;
//    }

    /**
     * @throws \Doctrine\ORM\NonUniqueResultException
     */
    public function findUserByToken(string $token): ?ApiAccessToken
    {
        $onlyToken = str_replace('Bearer ', '', $token);

        $now = new DateTimeImmutable();

        return $this->createQueryBuilder('t')
            ->andWhere('t.token = :token')
            ->andWhere('t.expiresAt > :now')
            ->setParameter('token', $onlyToken)
            ->setParameter('now', $now)
            ->getQuery()
            ->getOneOrNullResult()
            ;
    }
}

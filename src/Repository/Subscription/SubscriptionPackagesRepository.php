<?php

namespace App\Repository\Subscription;

use I2m\StandardTypes\Enum\CMSubscription;
use App\Entity\Subscribers;
use App\Entity\Subscription\OwnerSubscriptionPackages;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;
use I2m\StandardTypes\Enum\Currency;

/**
 * @extends ServiceEntityRepository<OwnerSubscriptionPackages>
 *
 * @method OwnerSubscriptionPackages|null find($id, $lockMode = null, $lockVersion = null)
 * @method OwnerSubscriptionPackages|null findOneBy(array $criteria, array $orderBy = null)
 * @method OwnerSubscriptionPackages[]    findAll()
 * @method OwnerSubscriptionPackages[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class SubscriptionPackagesRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, OwnerSubscriptionPackages::class);
    }

    public function save(OwnerSubscriptionPackages $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(OwnerSubscriptionPackages $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    /**
     * @return OwnerSubscriptionPackages[]
     */
    public function findByCurrency(Currency $currency): ?array
    {
        $plans = $this->findBy(['currency' => $currency, 'dealer' => null]);
        return empty($plans) ? null : $plans;
    }

    public function findBasePackage(CMSubscription $code, Currency $currency): OwnerSubscriptionPackages
    {
        return $this->findOneBy(['currency' => $currency, 'dealer' => null, 'monthsLength' => 1, 'code' => $code]);
    }

    public function findByDealer(Subscribers $dealer): ?array
    {
        $plans = $this->findBy(['dealer' => $dealer]);
        return empty($plans) ? null : $plans;
    }
}

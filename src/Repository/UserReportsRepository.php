<?php

namespace App\Repository;

use App\Entity\UserReports;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<UserReports>
 *
 * @method UserReports|null find($id, $lockMode = null, $lockVersion = null)
 * @method UserReports|null findOneBy(array $criteria, array $orderBy = null)
 * @method UserReports[]    findAll()
 * @method UserReports[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class UserReportsRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, UserReports::class);
    }

//    /**
//     * @return UserReports[] Returns an array of UserReports objects
//     */
//    public function findByExampleField($value): array
//    {
//        return $this->createQueryBuilder('u')
//            ->andWhere('u.exampleField = :val')
//            ->setParameter('val', $value)
//            ->orderBy('u.id', 'ASC')
//            ->setMaxResults(10)
//            ->getQuery()
//            ->getResult()
//        ;
//    }

//    public function findOneBySomeField($value): ?UserReports
//    {
//        return $this->createQueryBuilder('u')
//            ->andWhere('u.exampleField = :val')
//            ->setParameter('val', $value)
//            ->getQuery()
//            ->getOneOrNullResult()
//        ;
//    }
}

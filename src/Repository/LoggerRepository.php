<?php

namespace App\Repository;

use App\Entity\Carwashes;
use App\Entity\Logs;
use App\Entity\Subscribers;
use App\Entity\User;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;
use Psr\Log\LoggerInterface;

/**
 * @extends ServiceEntityRepository<Logs>
 *
 * @method Logs|null find($id, $lockMode = null, $lockVersion = null)
 * @method Logs|null findOneBy(array $criteria, array $orderBy = null)
 * @method Logs[]    findAll()
 * @method Logs[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class LoggerRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Logs::class);
    }

    public function info(?Subscribers $subscriber, ?User $user, ?Carwashes $carwash, string $source, string $message): void
    {
        $this->log($subscriber, $user, $carwash, $source, $message, Logs::LEVEL_INFO);
    }

    public function debug(?Subscribers $subscriber, ?User $user, ?Carwashes $carwash, string $source, string $message): void
    {
        $this->log($subscriber, $user, $carwash, $source, $message, Logs::LEVEL_DEBUG);
    }

    public function error(?Subscribers $subscriber, ?User $user, ?Carwashes $carwash, string $source, string $message): void
    {
        $this->log($subscriber, $user, $carwash, $source, $message, Logs::LEVEL_ERROR);
    }
    public function warning(?Subscribers $subscriber, ?User $user, ?Carwashes $carwash, string $source, string $message): void
    {
        $this->log($subscriber, $user, $carwash, $source, $message, Logs::LEVEL_WARNING);
    }

    public function log(
        ?Subscribers $subscriber,
        ?User $user,
        ?Carwashes $carwash,
        string $source,
        string $message,
        string $level
    ): void {
        $this->_em->getConnection()->insert('logs', [
            'subscriber_id' => $subscriber?->getId(),
            'user_id' => $user?->getId(),
            'carwash_id' => $carwash?->getId(),
            'message' => $message,
            'ctime' => (new \DateTime())->format('Y-m-d H:i:s'),
            'level' => $level,
            'source' => $source
        ]);
    }
}

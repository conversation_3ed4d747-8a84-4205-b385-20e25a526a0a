<?php

namespace App\Repository;

use App\Entity\VatTax;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * Class VatTaxRepository
 *
 * @package App\Entity\Repository
 */
class VatTaxRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, VatTax::class);
    }
}

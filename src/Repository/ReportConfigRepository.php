<?php

namespace App\Repository;

use App\Entity\Enum\ReportPeriod;
use App\Entity\ReportConfig;
use App\Entity\ReportFile;
use App\Entity\User;
use App\Service\Report2\Model\Data;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<ReportConfig>
 *
 * @method ReportConfig|null find($id, $lockMode = null, $lockVersion = null)
 * @method ReportConfig|null findOneBy(array $criteria, array $orderBy = null)
 * @method ReportConfig[]    findAll()
 * @method ReportConfig[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class ReportConfigRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, ReportConfig::class);
    }

//    /**
//     * @return ReportConfig[] Returns an array of ReportConfig objects
//     */
//    public function findByExampleField($value): array
//    {
//        return $this->createQueryBuilder('r')
//            ->andWhere('r.exampleField = :val')
//            ->setParameter('val', $value)
//            ->orderBy('r.id', 'ASC')
//            ->setMaxResults(10)
//            ->getQuery()
//            ->getResult()
//        ;
//    }

//    public function findOneBySomeField($value): ?ReportConfig
//    {
//        return $this->createQueryBuilder('r')
//            ->andWhere('r.exampleField = :val')
//            ->setParameter('val', $value)
//            ->getQuery()
//            ->getOneOrNullResult()
//        ;
//    }

    /**
     * @return ReportConfig[]
     */
    public function findByPeriod(ReportPeriod $period): array
    {
        return $this->findBy([
            'period' => $period->value,
            'enabled' => true
        ]);
    }

    public function save(ReportConfig $entity): ReportConfig
    {
        $this->getEntityManager()->persist($entity);
        $this->getEntityManager()->flush();

        return $entity;
    }

    public function getList(
        ?User $user = null,
        ?int $page = 1,
        ?int $perPage = 25,
    ) {
        $qb = $this
            ->createQueryBuilder('e')
            ->andWhere('e.enabled = true')
        ;

        if ($user) {
            $qb->andWhere('e.user = :user')
                ->setParameter('user', $user);
        }

        $countQuery = clone $qb;
        $count = $countQuery->select('COUNT(e.id)')
            ->getQuery()
            ->getSingleScalarResult();

        $qb->orderBy('e.id', 'DESC');
        if ($page && $perPage) {
            $qb->setFirstResult(($page - 1) * $perPage)
                ->setMaxResults($perPage);
        }

        $results = $qb->getQuery()->getResult();

        return (new Data(data: $results, total: $count));
    }
}

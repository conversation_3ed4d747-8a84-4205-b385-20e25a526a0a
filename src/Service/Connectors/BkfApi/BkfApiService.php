<?php

namespace App\Service\Connectors\BkfApi;

use App\Service\Connectors\BkfApi\Model\IssuesList;
use Exception;
use GuzzleHttp\Client;
use Psr\Log\LoggerInterface;
use Symfony\Component\Serializer\SerializerInterface;

class BkfApiService
{
    private const API_URL = 'https://api-bkf.bkf.pl';
    private const API_CLIENT_ID = '2e34f44016713cc9ca7ab5f6ee175483';
    private const API_CLIENT_SECRET = '6284004f2a7b515e4f90be10eb937a1abf6b7de5e325d803ad1c7a7bd955bc219' .
    '95eaa85db09f9eb0eb0ce2e05d33035d4d6bb377d8e4457f59e4e238090b071';

    /** @var Client - http api client */
    private Client $http;

    private ?string $token = null;

    public function __construct(
        private LoggerInterface $logger,
        private SerializerInterface $serializer
    ) {
        $this->http = new Client();
    }

    /**
     * API Authorization
     * @return mixed|null
     */
    private function getBkfApiToken()
    {
        if (is_null($this->token)) {
            $response = $this->http->request(
                'POST',
                self::API_URL . '/token',
                [
                    'headers' => [
                        'Content-Type' => 'application/x-www-form-urlencoded'
                    ],
                    'form_params' => [
                        'client_id' => self::API_CLIENT_ID,
                        'client_secret' => self::API_CLIENT_SECRET,
                        'grant_type' => 'client_credentials',
                    ]
                ]
            );
            $tokenArray = json_decode($response->getBody(), true);

            $this->token = $tokenArray['access_token'];
        }

        return $this->token;
    }

    public function makeBkfApiCall(
        string $endpointUrl,
        string $method = 'GET',
        array $postData = [],
        string $requestType = 'json',
        bool $httpErrors = false,
        bool $raw = false
    ) {
        $accessToken = $this->getBkfApiToken();
        $data = [
            'headers' => [
                'Authorization' => 'Bearer ' . $accessToken
            ],
            $requestType => $postData,
            'http_errors' => $httpErrors,
        ];

        try {
            $response = $this->http->request(
                $method,
                self::API_URL . $endpointUrl,
                $data
            );
            $content = $response->getBody()->getContents();
            if ($raw) {
                return $content;
            }
            return json_decode($content, true) ?? [];
        } catch (Exception $ex) {
            $this->logger->error(
                "Exception with bkfApi url: " . $endpointUrl
                . " Ex: " . $ex->getMessage()
            );
            throw $ex;
        }
    }

    public function getDevicesByGroup(array $groups, int $page, int $limit): array
    {
        $json = $this->makeBkfApiCall(
            '/api/devices/by_group2/' . implode(',', $groups),
            'GET',
            ['page' => $page, 'limit' => $limit],
            'query',
            false,
            true
        );
        $devices = $this->serializer->deserialize(
            $json,
            'App\Service\Connectors\BkfApi\Model\Device[]',
            'json'
        );

        return $devices;
    }

    public function sendSupportReply(
        int $id,
        string $username,
        string $description,
        string $role,
        string $action,
        array $files
    ): array {
        $data = [
            [
                'name' => 'author',
                'contents' => $username
            ],
            [
                'name' => 'description',
                'contents' => $description
            ],
            [
                'name' => 'role',
                'contents' => $role
            ],
            [
                'name' => 'action',
                'contents' => $action
            ],
        ];

        foreach ($files as $key => $file) {
            $data[] = [
                'name' => "plik{$key}",
                'contents' => $file['attachment'],
            ];
        }

        return $this->makeBkfApiCall(
            "/api/support/jr/{$id}/note",
            'POST',
            $data,
            'multipart',
            true
        );
    }

    public function getIssues(
        ?int $page = null,
        ?int $limit = null,
        ?\DateTime $cratedFrom = null,
        ?array $status = null,
        ?array $serial = null,
        ?string $orderDir = 'DESC'
    ): IssuesList {
        $params = [
            'page' => $page,
            'limit' => $limit,
            'createdFrom' => $cratedFrom?->format('Y-m-d'),
            'status' => $status ? implode(',', $status) : null,
            'serial' => $serial ? implode(',', $serial) : null,
            'orderDir' => $orderDir
        ];

        $json = $this->makeBkfApiCall(
            endpointUrl: '/api/service/incident_issues' . "?" . http_build_query($params),
            raw: true
        );


        $issues = $this->serializer->deserialize(
            $json,
            'App\Service\Connectors\BkfApi\Model\IssuesList',
            'json'
        );

        return $issues;
    }
}

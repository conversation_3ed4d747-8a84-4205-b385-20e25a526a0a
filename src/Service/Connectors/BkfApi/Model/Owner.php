<?php

namespace App\Service\Connectors\BkfApi\Model;

class Owner
{
    private ?int $id;
    private ?string $cmEmailAccount;
    private ?string $clientAcronym;
    private ?string $fullNip = null;
    private ?string $country = null;
    private ?string $organizationName = null;

    private ?string $adres;

    private ?string $city;

    private ?string $postcode;

    public function getOrganizationName(): ?string
    {
        return $this->organizationName;
    }

    public function setOrganizationName(?string $organizationName): Owner
    {
        $this->organizationName = $organizationName;
        return $this;
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(?int $id): Owner
    {
        $this->id = $id;
        return $this;
    }

    public function getCmEmailAccount(): ?string
    {
        return $this->cmEmailAccount;
    }

    public function setCmEmailAccount(?string $cmEmailAccount): Owner
    {
        $this->cmEmailAccount = !empty($cmEmailAccount) ? mb_strtolower($cmEmailAccount) : null;
        return $this;
    }

    public function getClientAcronym(): ?string
    {
        return $this->clientAcronym;
    }

    public function setClientAcronym(?string $clientAcronym): Owner
    {
        $this->clientAcronym = $clientAcronym;
        return $this;
    }

    public function getFullNip(): ?string
    {
        return $this->fullNip;
    }

    public function setFullNip(?string $fullNip): Owner
    {
        // zamieniam    PL664-185-32-19 na PL6641853219
        $this->fullNip = str_replace('-', '', $fullNip);
        ;
        return $this;
    }

    public function getCountry(): ?string
    {
        return $this->country;
    }

    public function setCountry(?string $country): Owner
    {
        $this->country = empty($country) ? null : $country;
        return $this;
    }

    public function getAdres(): ?string
    {
        return $this->adres;
    }

    public function setAdres(?string $adres): Owner
    {
        $this->adres = empty($adres) ? null : $adres;
        return $this;
    }

    public function getCity(): ?string
    {
        return $this->city;
    }

    public function setCity(?string $city): Owner
    {
        $this->city = empty($city) ? null : $city;
        return $this;
    }

    public function getPostcode(): ?string
    {
        return $this->postcode;
    }

    public function setPostcode(?string $postcode): Owner
    {
        $this->postcode = empty($postcode) ? null : $postcode;
        return $this;
    }


    public function companyDataOk(): bool
    {
        return
            !empty($this->getFullNip())
        && !empty($this->getCountry())
        && !empty($this->getOrganizationName());
    }
}

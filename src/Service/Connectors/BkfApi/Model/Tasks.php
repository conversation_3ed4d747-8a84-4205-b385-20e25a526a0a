<?php

namespace App\Service\Connectors\BkfApi\Model;

class Tasks
{
    private int $id;
    private Device $device;
    private ?Visits $visit = null;

    private Issues $issue;

    private ?string $serviceman;
    private \DateTime $taskStartTimestamp;
    private ?\DateTime $taskEndTimestamp;

    private ?string $problemSymptom;
    private ?string $problemSolution;

    private ?string $taskStatus;


    public function getId(): int
    {
        return $this->id;
    }

    public function setId(int $id): Tasks
    {
        $this->id = $id;
        return $this;
    }

    public function getDevice(): Device
    {
        return $this->device;
    }

    public function setDevice(Device $device): Tasks
    {
        $this->device = $device;
        return $this;
    }

    public function getVisit(): ?Visits
    {
        return $this->visit;
    }

    public function setVisit(?Visits $visit): Tasks
    {
        $this->visit = $visit;
        return $this;
    }

    public function getIssue(): Issues
    {
        return $this->issue;
    }

    public function setIssue(Issues $issue): Tasks
    {
        $this->issue = $issue;
        return $this;
    }

    public function getServiceman(): ?string
    {
        return $this->serviceman;
    }

    public function setServiceman(?string $serviceman): Tasks
    {
        $this->serviceman = $serviceman;
        return $this;
    }

    public function getTaskStartTimestamp(): \DateTime
    {
        return $this->taskStartTimestamp;
    }

    public function setTaskStartTimestamp(\DateTime $taskStartTimestamp): Tasks
    {
        $this->taskStartTimestamp = $taskStartTimestamp;
        return $this;
    }

    public function getTaskEndTimestamp(): ?\DateTime
    {
        return $this->taskEndTimestamp;
    }

    public function setTaskEndTimestamp(?\DateTime $taskEndTimestamp): Tasks
    {
        $this->taskEndTimestamp = $taskEndTimestamp;
        return $this;
    }

    public function getProblemSymptom(): ?string
    {
        return $this->problemSymptom;
    }

    public function setProblemSymptom(?string $problemSymptom): Tasks
    {
        $this->problemSymptom = $problemSymptom;
        return $this;
    }

    public function getProblemSolution(): ?string
    {
        return $this->problemSolution;
    }

    public function setProblemSolution(?string $problemSolution): Tasks
    {
        $this->problemSolution = $problemSolution;
        return $this;
    }

    public function getTaskStatus(): ?string
    {
        return $this->taskStatus;
    }

    public function setTaskStatus(?string $taskStatus): Tasks
    {
        $this->taskStatus = $taskStatus;
        return $this;
    }
}

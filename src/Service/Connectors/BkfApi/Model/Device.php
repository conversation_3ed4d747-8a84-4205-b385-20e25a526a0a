<?php

namespace App\Service\Connectors\BkfApi\Model;

class Device
{
    private int $serialNumber;
    private int $id;
    private ?int $discontinued;


    private ?string $plc;


    private ?string $mac;

    private ?\DateTime $warrantyTo;
    private ?\DateTimeInterface $timeStart;
    private ?Owner $owner;
    private ?Owner $dealer;
    private ?Owner $network;
    private ?DeviceCode $deviceCode;

    private ?string $country = null;
    private ?string $city = null;
    private ?string $address = null;

    private ?float $latitude;
    private ?float $longitude;

    private ?int $beloyalPort;
    private ?string $beloyalPayment;


    private ?string $objectName;
    private ?string $objectNumber;
    private ?string $wwwName;

    private ?string $lastProtocolFirmware;
    private ?string $lastProtocolIp;

    private ?\DateTimeInterface $nextInspection;
    private ?int $vncPortNumber = null;

    private ?bool $generateAlerts = null;

    public function getGenerateAlerts(): ?bool
    {
        return $this->generateAlerts;
    }

    public function setGenerateAlerts(?bool $generateAlerts): Device
    {
        $this->generateAlerts = $generateAlerts;
        return $this;
    }


    public function getWwwName(): ?string
    {
        return $this->wwwName;
    }

    public function setWwwName(?string $wwwName): Device
    {
        $this->wwwName = $wwwName;
        return $this;
    }

    public function getCity(): ?string
    {
        return $this->city;
    }

    public function setCity(?string $city): Device
    {
        $this->city = $city;
        return $this;
    }

    public function getObjectName(): ?string
    {
        return $this->objectName;
    }

    public function setObjectName(?string $objectName): Device
    {
        $this->objectName = empty($objectName) ? null : $objectName;
        return $this;
    }

    public function getObjectNumber(): ?string
    {
        return $this->objectNumber;
    }

    public function setObjectNumber(?string $objectNumber): Device
    {
        $this->objectNumber = $objectNumber;
        return $this;
    }

    private ?int $sendAlarmToJr;

    public function getSerialNumber(): ?int
    {
        return $this->serialNumber;
    }

    public function setSerialNumber(?int $serialNumber): Device
    {
        $this->serialNumber = $serialNumber;
        return $this;
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(?int $id): Device
    {
        $this->id = $id;
        return $this;
    }

    public function getDiscontinued(): ?int
    {
        return $this->discontinued;
    }

    public function setDiscontinued(?int $discontinued): Device
    {
        $this->discontinued = $discontinued;
        return $this;
    }

    public function getPlc(): ?string
    {
        return $this->plc;
    }

    public function setPlc(?string $plc): Device
    {
        $this->plc = $plc;
        return $this;
    }

    public function getMac(): ?string
    {
        return $this->mac;
    }

    public function setMac(?string $mac): Device
    {
        $this->mac = $mac;
        return $this;
    }

    public function getWarrantyTo(): ?\DateTime
    {
        return $this->warrantyTo;
    }

    public function setWarrantyTo(?\DateTime $warrantyTo): Device
    {
        $this->warrantyTo = $warrantyTo;
        return $this;
    }

    public function getTimeStart(): ?\DateTimeInterface
    {
        return $this->timeStart;
    }

    public function setTimeStart(?\DateTimeInterface $timeStart): Device
    {
        $this->timeStart = $timeStart;
        return $this;
    }

    public function getOwner(): ?Owner
    {
        return $this->owner;
    }

    public function setOwner(?Owner $owner): Device
    {
        $this->owner = $owner;
        return $this;
    }

    public function getNetwork(): ?Owner
    {
        return is_null($this->network) ? $this->owner : $this->network;
    }

    public function setNetwork(?Owner $network): Device
    {
        $this->network = $network;
        return $this;
    }

    public function getDealer(): ?Owner
    {
        return $this->dealer;
    }

    public function setDealer(?Owner $dealer): Device
    {
        $this->dealer = $dealer;
        return $this;
    }

    public function getDeviceCode(): ?DeviceCode
    {
        return $this->deviceCode;
    }

    public function setDeviceCode(?DeviceCode $deviceCode): Device
    {
        $this->deviceCode = $deviceCode;
        return $this;
    }

    public function getCountry(): ?string
    {
        return $this->country;
    }

    public function setCountry(?string $country): Device
    {
        $this->country = $country;
        return $this;
    }

    public function getLatitude(): ?float
    {
        return $this->latitude;
    }

    public function setLatitude(?float $latitude): Device
    {
        $this->latitude = $latitude;
        return $this;
    }

    public function getLongitude(): ?float
    {
        return $this->longitude;
    }

    public function setLongitude(?float $longitude): Device
    {
        $this->longitude = $longitude;
        return $this;
    }

    public function getAddress(): ?string
    {
        return $this->address;
    }

    public function setAddress(?string $address): Device
    {
        $this->address = $address;
        return $this;
    }

    public function getBeloyalPort(): ?int
    {
        return $this->beloyalPort;
    }

    public function setBeloyalPort(?int $beloyalPort): Device
    {
        $this->beloyalPort = $beloyalPort;
        return $this;
    }

    public function getBeloyalPayment(): ?string
    {
        return $this->beloyalPayment;
    }

    public function setBeloyalPayment(?string $beloyalPayment): Device
    {
        $this->beloyalPayment = $beloyalPayment;
        return $this;
    }

    public function getSendAlarmToJr(): ?int
    {
        return $this->sendAlarmToJr;
    }

    public function setSendAlarmToJr(?int $sendAlarmToJr): Device
    {
        $this->sendAlarmToJr = $sendAlarmToJr;
        return $this;
    }

    public function isBeloyalApp(): bool
    {
        return substr($this->getBeloyalPayment(), 0, strlen("BELOYAL")) === "BELOYAL";
    }

    public function getLastProtocolFirmware(): ?string
    {
        return $this->lastProtocolFirmware;
    }

    public function setLastProtocolFirmware(?string $lastProtocolFirmware): void
    {
        $this->lastProtocolFirmware = $lastProtocolFirmware;
    }

    public function getLastProtocolIp(): ?string
    {
        return $this->lastProtocolIp;
    }

    public function setLastProtocolIp(?string $lastProtocolIp): void
    {
        $this->lastProtocolIp = $lastProtocolIp;
    }

    public function getNextInspection(): ?\DateTimeInterface
    {
        return $this->nextInspection;
    }

    public function setNextInspection(?\DateTimeInterface $nextInspection): void
    {
        $this->nextInspection = $nextInspection;
    }

    public function getVncPortNumber(): ?int
    {
        return $this->vncPortNumber;
    }

    public function setVncPortNumber(?int $vncPortNumber): Device
    {
        $this->vncPortNumber = $vncPortNumber;
        return $this;
    }
}

<?php

namespace App\Service\Connectors\BkfApi\Model;

class DeviceCode
{
    private int $id;
    private string $name;
    private string $groupName;
    private string $mainName = 'UNKNOWN';

    public function getId(): int
    {
        return $this->id;
    }

    public function setId(int $id): DeviceCode
    {
        $this->id = $id;
        return $this;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function setName(string $name): DeviceCode
    {
        $this->name = $name;
        return $this;
    }

    public function getGroupName(): string
    {
        return $this->groupName;
    }

    public function setGroupName(string $groupName): DeviceCode
    {
        $this->groupName = $groupName;
        return $this;
    }

    public function getMainName(): string
    {
        return $this->mainName;
    }

    public function setMainName(string $mainName): DeviceCode
    {
        $this->mainName = $mainName;
        return $this;
    }
}

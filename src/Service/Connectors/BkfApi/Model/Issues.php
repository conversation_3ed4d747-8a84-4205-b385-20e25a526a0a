<?php

namespace App\Service\Connectors\BkfApi\Model;

class Issues
{
    private int $id;
    private Device $device;
    private ?string $priority;
    private string $reportSource;
    private string $title;
    private string $content;
    private \DateTime $reportTime;
    private ?\DateTime $completionTime;
    private ?string $reportedBy;

    private ?\DateTime $targetTime = null;

    private string $status;

    public function getId(): int
    {
        return $this->id;
    }

    public function setId(int $id): Issues
    {
        $this->id = $id;
        return $this;
    }

    public function getDevice(): Device
    {
        return $this->device;
    }

    public function setDevice(Device $device): Issues
    {
        $this->device = $device;
        return $this;
    }

    public function getPriority(): ?string
    {
        return $this->priority;
    }

    public function setPriority(?string $priority): Issues
    {
        $this->priority = empty($priority) ? null : $priority;
        return $this;
    }

    public function getReportSource(): string
    {
        return $this->reportSource;
    }

    public function setReportSource(string $reportSource): Issues
    {
        $this->reportSource = $reportSource;
        return $this;
    }



    public function getTitle(): string
    {
        return $this->title;
    }

    public function setTitle(string $title): Issues
    {
        $this->title = $title;
        return $this;
    }

    public function getContent(): string
    {
        return $this->content;
    }

    public function setContent(string $content): Issues
    {
        $this->content = $content;
        return $this;
    }

    public function getReportTime(): \DateTime
    {
        return $this->reportTime;
    }

    public function setReportTime(\DateTime $reportTime): Issues
    {
        $this->reportTime = $reportTime;
        return $this;
    }

    public function getCompletionTime(): ?\DateTime
    {
        return $this->completionTime;
    }

    public function setCompletionTime(?\DateTime $completionTime): Issues
    {
        $this->completionTime = $completionTime;
        return $this;
    }



    public function getReportedBy(): ?string
    {
        return $this->reportedBy;
    }

    public function setReportedBy(?string $reportedBy): Issues
    {
        $this->reportedBy = $reportedBy;
        return $this;
    }

    public function getStatus(): string
    {
        return $this->status;
    }

    public function setStatus(string $status): Issues
    {
        $this->status = $status;
        return $this;
    }

    public function getTargetTime(): ?\DateTime
    {
        return $this->targetTime;
    }

    public function setTargetTime(?\DateTime $targetTime): Issues
    {
        $this->targetTime = $targetTime;
        return $this;
    }
}

<?php

namespace App\Service\Connectors\BkfApi\Model;

class Visits
{
    private int $id;
    private Device $device;
    private string $status;
    private \DateTime $registrationTimestamp;

    private ?string $serviceman;

    private ?\DateTime $plannedDate;

    public function getId(): int
    {
        return $this->id;
    }

    public function setId(int $id): Visits
    {
        $this->id = $id;
        return $this;
    }

    public function getDevice(): Device
    {
        return $this->device;
    }

    public function setDevice(Device $device): Visits
    {
        $this->device = $device;
        return $this;
    }

    public function getStatus(): string
    {
        return $this->status;
    }

    public function setStatus(string $status): Visits
    {
        $this->status = $status;
        return $this;
    }

    public function getRegistrationTimestamp(): \DateTime
    {
        return $this->registrationTimestamp;
    }

    public function setRegistrationTimestamp(\DateTime $registrationTimestamp): Visits
    {
        $this->registrationTimestamp = $registrationTimestamp;
        return $this;
    }

    public function getServiceman(): ?string
    {
        return $this->serviceman;
    }

    public function setServiceman(?string $serviceman): Visits
    {
        $this->serviceman = $serviceman;
        return $this;
    }

    public function getPlannedDate(): ?\DateTime
    {
        return $this->plannedDate;
    }

    public function setPlannedDate(?\DateTime $plannedDate): Visits
    {
        $this->plannedDate = $plannedDate;
        return $this;
    }
}

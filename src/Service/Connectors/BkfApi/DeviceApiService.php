<?php

namespace App\Service\Connectors\BkfApi;

class DeviceApiService
{
    public function __construct(private BkfApiService $bkfApi)
    {
    }

    public function getDeviceContactDetails(string $sn): array
    {
        $uri = '/api/device_details/' . $sn;
        return $this->bkfApi->makeBkfApiCall(
            $uri,
            'GET',
        );
    }

    public function getCarwashServiceVisits(string $sn): array
    {
        $uri = '/api/planner/get_visits/' . $sn;
        return $this->bkfApi->makeBkfApiCall(
            $uri,
            'GET',
        );
    }
}

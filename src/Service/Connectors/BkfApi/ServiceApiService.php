<?php

namespace App\Service\Connectors\BkfApi;

class ServiceApiService
{
    public function __construct(private BkfApiService $bkfApi)
    {
    }

    public function getServiceByOwner(
        int $ownerId,
        ?array $carwashes = null,
        ?string $dateFrom = null,
        ?string $dateTo = null,
        ?int $page = null,
        ?int $limit = null,
        ?string $status = null,
        ?string $sortBy = null,
        bool $sortDesc = true,
        ?string $reporter = null,
        ?string $type = null,
        ?string $source = null
    ) {
        $params = [
            'serial' => implode(',', $carwashes),
            'dateFrom' => $dateFrom,
            'dateTo' => $dateTo,
            'type' => 'paginated',
            'page' => $page,
            'perPage' => $limit,
            'status' => $status,
            'sortBy' => $sortBy,
            'sortDesc' => $sortDesc ? 'true' : 'false',
            'reporter' => $reporter,
            'typ' => strtolower($type),
            'source' => $source,
        ];
        $uri = '/api/service/by_owner/' . $ownerId . "?" . http_build_query($params);
        $data = $this->bkfApi->makeBkfApiCall(
            $uri,
            'GET',
        );

        return [
            'data' => $data['services_orders'],
            'meta' => [
                'total' => (int)$data['services_orders_qty'],
                'filtering' => [
                    'issue_reported_by' => $data['reporters'],
                    'type' => $data['types'],
                    'issue_report_source' => $data['sources'],
                ],
            ],
        ];
    }

    public function getServiceIncidentIssues(?array $sns)
    {
        $params = [];

        $params['seStatuses'] = 'PEND';

        $sns ? $params['serial'] = implode($sns) : null ;

        $uri = "/api/service/incident_issues" . "?" . http_build_query($params);

        return $this->bkfApi->makeBkfApiCall(
            $uri,
            'GET',
        );
    }
}

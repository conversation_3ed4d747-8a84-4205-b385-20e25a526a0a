<?php

namespace App\Service\Connectors\BkfApi;

class SupportApiService
{
    private BkfApiService $bkfApi;

    public function __construct(BkfApiService $bkfApi)
    {
        $this->bkfApi = $bkfApi;
    }

    public function getSupportByOwner(
        int $ownerId,
        int $page = 1,
        int $limit = 10,
        ?string $status = null
    ): array {
        $params = [
            'type' => 'paginated',
            'page' => $page,
            'perPage' => $limit,
            'status' => $status,
        ];
        $uri = '/api/support/by_owner/' . $ownerId . "?" . http_build_query($params);
        $data = $this->bkfApi->makeBkfApiCall($uri);

        return [
            'data' => $data['data'] ?? [],
            'total' => (int)($data['qty'] ?? 0),
        ];
    }

    public function getSupportIssueByOwnerAndId(int $ownerId, int $id)
    {
        $uri = '/api/support/by_owner/' . $ownerId . "/issue/" . $id;
        $data = $this->bkfApi->makeBkfApiCall($uri);
        return ['data' => $data];
    }
}

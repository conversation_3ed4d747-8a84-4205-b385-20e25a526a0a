<?php

namespace App\Service\Connectors\CarwashApi;

use App\Service\CarwashApi\CarwashApiConnector;

class CarwashIssuesApiService
{
    public function __construct(private CarwashApiConnector $apiConnector)
    {
    }

    public function getCarwashIssues(
        ?array $groups = null,
        ?array $sn = null,
        string $lang = 'pl',
        string $timezone = 'Europe/Warsaw',
        int $page = 1,
        int $perPage = 999,
        string $orderBy = 'id',
        string $orderDir = 'DESC',
        ?string $search = null,
        ?array $owner = null,
        ?string $dateFrom = null,
        ?string $dateTo = null,
        ?bool $isOpen = null
    ) {
        $params = [
            'lang' => $lang,
            'timezone' => $timezone,
            'date_from' => $dateFrom,
            'date_to' => $dateTo,
            'page' => $page,
            'itemsPerPage' => $perPage,
            'owner' => $owner ? implode(',', $owner) : null,
            'serial' => $sn ? implode(',', $sn) : null,
            'groups' => $groups ? implode(',', $groups) : null,
            'search' => $search,
            'orderBy' => $orderBy,
            'orderDir' => $orderDir,
            'isOpen' => $isOpen
        ];

        $response = $this->apiConnector->request(
            "/api/carwashissues",
            'GET',
            $params
        );

        return $response;
    }

    public function updateCarwashIssue(
        int $issueId,
        string $date,
        string $explanation,
        string $action,
        ?string $email = null,
        ?string $processId = null
    ) {

        $params = [
            'issues' => $issueId,
            'date' => $date,
            'explanation' => $explanation,
            'action' => $action,
            'process_id' => $processId,
            'user' => $email
        ];

        $response = $this->apiConnector->request(
            "/api/carwashissues/update",
            'PUT',
            $params
        );

        return $response;
    }

    public function getIssueDetails(
        int $issueId,
        string $lang = 'pl',
        string $timezone = 'Europe/Warsaw'
    ) {
        $params = [
            'lang' => $lang,
            'timezone' => $timezone,
        ];

        return $this->apiConnector->request(
            "/api/carwashissue/$issueId",
            "GET",
            $params
        );
    }

    public function getSimilarIssue(
        int $issueId,
        string $lang = 'pl',
        string $timezone = 'Europe/Warsaw',
        ?string $date_from = null,
        ?string $date_to = null
    ) {
        $params = [
            'lang' => $lang,
            'timezone' => $timezone,
            'date_from' => $date_from,
            'date_to' => $date_to
        ];

        return $this->apiConnector->request(
            "/api/carwashissue/$issueId/similar",
            "GET",
            $params
        );
    }

    public function getIssuesHistory(
        ?array $groups = null,
        ?array $sn = null,
        string $lang = 'pl',
        string $timezone = 'Europe/Warsaw',
        int $page = 1,
        int $perPage = 999,
        string $orderBy = 'id',
        string $orderDir = 'DESC',
        ?string $search = null,
        ?string $dateFrom = null,
        ?string $dateTo = null,
    ) {
        $params = [
            'serial' => $sn ? implode(',', $sn) : null,
            'lang' => $lang,
            'timezone' => $timezone,
            'date_from' => $dateFrom,
            'date_to' => $dateTo,
            'page' => $page,
            'itemsPerPage' => $perPage,
            'groups' => $groups ? implode(',', $groups) : null,
            'search' => $search,
            'orderBy' => $orderBy,
            'orderDir' => $orderDir,
        ];

        $response = $this->apiConnector->request(
            "/api/carwashissues/history",
            'GET',
            $params
        );

        return $response;
    }

    public function getIssuesHistoryStats(
        ?string $dateFrom = null,
        ?string $dateTo = null,
        ?string $timezone = null,
    ) {
        $params = [
            'date_from' => $dateFrom,
            'date_to' => $dateTo,
            'timezone' => $timezone,
        ];

        $response = $this->apiConnector->request(
            "/api/carwashissues/history/stats",
            "GET",
            $params,
        );

        return $response;
    }
}

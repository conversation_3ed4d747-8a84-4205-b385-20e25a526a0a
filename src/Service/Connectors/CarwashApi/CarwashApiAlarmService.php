<?php

namespace App\Service\Connectors\CarwashApi;

use App\Service\CarwashApi\CarwashApiConnector;

class CarwashApiAlarmService
{
    /**
     * CardsApiService constructor.
     */
    public function __construct(
        private CarwashApiConnector $apiConnector
    ) {
    }

    public function getSpecificAlarms(int $id, string $lang, string $timezone)
    {
        $params = [
            'lang' => $lang,
            'timezone' => $timezone
        ];

        return $this->apiConnector->request(
            "/api/alarm/$id",
            'GET',
            $params
        );
    }

    public function getAlarmBySerials(array $sn, string $lang = 'pl', string $dateTimeZone = 'Europe/Warsaw')
    {
        $params = [
            'lang' => $lang,
            'timezone' => $dateTimeZone
        ];

        return $this->apiConnector->request(
            "/api/carwash/by_serials/" . implode(",", $sn),
            'GET',
            $params
        );
    }

    public function getHistory(
        array $sn,
        string $lang,
        string $timezone,
        string $dateFrom,
        string $dateTo,
        int $page = 1,
        int $perPage = 50,
        string $orderBy = 'ct',
        string $orderDir = 'DESC',
        ?string $search = null,
        ?array $alarmIdFilter = null
    ) {
        $params = [
            'lang' => $lang,
            'timezone' => $timezone,
            'date_from' => $dateFrom,
            'date_to' => $dateTo,
            'page' => $page,
            'perPage' => $perPage,
            'orderBy' => $orderBy,
            'orderDir' => $orderDir,
            'alarmIdFilter' => implode(',', $alarmIdFilter),
            'search' => $search
        ];
        return $this->apiConnector->request(
            "/api/alarm/history/" . implode(",", $sn),
            'GET',
            $params
        );
    }
}

<?php

namespace App\Service\Report2\Data;

use App\Entity\Carwashes;
use I2m\StandardTypes\Enum\CMSubscription;
use App\Entity\Subscribers;
use App\Entity\User;
use App\Repository\CarwashesRepository;
use App\Repository\LoggerRepository;
use App\Service\Report2\Model\Data;
use App\Service\Report2\ReportException;
use App\Service\UserGrantedChecker;
use I2m\Reports\Enum\FileExtention;
use I2m\Reports\Interface\ReportDataInterface;
use Symfony\Bundle\FrameworkBundle\Translation\Translator;
use Symfony\Contracts\Translation\TranslatorInterface;

abstract class ReportDataAbstract implements ReportDataInterface
{
    public function __construct(
        private TranslatorInterface $translator,
        protected CarwashesRepository $carwashesRepository,
        private UserGrantedChecker $userGrantedChecker,
        private LoggerRepository $loggerRepository
    ) {
    }

    private array $criteria;
    private ?User $user;
    private ?Subscribers $subscriber;

    abstract public function getEmailText(): ?string;
    abstract public function getData(?int $page = null, ?int $perPage = null): Data;

    public function setConfig(array $criteria, ?Subscribers $subscriber = null, ?User $user = null): ReportDataAbstract
    {
        $this->subscriber = $subscriber;
        $this->user = $user;
        $this->criteria = $criteria;
        return $this;
    }

    public function getTwigTemplate(): string
    {
        return 'Reports2/default.html.twig';
    }

    public function getCriteria(): array
    {
        return $this->criteria;
    }

    public function getInfo(): array
    {
        return
        [
            'subscriber' => $this->getSubscriber(),
            'user' => $this->getUser()
        ];
    }



    public function getDateCriteria(string $name, ?\DateTimeZone $timezone = null): ?\DateTime
    {
        return $this->getCriteria()[$name] ?? null ? new \DateTime($this->getCriteria()[$name], $timezone) : null;
    }

    public function getUser(): ?User
    {
        return $this->user;
    }

    public function getSubscriber(): Subscribers
    {
        return $this->subscriber;
    }

    public function getHeader(): array
    {
        $header = [];

        if ($this->getCriteria()['startDate'] ?? null) {
            $header[] =
                [
                    $this->translator->trans("table.date") . ":",
                    $this->getCriteria()['startDate'],
                    ' - ',
                    $this->getCriteria()['endDate']
                ]
            ;
        }

        if ($this->getCriteria()['serial'] ?? null) {
            $header[] = [
                $this->trans('finance_carwash') . ":",
                $this->getCarwash($this->getCriteria()['serial'])->getLongName()
            ];
        }

        if ($this->getCriteria()['search'] ?? null) {
            $header[] = [$this->getCriteria()['search']];
        }

        return $header;
    }

    protected function getCarwashCriteriaSns(?int $sn = null): ?array
    {
        $user = $this->getUser();
        if (is_numeric($sn)) {
            if (!$user->hasAccessToCarwashBySn($sn)) {
                return [];
            }
            return [$sn];
        }

        if ($user->isAdmin()) {
            $sns =
                $this->getSubscriber()
                ->getCarwashes()
                ->map(fn(Carwashes $cw) => $cw->getSerialNumber())
                ->toArray();
        } else {
            $sns =
                $user->getUserStartedCarwashes()
                ->map(fn(Carwashes $cw) => $cw->getSerialNumber())
                ->toArray();
        }


        sort($sns);
        return $sns;
    }


    protected function checkPermision(CMSubscription $subscription, array $roles = []): bool
    {

        $userSubscription = $this->getSubscriber()->getSubscription();
        if (!$userSubscription->isGreaterEqualThan($subscription)) {
            // nie raportuje gdy ktos ma subskrypcje free
            if ($userSubscription != CMSubscription::FREE) {
                $this->loggerRepository->warning($this->getSubscriber(), $this->getUser(), null, "REPORTS", "subscriber {$this->getSubscriber()->getName()} ma za mała subskrypcje $userSubscription->value < $subscription->value");
            }
            return false;
        }

        // gdy raport nie jest generowany przez uzytkownika pomijam sprawdzanie rol ()
        if (
            !is_null($this->getUser()) &&
            !$this->userGrantedChecker->isGrantedBatch(
                $this->getUser(),
                $roles
            )
        ) {
            $this->loggerRepository->warning($this->getSubscriber(), $this->getUser(), null, "REPORTS", "użytkownik {$this->getUser()->getEmail()} ma za małe uprawnienia do genracji raportu");
            return false;
        }
        return true;
    }

    protected function trans($nameOrg)
    {
        $lang = $this->getUser()?->getLanguage() ?? $this->getSubscriber()->getLanguage();
        /** @var Translator $translator */
        $translator = $this->translator;
        $translator->setLocale($lang->getLocale());
        return $translator->trans($nameOrg);
    }

    protected function getTimezone(): \DateTimeZone
    {
        $timeZone = $this->getUser()?->getTimezone() ?? $this->getSubscriber()->getTimezone();
        return new \DateTimeZone($timeZone->getLocation());
    }

    protected function getDeviceName(string $source, ?int $bayId = null): string
    {
        $name =  $this->trans("fiscal.source.$source") ;

        if ($bayId) {
            $name .= " #" . $bayId;
        }
        return $name;
    }

    protected function getCarwash(int $sn, bool $skipAccessCheck = false): Carwashes
    {
        // sprawdzam czy ten subskryber ma dostęp do myjni
        $carwash = $this->carwashesRepository->findOneBy([
                                                             'serialNumber' => $sn,
                                                             'subscriber' => $this->getSubscriber()]);
        if (is_null($carwash)) {
            throw new ReportException("Subscriber don't have access to this carwash");
        }

        // sprwdzam czy ten user ma dostęp do myjni, ale tylko pod warunkiem że robimy raport dla usera
        if (!$skipAccessCheck && $this->getUser() && !$this->getUser()->hasAccessToCarwashBySn($carwash->getSerialNumber())) {
            throw new ReportException("User don't have access to this carwash");
        }
        return $carwash;
    }

    public function download(FileExtention $fileExtention): ?string
    {
        return null;
    }


    protected function saveStreamToFile(FileExtention $fileType, $inputStream): string
    {
        $filePath = $this->getFilePath($fileType->value);
        // Otwórz strumień do zapisu do pliku
        $outputStream = fopen($filePath, 'wb');
        if ($outputStream === false) {
            throw new \RuntimeException("Nie można otworzyć pliku do zapisu: {$filePath}");
        }

        try {
            // Skopiuj dane ze strumienia wejściowego do pliku
            stream_copy_to_stream($inputStream, $outputStream);
        } finally {
            // Zamknij oba strumienie
            fclose($outputStream);
        }

        return $filePath;
    }
    private function getFilePath(string $fileType): string
    {
        $dir = sys_get_temp_dir() . DIRECTORY_SEPARATOR . 'cm-reports';
        if (!file_exists($dir)) {
            mkdir($dir);
            chmod($dir, 0777);
        }
        return $dir . DIRECTORY_SEPARATOR . uniqid() . ".$fileType";
    }

    public function getPdfOptions(): array
    {
        return [];
        //return [
        //    'orientation' => 'Landscape',
        //    'page-size' => 'A4',
        //    'margin-top' => '10mm',
        //    'margin-bottom' => '10mm',
        //    'margin-left' => '10mm',
        //    'margin-right' => '10mm'
        //];
    }
}

<?php

namespace App\Service\InvoiceApi\SelfInvoice;

use App\Entity\Subscribers;
use App\Entity\VatTax;
use App\Service\InvoiceApi\Dto\Request\InvoiceDto;
use App\Service\InvoiceApi\Dto\Request\PositionDto;
use App\Service\InvoiceApi\Dto\Response\InvoiceResponseDto;
use App\Service\InvoiceApi\Exceptions\NoMobilePaymentsToGenerateInvoiceException;
use App\Service\InvoiceApi\InvoiceApi;
use App\Service\InvoiceApi\InvoiceSenders\SelfInvoiceSender;
use App\Service\JrProcessService;
use App\Service\LoyalAppStatistics\LoyalAppStatistics;
use DateTime;
use DateTimeInterface;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Translation\Translator;
use Symfony\Contracts\Translation\TranslatorInterface;

class SelfInvoiceApiGenerator
{
    public function __construct(
        private readonly InvoiceApi $invoiceApi,
        private TranslatorInterface $translator,
        private EntityManagerInterface $em,
        private JrProcessService $jrProcessService,
        private SelfInvoiceSender $selfInvoiceSender,
        private LoyalAppStatistics $loyalAppStatistics,
    ) {
    }
    public function generateInvoice(
        Subscribers $reciver,
        Subscribers $owner,
        string $appName,
        DateTime $from,
        DateTime $to,
    ) {
        $params = [
            'owner' => $owner->getOwnerBkf(),
            'dateFrom' => $from->format('Y-m-d H:i:s'),
            'dateTo' => $to->format('Y-m-d H:i:s'),
            'type' => 'PAYMENT',
            'status' => 'confirmed',
            'itemsPerPage' => -1,
            'pageNumber' => 0,
        ];

        $transctions = json_decode($this->loyalAppStatistics->getUserTransactions($appName, null, $params), true);

        if ($transctions['totalItems'] === 0) {
            throw new NoMobilePaymentsToGenerateInvoiceException('No mobile payments to generate invoice.');
        }


        $translator = $this->translator;
        /** @var Translator $translator */
        $translator->setLocale($owner->getLanguage()->getLocale());

        $interval = $to->format('Y-m');

        $summary = $this->getSummary(
            $transctions
        );

        $positionsDto = $this->getPositions(
            $summary['summary'],
            $owner->getLanguage()->getLocale()
        );

        $invoiceDto = InvoiceDto::getSelfInvoice2(
            $reciver,
            $owner,
            $interval,
            $positionsDto,
            $summary
        );

        $invoice = $this->invoiceApi->generateInvoice($invoiceDto);
        $invoiceId = $invoice['id'];

        $this->selfInvoiceSender->sendSelfInvoiceEmail(
            $invoiceId,
            $owner
        );

        $this->sendSelfInvoicetoJr($invoiceDto, $invoiceId);

        return $invoiceDto;
    }

    private function getSummary($data)
    {
        $itemsFinal = [
            'sumPaymentValue' => 0,
            'sumPaymentValueAfterDiscount' => 0,
        ];
        $result = [];

        foreach ($data['items'] as $item) {
            $carwashKey = $item['carwash']['serial_number'];
            $value = (($item['value'] < 0) ? -$item['value'] : $item['value']);

            if (!isset($itemsFinal[$carwashKey])) {
                $itemsFinal[$carwashKey] = [
                    'carwashSerialNumber' =>  $item['carwash']['serial_number'],
                    'carwash' => $item['carwash'],
                    'paymentValue' => 0,
                    'paymentValueAfterDiscount' => 0,
                ];
            }

            $itemsFinal[$carwashKey]['paymentValue'] += $value;
            $itemsFinal[$carwashKey]['paymentValueAfterDiscount'] += $value * 0.9;

            $itemsFinal['sumPaymentValue'] += $value;
            $itemsFinal['sumPaymentValueAfterDiscount'] += $value * 0.9;

            $result[] = [
                'confirmationDate' => $item['confirmed_timestamp'],
                'status' => $item['status'],
                'carwashSerialNumber' => $item['carwash']['serial_number'],
                'paymentValue' => $value,
                'paymentValueAfterDiscount' => $value * 0.9,
            ];
        }

        ksort(
            $itemsFinal
        );

        usort(
            $result,
            fn($a, $b) => strtotime($a['confirmationDate']) - strtotime($b['confirmationDate'])
        );

        return ['summary' => $itemsFinal, 'results' => $result];
    }

    private function getPositions(array $summaryData, string $locale): ArrayCollection
    {
        $positions = new ArrayCollection();

        $vatTax = $this->getVatTax();
        $netGrossMultiply = $vatTax->getVatTaxNetToGrossMultiplier();

        foreach ($summaryData as $mobilePaymentSum) {
            if (!is_array($mobilePaymentSum)) {
                continue;
            }

            $netValue = $mobilePaymentSum['paymentValueAfterDiscount'] / $netGrossMultiply;
            $value = $mobilePaymentSum['paymentValueAfterDiscount'];
            $taxValue = $value - $netValue;

            $position = new PositionDto();
            $position
                ->setName($this->translator->trans('table.car-wash-service', locale: $locale) . ' ' . $mobilePaymentSum['carwash']['long_name'])
                ->setQuantity(1)
                ->setUnitPrice($netValue)
                ->setTax((int)$vatTax->getTaxValue())
                ->setTaxValue($taxValue)
                ->setTotal($value)
                ->setTotalWithoutTax($netValue);

            $positions->add($position);
        }

        return $positions;
    }

    public function getVatTax(): ?VatTax
    {
        return $this->em->getRepository(VatTax::class)->findOneBy([
            'taxKey' => '23'
        ]);
    }

    public function confirmSelfInvoice(
        InvoiceResponseDto $selfInvoice,
        string $userEmail,
        DateTime $confirmData,
    ): bool {
        $invoiceTmpFilePath = tempnam(sys_get_temp_dir(), 'cmSelfInvoice') . '.pdf';

        $fileStream = $this->invoiceApi->getInvoiceFileStream($selfInvoice->getId());

        $invoiceFileResourceTo = fopen($invoiceTmpFilePath, 'w');
        stream_copy_to_stream($fileStream, $invoiceFileResourceTo);
        fclose($fileStream);

        $invoiceFileResource = fopen($invoiceTmpFilePath, 'r');

        $inputParam = [
            'invoice_number' => $selfInvoice->getParsedInvoiceNumber(),
            'invoice_file' => $invoiceFileResource,
            'email_date' => $confirmData->format(DateTimeInterface::ATOM),
            'acceptator_username' => $userEmail,
            'invoice_beloyal' => 1
        ];

        $process = $this->jrProcessService->startProcess('ObiegFakturI2M', 50, $inputParam);

        if (!array_key_exists('incidents', $process)) {
            return false;
        }

        return true;
    }

    private function sendSelfInvoicetoJr(InvoiceDto $selfInvoice, int $invoiceId): bool
    {
        $crDate = new DateTime();
        $createDate = $crDate->format(DateTimeInterface::ATOM);
        $sumPaymentNett = $selfInvoice->getAdditionalData()['summary']['sumPaymentValueAfterDiscount'] / 1.23;

        $invoiceTmpFilePath = tempnam(sys_get_temp_dir(), 'cmSelfInvoice') . '.pdf';

        $fileStream = $this->invoiceApi->getInvoiceFileStream($invoiceId);

        $invoiceFileResourceTo = fopen($invoiceTmpFilePath, 'w');
        stream_copy_to_stream($fileStream, $invoiceFileResourceTo);
        fclose($fileStream);

        $invoiceFileResource = fopen($invoiceTmpFilePath, 'r');

        $inputParam = [
            'contractor' => $selfInvoice->getIssuer()->getName(),
            'contractor_nip' =>  $selfInvoice->getIssuer()->getVatId(),
            'payment_method' => 'przelew_terminowy',
            'invoice_number' => $selfInvoice->getNumberFormatted(),
            'invoice_paper' => '0',
            'invoice_currency' => 'PLN',
            'invoice_nett' =>  $sumPaymentNett,
            'invoice_receipt_date' => $createDate,
            'invoice_issue_date' => $createDate,
            'invoice_file' => $invoiceFileResource,
            'invoice_beloyal' => 1,
            'ST_I2M_FAKTUROWANIE' => [
                [
                    'invoice_cost' => 'Usługa mycia na myjni',
                    'invoice_department' => 'BL-Mycie',
                    'value' => $sumPaymentNett
                ],
            ]
        ];

        $process = $this->jrProcessService->startProcess('ObiegFakturI2M', 1, $inputParam);

        if (!array_key_exists('incidents', $process)) {
            return false;
        }

        return true;
    }
}

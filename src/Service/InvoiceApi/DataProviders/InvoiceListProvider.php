<?php

namespace App\Service\InvoiceApi\DataProviders;

use App\Service\InvoiceApi\Dto\Response\SelfInvoiceList\InvoiceDto;
use App\Service\InvoiceApi\Dto\Response\SelfInvoiceList\InvoiceListDto;
use DateTimeZone;
use Symfony\Component\Serializer\SerializerInterface;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;
use Symfony\Contracts\HttpClient\HttpClientInterface;

class InvoiceListProvider extends AbstractInvoiceApiProvider
{
    public function __construct(
        ParameterBagInterface $parameterBag,
        private readonly UrlGeneratorInterface $urlGenerator,
    ) {
        parent::__construct(parameterBag: $parameterBag);
    }



    public function getInvoiceList(
        string $invoiceType,
        int $externalIssuerId,
        DateTimeZone $timezone,
        ?string $downloadUrl = null
    ): array {
        $response = $this->http->request(
            'GET',
            "{$this->apiUrl}/api/invoices/list/{$invoiceType}/{$externalIssuerId}",
            [
                'headers' => [
                    'authorization' => 'Bearer ' . self::CM_USER_TOKEN
                ],
            ]
        );

        $data = json_decode($response->getBody()->getContents(), true);

        $invoices = [];

        foreach ($data as $dat) {
            $invoice = InvoiceDto::fromArray($dat, $timezone);

            $invoice->setDownloadUrl(
                $this->urlGenerator->generate(
                    $downloadUrl,
                    [
                        'id' => $invoice->getId()
                    ],
                    UrlGeneratorInterface::ABSOLUTE_URL
                )
            );

            $invoices[] = $invoice;
        }

        return $invoices;
    }

    public function getAllInvoiceList(
        string $invoiceType,
        DateTimeZone $timezone,
        ?int $page = null,
        ?int $perPage = null,
        ?bool $confirmed = null,
        ?string $downloadUrl = null
    ): array {
        $response = $this->http->request(
            'GET',
            "{$this->apiUrl}/api/invoices/all/list/{$invoiceType}",
            [
                'headers' => [
                    'authorization' => 'Bearer ' . self::CM_USER_TOKEN
                ],
                'query' => [
                    'page' => $page,
                    'perPage' => $perPage,
                    'confirmed' => $confirmed,
                ],
            ]
        );

        $data = json_decode($response->getBody()->getContents(), true);

        $invoices = [];

        foreach ($data['data'] as $dat) {
            $invoice = InvoiceDto::fromArray($dat, $timezone);

            $invoice->setDownloadUrl(
                $this->urlGenerator->generate(
                    $downloadUrl,
                    [
                        'id' => $invoice->getId()
                    ],
                    UrlGeneratorInterface::ABSOLUTE_URL
                )
            );

            $invoices[] = $invoice;
        }

        return ['data' => $invoices, 'total' => $data['total']];
    }
}

<?php

namespace App\Service\InvoiceApi\DataProviders;

use Carbon\Carbon;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;

class InvoiceConfirmedListProvider extends AbstractInvoiceApiProvider
{
    public function __construct(
        ParameterBagInterface $parameterBag
    ) {
        parent::__construct(parameterBag: $parameterBag);
    }

    public function getInvoiceConfirmedList(
        string $invoiceType,
    ): array {
        $response = $this->http->request(
            'GET',
            "{$this->apiUrl}/api/invoices/list/confirmation/{$invoiceType}?confirmed=0",
            [
                'headers' => [
                    'authorization' => 'Bearer ' . self::CM_USER_TOKEN
                ],
            ]
        );

        $invoiceData = json_decode($response->getBody()->getContents(), true);

        $invoices = [];

        $today = Carbon::today()->setTime(0, 0);

        foreach ($invoiceData as $data) {
            $invoiceDate = Carbon::parse($data['invoiceDate']);

            $diffInDays = $invoiceDate->diffInDays($today);

            if ($diffInDays >= 7) {
                $invoices[] = $data;
            }
        }

        return $invoices;
    }
}

<?php

namespace App\Service\InvoiceApi\DataProviders;

use App\Service\InvoiceApi\Dto\Response\InvoiceResponseDto;
use GuzzleHttp\Exception\ClientException;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\ExpressionLanguage\ExpressionLanguage;
use Symfony\Component\Serializer\SerializerInterface;

class InvoiceProvider extends AbstractInvoiceApiProvider
{
    public function __construct(
        ParameterBagInterface $parameterBag,
        private SerializerInterface $serializer
    ) {
        parent::__construct(parameterBag: $parameterBag);
    }

    public function getInvoiceData(int $invoiceId): ?InvoiceResponseDto
    {
        try {
            $response = $this->http->request(
                'GET',
                "{$this->apiUrl}/api/invoices/{$invoiceId}",
                [
                    'headers' => [
                        'authorization' => 'Bearer ' . self::CM_USER_TOKEN
                    ],
                ]
            );
        } catch (ClientException $e) {
            return null;
        }

        return $this->serializer->deserialize(
            $response->getBody()->getContents(),
            InvoiceResponseDto::class,
            'json'
        );
    }

    public function getInvoiceDataByUuid(string $uuid): ?InvoiceResponseDto
    {
        try {
            $response = $this->http->request(
                'GET',
                "{$this->apiUrl}/api/invoices/uuid/{$uuid}",
                [
                    'headers' => [
                        'authorization' => 'Bearer ' . self::CM_USER_TOKEN
                    ],
                ]
            );
        } catch (ClientException $e) {
            return null;
        }

        return $this->serializer->deserialize(
            $response->getBody()->getContents(),
            InvoiceResponseDto::class,
            'json'
        );
    }
}

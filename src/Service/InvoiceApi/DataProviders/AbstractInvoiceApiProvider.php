<?php

namespace App\Service\InvoiceApi\DataProviders;

use GuzzleHttp\Client;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;

abstract class AbstractInvoiceApiProvider
{
    protected Client $http;

    protected string $apiUrl;

    const CM_USER_TOKEN = 'eFCBNpE5CTqaG96ZBJfgtaH5Ru';

    const INVOICE_TYPE_SELF_INVOICE = 'self_invoice_be_loyal';
    const INVOICE_TYPE_CARD_TOP_UP = 'card_top_up';

    public function __construct(
        ParameterBagInterface $parameterBag
    ) {
        $this->http = new Client();
        $this->apiUrl = $parameterBag->get('invoice-api-url');
    }
}

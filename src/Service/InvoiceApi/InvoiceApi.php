<?php

namespace App\Service\InvoiceApi;

use App\Service\InvoiceApi\DataProviders\AbstractInvoiceApiProvider;
use App\Service\InvoiceApi\Dto\Request\InvoiceDto;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\HeaderUtils;
use Symfony\Component\HttpFoundation\StreamedResponse;
use DateTimeZone;
use DateTime;
use Symfony\Component\Serializer\SerializerInterface;

class InvoiceApi extends AbstractInvoiceApiProvider
{
    public function __construct(
        ParameterBagInterface $parameterBag,
        private SerializerInterface $serializer
    ) {
        parent::__construct(parameterBag: $parameterBag);
    }

    public function generateInvoice(InvoiceDto $invoice): array
    {
        $json = $this->serializer->serialize(
            $invoice,
            'json'
        );

        $response = $this->http->request(
            'POST',
            $this->apiUrl . '/api/invoices',
            [
                'headers' => [
                    'authorization' => 'Bearer ' . self::CM_USER_TOKEN
                ],
                'body' => $json,
            ]
        );

        return json_decode($response->getBody(), true);
    }

    public function getInvoiceFileResponse(
        string $originalFileName,
        int $invoiceId
    ): StreamedResponse {
        $inputStream = $this->getInvoiceFileStream(
            $invoiceId
        );

        $response = new StreamedResponse(function () use ($inputStream) {
            $outputStream = fopen('php://output', 'wb');

            stream_copy_to_stream($inputStream, $outputStream);
        });

        $disposition = HeaderUtils::makeDisposition(
            HeaderUtils::DISPOSITION_ATTACHMENT,
            $originalFileName
        );

        $response->headers->set('Content-Type', 'application/pdf');
        $response->headers->set('Content-Disposition', $disposition);
        //$response->headers->set('Content-Disposition', 'attachment; filename="' . $file->getOryginalFileName() . '"');
        return $response;
    }

    public function getInvoiceFileStream(int $invoiceId)
    {
        $url = "{$this->apiUrl}/api/invoices/{$invoiceId}/pdf";
        $opts = [
            'http' => [
                'method' => "GET",
                'header' => "Authorization: Bearer " . self::CM_USER_TOKEN
            ]
        ];

        $context = stream_context_create($opts);

        return fopen($url, 'rb', false, $context);
    }

    public function confirmSelfInvoice(
        int $invoiceId,
        string $userEmail,
        string $userIp,
        DateTime $confirmDate,
    ): ?int {
        $json = json_encode([
            "user_email" => $userEmail,
            "confirmation_time" => $confirmDate->format('Y-m-d H:i'),
            "ip" => $userIp
        ]);

        $response = $this->http->request(
            'PATCH',
            "{$this->apiUrl}/api/invoices/{$invoiceId}/confirm",
            [
                'headers' => [
                    'authorization' => 'Bearer ' . self::CM_USER_TOKEN
                ],
                'body' => $json,
            ]
        );

        $json = json_decode($response->getBody(), true);

        return $json['id'];
    }
}

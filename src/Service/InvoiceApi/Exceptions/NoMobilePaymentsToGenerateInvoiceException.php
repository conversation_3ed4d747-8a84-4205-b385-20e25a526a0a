<?php

namespace App\Service\InvoiceApi\Exceptions;

use App\Classes\Exception\ClientSideException;
use Throwable;

class NoMobilePaymentsToGenerateInvoiceException extends ClientSideException
{
    public function __construct(string $message = "", int $code = 0, Throwable $previous = null)
    {
        parent::__construct($message, $code, $previous, 'no_mobile_payments_to_generate_invoice');
    }
}

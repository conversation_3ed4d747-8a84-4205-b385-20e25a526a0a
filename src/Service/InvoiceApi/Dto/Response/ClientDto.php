<?php

namespace App\Service\InvoiceApi\Dto\Response;

class ClientDto
{
    private int $externalId;
    private string $name;
    private string $address;
    private string $zipCode;

    private string $city;

    private string $country;

    private string $vatId;

    public function getExternalId(): int
    {
        return $this->externalId;
    }

    public function setExternalId(int $externalId): ClientDto
    {
        $this->externalId = $externalId;
        return $this;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function setName(string $name): ClientDto
    {
        $this->name = $name;
        return $this;
    }

    public function getAddress(): string
    {
        return $this->address;
    }

    public function setAddress(string $address): ClientDto
    {
        $this->address = $address;
        return $this;
    }

    public function getZipCode(): string
    {
        return $this->zipCode;
    }

    public function setZipCode(string $zipCode): ClientDto
    {
        $this->zipCode = $zipCode;
        return $this;
    }

    public function getCity(): string
    {
        return $this->city;
    }

    public function setCity(string $city): ClientDto
    {
        $this->city = $city;
        return $this;
    }

    public function getCountry(): string
    {
        return $this->country;
    }

    public function setCountry(string $country): ClientDto
    {
        $this->country = $country;
        return $this;
    }

    public function getVatId(): string
    {
        return $this->vatId;
    }

    public function setVatId(string $vatId): ClientDto
    {
        $this->vatId = $vatId;
        return $this;
    }
}

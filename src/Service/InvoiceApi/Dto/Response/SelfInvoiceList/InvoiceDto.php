<?php

namespace App\Service\InvoiceApi\Dto\Response\SelfInvoiceList;

use DateTime;
use DateTimeZone;

class InvoiceDto
{
    public function __construct(
        private int $id,
        private string $number,
        private DateTime $issuanceDate,
        private DateTime $serviceDate,
        private DateTime $createdAt,
        private ?string $currency,
        private string $price,
        private string $priceNet,
        private string $period,
        private string $type,
        private ?string $clientName,
        private ?string $clientVatId,
        private ?string $issuerName,
        private ?string $issuerVatId,
        private ?int $clientId,
        private ?string $confirmedByUser,
        private ?DateTime $confirmationDate,
        private string $downloadUrl = ''
    ) {
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function setId(int $id): InvoiceDto
    {
        $this->id = $id;
        return $this;
    }

    public function getNumber(): string
    {
        return $this->number;
    }

    public function setNumber(string $number): InvoiceDto
    {
        $this->number = $number;
        return $this;
    }

    public function getIssuanceDate(): DateTime
    {
        return $this->issuanceDate;
    }

    public function setIssuanceDate(DateTime $issuanceDate): InvoiceDto
    {
        $this->issuanceDate = $issuanceDate;
        return $this;
    }

    public function getPrice(): string
    {
        return $this->price;
    }

    public function setPrice(string $price): InvoiceDto
    {
        $this->price = $price;
        return $this;
    }

    public function getPriceNet(): string
    {
        return $this->priceNet;
    }

    public function setPriceNet(string $priceNet): InvoiceDto
    {
        $this->priceNet = $priceNet;
        return $this;
    }

    public function getPeriod(): string
    {
        return $this->period;
    }

    public function setPeriod(string $period): InvoiceDto
    {
        $this->period = $period;
        return $this;
    }

    public function getConfirmedByUser(): ?string
    {
        return $this->confirmedByUser;
    }

    public function setConfirmedByUser(?string $confirmedByUser): InvoiceDto
    {
        $this->confirmedByUser = $confirmedByUser;
        return $this;
    }

    public function getConfirmationDate(): ?DateTime
    {
        return $this->confirmationDate;
    }

    public function setConfirmationDate(?DateTime $confirmationDate): InvoiceDto
    {
        $this->confirmationDate = $confirmationDate;
        return $this;
    }

    public function getDownloadUrl(): string
    {
        return $this->downloadUrl;
    }

    public function setDownloadUrl(string $downloadUrl): InvoiceDto
    {
        $this->downloadUrl = $downloadUrl;
        return $this;
    }

    public function getType(): string
    {
        return $this->type;
    }

    public function setType(string $type): InvoiceDto
    {
        $this->type = $type;
        return $this;
    }

    public function getClientName(): ?string
    {
        return $this->clientName;
    }

    public function setClientName(?string $clientName): InvoiceDto
    {
        $this->clientName = $clientName;
        return $this;
    }

    public function getClientId(): ?int
    {
        return $this->clientId;
    }

    public function setClientId(?int $clientId): InvoiceDto
    {
        $this->clientId = $clientId;
        return $this;
    }

    public function getServiceDate(): DateTime
    {
        return $this->serviceDate;
    }

    public function setServiceDate(DateTime $serviceDate): InvoiceDto
    {
        $this->serviceDate = $serviceDate;
        return $this;
    }

    public function getCreatedAt(): DateTime
    {
        return $this->createdAt;
    }

    public function setCreatedAt(DateTime $createdAt): InvoiceDto
    {
        $this->createdAt = $createdAt;
        return $this;
    }

    public function getClientVatId(): ?string
    {
        return $this->clientVatId;
    }

    public function setClientVatId(?string $clientVatId): InvoiceDto
    {
        $this->clientVatId = $clientVatId;
        return $this;
    }

    public function getCurrency(): ?string
    {
        return $this->currency;
    }

    public function setCurrency(?string $currency): InvoiceDto
    {
        $this->currency = $currency;
        return $this;
    }

    public function getIssuerName(): ?string
    {
        return $this->issuerName;
    }

    public function setIssuerName(?string $issuerName): InvoiceDto
    {
        $this->issuerName = $issuerName;
        return $this;
    }

    public function getIssuerVatId(): ?string
    {
        return $this->issuerVatId;
    }

    public function setIssuerVatId(?string $issuerVatId): InvoiceDto
    {
        $this->issuerVatId = $issuerVatId;
        return $this;
    }

    public static function fromArray(
        array $data,
        DateTimeZone $timezone
    ): ?InvoiceDto {
        $confirmedByUser = $data['confirmation'] !== null ? $data['confirmation']['userEmail'] : null;
        $confirmationDate = $data['confirmation'] !== null
            ? (new DateTime($data['confirmation']['ctime']))->setTimezone($timezone)
            : null;
        $invoiceDate = $data['invoiceDate'] !== null
            ? (new DateTime($data['invoiceDate']))->setTimezone($timezone)
            : null;

        $period = $data['serviceDate'] !== null
            ? (new DateTime($data['serviceDate']))->format('Y-m')
            : null;

        $serviceData = $data['serviceDate'] !== null
            ? (new DateTime($data['serviceDate']))->setTimezone($timezone)
            : null;

        $createdAt = $data['createdAt'] !== null
            ? (new DateTime($data['createdAt']))->setTimezone($timezone)
            : null;

        return new InvoiceDto(
            id: $data['id'],
            number: $data['parsedInvoiceNumber'],
            issuanceDate: $invoiceDate,
            serviceDate: $serviceData,
            createdAt: $createdAt,
            currency: $data['currency'],
            price: $data['total'],
            priceNet: $data['totalNet'],
            period: $period,
            type: $data['type'],
            clientName: $data['client']['name'] ?? null,
            clientVatId: $data['client']['vatId'] ?? null,
            clientId: $data['client']['externalId'] ?? null,
            confirmedByUser: $confirmedByUser,
            confirmationDate: $confirmationDate,
            issuerName: $data['issuer']['name'] ?? null,
            issuerVatId: $data['issuer']['vatId'] ?? null,
        );
    }
}

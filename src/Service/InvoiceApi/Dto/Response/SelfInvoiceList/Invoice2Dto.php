<?php

namespace App\Service\InvoiceApi\Dto\Response\SelfInvoiceList;

use DateTime;
use DateTimeZone;

class Invoice2Dto
{
    private int $id;
    private ?string $parsedInvoiceNumber;
    private DateTime $invoiceDate;
    private DateTime $serviceDate;
    private DateTime $createdAt;
    private ?string $currency;
    private ?string $total;
    private ?string $totalNet;
    private ?string $period;
    private ?string $type;
    private ?string $confirmedByUser;
    private ?DateTime $confirmationDate;
    private ?IssuerDto $issuer;
    private ?ClientDto $client;

    public function getId(): int
    {
        return $this->id;
    }

    public function setId(int $id): Invoice2Dto
    {
        $this->id = $id;
        return $this;
    }

    public function getParsedInvoiceNumber(): ?string
    {
        return $this->parsedInvoiceNumber;
    }

    public function setParsedInvoiceNumber(?string $parsedInvoiceNumber): Invoice2Dto
    {
        $this->parsedInvoiceNumber = $parsedInvoiceNumber;
        return $this;
    }

    public function getInvoiceDate(): DateTime
    {
        return $this->invoiceDate;
    }

    public function setInvoiceDate(DateTime $invoiceDate): Invoice2Dto
    {
        $this->invoiceDate = $invoiceDate;
        return $this;
    }

    public function getServiceDate(): DateTime
    {
        return $this->serviceDate;
    }

    public function setServiceDate(DateTime $serviceDate): Invoice2Dto
    {
        $this->serviceDate = $serviceDate;
        return $this;
    }

    public function getCreatedAt(): DateTime
    {
        return $this->createdAt;
    }

    public function setCreatedAt(DateTime $createdAt): Invoice2Dto
    {
        $this->createdAt = $createdAt;
        return $this;
    }

    public function getCurrency(): ?string
    {
        return $this->currency;
    }

    public function setCurrency(?string $currency): Invoice2Dto
    {
        $this->currency = $currency;
        return $this;
    }

    public function getTotal(): ?string
    {
        return $this->total;
    }

    public function setTotal(?string $total): Invoice2Dto
    {
        $this->total = $total;
        return $this;
    }

    public function getTotalNet(): ?string
    {
        return $this->totalNet;
    }

    public function setTotalNet(?string $totalNet): Invoice2Dto
    {
        $this->totalNet = $totalNet;
        return $this;
    }

    public function getPeriod(): ?string
    {
        return $this->period;
    }

    public function setPeriod(?string $period): Invoice2Dto
    {
        $this->period = $period;
        return $this;
    }

    public function getType(): ?string
    {
        return $this->type;
    }

    public function setType(?string $type): Invoice2Dto
    {
        $this->type = $type;
        return $this;
    }

    public function getConfirmedByUser(): ?string
    {
        return $this->confirmedByUser;
    }

    public function setConfirmedByUser(?string $confirmedByUser): Invoice2Dto
    {
        $this->confirmedByUser = $confirmedByUser;
        return $this;
    }

    public function getConfirmationDate(): ?DateTime
    {
        return $this->confirmationDate;
    }

    public function setConfirmationDate(?DateTime $confirmationDate): Invoice2Dto
    {
        $this->confirmationDate = $confirmationDate;
        return $this;
    }

    public function getIssuer(): ?IssuerDto
    {
        return $this->issuer;
    }

    public function setIssuer(?IssuerDto $issuer): Invoice2Dto
    {
        $this->issuer = $issuer;
        return $this;
    }

    public function getClient(): ?ClientDto
    {
        return $this->client;
    }

    public function setClient(?ClientDto $client): Invoice2Dto
    {
        $this->client = $client;
        return $this;
    }
}

<?php

namespace App\Service\InvoiceApi\Dto\Response\SelfInvoiceList;

class ClientDto
{
    private ?string $name;
    private ?string $vatId;
    private ?int $externalId;

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(?string $name): ClientDto
    {
        $this->name = $name;
        return $this;
    }

    public function getVatId(): ?string
    {
        return $this->vatId;
    }

    public function setVatId(?string $vatId): ClientDto
    {
        $this->vatId = $vatId;
        return $this;
    }

    public function getExternalId(): ?int
    {
        return $this->externalId;
    }

    public function setExternalId(?int $externalId): ClientDto
    {
        $this->externalId = $externalId;
        return $this;
    }
}

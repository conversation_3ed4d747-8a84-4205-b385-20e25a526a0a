<?php

namespace App\Service\InvoiceApi\Dto\Response;

use DateTimeImmutable;
use Symfony\Component\Validator\Constraints as Assert;

class ConfirmationDto
{
    private ?DateTimeImmutable $confirmationTime = null;

    #[Assert\NotBlank]
    #[Assert\NotNull]
    private ?string $userEmail = null;

    #[Assert\NotBlank]
    #[Assert\NotNull]
    private ?string $ip = null;

    public function getConfirmationTime(): ?DateTimeImmutable
    {
        return $this->confirmationTime;
    }

    public function setConfirmationTime(?DateTimeImmutable $confirmationTime): ConfirmationDto
    {
        $this->confirmationTime = $confirmationTime;
        return $this;
    }

    public function getUserEmail(): ?string
    {
        return $this->userEmail;
    }

    public function setUserEmail(?string $userEmail): ConfirmationDto
    {
        $this->userEmail = $userEmail;
        return $this;
    }

    public function getIp(): ?string
    {
        return $this->ip;
    }

    public function setIp(?string $ip): ConfirmationDto
    {
        $this->ip = $ip;
        return $this;
    }
}

<?php

namespace App\Service\InvoiceApi\Dto\Response;

/**
 */
class InvoiceResponseDto
{
    private int $id;
    private ?string $parsedInvoiceNumber = null;
    private ?IssuerDto $issuer = null;
    private ?ClientDto $client = null;
    private float $total;
    private string $currency;
    private string $invoiceFullPath;
    private string $invoiceFileName;
    private string $invoiceDate;
    private ?string $fileName;
    private string $uuid;
    private ?ConfirmationDto $confirmation = null;

    public function getParsedInvoiceNumber(): ?string
    {
        return $this->parsedInvoiceNumber;
    }

    public function setParsedInvoiceNumber(?string $parsedInvoiceNumber): InvoiceResponseDto
    {
        $this->parsedInvoiceNumber = $parsedInvoiceNumber;
        return $this;
    }

    public function getTotal(): float
    {
        return $this->total;
    }

    public function setTotal(string $total): InvoiceResponseDto
    {
        $this->total = (float)$total;
        return $this;
    }

    public function getCurrency(): string
    {
        return $this->currency;
    }

    public function setCurrency(string $currency): InvoiceResponseDto
    {
        $this->currency = $currency;
        return $this;
    }

    public function getIssuer(): ?IssuerDto
    {
        return $this->issuer;
    }

    public function setIssuer(?IssuerDto $issuer): InvoiceResponseDto
    {
        $this->issuer = $issuer;
        return $this;
    }

    public function getInvoiceFullPath(): string
    {
        return $this->invoiceFullPath;
    }

    public function setInvoiceFullPath(string $invoiceFullPath): InvoiceResponseDto
    {
        $this->invoiceFullPath = $invoiceFullPath;
        return $this;
    }

    public function getInvoiceFileName(): string
    {
        return $this->invoiceFileName;
    }

    public function setInvoiceFileName(string $invoiceFileName): InvoiceResponseDto
    {
        $this->invoiceFileName = $invoiceFileName;
        return $this;
    }


    public function getInvoiceDate(): string
    {
        return $this->invoiceDate;
    }

    public function setInvoiceDate(string $invoiceDate): InvoiceResponseDto
    {
        $this->invoiceDate = $invoiceDate;
        return $this;
    }

    public function getClient(): ?ClientDto
    {
        return $this->client;
    }

    public function setClient(?ClientDto $client): InvoiceResponseDto
    {
        $this->client = $client;
        return $this;
    }

    public function getFileName(): ?string
    {
        return $this->fileName;
    }

    public function setFileName(?string $fileName): InvoiceResponseDto
    {
        $this->fileName = $fileName;
        return $this;
    }

    public function getConfirmation(): ?ConfirmationDto
    {
        return $this->confirmation;
    }

    public function setConfirmation(?ConfirmationDto $confirmation): InvoiceResponseDto
    {
        $this->confirmation = $confirmation;
        return $this;
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function setId(int $id): InvoiceResponseDto
    {
        $this->id = $id;
        return $this;
    }

    public function getUuid(): string
    {
        return $this->uuid;
    }

    public function setUuid(string $uuid): InvoiceResponseDto
    {
        $this->uuid = $uuid;
        return $this;
    }
}

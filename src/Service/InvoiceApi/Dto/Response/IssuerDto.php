<?php

namespace App\Service\InvoiceApi\Dto\Response;

class IssuerDto
{
    private int $externalId;

    private string $name;

    private string $address;

    private string $zipCode;

    private string $city;

    private string $country;

    private string $vatId;

    private ?string $logo;

    private ?string $state = null;

    private ?string $bankAccountNumber = null;

    public function getExternalId(): int
    {
        return $this->externalId;
    }

    public function setExternalId(int $externalId): IssuerDto
    {
        $this->externalId = $externalId;
        return $this;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function setName(string $name): IssuerDto
    {
        $this->name = $name;
        return $this;
    }

    public function getAddress(): string
    {
        return $this->address;
    }

    public function setAddress(string $address): IssuerDto
    {
        $this->address = $address;
        return $this;
    }

    public function getZipCode(): string
    {
        return $this->zipCode;
    }

    public function setZipCode(string $zipCode): IssuerDto
    {
        $this->zipCode = $zipCode;
        return $this;
    }

    public function getCity(): string
    {
        return $this->city;
    }

    public function setCity(string $city): IssuerDto
    {
        $this->city = $city;
        return $this;
    }

    public function getCountry(): string
    {
        return $this->country;
    }

    public function setCountry(string $country): IssuerDto
    {
        $this->country = $country;
        return $this;
    }

    public function getVatId(): string
    {
        return $this->vatId;
    }

    public function setVatId(string $vatId): IssuerDto
    {
        $this->vatId = $vatId;
        return $this;
    }

    public function getLogo(): ?string
    {
        return $this->logo;
    }

    public function setLogo(?string $logo): IssuerDto
    {
        $this->logo = $logo;
        return $this;
    }

    public function getState(): ?string
    {
        return $this->state;
    }

    public function setState(?string $state): IssuerDto
    {
        $this->state = $state;
        return $this;
    }

    public function getBankAccountNumber(): ?string
    {
        return $this->bankAccountNumber;
    }

    public function setBankAccountNumber(?string $bankAccountNumber): IssuerDto
    {
        $this->bankAccountNumber = $bankAccountNumber;
        return $this;
    }
}

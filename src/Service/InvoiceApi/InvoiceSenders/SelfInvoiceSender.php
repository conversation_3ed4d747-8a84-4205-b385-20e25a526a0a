<?php

namespace App\Service\InvoiceApi\InvoiceSenders;

use App\Entity\Currency;
use App\Entity\Subscribers;
use App\Entity\User;
use App\Service\Email\EmailService;
use App\Service\InvoiceApi\DataProviders\InvoiceProvider;
use App\Service\InvoiceApi\Dto\Response\InvoiceResponseDto;
use App\Service\InvoiceApi\InvoiceApi;
use Carbon\Carbon;
use DateTime;
use DateTimeZone;
use Doctrine\ORM\EntityManagerInterface;
use Exception;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Contracts\Translation\TranslatorInterface;

class SelfInvoiceSender
{
    private array $invoiceConfirmationEmailsCopy;
    private string $cmApiUrl;

    public function __construct(
        private InvoiceProvider $invoiceProvider,
        private InvoiceApi $invoiceApi,
        private EntityManagerInterface $em,
        private ParameterBagInterface $parameterBag,
        private EmailService $emailService,
        private TranslatorInterface $translator,
    ) {
        $this->invoiceConfirmationEmailsCopy = $this->parameterBag->get('self_invoice_confirmation_copy');
        $this->cmApiUrl = $parameterBag->get('web_url');
    }
    public function sendSelfInvoiceEmail(
        int $invoiceId,
        Subscribers $subscriber
    ): void {
        $languageLocale  = $subscriber->getLanguage()->getLocale();
        $email = $subscriber->getEmail();

        $invoice = $this->invoiceProvider->getInvoiceData($invoiceId);

        if (null === $invoice) {
            throw new Exception('Could not find invoice with given id');
        }

        $issuer = $invoice->getIssuer();

        if ($issuer->getExternalId() !== $subscriber->getId()) {
            throw new Exception('Access denied');
        }

        $attachments = $this->prepareAttachments($invoiceId, $invoice->getInvoiceFileName());

        $currency = $this->em->getRepository(Currency::class)->findOneBy(['code' => $invoice->getCurrency()]);

        $this->emailService->sendEmail(
            template: 'Email/self_invoice/mail_content.html.twig',
            data: [
                'issuenceDate' => Carbon::parse($invoice->getInvoiceDate()),
                'number' => $invoice->getParsedInvoiceNumber(),
                'price' => $invoice->getTotal(),
                'currencySymbol' => $currency->getSymbol(),
                'name' => $invoice->getIssuer()->getName(),
                'vatId' => $invoice->getIssuer()->getVatId(),
                'confirmUrl' => "{$this->cmApiUrl}/cm/self_invoice/confirm_dialog/{$invoice->getUuid()}",
            ],
            title: $this->translator->trans('invoices.issued', locale: $languageLocale) . ' - BE Loyal',
            emails: array_merge([$email], $this->invoiceConfirmationEmailsCopy),
            language: $languageLocale,
            attachments: $attachments,
            dealer: $subscriber,
        );
    }

    public function sendConfirmationEmail(
        string $ownerEmail,
        InvoiceResponseDto $invoice,
        string $userEmail,
        DateTime $confirmDate
    ) {
        $confirmEmail = $userEmail;

        $confirmDate = $confirmDate->format('Y-m-d H:i');
        $invoiceNumber = $invoice->getParsedInvoiceNumber();

        $this->emailService->sendEmail(
            template: 'Email/self_invoice/confirm_bl_invoice.html.twig',
            data: [
                'ownerEmail' => $ownerEmail,
                'confirmEmail' => $confirmEmail,
                'confirmDate' => $confirmDate,
                'invoiceNumber' => $invoiceNumber
            ],
            title: $this->translator->trans('self-invoice-confirmation.email-title', locale: 'pl'),
            emails: array_merge([$ownerEmail], $this->invoiceConfirmationEmailsCopy),
            language: 'pl',
        );
    }

    private function prepareAttachments(int $invoiceId, string $fileName): array
    {
        $invoiceTmpFilePath = tempnam(sys_get_temp_dir(), 'cmSelfInvoice') . '.pdf';

        $fileStream = $this->invoiceApi->getInvoiceFileStream($invoiceId);

        $invoiceFileResourceTo = fopen($invoiceTmpFilePath, 'w');
        stream_copy_to_stream($fileStream, $invoiceFileResourceTo);
        fclose($fileStream);

        return [$fileName => $invoiceTmpFilePath];
    }
}

<?php

namespace App\Service;

use App\Entity\User;
use Symfony\Component\Security\Core\Authentication\Token\UsernamePasswordToken;
use Symfony\Component\Security\Core\Role\RoleHierarchyInterface;

/**
 * Class to check if role is granted for selected user
 */
class UserGrantedChecker
{
    public function __construct(private RoleHierarchyInterface $hierarchy)
    {
    }

    /**
     * Check if role is granted for selected user
     * This method is used to check if other user than logged has permission
     * Use privileges inheritance
     *
     * @param $user
     * @param $role
     * @return bool
     */
    public function isGranted(User $user, $role)
    {
        $roles = $this->hierarchy->getReachableRoleNames($user->getRoles());
        return in_array($role, $roles);
    }

    public function isGrantedBatch(User $user, $roles)
    {
        $user_roles = $this->hierarchy->getReachableRoleNames($user->getRoles());

        foreach ($roles as $role) {
            if (!in_array($role, $user_roles)) {
                return false;
            }
        }

        return true;
    }
}

<?php

namespace App\Service\Email;

use App\Entity\Subscribers;
use App\Repository\LoggerRepository;
use App\Repository\SubscribersRepository;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\Mailer\Exception\TransportExceptionInterface;
use Symfony\Component\Mailer\MailerInterface;
use Twig\Environment;
use Symfony\Component\Mime\Address;
use Symfony\Component\Mime\Email;

use function Sentry\captureException;

class EmailService
{
    public const MAILER_FROM = '<EMAIL>';
    public const DEFAULT_TEMPLATE = 'Email/base.html.twig';

    private string $projectDir;

    public function __construct(
        private Environment $templating,
        private MailerInterface $mailer,
        private ParameterBagInterface $parameterBag,
        private SubscribersRepository $subsciberRepository,
        private LoggerRepository $loggerRepository,
    ) {
        $this->projectDir = $this->parameterBag->get('kernel.project_dir');
    }

    public function sendEmail(
        ?string $template,
        array $data,
        string $title,
        array $emails,
        string $language = 'en',
        ?array $attachments = null,
        ?Subscribers $dealer = null,
    ) {
        $message = new Email();

        $this->loggerRepository->debug(
            null,
            null,
            null,
            source: self::class,
            message: 'new email to address: ' . implode(',', $emails) . ' - ' . $title
        );

        if (is_null($dealer)) {
            $dealer = $this->subsciberRepository->findOneBy(['ownerBkf' => 43141]); //I2M
        }

        $replyTo = $dealer->getEmail() ?? self::MAILER_FROM;

        if ($template === null) {
            $template = self::DEFAULT_TEMPLATE;
        }

        if (is_array($attachments) && !empty($attachments)) {
            foreach ($attachments as $fileName => $file) {
                $message->attachFromPath($file, !is_numeric($fileName) ? $fileName : null);
            }
        }

        if (!empty($dealer->getLogo())) {
            $img = str_replace('data:image/png;base64,', '', $dealer->getLogo());
            $message->embed(base64_decode($img), 'logo.png', 'image/png');
        } else {
            $message->embedFromPath($this->projectDir .  '/public/assets/i2m/images/mail/cm-small.png', 'logo.png');
        }

        $message->embedFromPath(
            $this->projectDir . '/public/assets/i2m/images/badges/en/appstore.png',
            'appStoreLogo.png'
        );
        $message->embedFromPath(
            $this->projectDir .  '/public/assets/i2m/images/badges/en/googleplay.png',
            'googlePlayLogo.png'
        );
        $message->embedFromPath(
            $this->projectDir .  '/public/assets/i2m/images/mail/link-fb.png',
            'fbIcon.png'
        );

        $message
            ->html(
                $this->templating->render(
                    $template,
                    [
                        'data' => $data,
                        'dealer' => $dealer,
                        'assets' => [
                            'android' => 'https://play.google.com/store/apps/details?id=com.bkf.carwash_manager',
                            'apple' => 'https://apps.apple.com/pl/app/carwash-manager/id1560935824',
                        ],
                        'language' => $language,
                    ]
                )
            )
            ->subject($title)
            ->from(new Address(self::MAILER_FROM))
            ->replyTo(new Address($replyTo));

        $status = true;
        foreach ($emails as $email) {
            try {
                $message->to(new Address($email));
                $this->mailer->send($message);
            } catch (TransportExceptionInterface $e) {
                $status &= false;
                captureException($e);
            }
        }

        if ($status) {
            $this->loggerRepository->debug(
                null,
                null,
                null,
                source: self::class,
                message: 'new email send to address: ' . implode(',', $emails) . ' - ' . $title
            );
        }
    }
}

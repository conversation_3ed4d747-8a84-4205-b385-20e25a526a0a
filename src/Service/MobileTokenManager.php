<?php

namespace App\Service;

use App\Entity\MobileToken;
use App\Entity\User;
use DateTimeImmutable;
use Doctrine\ORM\EntityManagerInterface;

class MobileTokenManager
{
    private EntityManagerInterface $entityManager;

    public function __construct(EntityManagerInterface $entityManager)
    {
        $this->entityManager = $entityManager;
    }

    public function storeUserMobileToken(User $user, string $tokenString, string $deviceInfo = ''): void
    {
        $token = $this->findUserToken($user, $tokenString);

        if ($token === null) {
            $token = $this->createNewMobileToken($user, $tokenString, $deviceInfo);
        }

        $token->setUpdatedAt(new DateTimeImmutable());
        $user->addMobileToken($token);
        $this->entityManager->persist($token);
        $this->entityManager->flush();
    }

    public function removeUserMobileToken(User $user, string $tokenString): void
    {
        $token = $this->findUserToken($user, $tokenString);

        if ($token === null) {
            return;
        }

        $user->removeMobileToken($token);
        $this->entityManager->remove($token);
        $this->entityManager->flush();
    }

    public function removeMobileTokenByTokenString(string $tokenString): void
    {
        $token = $this->entityManager
            ->getRepository(MobileToken::class)
            ->findOneBy(['token' => $tokenString]);

        if ($token === null) {
            return;
        }

        $user = $token->getUser();

        $user->removeMobileToken($token);
        $this->entityManager->remove($token);
    }

    public function flush(): void
    {
        $this->entityManager->flush();
    }

    private function findUserToken(User $user, string $tokenString): ?MobileToken
    {
        return $this->entityManager
            ->getRepository(MobileToken::class)
            ->findOneBy(['user' => $user, 'token' => $tokenString]);
    }

    private function createNewMobileToken(User $user, string $tokenString, string $deviceInfo = ''): MobileToken
    {
        return (new MobileToken())
            ->setUser($user)
            ->setToken($tokenString)
            ->setMobileDevice($deviceInfo);
    }
}

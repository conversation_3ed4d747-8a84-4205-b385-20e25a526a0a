<?php

namespace App\Service\Subscription;

use App\Entity\Enum\SubscriptionCarwashType;
use App\Entity\Enum\SubscriptionStatus;
use I2m\StandardTypes\Enum\CMSubscription;
use App\Entity\Invoices;
use App\Entity\Subscribers;
use App\Entity\Subscription\Invoice;
use App\Entity\Subscription\OwnerSubscriptionCarwash;
use App\Entity\Subscription\OwnerSubscriptionPayment;
use App\Entity\User;
use App\Repository\SubscribersRepository;
use App\Repository\Subscription\InvoiceRepository;
use App\Repository\Subscription\OwnerSubscriptionCarwashRepository;
use App\Repository\Subscription\SubscriptionPackagesRepository;
use App\Service\Invoice\Model\Position;
use I2m\Invoices\Enum\InvoiceGeneratorType;
use I2m\Invoices\Enum\InvoiceKindType;
use I2m\Invoices\Enum\PaymentStatus;
use I2m\Invoices\Enum\PaymentType;
use I2m\Invoices\Service\InvoiceGenerator\InvoiceGeneratorFactory;
use I2m\Invoices\Service\InvoiceService;
use I2m\StandardTypes\Enum\Country;
use I2m\StandardTypes\Enum\Currency;
use Symfony\Contracts\Translation\TranslatorInterface;

use function Sentry\captureMessage;

class SubscriptionInvoiceService
{
    private const CATEGORY_CM = 51449;


    private const TEST_CARWASHES = [
        36814, //deia test
        43141, //i2m sp. z o.o.
    ];

    private const EXCLUDE_DEALER_INVOICING = [
        7280, // myjnie bkf
        18949, // BP
        43908, // shell
    ];

    private const BKF_SPECIAL_CLIENTS = [
        16161, // udo
        24032, // udo
    ];


    private const FAKTUROWNIA_CONFIG = [

            "token" => "XDa-ju-D9j2R663tbAvw",
            "url" => "https://ebkf.fakturownia.pl/",
    ];

    public function __construct(
        private InvoiceRepository $subscriptionInvoiceRepository,
        private SubscribersRepository $subscribersRepository,
        private TranslatorInterface $translator,
        private InvoiceService $invoiceService,
        private InvoiceGeneratorFactory $invoiceGenerator,
        private OwnerSubscriptionCarwashRepository $subscriptionCarwashRepository,
        private SubscriptionPackagesRepository $packagesRepository,
    ) {
    }

    public function generateForClient(OwnerSubscriptionPayment $osp, bool $send = false)
    {
        $kind = in_array($osp->getStatus(), [SubscriptionStatus::PAID]) ?
            InvoiceKindType::vat :
            InvoiceKindType::proforma ;

        if (empty($osp->getGrossValue())) {
            $this->log("dla subskrypcji {$osp->getId()} jest 0 {$osp->getCurrency()->symbol()} do zapłaty");
            return null;
        }

        // jesli sprzedaz do dilera, wtedy nie wystawiam FV
        if ($osp->getSubscriber()->getDealer()) {
            $this->log(
                "dla subskrypcji {$osp->getId()} " .
                ", zakupionej przez klienta {$osp->getSubscriber()->getName()} nie wystawiam FV" .
                ", ponieważ sprzedaż przez dilera"
            );
            return null;
        }


        // dla rynku rosyjskiego, i białoruskiego nie wystawiam FV zw na sakncje
        if (in_array($osp->getSubscriber()->getCountry(), [Country::RU, Country::BY])) {
            $this->log(
                "dla subskrypcji {$osp->getId()} " .
                ", zakupionej przez klienta {$osp->getSubscriber()->getName()} nie wystawiam FV" .
                ", ponieważ jest z BY lu RU"
            );
            return null;
        }

        if ($kind == InvoiceKindType::vat) {
            if ($osp->getInvoice()?->getKind() === InvoiceKindType::vat) {
                $this->log("dla subskrypcji {$osp->getId()} jest juz wystawiona FV");
                return null;
            }
            $existedInvoice = $osp->getInvoice();
            if ($existedInvoice?->getKind() === InvoiceKindType::proforma) {
                $this->log("dla subskrypcji {$osp->getId()} wystawiam dokument na podstawie {$existedInvoice->getNumber()}");
                $this->invoiceFromProforma($existedInvoice, $osp);
                return null;
            }
        }

        $invoice = $this->invoiceFromSubscription($osp, $kind);
        $osp->setInvoice($invoice);
        foreach ($osp->getItem() as $item) {
            $item->setInvoice($invoice->getNumber());
            $this->subscriptionCarwashRepository->save($item);
        }

        if ($send) {
            $this->send($invoice);
        }

        return  $invoice;
    }
    public function send(Invoice $invoice)
    {
        $this->invoiceGenerator->create(InvoiceGeneratorType::Fakturownia, self::FAKTUROWNIA_CONFIG)->send($invoice);
    }

    public function generateForDealer(Subscribers $dealer, \DateTime $from, \DateTime $to): ?Invoice
    {
        // jesli dilera ma dilera, wtedy sprzedaz do niego, np. BKF sprzedaje do nowak
        $dealer = $dealer->getDealer() ?? $dealer;
        $subscriptions = $this->subscriptionCarwashRepository->search(
            dealer: $dealer,
            addedFrom: $from,
            addedTo: $to,
            isInvoiced: false,
            statuses: [SubscriptionStatus::PAID],
            type: [SubscriptionCarwashType::STANDARD],
            exludeSubscriber: $this->excludeSubscribers()
        )['items'];

        $invoice = $this->initDealerInvoice($dealer, $to);


        $tax = $this->getTax($dealer->getCountry(), $invoice);

        $invoice->addDescription("Subscription list: ");

        $warrantySummary = [];
        foreach ($subscriptions as $item) {
            /** @var OwnerSubscriptionCarwash $item */
            if ($item->getQuantity() == 0) {
                continue;
            }

            if ($invoice->getNumber()) {
                captureMessage("faktura {$invoice->getNumber()} juz wystawiona dla {$item->getId()}");
            }

            $code = $item->getSubscription()->getOwnerSubscriptionPackage()->getCode();
            if (!array_key_exists($code->value, $warrantySummary)) {
                $warrantySummary[$code->value] = ['count' => 0];
            }
            $warrantySummary[$code->value]['count'] += $item->getQuantity();
            $companyName = $item->getSubscription()->getSubscriber()->getName();
            $invoice->addDescription(
                "{$item->getCarwash()->getSerialNumber()}, {$code->value}, " .
                "{$item->getStartTime()->format('Y-m-d')} - {$item->getEndTime()->format('Y-m-d')} " .
                "$companyName ({$item->getSubscription()->getSubscriber()->getOwnerBkf()}) " .
                "Subscription id: {$item->getSubscription()->getId()}, addedBy: {$item->getSubscription()->getWhoAdded()->getEmail()}"
            );
        }

        if (empty($warrantySummary)) {
            return null;
        }

        $this->subscriptionInvoiceRepository->save($invoice);

        foreach ($warrantySummary as $codeString => $item) {
            $price = $this->packagesRepository->findBasePackage(
                code: CMSubscription::from($codeString),
                currency: $invoice->getCurrency()
            )->getValue() * ($tax ? (100 + $tax) / 100 : 1);

            $invoice->addPosition("Subscription $codeString", $item['count'], $price, $tax);
        }

        $this->generate($invoice);


        foreach ($subscriptions as $item) {
            $item->setInvoice($invoice->getNumber());
            $this->subscriptionCarwashRepository->save($item);
        }


        return $invoice;
    }

    public function generateBkfSpecjal(Subscribers $dealer, \DateTime $checkDate)
    {
        $subscribers = $this->subscribersRepository->findBy(['ownerBkf' => self::BKF_SPECIAL_CLIENTS]);


        $invoice = $this->initDealerInvoice($dealer, $checkDate);

        $invoice->addDescription("Myjnie na bazie umowy BKF z klientami: ");

        foreach ($subscribers as $subscriber) {
            /** @var Subscribers $subscriber */
            $count = 0;
            $invoice->addDescription("{$subscriber->getName()} ({$subscriber->getOwnerBkf()})");
            foreach ($subscriber->getCarwashes() as $carwash) {
                if (!$carwash->isCarWashStarted()) {
                    continue;
                }
                $invoice->addDescription(" * " .
                    "{$carwash->getSerialNumber()}, data uruchomienia {$carwash->getStartDate()->format('Y-m-d')}");

                $count++;
            }
            $tax = 23;
            $price = $this->packagesRepository->findBasePackage(
                code: CMSubscription::PREMIUM,
                currency: $invoice->getCurrency()
            )->getValue() * ((100 + $tax) / 100);

            $invoice->addPosition("Subskrypcje dla {$subscriber->getName()}", $count, $price, $tax);
        }


        $this->generate($invoice);

            captureMessage(
                "Wystawiona FV {$invoice->getNumber()} za myjnie specjalne {$invoice->getClient()->getName()}"
            );
        return $invoice;
    }

    public function generateWarranty(Subscribers $dealer, \DateTime $checkDate): ?Invoice
    {
        $subscriptions = $this->subscriptionCarwashRepository->search(
            statuses:         [SubscriptionStatus::PAID],
            type:             [SubscriptionCarwashType::WARRANTY],
            checkDate:        $checkDate,
            exludeSubscriber: $this->excludeSubscribers()
        )['items'];

        $invoice = $this->initDealerInvoice($dealer, $checkDate);

        $invoice->addDescription("Myjnie gwarancyjne: ");

        $warrantySummary = [];
        foreach ($subscriptions as $item) {
            /** @var OwnerSubscriptionCarwash $item */
            $code = $item->getSubscription()->getOwnerSubscriptionPackage()->getCode();

            $subscriber = $item->getSubscription()->getSubscriber();

            $activeUsers = $subscriber->getUsers()->filter(fn(User $user) => $user->getLastLogin() > new \DateTime("-1 month"));

            $warrantyVoided = $item->getCarwash()->getWarrantyVoided()?->format('Y-m-d');

            if ($activeUsers->isEmpty()) {
                $invoice->addDescription(" - " .
                    "{$item->getCarwash()->getSerialNumber()}, {$code->value}, warranty: $warrantyVoided" .
                    " {$subscriber->getName()} ({$subscriber->getOwnerBkf()}) - nieaktywny");
                continue;
            }

            if (!array_key_exists($code->value, $warrantySummary)) {
                $warrantySummary[$code->value] = ['count' => 0];
            }
            $warrantySummary[$code->value]['count'] += 1 ;


            $invoice->addDescription(" + " .
                "{$item->getCarwash()->getSerialNumber()}, {$code->value}, warranty: $warrantyVoided" .
                " {$subscriber->getName()} ({$subscriber->getOwnerBkf()})");
        }

        $tax = 23;
        foreach ($warrantySummary as $codeString => $item) {
            $price = $this->packagesRepository->findBasePackage(
                code: CMSubscription::from($codeString),
                currency: $invoice->getCurrency()
            )->getValue() * ((100 + $tax) / 100);

            $invoice->addPosition("Subscription $codeString", $item['count'], $price, $tax);
        }
        if ($invoice->getPositions()->isEmpty()) {
            return null;
        }

        $this->generate($invoice);

        captureMessage(
            "Wystawiona FV {$invoice->getNumber()} za myjnie gwarancyjne dla {$invoice->getClient()->getName()}"
        );
        return $invoice;
    }

    private function invoiceFromSubscription(OwnerSubscriptionPayment $osp, InvoiceKindType $kind): Invoice
    {
        $client = $osp->getSubscriber();
        $locale = $client->getLanguage()->getLocale();
        $externalPayment = $osp->getExternalPayment();

        $invoice = (new Invoice())
            ->setCurrency($osp->getCurrency())
            ->setClient($client)
            ->setLanguage($locale)
            ->setKind($kind)
            ->setIssuer($this->subscribersRepository->getI2mSubscriber())
            ->setPaymentType($externalPayment ? PaymentType::Online : PaymentType::Transfer)
            ->setPaymentTerm(null)
            ->setServiceDate(\DateTimeImmutable::createFromMutable($osp->getCtime()));

        if ($externalPayment) {
            $invoice
                ->setPaid($externalPayment->getValue())
                ->setPaymentDate(\DateTimeImmutable::createFromInterface($externalPayment->getConfirmedTimestamp()))
                ;
        }

        foreach ($osp->getItem() as $item) {
            /** @var OwnerSubscriptionCarwash $item */
            if ($item->getType() == SubscriptionCarwashType::STANDARD) {
                $description = "* {$item->getCarwash()->getSerialNumber()} {$item->getType()->value}  {$item->getStartTime()->format('Y-m-d')} - {$item->getEndTime()->format('Y-m-d')}";
                $invoice->addDescription($description);
            }
        }

        $baseName = $this->translator->trans('fakturownia.cm-sub-description', [], null, $locale) . ': ';

        $positionName = $baseName . " {$osp->getOwnerSubscriptionPackage()->getCode()->value} {$osp->getStartDate()->format('Y-m-d')} - {$osp->getEndDate()->format('Y-m-d')}";
        $invoice->addPosition($positionName, 1, $osp->getGrossValue(), $osp->getTax());

        $this->subscriptionInvoiceRepository->save($invoice);

        $this->generate($invoice);

          $this->subscriptionInvoiceRepository->save($invoice);

        $this->log("dla subskrypcji {$osp->getId()} wystawiam FV na kwotę {$invoice->getTotalGross()} {$invoice->getCurrency()->value}");

        $osp->addAutoComment("faktura {$invoice->getKind()->value} {$invoice->getNumber()}");
        return $invoice;
    }

    private static function getDepartmentId(Subscribers $subscriber, Currency $currency): FakturowniaDepartment
    {
        if (
            in_array($subscriber->getOwnerBkf(), self::TEST_CARWASHES)
        ) {
            return FakturowniaDepartment::TEST;
        }

        if ($currency === Currency::PLN) {
            return FakturowniaDepartment::PLN;
            // return FakturowniaDepartment::TEST;
        }

        if ($currency === Currency::EUR) {
            return FakturowniaDepartment::EUR;
            // return FakturowniaDepartment::TEST;
        }

        throw new \Exception("nie udało się ustalić departamentu");
    }
    private function invoiceFromProforma(?Invoice $existedInvoice, OwnerSubscriptionPayment $osp): void
    {
        // ta encja bedzie zaaktualizowana, wewnątrz $this->invoiceService->fromInvoice !!!
        $vatInvoice = (new Invoice())
            ->setKind(InvoiceKindType::vat)
            ->setClient($existedInvoice->getClient())
            ->setIssuer($existedInvoice->getIssuer())
            ->setExternalId($existedInvoice->getExternalId())
            ->setCurrency($existedInvoice->getCurrency())
            ->setLanguage($existedInvoice->getLanguage())
            ->setPaymentDate(new \DateTimeImmutable())
            ->setPaymentType($existedInvoice->getPaymentType())
            ->setServiceDate($existedInvoice->getServiceDate())
        ;
        $this->subscriptionInvoiceRepository->save($vatInvoice);

        $this->invoiceService->fromInvoice(
            InvoiceGeneratorType::Fakturownia,
            self::FAKTUROWNIA_CONFIG,
            $vatInvoice
        );

        $osp->setInvoice($vatInvoice);
        $osp->addAutoComment("faktura {$vatInvoice->getKind()->value} {$vatInvoice->getNumber()}");
        $this->subscriptionInvoiceRepository->save($vatInvoice);
    }

    private function log(string $text)
    {
        captureMessage($text);
        print_r($text);
    }

    public function status(Invoice $invoices): PaymentStatus
    {
        return $this->invoiceService->getStatus(
            InvoiceGeneratorType::Fakturownia,
            self::FAKTUROWNIA_CONFIG,
            $invoices
        );
    }

    public function getTax(Country $country, Invoice $invoice): ?int
    {
        if ($country === Country::PL) {
            $tax = 23; // 23% VAT for PLN
        } else {
            $invoice->addDescription("Odwrócone obciążenie VAT / Reverse VAT charge");
            $tax = null; // No VAT for EUR
        }
        return $tax;
    }

    public function lostSubscriptions(\DateTime $from, \DateTime $to)
    {
        return $this->subscriptionCarwashRepository->search(
            addedFrom: $from,
            addedTo: $to,
            isInvoiced: false,
            statuses: [SubscriptionStatus::PAID],
            type: [SubscriptionCarwashType::STANDARD],
            exludeSubscriber: $this->excludeSubscribers()
        )['items'];
    }

    public function initDealerInvoice(Subscribers $dealer, \DateTime $to): Invoice
    {
        $locale = $dealer->getLanguage()->getLocale();
        $invoice = (new Invoice())
            ->setCurrency($dealer->getCountry() === Country::PL ? Currency::PLN : Currency::EUR)
            ->setClient($dealer)
            ->setLanguage($locale)
            ->setKind(InvoiceKindType::vat)
            ->setIssuer($this->subscribersRepository->getI2mSubscriber())
            ->setPaymentType(PaymentType::Transfer)
            ->setPaymentTerm('P2W')
            ->setServiceDate(\DateTimeImmutable::createFromMutable($to))
            ->setDiscount($dealer->getSubscriptionDiscount())
        ;
        return $invoice;
    }

    public function generate(Invoice $invoice): void
    {
        $config = [
            "categoryId" => self::CATEGORY_CM,
            "departmentId" => $this->getDepartmentId($invoice->getClient(), $invoice->getCurrency()),
        ];
        $this->invoiceService->generate(
            InvoiceGeneratorType::Fakturownia,
            array_merge(self::FAKTUROWNIA_CONFIG, $config),
            $invoice
        );
    }

    public function excludeSubscribers(): array
    {
        return array_merge(
            self::TEST_CARWASHES,
            self::EXCLUDE_DEALER_INVOICING,
            self::BKF_SPECIAL_CLIENTS
        );
    }
}

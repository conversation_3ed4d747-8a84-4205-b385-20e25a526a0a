<?php

namespace App\Service\Subscription;

use App\Entity\Enum\SubscriptionPayer;
use App\Entity\Enum\SubscriptionStatus;
use I2m\StandardTypes\Enum\CMSubscription;
use App\Entity\Subscribers;
use App\Entity\Subscription\ExternalPayment;
use App\Entity\Subscription\OwnerSubscriptionPackages;
use App\Entity\Subscription\OwnerSubscriptionPayment;
use App\Entity\User;
use App\Repository\LoggerRepository;
use App\Repository\SubscribersRepository;
use App\Repository\Subscription\OwnerSubscriptionPaymentRepository;
use App\Repository\Subscription\SubscriptionPackagesRepository;
use App\Service\Email\EmailService;
use Carbon\Carbon;
use DateInterval;
use DateTime;
use Exception;
use I2m\StandardTypes\Enum\Currency;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Symfony\Contracts\Translation\TranslatorInterface;
use Twig\Error\Error;

use function Sentry\captureMessage;

/**
 * @deprecated  używaj SubscriptionService
 */
class OwnerSubscriptionService
{
    private const  ALLOWED_CURRENCY = [
                        Currency::EUR,
                        Currency::PLN,
                        Currency::CZK
                    ];

    public function __construct(
        private TranslatorInterface $translator,
        private OwnerSubscriptionPaymentRepository $ospRepository,
        private SubscriptionPackagesRepository $packagesRepository,
        private SubscribersRepository $subscribersRepository,
        private LoggerRepository $loggerRepository,
        private EmailService $emailService,
        private SubscriptionInvoiceService $invoice2Service,
    ) {
    }

    /**
     * @throws Error
     * @throws Exception
     */
    public function pushEndNotification(Subscribers $subscriber, \DateTimeInterface $expireDate)
    {
        $locale = $subscriber->getLanguage()->getLocale();
        $endDate = $expireDate->format('Y-m-d');

        $this->loggerRepository->warning(
            $subscriber,
            null,
            null,
            self::class,
            "end subscriptpion notification to $endDate for email {$subscriber->getEmail()}"
        );

        $emails = [
            '<EMAIL>',
            '<EMAIL>',
            $subscriber->getEmail(),
        ];

        if (!$subscriber->isDealer() && $subscriber->getDealer()) {
            $emails[] = $subscriber->getDealer()->getEmail();
        }

        $this->emailService->sendEmail(
            template: 'Email/subscription_ending_notification.html.twig',
            data: [
                'expire_date' => $endDate,
            ],
            title: $this->translator->trans('mail.subscription-ending-title', [], null, $locale),
            emails: $emails,
            language: $locale,
            dealer: $subscriber->getDealer(),
        );
    }



    /**
     * @throws Exception
     * @deprecated
     */
    public function getActiveSubscriptionData(Subscribers $subcriber): array
    {
        $expired_at = $this->ospRepository->getSubscriptionEnd($subcriber);
        $expired_soon = (new \DateTime())->add(new \DateInterval('P7D'));
        $type = $subcriber->getSubscription();

        $subscription = [
            'activeToDate' => $expired_at?->format('Y-m-d'),
            'todayType' => $type,
            'type' => $type,
            "toPayValue" => 0,
            'isExpiredToday' => $expired_at < new \DateTime(),
            'isExpired' => $expired_at < new \DateTime(),
            'willExpireSoon' => $expired_at <= $expired_soon,
        ];
        return $subscription;
    }



    public function getSubscriptionPackageByDates(
        \DateTimeInterface $from,
        \DateTimeInterface $to,
        Currency $currency,
        CMSubscription $packageType,
    ): ?OwnerSubscriptionPackages {
        /** @var \DateInterval $diff */
        $diff = $to->diff($from);

        $months = ($diff->y * 12) + $diff->m;

        return $this->getSubscriptionPackage($months, $currency, $packageType);
    }


    private function getSubscriptionPackage(
        int $months,
        Currency $currency,
        CMSubscription $packageType,
    ): ?OwnerSubscriptionPackages {

        if ($months >= 12) {
            $length = 12;
        } else if ($months >= 6) {
            $length = 6;
        } else if ($months >= 3) {
            $length = 3;
        } else {
            $length = 1;
        }

        if (!in_array($currency, self::ALLOWED_CURRENCY)) {
            throw new HttpException(404, 'Currency not in allowed types');
        }

        if ($packageType == CMSubscription::FREE) {
            throw new HttpException(404, 'Type not in allowed');
        }

        return $this->packagesRepository->findOneBy([
            'code' => $packageType,
            'currency' => $currency,
            'monthsLength' => $length,
        ]);
    }

    public function generate(
        \DateTimeInterface $from,
        \DateTimeInterface $to,
        Currency $currency,
        CMSubscription $CMSubscription,
        Subscribers $subscriber,
        User $whoAdded,
        SubscriptionPayer $payerType,
        SubscriptionStatus $status = SubscriptionStatus::INITIATED,
        ?string $comment = null,
        ?ExternalPayment $externalPayment = null,
        bool $save = false,
    ) {

        if ($payerType == SubscriptionPayer::CLIENT) {
            $package = $this->getSubscriptionPackageByDates(
                $from,
                $to,
                $currency,
                $CMSubscription
            );
        } else {
            $package = $this->getSubscriptionPackage(
                1,
                $currency,
                $CMSubscription
            );
        }

        $subscriptionPayment = $this->ospRepository->initNew(
            $subscriber,
            $whoAdded,
            $from,
            $to,
            $package,
            $payerType,
            $status,
            $comment,
            $externalPayment,
            $save
        );
        $this->updateSubscriber($subscriptionPayment->getSubscriber());

        if ($save) {
            $this->loggerRepository->warning(
                $subscriber,
                $whoAdded,
                null,
                self::class,
                "new subscriptpion saved {$subscriptionPayment->getId()}"
            );
        }

        return $subscriptionPayment;
    }

    public function getActiveSubscritpion(?Subscribers $subscriber, ?DateTime $checkDate = null): ?OwnerSubscriptionPayment
    {

        $subscriptionsToProcess = $this->ospRepository->findSubscriptions(
            subscriber: $subscriber,
            statuses:  [SubscriptionStatus::PAID],
            checkDate: $checkDate ?? new DateTime()
        )['items'];

        if (empty($subscriptionsToProcess)) {
            return null;
        }
        /** @var ?OwnerSubscriptionPayment $result */
        $result = null;

        // na wypadek gdyby dla jednego okresu bylo więcej niż 1 subskrypcja
        foreach ($subscriptionsToProcess as $item) {
            /** @var OwnerSubscriptionPayment $item */
            $code = $item->getOwnerSubscriptionPackage()->getCode();
            // szukam subscripcji w najwyżej wersji
            if (
                is_null($result) ||
                $code->isGreaterEqualThan($result->getOwnerSubscriptionPackage()->getCode())
            ) {
                $result = $item;
            }
        }

        return $result;
    }

    public function confirm(OwnerSubscriptionPayment $ownerSubscriptionPayment, ?string $comment = null, ?\DateTime $startDate = null, string $invoiceType = 'none'): ?OwnerSubscriptionPayment
    {

        if ($ownerSubscriptionPayment->getStatus() !== SubscriptionStatus::PROFORMA) {
            throw new \Exception("nie można potwierdzić tego typu subskrypcji");
        }

        $ownerSubscriptionPayment
            ->setMtime(Carbon::now())
            ->setStatus(SubscriptionStatus::PAID);
        if (null !== $comment) {
            $ownerSubscriptionPayment->setComment($comment);
        }

        $daysDifference = $ownerSubscriptionPayment->getDaysDifference();

        if (null !== $startDate) {
            $ownerSubscriptionPayment->setStartDate($startDate);
            $interval = new DateInterval("P{$daysDifference}D");
            $endDate = clone $startDate;
            $endDate = $endDate->add($interval)->setTime(23, 59, 59, 999);
            $ownerSubscriptionPayment->setEndDate($endDate);
        }


        $this->ospRepository->save($ownerSubscriptionPayment);
        $subscriber = $ownerSubscriptionPayment->getSubscriber();
        $this->updateSubscriber($subscriber);

        switch ($invoiceType) {
            case 'none':
                break;
            case 'issue':
                $this->invoice2Service->generateForClient($ownerSubscriptionPayment, false);
                break;
            case 'send':
                $this->invoice2Service->generateForClient($ownerSubscriptionPayment, true); // testowej nie wysylam
                break;
        }
        return $ownerSubscriptionPayment;
    }

    public function cancel(OwnerSubscriptionPayment $ownerSubscriptionPayment, ?string $comment = null)
    {
        if ($ownerSubscriptionPayment->getStatus() == SubscriptionStatus::CANCELED) {
            captureMessage("subskrypcja {$ownerSubscriptionPayment->getId()} jest już anulowana");
        }

        $ownerSubscriptionPayment
            ->setStatus(SubscriptionStatus::CANCELED)
        ;
        $ownerSubscriptionPayment->addAutoComment($comment);

        $this->ospRepository->save($ownerSubscriptionPayment);
        $this->updateSubscriber($ownerSubscriptionPayment->getSubscriber());
    }
    private function updateSubscriber(?Subscribers $subscriber): void
    {
        $code = $this->getActiveSubscritpion($subscriber)?->getOwnerSubscriptionPackage()->getCode();
        $subscriber->setSubscription($code);
        $this->subscribersRepository->save($subscriber);
    }
}

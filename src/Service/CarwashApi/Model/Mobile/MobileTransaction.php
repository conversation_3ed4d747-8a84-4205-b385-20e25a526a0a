<?php

namespace App\Service\CarwashApi\Model\Mobile;

use App\Service\CarwashApi\Model\Carwash;
use App\Service\CarwashApi\Model\Source;

class MobileTransaction
{
    private int $id;
    private Carwash $carwash;
    private \DateTime $time;
    private float $value;
    private int $bayId;
    private Source $source;
    private string $status;
    private string $app;
    private string $standCode;

    public function getId(): int
    {
        return $this->id;
    }

    public function setId(int $id): MobileTransaction
    {
        $this->id = $id;
        return $this;
    }

    public function getCarwash(): Carwash
    {
        return $this->carwash;
    }

    public function setCarwash(Carwash $carwash): MobileTransaction
    {
        $this->carwash = $carwash;
        return $this;
    }

    public function getTime(): \DateTime
    {
        return $this->time;
    }

    public function setTime(\DateTime $time): MobileTransaction
    {
        $this->time = $time;
        return $this;
    }

    public function getValue(): float
    {
        return $this->value;
    }

    public function setValue(float $value): MobileTransaction
    {
        $this->value = $value;
        return $this;
    }

    public function getBayId(): int
    {
        return $this->bayId;
    }

    public function setBayId(int $bayId): MobileTransaction
    {
        $this->bayId = $bayId;
        return $this;
    }

    public function getSource(): Source
    {
        return $this->source;
    }

    public function setSource(Source $source): MobileTransaction
    {
        $this->source = $source;
        return $this;
    }

    public function getStatus(): string
    {
        return $this->status;
    }

    public function setStatus(string $status): MobileTransaction
    {
        $this->status = $status;
        return $this;
    }

    public function getApp(): string
    {
        return $this->app;
    }

    public function setApp(string $app): MobileTransaction
    {
        $this->app = $app;
        return $this;
    }

    public function getStandCode(): string
    {
        return $this->standCode;
    }

    public function setStandCode(string $standCode): MobileTransaction
    {
        $this->standCode = $standCode;
        return $this;
    }
}

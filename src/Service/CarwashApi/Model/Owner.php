<?php

namespace App\Service\CarwashApi\Model;

use Symfony\Component\Serializer\Annotation\Groups;

class Owner
{
    /**
     * @Groups({"add_client"})
     */
    private ?int $id;

    /**
     * @var Carwash[] $carwash
     */
    private ?array $carwash;

    /**
     * @return Carwash[]
     */
    public function getCarwash(): ?array
    {
        return $this->carwash;
    }

    /**
     * @param Carwash[] $carwash
     */
    public function setCarwash(?array $carwash): Owner
    {
        $this->carwash = $carwash;
        return $this;
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(?int $id): Owner
    {
        $this->id = $id;
        return $this;
    }
}

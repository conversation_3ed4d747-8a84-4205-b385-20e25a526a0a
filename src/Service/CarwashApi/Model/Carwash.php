<?php

namespace App\Service\CarwashApi\Model;

class Carwash
{
    private int $sn;
    private ?string $name;

    public function getSn(): int
    {
        return $this->sn;
    }

    public function setSn(int $sn): Carwash
    {
        $this->sn = $sn;
        return $this;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(?string $name): Carwash
    {
        $this->name = $name;
        return $this;
    }
}

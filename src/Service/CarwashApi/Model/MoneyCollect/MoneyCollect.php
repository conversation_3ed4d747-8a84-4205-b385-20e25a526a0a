<?php

namespace App\Service\CarwashApi\Model\MoneyCollect;

use App\Service\CarwashApi\Model\Carwash;
use App\Service\CarwashApi\Model\Source;

class MoneyCollect
{
    private int $id;
    private \DateTime $time;
    private Carwash $carwash;
    private Source $source;
    private float $sum;
    private array $content;

    public function getId(): int
    {
        return $this->id;
    }

    public function setId(int $id): MoneyCollect
    {
        $this->id = $id;
        return $this;
    }

    public function getTime(): \DateTime
    {
        return $this->time;
    }

    public function setTime(\DateTime $time): MoneyCollect
    {
        $this->time = $time;
        return $this;
    }

    public function getCarwash(): Carwash
    {
        return $this->carwash;
    }

    public function setCarwash(Carwash $carwash): MoneyCollect
    {
        $this->carwash = $carwash;
        return $this;
    }

    public function getSource(): Source
    {
        return $this->source;
    }

    public function setSource(Source $source): MoneyCollect
    {
        $this->source = $source;
        return $this;
    }

    public function getSum(): float
    {
        return $this->sum;
    }

    public function setSum(float $sum): MoneyCollect
    {
        $this->sum = $sum;
        return $this;
    }

    public function getContent(): array
    {
        return $this->content;
    }

    public function setContent(array $content): MoneyCollect
    {
        $this->content = $content;
        return $this;
    }
}

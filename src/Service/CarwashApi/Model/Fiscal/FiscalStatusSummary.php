<?php

namespace App\Service\CarwashApi\Model\Fiscal;

class FiscalStatusSummary
{
    private string $fiscal;
    private int $count;
    private float $value;
    private float $vat;
    private float $net;

    public function getFiscal(): string
    {
        return $this->fiscal;
    }

    public function setFiscal(string $fiscal): FiscalStatusSummary
    {
        $this->fiscal = $fiscal;
        return $this;
    }

    public function getCount(): int
    {
        return $this->count;
    }

    public function setCount(int $count): FiscalStatusSummary
    {
        $this->count = $count;
        return $this;
    }

    public function getValue(): float
    {
        return $this->value;
    }

    public function setValue(float $value): FiscalStatusSummary
    {
        $this->value = $value;
        return $this;
    }

    public function getVat(): float
    {
        return $this->vat;
    }

    public function setVat(float $vat): FiscalStatusSummary
    {
        $this->vat = $vat;
        return $this;
    }

    public function getNet(): float
    {
        return $this->net;
    }

    public function setNet(float $net): FiscalStatusSummary
    {
        $this->net = $net;
        return $this;
    }
}

<?php

namespace App\Service\CarwashApi\Model\Fiscal;

class FiscalTransactionSummaryDevice
{
    private float $value;
    private ?float $net;
    private ?float $vat;
    private string $source;
    private int $bayId;
    private int $sn;

    private int $count;

    private ?string $fiscalDevice;

    private \DateTime $last;

    public function getValue(): float
    {
        return $this->value;
    }

    public function setValue(float $value): FiscalTransactionSummaryDevice
    {
        $this->value = $value;
        return $this;
    }

    public function getNet(): ?float
    {
        return $this->net;
    }

    public function setNet(?float $net): FiscalTransactionSummaryDevice
    {
        $this->net = $net;
        return $this;
    }

    public function getVat(): ?float
    {
        return $this->vat;
    }

    public function setVat(?float $vat): FiscalTransactionSummaryDevice
    {
        $this->vat = $vat;
        return $this;
    }

    public function getSource(): string
    {
        return $this->source;
    }

    public function setSource(string $source): FiscalTransactionSummaryDevice
    {
        $this->source = $source;
        return $this;
    }

    public function getBayId(): int
    {
        return $this->bayId;
    }

    public function setBayId(int $bayId): FiscalTransactionSummaryDevice
    {
        $this->bayId = $bayId;
        return $this;
    }

    public function getSn(): int
    {
        return $this->sn;
    }

    public function setSn(int $sn): FiscalTransactionSummaryDevice
    {
        $this->sn = $sn;
        return $this;
    }

    public function getLast(): \DateTime
    {
        return $this->last;
    }

    public function setLast(\DateTime $last): FiscalTransactionSummaryDevice
    {
        $this->last = $last;
        return $this;
    }

    public function getCount(): int
    {
        return $this->count;
    }

    public function setCount(int $count): FiscalTransactionSummaryDevice
    {
        $this->count = $count;
        return $this;
    }

    public function getFiscalDevice(): ?string
    {
        return $this->fiscalDevice;
    }

    public function setFiscalDevice(?string $fiscalDevice): FiscalTransactionSummaryDevice
    {
        $this->fiscalDevice = $fiscalDevice;
        return $this;
    }
}

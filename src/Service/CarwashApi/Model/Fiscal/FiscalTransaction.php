<?php

namespace App\Service\CarwashApi\Model\Fiscal;

use App\Service\CarwashApi\Model\Carwash;
use App\Service\CarwashApi\Model\Currency;
use App\Service\CarwashApi\Model\Source;
use App\Service\CarwashApi\Model\Type;

class FiscalTransaction
{
    private int $id;
    private \DateTime $time;
    private Carwash $carwash;
    private Source $source;
    private Currency $currency;
    private Type $type;
    private ?int $bayId;
    private string $fiscal;
    private float $value;
    private ?float $net;
    private ?float $vat;
    private ?array $details;

    public function getId(): int
    {
        return $this->id;
    }

    public function setId(int $id): FiscalTransaction
    {
        $this->id = $id;
        return $this;
    }

    public function getTime(): \DateTime
    {
        return $this->time;
    }

    public function setTime(\DateTime $time): FiscalTransaction
    {
        $this->time = $time;
        return $this;
    }

    public function getCarwash(): Carwash
    {
        return $this->carwash;
    }

    public function setCarwash(Carwash $carwash): FiscalTransaction
    {
        $this->carwash = $carwash;
        return $this;
    }

    public function getSource(): Source
    {
        return $this->source;
    }

    public function setSource(Source $source): FiscalTransaction
    {
        $this->source = $source;
        return $this;
    }

    public function getCurrency(): Currency
    {
        return $this->currency;
    }

    public function setCurrency(Currency $currency): FiscalTransaction
    {
        $this->currency = $currency;
        return $this;
    }

    public function getType(): Type
    {
        return $this->type;
    }

    public function setType(Type $type): FiscalTransaction
    {
        $this->type = $type;
        return $this;
    }

    public function getBayId(): ?int
    {
        return $this->bayId;
    }

    public function setBayId(?int $bayId): FiscalTransaction
    {
        $this->bayId = $bayId;
        return $this;
    }

    public function getFiscal(): string
    {
        return $this->fiscal;
    }

    public function setFiscal(string $fiscal): FiscalTransaction
    {
        $this->fiscal = $fiscal;
        return $this;
    }

    public function getValue(): float
    {
        return $this->value;
    }

    public function setValue(float $value): FiscalTransaction
    {
        $this->value = $value;
        return $this;
    }

    public function getNet(): ?float
    {
        return $this->net;
    }

    public function setNet(?float $net): FiscalTransaction
    {
        $this->net = $net;
        return $this;
    }

    public function getVat(): ?float
    {
        return $this->vat;
    }

    public function setVat(?float $vat): FiscalTransaction
    {
        $this->vat = $vat;
        return $this;
    }

    public function getDetails(): ?array
    {
        return $this->details;
    }

    public function setDetails(?array $details): FiscalTransaction
    {
        $this->details = $details;
        return $this;
    }
}

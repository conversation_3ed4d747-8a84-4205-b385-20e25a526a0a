<?php

namespace App\Service\CarwashApi\Model\Fiscal;

class FiscalTransactionSums
{
    private float $net;
    private float $vat;
    private float $value;

    public function getNet(): float
    {
        return $this->net;
    }

    public function setNet(float $net): FiscalTransactionSums
    {
        $this->net = $net;
        return $this;
    }

    public function getVat(): float
    {
        return $this->vat;
    }

    public function setVat(float $vat): FiscalTransactionSums
    {
        $this->vat = $vat;
        return $this;
    }

    public function getValue(): float
    {
        return $this->value;
    }

    public function setValue(float $value): FiscalTransactionSums
    {
        $this->value = $value;
        return $this;
    }
}

<?php

namespace App\Service\CarwashApi\Model\Fiscal;

class FiscalByDaySummary
{
    private string $time;
    private int $sn;
    private string $fiscal;
    private ?string $type;
    private int $count;
    private float $value;
    private float $vat;
    private float $net;

    public function getTime(): string
    {
        return $this->time;
    }

    public function setTime(string $time): FiscalByDaySummary
    {
        $this->time = $time;
        return $this;
    }

    public function getSn(): int
    {
        return $this->sn;
    }

    public function setSn(int $sn): FiscalByDaySummary
    {
        $this->sn = $sn;
        return $this;
    }

    public function getFiscal(): string
    {
        return $this->fiscal;
    }

    public function setFiscal(string $fiscal): FiscalByDaySummary
    {
        $this->fiscal = $fiscal;
        return $this;
    }

    public function getType(): ?string
    {
        return $this->type;
    }

    public function setType(?string $type): FiscalByDaySummary
    {
        $this->type = $type;
        return $this;
    }

    public function getCount(): int
    {
        return $this->count;
    }

    public function setCount(int $count): FiscalByDaySummary
    {
        $this->count = $count;
        return $this;
    }

    public function getValue(): float
    {
        return $this->value;
    }

    public function setValue(float $value): FiscalByDaySummary
    {
        $this->value = $value;
        return $this;
    }

    public function getVat(): float
    {
        return $this->vat;
    }

    public function setVat(float $vat): FiscalByDaySummary
    {
        $this->vat = $vat;
        return $this;
    }

    public function getNet(): float
    {
        return $this->net;
    }

    public function setNet(float $net): FiscalByDaySummary
    {
        $this->net = $net;
        return $this;
    }
}

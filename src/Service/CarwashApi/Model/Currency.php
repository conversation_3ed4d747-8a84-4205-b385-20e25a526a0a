<?php

namespace App\Service\CarwashApi\Model;

class Currency
{
    private string $code;
    private string $symbol;

    public function getCode(): string
    {
        return $this->code;
    }

    public function setCode(string $code): Currency
    {
        $this->code = $code;
        return $this;
    }

    public function getSymbol(): string
    {
        return $this->symbol;
    }

    public function setSymbol(string $symbol): Currency
    {
        $this->symbol = $symbol;
        return $this;
    }
}

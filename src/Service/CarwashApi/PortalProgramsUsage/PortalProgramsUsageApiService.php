<?php

namespace App\Service\CarwashApi\PortalProgramsUsage;

use Exception;
use GuzzleHttp\Client;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class PortalProgramsUsageApiService
{
    protected Client $http;
    protected string $apiUrl;

    public function __construct(ParameterBagInterface $parameterBag)
    {
        $this->apiUrl = $parameterBag->get('carwash_api_url');
        $this->http = new Client();
    }

    public function getPortalUsageTotal(
        string $serialNumbers,
        string $dateFrom,
        string $dateTo,
        ?string $timezone = null
    ): array {
        if (empty($serialNumbers)) {
            return [];
        }

        $response = $this->http->request(
            Request::METHOD_GET,
            $this->apiUrl . "/api/portal/" . urlencode($serialNumbers) . "/usage/total",
            [
                'query' => [
                    'dateFrom' => $dateFrom,
                    'dateTo' => $dateTo,
                    'timezone' => $timezone,
                ],
            ]
        );
        $body = json_decode($response->getBody()->getContents(), true);

        if (Response::HTTP_OK !== $response->getStatusCode()) {
            throw new Exception('Wrong response code from API');
        }

        return $body;
    }

    public function getPortalUsageDaily(
        string $serialNumbers,
        string $dateFrom,
        string $dateTo,
        ?array $days,
        ?string $timezone = null
    ): array {
        if (empty($days)) {
            $days = [1, 2, 3, 4, 5, 6, 7];
        }
        $daysStr = implode(',', $days);

        $response = $this->http->request(
            Request::METHOD_GET,
            $this->apiUrl . "/api/portal/" . urlencode($serialNumbers) . "/usage/daily",
            [
                'query' => [
                    'dateFrom' => $dateFrom,
                    'dateTo' => $dateTo,
                    'days' => $daysStr,
                    'timezone' => $timezone,
                ],
            ]
        );
        $body = json_decode($response->getBody()->getContents(), true);

        if (Response::HTTP_OK !== $response->getStatusCode()) {
            throw new Exception('Wrong response code from API');
        }

        return $body;
    }

    public function getPortalUsageOverTime(
        string $type,
        string $serials,
        string $dateFrom,
        string $dateTo,
        string $timezone,
    ): array {
        $params = [
            "serial" => $serials,
            "date_from" => $dateFrom,
            "date_to" => $dateTo,
            "timezone" => $timezone
        ];

        $response = $this->http->request(
            Request::METHOD_GET,
            $this->apiUrl . "/api/finance/portal/time_grouped/" . $type,
            [
                'query' => $params,
            ]
        );

        $body = json_decode($response->getBody()->getContents(), true);

        if (Response::HTTP_OK !== $response->getStatusCode()) {
            throw new Exception('Wrong response code from API');
        }

        return $body;
    }
}

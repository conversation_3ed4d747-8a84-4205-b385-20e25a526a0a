<?php

namespace App\Service\CarwashApi;

use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Contracts\HttpClient\HttpClientInterface;

class CarwashApiConnector
{
    public string $apiUrl;
    private const SUCCESS_CODES = [
        Response::HTTP_OK,       // 200
        Response::HTTP_NO_CONTENT // 204
    ];

    public function __construct(
        ParameterBagInterface $parameterBag,
        private HttpClientInterface $httpClient
    ) {
        $this->apiUrl = $parameterBag->get('carwash_api_url');
    }

    public function request($uri, $method, array $params = [], $type = 'query'): array
    {
        $response = $this->requestJson($uri, $method, $params, $type);
        $body = json_decode($response, true);
        return $body;
    }
    public function requestJson($uri, $method, array $params = [], $type = 'query'): string
    {
        $response = $this->httpClient->request(
            $method,
            $this->apiUrl . $uri,
            [
                $type   => $params,
            ]
        );
        if (!in_array($response->getStatusCode(), self::SUCCESS_CODES)) {
            throw new \Exception("Wrong response code from API: {$response->getStatusCode()}");
        }
        $body = $response->getContent();

        return $body;
    }
}

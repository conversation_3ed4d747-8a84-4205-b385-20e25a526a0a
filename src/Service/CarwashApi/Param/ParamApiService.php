<?php

namespace App\Service\CarwashApi\Param;

use App\Entity\Carwashes;
use App\Entity\User;
use Closure;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\EntityManagerInterface;
use Exception;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Contracts\HttpClient\HttpClientInterface;

class ParamApiService
{
    private string $apiUrl;

    public function __construct(
        ParameterBagInterface $parameterBag,
        private HttpClientInterface $http
    ) {
        $this->apiUrl = $parameterBag->get('carwash_api_url');
    }

    public function getCarwashParams(int $serialNumbers, ?string $lang, ?string $timezone)
    {
        $response = $this->http->request(
            Request::METHOD_GET,
            $this->apiUrl . "/api/carwashes/" . $serialNumbers . '/param',
            [
                'query' => [
                    'lang' => $lang,
                    'timezone' => $timezone,
                ],
            ]
        );
        $body = json_decode($response->getContent(), true);

        if (Response::HTTP_OK !== $response->getStatusCode()) {
            throw new \Exception('Wrong response code from API');
        }

        return $body;
    }

    public function getCarwashParamsHistory(
        int $serialNumbers,
        int $param,
        string $dateFrom,
        string $dateTo,
        string $timezone,
        ?string $lang
    ) {

        $response = $this->http->request(
            Request::METHOD_GET,
            $this->apiUrl . "/api/carwashes/" . $serialNumbers . '/param/' . $param,
            [
               'query' => [
               'lang' => $lang,
               'timezone' => $timezone,
               'dateFrom' => $dateFrom,
               'dateTo' => $dateTo,
               ],
            ]
        );
        $body = json_decode($response->getContent(), true);

        if (Response::HTTP_OK !== $response->getStatusCode()) {
            throw new \Exception('Wrong response code from API');
        }

        return $body;
    }
}

<?php

namespace App\Service\CarwashApi\ProgramsUsage;

use DateTimeZone;
use Exception;
use GuzzleHttp\Client;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class ProgramsUsageApiService
{
    protected Client $http;
    protected string $apiUrl;

    public function __construct(ParameterBagInterface $parameterBag)
    {
        $this->apiUrl = $parameterBag->get('carwash_api_url');
        $this->http = new Client();
    }

    public function getWorkTimeTotal(
        string $serialNumbers,
        string $dateFrom,
        string $dateTo,
        ?string $timezone = null
    ): array {
        if (empty($serialNumbers)) {
            return [];
        }

        $response = $this->http->request(
            Request::METHOD_GET,
            $this->apiUrl . "/api/worktime/" . urlencode($serialNumbers) . "/total",
            [
                'query' => [
                    'dateFrom' => $dateFrom,
                    'dateTo' => $dateTo,
                    'timezone' => $timezone,
                ],
            ]
        );
        $body = json_decode($response->getBody()->getContents(), true);

        if (Response::HTTP_OK !== $response->getStatusCode()) {
            throw new Exception('Wrong response code from API');
        }

        return $body;
    }

    public function getWorkTimeDaily(
        string $serialNumbers,
        string $dateFrom,
        string $dateTo,
        array $days = [1, 2, 3, 4, 5, 6, 7],
        ?DateTimeZone $timezone = null
    ): array {
        $timezoneUtc = new DateTimeZone('GMT-0');
        $timezone = $timezone ?? $timezoneUtc;


        $daysStr = implode(',', $days);

        $response = $this->http->request(
            Request::METHOD_GET,
            $this->apiUrl . "/api/worktime/" . urlencode($serialNumbers) . "/daily",
            [
                'query' => [
                    'dateFrom' => $dateFrom,
                    'dateTo' => $dateTo,
                    'days' => $daysStr,
                    'timezone' => $timezone->getName()
                ],
            ]
        );
        $body = json_decode($response->getBody()->getContents(), true);

        if (Response::HTTP_OK !== $response->getStatusCode()) {
            throw new Exception('Wrong response code from API');
        }

        return $body;
    }
}

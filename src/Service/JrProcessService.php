<?php

namespace App\Service;

use GuzzleHttp\Client;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;

class JrProcessService
{
    private const API_USER = 'CMRobot';
    private const API_PASS = '#F4#rtdc';

    private Client $http;

    private ?string $token = null;

    public function __construct(ParameterBagInterface $parameterBag)
    {
        $jrApiUrl = $parameterBag->get('jr-api-url');

        $this->http = new Client(
            [
                'base_uri' => $jrApiUrl,
                'cookies'  => true,
            ]
        );
    }

    private function auth()
    {
        if (is_null($this->token)) {
            $response = $this->http->post('/api/rest/v2/application/tokens', [
                'json' => [
                    'username' => self::API_USER,
                    'password' => self::API_PASS,
                ],
            ]);

            $tokenArray = json_decode($response->getBody(), true);
            $this->token = $tokenArray['tokens'][0];
        }
        return $this->token;
    }

    private function makeBkfApiCall(
        string $endpointUrl,
        string $method = 'POST',
        array $postData = [],
        string $type = 'multipart'
    ): array {
        $accessToken = $this->auth();
        $data = [
            'headers' => [
                'X-Jobrouter-Authorization' => 'Bearer ' . $accessToken,
            ],
            $type     => $postData,
        ];

        $response = $this->http->request(
            $method,
            $endpointUrl,
            $data
        );
        $content = $response->getBody();

        return json_decode($content, true);
    }

    public function startProcess(
        string $processName,
        int $step,
        $inputParam = null,
        ?string $username = "JRrobot"
    ) {
        $url = '/api/rest/v2/application/incidents/' . $processName;

        // zmien input param na formularz

        $input = [
            [
                'name'     => 'step',
                'contents' => $step,
            ],
            [
                'name'     => 'username',
                'contents' => $username,
            ],
        ];
        $processField = 0;
        $subTable = 0;
        foreach ($inputParam as $processKey => $processValue) {
            if (is_array($processValue)) { // subtabele później
                $input[] = ['name' => "subtables[$subTable][name]", "contents" => $processKey];
                foreach ($processValue as $tableRow => $tableValue) {
                    $subTableField = 0;
                    foreach ($tableValue as $subtableKey => $subtableValue) {
                        $input[] = [
                            'name'     => "subtables[$subTable][rows][$tableRow][fields][$subTableField][name]",
                            'contents' => $subtableKey,
                        ];
                        $input[] = [
                            'name'     => "subtables[$subTable][rows][$tableRow][fields][$subTableField][value]",
                            'contents' => $subtableValue,
                        ];
                        $subTableField++;
                    }
                }
            } else {
                $input[] = [
                    'name'     => "processtable[fields][$processField][name]",
                    'contents' => $processKey,
                ];
                $input[] = [
                    'name'     => "processtable[fields][$processField][value]",
                    'contents' => $processValue,
                ];
                $processField++;
            }
        }

        return $this->makeBkfApiCall($url, 'POST', $input);
    }

    public function getStep($processName, $id)
    {
        $url = "/api/rest/v2/application/workitems/inbox?where[jrprocessname][eq]=$processName&where[jrincident][eq]=$id";
        $steps = $this->makeBkfApiCall($url, 'GET');

        return $steps['workitems'][0];
    }

    public function sendStep($processName, $id, $inputParam = [])
    {
        $step = $this->getStep($processName, $id);
        $workflowId = $step['jrworkflowid'];
        $url = "/api/rest/v2/application/steps/$workflowId";

        $dialog = [];
        foreach ($inputParam as $key => $value) {
            $dialog[] = ['name' => $key, 'value' => $value];
        }

        $input = [
            'dialogType' => 'desktop',
            'action'     => 'send',

            'processName'    => $step['jrprocessname'],
            'processVersion' => $step['jrversion'],
            'stepNo'         => $step['jrstep'],
            'workflowId'     => $step['jrworkflowid'],

            'dialog' => ['fields' => $dialog],
        ];

        $steps = $this->makeBkfApiCall($url, 'PUT', $input, 'json');
    }
}

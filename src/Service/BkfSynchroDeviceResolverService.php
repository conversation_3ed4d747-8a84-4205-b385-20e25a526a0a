<?php

namespace App\Service;

use App\Entity\Carwashes;
use App\Entity\Subscribers;
use App\Entity\User;
use App\Repository\LanguagesRepository;
use App\Service\Connectors\BkfApi\Model\Device;
use App\Service\Connectors\BkfApi\Model\Owner;
use App\Repository\CarwashesRepository;
use App\Repository\CurrencyRepository;
use App\Repository\SubscribersRepository;
use App\Repository\TimezonesRepository;
use App\Repository\UserRepository;
use App\Service\Admin\UserManager;
use Doctrine\ORM\EntityManagerInterface;
use I2m\StandardTypes\Enum\Country;
use Psr\Log\LoggerInterface;

use function Sentry\captureMessage;

/**
 * Class BkfSynchroDeviceResolverService.
 */
class BkfSynchroDeviceResolverService
{
    /**
     * Class get carwashes, device
     * BkfSynchroDeviceResolverService constructor.
     */
    public function __construct(
        private LoggerInterface $logger,
        private EntityManagerInterface $em,
        private UserManager $userManager,
        private CarwashesRepository $carwashRepository,
        private CurrencyRepository $currencyRepository,
        private UserRepository $userRepository,
        private SubscribersRepository $subscribersRepository,
        private TimezonesRepository $timezonesRepository,
        private LanguagesRepository $languagesRepository,
    ) {
    }

    public function resolveCarwashAndOwner(Device $bkfApiDevice): ?Carwashes
    {
        $carwash = $this->carwashRepository->findOneBy(
            ['serialNumber' => $bkfApiDevice->getSerialNumber()]
        );

        // myjnie dodaje tylko wtedy gdy:
        if (
            is_null($carwash)
            &&  empty($bkfApiDevice->getDiscontinued()) // nie została wyłączona z użytkownia
            //&&  !is_null($bkfApiDevice->getTimeStart()) //  została juz uruchomiona
            &&  !is_null($bkfApiDevice->getPlc()) &&  !is_null($bkfApiDevice->getMac())// i jest ze sterownikiem B&R
            &&  !empty($bkfApiDevice->getCountry()) // i jest ustawiony kraj
        ) {
            $carwash = new Carwashes();
            $carwash->setSerialNumber($bkfApiDevice->getSerialNumber());
            $this->em->persist($carwash);

            $this->notify(
                "utworzono nową myjnie {$carwash->getSerialNumber()}"
            );
        }

        if (is_null($carwash)) {
            return null;
        }

        // tworze konto dileara
        $dealerCompany = $this->updateSubscriber($bkfApiDevice->getDealer());
        $this->updateUser($bkfApiDevice->getDealer()?->getCmEmailAccount(), $dealerCompany);

        // tworze konto ownera
        $ownerCompany = $this->updateSubscriber($bkfApiDevice->getNetwork());
        $owner = $this->updateUser($bkfApiDevice->getNetwork()?->getCmEmailAccount(), $ownerCompany);
        $this->updateDealer($ownerCompany);

        $this->updateCarwash($carwash, $ownerCompany, $bkfApiDevice);


        return $carwash;
    }

    private function updateSubscriber(?Owner $bkfClient): ?Subscribers
    {

        if (is_null($bkfClient)) {
            return null;
        }
        $subscriber = $this->subscribersRepository->findOneBy(['ownerBkf' => $bkfClient->getId()]);
        if (is_null($subscriber) && !$bkfClient->companyDataOk()) {
            return null;
        }

        $country = $bkfClient->getCountry() ? Country::tryFrom($bkfClient->getCountry()) : null;
        if (is_null($country)) {
            $this->notify(
                "Nazwa kraju niepoprawna  {$bkfClient->getCountry()}," .
                "sprawdź nazwę ustawioną w edycji urządzenia JR dla użytkownika {$bkfClient->getCmEmailAccount()} ({$bkfClient->getId()})",
                false
            );
            return $subscriber;
        }


        if (!$bkfClient->companyDataOk()) {
            $this->notify(
                "dla właściciela {$bkfClient->getCmEmailAccount()}" .
                          " błędne dane firmy, sprawdź w JR:\n" .
                          " * nazwę firmy {$bkfClient->getOrganizationName()},\n" .
                          " * NIP i prefix {$bkfClient->getFullNip()},\n" .
                          " * kraj {$bkfClient->getCountry()}\n",
                false
            );
            return $subscriber;
        }


        if (is_null($subscriber)) {
            $currency = $this->currencyRepository->getByCountry($country);
            if (is_null($currency)) {
                return null;
            }
            $timezone = $this->timezonesRepository->getByCountry($country);
            $language = $this->languagesRepository->getByCountry($country);


            $subscriber = new Subscribers();
            $subscriber
                ->setTaxNumber($bkfClient->getFullNip())
                ->setName($bkfClient->getOrganizationName())
                ->setCountry($country)
                ->setCurrency($currency)
                ->setTimezone($timezone)
                ->setLanguage($language)
                ->setOwnerBkf($bkfClient->getId())
            ;

            $this->notify(
                "Utworzylem nowe dane do faktury dla uzytkownika {$bkfClient->getCmEmailAccount()}:\n" .
                " * nazwę firmy {$bkfClient->getOrganizationName()} ,\n" .
                " * NIP {$bkfClient->getFullNip()},\n" .
                " * kraj {$bkfClient->getCountry()}",
                true
            );
            $this->em->persist($subscriber);
            $this->em->flush();
        }


        if (
            ($bkfClient->getFullNip() !== $subscriber->getTaxNumber() && !empty($subscriber->getTaxNumber()))  ||
            ($bkfClient->getOrganizationName() !== $subscriber->getName() && !empty($subscriber->getName()))
        ) {
            $this->notify(
                "zmieniam dane do faktury wlasciciela  {$bkfClient->getCmEmailAccount()}:\n" .
                " * nazwę firmy {$subscriber->getName()} -> {$bkfClient->getOrganizationName()} ,\n" .
                " * NIP {$subscriber->getTaxNumber()} -> {$bkfClient->getFullNip()},\n" .
                " * kraj {$subscriber->getCountry()->value} -> {$bkfClient->getCountry()}",
                true
            );
        }


        $subscriber
            ->setTaxNumber($bkfClient->getFullNip())
            ->setName($bkfClient->getOrganizationName())
            ->setCountry($country)
            ->setOwnerBkf($bkfClient->getId())
        ;

        if ($bkfClient->getAdres() && $bkfClient->getPostcode() && $bkfClient->getCity()) {
            $subscriber
                ->setAddress($subscriber->getAddress() ?? $bkfClient->getAdres())
                ->setPostCode($subscriber->getPostCode() ?? $bkfClient->getPostcode())
                ->setCity($subscriber->getCity() ?? $bkfClient->getCity())
            ;
        }
        return $subscriber;
    }

    private function updateCarwash(Carwashes $carwash, ?Subscribers $subscriber, Device $bkfApiDevice): void
    {

        $newName = $this->getNewName($carwash, $bkfApiDevice);
        if ($carwash->getStreet() != $bkfApiDevice->getAddress()) {
            $this->notify(
                "Zmieniam ulice myjni {$carwash->getSerialNumber()}:" .
                "\n\tz\t{$carwash->getStreet()}" .
                "\n\tna\t{$bkfApiDevice->getAddress()}"
            );
        }

        if ($carwash->getCity() != $bkfApiDevice->getCity() && !empty($carwash->getCity())) {
            $this->notify(
                "Zmieniam miasto myjni {$carwash->getSerialNumber()}:" .
                "\n\tz\t{$carwash->getCity()}" .
                "\n\tna\t{$bkfApiDevice->getCity()}"
            );
        }

        if ($carwash->getSubscriber()?->getId() != $subscriber?->getId() && !empty($carwash->getSubscriber())) {
            $this->notify(
                "Zmieniam właściciela {$carwash->getSerialNumber()}:" .
                "\n\tz\t{$carwash->getSubscriber()->getName()}" .
                "\n\tna\t{$subscriber?->getName()}"
            );
        }

        $carwash
            ->setSerialNumber($bkfApiDevice->getSerialNumber())
            ->setWarrantyVoided($bkfApiDevice->getWarrantyTo())
            ->setStartDate($bkfApiDevice->getTimeStart())
            ->setProduct($bkfApiDevice->getDeviceCode()->getMainName())
            ->setName($newName)
            ->setCity($bkfApiDevice->getCity())
            ->setStreet($bkfApiDevice->getAddress())
            ->setSubscriber($subscriber)
        ;
    }

    private function updateUser(?string $email, ?Subscribers $subscriber): ?User
    {

        if (is_null($email)) {
            return null;
        }

        if (is_null($subscriber)) {
            return null;
        }

        // czy ten użytkownik istnieje
        $user = $this->userRepository->findOneBy(['email' => $email]);

        if (is_null($user)) {
            $this->notify(
                "automatycznie tworzę właściciela $email" .
                          " bkf-id: {$subscriber->getOwnerBkf()}" .
                          " z kraju {$subscriber->getCountry()->value}",
                true
            );

            return $this->userManager->createSimpleUser(
                subscriber: $subscriber,
                email: $email,
                comment: "użytkownik utworzony automatycznie na podstawie danych JR"
            );
        }

        if ($user->getSubscriber()->getId() != $subscriber->getId()) {
            return null;
        }

        return $user;
    }
    private function notify(string $message, bool $sentry = false): void
    {
        $this->logger->notice(
            $message
        );

        if ($sentry) {
            captureMessage($message);
        }
    }

    private function getNewName(Carwashes $carwash, Device $bkfApiDevice): ?string
    {
        $newName = $bkfApiDevice->getObjectName();
        if (!empty($bkfApiDevice->getObjectNumber())) {
            $newName .= " / " . $bkfApiDevice->getObjectNumber();
        }

        if (
            ($carwash->getName() != $newName)
        ) {
            $this->notify(
                "Zmieniam nazwe myjni {$carwash->getSerialNumber()}:" .
                "\n\tz\t{$carwash->getName()}" .
                "\n\tna\t$newName"
            );
        }
        return $newName;
    }

    private function updateDealer(?Subscribers $subscribers)
    {
        if (is_null($subscribers)) {
            return;
        }

        // nie przepinam dilera, tylko dodaje gdy jest pusty
        if (!is_null($subscribers->getDealer())) {
            return;
        }
        $dealer = null;
        switch ($subscribers->getCountry()) {
            case Country::CZ:
                $dealer = $this->subscribersRepository->find(1947); // bkf mycky
                break ;
            case Country::HR:
                $dealer = $this->subscribersRepository->find(397); // kroma
                break;
        }

        if ($dealer) {
            $this->notify(
                "ustawiam dilera {$dealer->getName()} dla {$subscribers->getName()}, NIP: {$subscribers->getTaxNumber()}",
                true
            );
            $subscribers->setDealer($dealer);
        }

        return;
    }
}

<?php

namespace App\Service\Listeners;

use App\Entity\Logs;
use App\Entity\Subscribers;
use App\Entity\User;
use App\Repository\LoggerRepository;
use Doctrine\Bundle\DoctrineBundle\Attribute\AsEntityListener;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\Event\PreUpdateEventArgs;
use Doctrine\ORM\Events;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;
use Doctrine\DBAL\Connection;

#[AsEntityListener(event: Events::preUpdate, method: 'preUpdate', entity: Subscribers::class)]
class SubscribersListener
{
    public function __construct(
        private TokenStorageInterface $tokenStorage,
        private LoggerRepository $loggerRepository
    ) {
    }

    public function preUpdate(Subscribers $subscriber, PreUpdateEventArgs $args)
    {
        //$subscriber = $args->getObject();
        $this->checkField($subscriber, $args, 'nip');
        $this->checkField($subscriber, $args, 'name');
        $this->checkField($subscriber, $args, 'email');
        $this->checkField($subscriber, $args, 'address');
        $this->checkField($subscriber, $args, 'regon');
        $this->checkField($subscriber, $args, 'postCode');
        $this->checkField($subscriber, $args, 'city');
        $this->checkField($subscriber, $args, 'country');
        $this->checkField($subscriber, $args, 'bankAccountNumber');
        $this->checkField($subscriber, $args, 'ownerBkf');
        $this->checkField($subscriber, $args, 'subscriptionDiscount');
        $this->checkField($subscriber, $args, 'timezone');
        $this->checkField($subscriber, $args, 'language');
        $this->checkField($subscriber, $args, 'currency');
    }

    private function checkField(Subscribers $subscriber, PreUpdateEventArgs $args, $field)
    {

        if ($args->hasChangedField($field)) {
            $oldValue = $args->getOldValue($field);
            $newValue = $args->getNewValue($field);

            $editedByUser = $this->tokenStorage->getToken()?->getUserIdentifier() ?? '????';

            $this->loggerRepository->log(
                subscriber: $subscriber,
                user: null,
                carwash: null,
                source: self::class,
                message: "changing by $editedByUser subscriber.$field: $oldValue -> $newValue",
                level: Logs::LEVEL_CHANGELOG
            );
        }
    }
}

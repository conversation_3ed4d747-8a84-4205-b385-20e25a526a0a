<?php

namespace App\Service\Listeners;

use App\Entity\Carwashes;
use App\Entity\Logs;
use App\Repository\LoggerRepository;
use Doctrine\Bundle\DoctrineBundle\Attribute\AsEntityListener;
use Doctrine\ORM\Event\PreUpdateEventArgs;
use Doctrine\ORM\Events;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;

#[AsEntityListener(event: Events::preUpdate, method: 'preUpdate', entity: Carwashes::class)]
class CarwashListener
{
    public function __construct(
        private TokenStorageInterface $tokenStorage,
        private LoggerRepository $loggerRepository
    ) {
    }

    public function preUpdate(Carwashes $carwash, PreUpdateEventArgs $args)
    {
        $this->checkField($carwash, $args, 'name');
        $this->checkField($carwash, $args, 'userDefinedName');
        $this->checkField($carwash, $args, 'serialNumber');
        //$this->checkField($carwash, $args, 'startDate');
        //$this->checkField($carwash, $args, 'warrantyVoided');
        $this->checkField($carwash, $args, 'street');
        $this->checkField($carwash, $args, 'city');
        $this->checkField($carwash, $args, 'country');
        $this->checkField($carwash, $args, 'product');
        $this->checkField($carwash, $args, 'unsubscribed');
        $this->checkField($carwash, $args, 'subscriber');
    }

    private function checkField(Carwashes $carwash, PreUpdateEventArgs $args, $field)
    {

        if ($args->hasChangedField($field)) {
            $oldValue = $args->getOldValue($field);
            $newValue = $args->getNewValue($field);

            $editedByUser = $this->tokenStorage->getToken()?->getUserIdentifier() ?? '????';

            $this->loggerRepository->log(
                subscriber: $carwash->getSubscriber(),
                user: null,
                carwash: $carwash,
                source: self::class,
                message: "changing by $editedByUser carwash.$field: $oldValue -> $newValue",
                level: Logs::LEVEL_CHANGELOG
            );
        }
    }
}

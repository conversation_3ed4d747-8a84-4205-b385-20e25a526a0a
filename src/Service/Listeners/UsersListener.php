<?php

namespace App\Service\Listeners;

use App\Entity\Logs;
use App\Entity\User;
use App\Repository\LoggerRepository;
use Doctrine\Bundle\DoctrineBundle\Attribute\AsEntityListener;
use Doctrine\ORM\Event\PreUpdateEventArgs;
use Doctrine\ORM\Events;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;

use function Sentry\captureMessage;

#[AsEntityListener(event: Events::preUpdate, method: 'preUpdate', entity: User::class)]
class UsersListener
{
    public function __construct(
        private TokenStorageInterface $tokenStorage,
        private LoggerRepository $loggerRepository
    ) {
    }

    public function preUpdate(User $user, PreUpdateEventArgs $args)
    {
        $this->checkField($user, $args, 'email');
        $this->checkField($user, $args, 'firstname');
        $this->checkField($user, $args, 'lastname');
        $this->checkField($user, $args, 'language');
        $this->checkField($user, $args, 'timezone');
        $this->checkField($user, $args, 'comment');
        $this->checkField($user, $args, 'phone');
        $this->checkField($user, $args, 'mobileAppVer');
    }

    private function checkField(User $user, PreUpdateEventArgs $args, $field)
    {

        if ($args->hasChangedField($field)) {
            $oldValue = $args->getOldValue($field);
            $newValue = $args->getNewValue($field);

            $editedByUser = $this->tokenStorage->getToken()?->getUserIdentifier() ?? '????';
            $this->loggerRepository->log(
                subscriber: $user->getSubscriber(),
                user: $user,
                carwash: null,
                source: self::class,
                message: "changing by $editedByUser user.$field: $oldValue -> $newValue",
                level: Logs::LEVEL_CHANGELOG
            );
        }
    }
}

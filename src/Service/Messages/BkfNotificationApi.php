<?php

namespace App\Service\Messages;

use App\Entity\MobileToken;
use App\Entity\User;
use App\Service\Messages\Model\MessageModel;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;
use Symfony\Component\Serializer\SerializerInterface;
use Symfony\Contracts\HttpClient\Exception\ClientExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\RedirectionExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\ServerExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface;
use Symfony\Contracts\HttpClient\HttpClientInterface;

class BkfNotificationApi implements NotificationInterface
{
    public function __construct(
        private readonly TokenStorageInterface $tokenStorage,
        private readonly SerializerInterface $serializer,
        private readonly HttpClientInterface $notificationClient,
    ) {
    }

    private function getUserId(): int
    {
        /** @var User $user */
        $user = $this->tokenStorage->getToken()->getUser();
        return $user->getId();
    }

    private function getUserMobileTokens(): array
    {
        /** @var User $user */
        $user = $this->tokenStorage->getToken()->getUser();

        return $user->getMobileTokens()->map(function (MobileToken $mobileToken) {
            return $mobileToken->getToken();
        })->toArray();
    }

    /**
     * @throws TransportExceptionInterface
     * @throws ServerExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ClientExceptionInterface
     */
    public function getMessagesForUser(): string
    {
        $response = $this->notificationClient->request(
            'GET',
            '/api/user/' . $this->getUserId() . '/messages'
        );

        return $response->getContent();
    }

    /**
     * @throws TransportExceptionInterface
     * @throws ServerExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ClientExceptionInterface
     */
    public function getUnreadMessagesForUser(): string
    {
        $response = $this->notificationClient->request(
            'GET',
            '/api/user/' . $this->getUserId() . '/messages/unread'
        );

        return $response->getContent();
    }

    /**
     * @throws TransportExceptionInterface
     * @throws ServerExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ClientExceptionInterface
     */
    public function markMessageRead(int $massageId): void
    {
        $this->notificationClient->request(
            'PATCH',
            '/api/user/' . $this->getUserId() . '/messages/' . $massageId . '/read',
            [
                'body' => json_encode($this->getUserMobileTokens()),
            ]
        );
    }

    /**
     * @throws TransportExceptionInterface
     */
    public function sendNewMessage(MessageModel $message): void
    {
        $json = $this->serializer->serialize(
            $message,
            'json',
        );

        $this->notificationClient->request(
            'POST',
            '/api/user/messages',
            [
                'body' => $json,
            ]
        );
    }

    public function markAllMessageRead(): void
    {
        $this->notificationClient->request(
            'PATCH',
            '/api/user/' . $this->getUserId() . '/messages/read'
        );
    }
}

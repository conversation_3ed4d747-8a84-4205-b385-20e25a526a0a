<?php

namespace App\Service\Messages\Model;

use DateTimeImmutable;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;

class MessageModel
{
    private string $type;

    private string $title;
    private string $body;
    private ?string $icon;
    private ?string $iconColor;
    private string $carwashName;
    private Collection $users;
    private ?DateTimeImmutable $createdAt;
    private bool $allowDuplicates;
    private array $notifications = [];

    public function __construct(
        string $type,
        string $title,
        string $body,
        string $carwashName,
        array $users,
        ?DateTimeImmutable $createdAt = null,
        ?string $icon = null,
        ?string $iconColor = null,
        bool $allowDuplicates = false
    ) {
        $this->type = $type;
        $this->body = $body;
        $this->title = $title;
        $this->icon = $icon;
        $this->iconColor = $iconColor;
        $this->carwashName = $carwashName;
        $this->createdAt = $createdAt ?? new DateTimeImmutable();
        $this->users = new ArrayCollection($users);
        $this->allowDuplicates = $allowDuplicates;
    }

    public function getType(): string
    {
        return $this->type;
    }

    public function getExternalUserIds(): array
    {
        return $this->users->toArray();
    }

    public function getCreatedAt(): ?DateTimeImmutable
    {
        return $this->createdAt;
    }

    public function getContent(): array
    {
        return [
            'title' => $this->title,
            'body' => $this->body,
            'icon' => $this->icon,
            'icon_color' => $this->iconColor,
            'carwash' => $this->carwashName,
        ];
    }

    public function getAllowDuplicates(): bool
    {
        return $this->allowDuplicates;
    }

    public function addNotification(NotificationModel $notification): void
    {
        $this->notifications[] = $notification;
    }

    public function getNotifications(): array
    {
        return $this->notifications;
    }
}

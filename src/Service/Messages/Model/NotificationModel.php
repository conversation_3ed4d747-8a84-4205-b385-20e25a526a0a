<?php

namespace App\Service\Messages\Model;

class NotificationModel
{
    private string $channel;
    private string $subject;
    private string $body;
    private string $language;
    private array $recipients = [];
    private array $attachments = [];
    private array $context = [];

    public function getChannel(): string
    {
        return $this->channel;
    }

    public function setChannel(string $channel): NotificationModel
    {
        $this->channel = $channel;
        return $this;
    }

    public function getSubject(): string
    {
        return $this->subject;
    }

    public function setSubject(string $subject): NotificationModel
    {
        $this->subject = $subject;
        return $this;
    }

    public function getBody(): string
    {
        return $this->body;
    }

    public function setBody(string $body): NotificationModel
    {
        $this->body = $body;
        return $this;
    }

    public function getLanguage(): string
    {
        return $this->language;
    }

    public function setLanguage(string $language): NotificationModel
    {
        $this->language = $language;
        return $this;
    }

    public function getRecipients(): array
    {
        return $this->recipients;
    }

    public function setRecipients(array $recipients): NotificationModel
    {
        $this->recipients = $recipients;
        return $this;
    }

    public function getAttachments(): array
    {
        return $this->attachments;
    }

    public function setAttachments(array $attachments): NotificationModel
    {
        $this->attachments = $attachments;
        return $this;
    }

    public function getContext(): array
    {
        return $this->context;
    }

    public function setContext(array $context): NotificationModel
    {
        $this->context = $context;
        return $this;
    }
}

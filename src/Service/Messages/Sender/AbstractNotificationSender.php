<?php

namespace App\Service\Messages\Sender;

use App\Service\Messages\Model\MessageModel;
use App\Service\Messages\NotificationInterface;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Symfony\Contracts\Translation\TranslatorInterface;

abstract class AbstractNotificationSender implements NotificationSenderInterface
{
    protected NotificationInterface $notificationApi;
    protected TranslatorInterface $translator;

    public function __construct(
        NotificationInterface $notificationApi,
        TranslatorInterface $translator,
    ) {
        $this->notificationApi = $notificationApi;
        $this->translator = $translator;
    }

    public function sendNotification(array $data, array $users, array $attachments, $withNotifications = false): void
    {
        $userList = $this->getUsers($users);
        $msg = $this->createMessage($data, $userList);

        if ($withNotifications) {
            $notifications = $this->createNotifications($data, $users, $attachments);

            foreach ($notifications as $n) {
                $msg->addNotification($n);
            }
        }

        $this->makeApiCall($msg);
    }

    protected function createMessage(array $data, Collection $users): ?MessageModel
    {
        return null;
    }

    protected function createNotifications(array $data, array $users, array $attachments): ?Collection
    {
        return null;
    }

    protected function getUsers(array $users): Collection
    {
        return new ArrayCollection($users);
    }

    protected function makeApiCall(MessageModel $message)
    {
        $this->notificationApi->sendNewMessage($message);
    }
}

<?php

namespace App\Service\Alert\Model;

use App\Entity\Enum\AlertLevel;
use Symfony\Component\Serializer\Annotation\Groups;

class Alert
{
    #[Groups(["alert:info"])]
    private string $title;

    #[Groups(["alert:info"])]
    private string $text;

    #[Groups(["alert:info"])]
    private AlertLevel $level;

    #[Groups(["alert:info"])]
    private ?string $link;

    #[Groups(["alert:info"])]
    private ?string $linkText;

    #[Groups(["alert:info"])]
    private ?string $apiCall;

    #[Groups(["alert:info"])]
    private ?string $apiCallMethod;

    public function getTitle(): string
    {
        return $this->title;
    }

    public function setTitle(string $title): Alert
    {
        $this->title = $title;
        return $this;
    }

    public function getText(): string
    {
        return $this->text;
    }

    public function setText(string $text): Alert
    {
        $this->text = $text;
        return $this;
    }

    public function getLevel(): AlertLevel
    {
        return $this->level;
    }

    public function setLevel(AlertLevel $level): Alert
    {
        $this->level = $level;
        return $this;
    }

    public function getLink(): ?string
    {
        return $this->link;
    }

    public function setLink(?string $link): Alert
    {
        $this->link = $link;
        return $this;
    }

    public function getLinkText(): ?string
    {
        return $this->linkText;
    }

    public function setLinkText(?string $linkText): Alert
    {
        $this->linkText = $linkText;
        return $this;
    }

    public function getApiCall(): ?string
    {
        return $this->apiCall;
    }

    public function setApiCall(?string $apiCall): Alert
    {
        $this->apiCall = $apiCall;
        return $this;
    }

    public function getApiCallMethod(): ?string
    {
        return $this->apiCallMethod;
    }

    public function setApiCallMethod(?string $apiCallMethod): Alert
    {
        $this->apiCallMethod = $apiCallMethod;
        return $this;
    }
}

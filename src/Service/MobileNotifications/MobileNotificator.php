<?php

namespace App\Service\MobileNotifications;

use App\Service\MobileTokenManager;
use Kreait\Firebase\Exception\FirebaseException;
use Kreait\Firebase\Exception\MessagingException;
use Kreait\Firebase\Contract\Messaging;
use Kreait\Firebase\Messaging\ApnsConfig;
use Kreait\Firebase\Messaging\AndroidConfig;
use Kreait\Firebase\Messaging\CloudMessage;
use Kreait\Firebase\Messaging\MessageData;
use Kreait\Firebase\Messaging\MulticastSendReport;
use Kreait\Firebase\Messaging\Notification;
use Symfony\Contracts\Translation\TranslatorInterface;

class MobileNotificator
{
    public function __construct(
        private readonly Messaging $messaging,
        protected readonly TranslatorInterface $translator,
        private readonly MobileTokenManager $mobileTokenManager
    ) {
    }

    /**
     * @throws MessagingException
     * @throws FirebaseException
     */
    protected function sendNotifications(MessageData $messageData, Notification $notification, array $tokens): void
    {
        $message = CloudMessage::new()
            ->withData($messageData)
            ->withNotification($notification)
            ->withDefaultSounds()
            ->withAndroidConfig(AndroidConfig::fromArray(['collapse_key' => 'alarm']))
            ->withHighestPossiblePriority();

        $sendReport = $this->messaging->sendMulticast($message, $tokens);
        $this->removeUnusedMobileTokens($sendReport);
    }

    protected function removeUnusedMobileTokens(MulticastSendReport $sendReport): void
    {
        if (!$sendReport->hasFailures()) {
            return;
        }

        foreach ($sendReport->invalidTokens() as $tokenString) {
            $this->mobileTokenManager->removeMobileTokenByTokenString($tokenString);
        }

        $this->mobileTokenManager->flush();
    }
}

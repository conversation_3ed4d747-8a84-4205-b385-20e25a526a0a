<?php

namespace App\Service\MobileNotifications;

use App\Entity\Carwashes;
use Kreait\Firebase\Exception\FirebaseException;
use Kreait\Firebase\Exception\MessagingException;
use Kreait\Firebase\Messaging\MessageData;
use Kreait\Firebase\Messaging\Notification;

class CarwashAlarmsNotificator extends MobileNotificator
{
    /**
     * @throws MessagingException
     * @throws FirebaseException
     */
    public function sendAlarmNotifications(
        array $tokens,
        Carwashes $carwash,
        string $description
    ): void {
        $messageData = $this->getMessageData($carwash);
        $notification = $this->getNotification($carwash, $description);

        $this->sendNotifications($messageData, $notification, $tokens);
    }

    private function getMessageData(Carwashes $carwash): MessageData
    {
        return MessageData::fromArray([
            'click_action'   => 'FLUTTER_NOTIFICATION_CLICK',
            'carwash_serial' => (string)$carwash->getSerialNumber(),
        ]);
    }

    private function getNotification(
        Carwashes $carwash,
        string $description
    ): Notification {
        return Notification::fromArray([
            'title' => $this->translator->trans('reports.email-alarms-title') . ' ' . $carwash
                    ->getName(),
            'body'  => $description,
        ]);
    }
}

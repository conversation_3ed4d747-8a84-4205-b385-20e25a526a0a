<?php

declare(strict_types=1);

namespace App\TokenManager;

use App\Entity\ApiAccessToken;
use App\Entity\User;
use App\Repository\ApiAccessTokenRepository;
use DateInterval;
use DateTimeImmutable;
use Exception;

class ApiTokenManager implements ApiTokenManagerInterface
{
    public function __construct(
        private readonly ApiAccessTokenRepository $accessTokenRepository,
    ) {
    }

    /**
     * @throws Exception
     */
    public function generateTokens(User $user): ApiAccessToken
    {
        return $this->generateAccessToken($user);
    }

    /**
     * @throws Exception
     */
    private function getAccessTokenString(): string
    {
        return rtrim(strtr(base64_encode(random_bytes(32)), '+/', '-_'), '=');
    }

    /**
     * @throws Exception
     */
    private function generateAccessToken(User $user): ApiAccessToken
    {
        $expiredAt = (new DateTimeImmutable())->add(new DateInterval('P31D'));
        $tokenString = $this->getAccessTokenString();

        $accessToken = (new ApiAccessToken())
            ->setUser($user)
            ->setToken($tokenString)
            ->setExpiresAt($expiredAt);

        $this->accessTokenRepository->save($accessToken, true);

        return $accessToken;
    }
}

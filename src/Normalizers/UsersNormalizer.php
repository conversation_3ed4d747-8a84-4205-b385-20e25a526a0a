<?php

namespace App\Normalizers;

use App\Entity\User;
use App\Service\Alert\AlertUserService;
use Symfony\Component\Security\Core\Role\RoleHierarchyInterface;
use Symfony\Component\Serializer\Normalizer\NormalizerInterface;
use Symfony\Component\Serializer\Normalizer\ObjectNormalizer;

class UsersNormalizer implements NormalizerInterface
{
    public function __construct(
        private ObjectNormalizer $normalizer,
        private RoleHierarchyInterface $roleHierarchy,
        private AlertUserService $alertUserService,
    ) {
    }
    public function normalize($object, string $format = null, array $context = [])
    {
        /** @var User $object */
        $groups = $context['groups'] ?? null;

        // na razie nic nie zwracamy
        $details = [];
        $object->setDetails($details);

        $lang = null;
        if (isset($context['language'])) {
            $lang = $context['language'];
        }

        if (in_array('user:info', $groups)) {
            $info = $this->alertUserService->get($object, $lang);
            $object->setInfo($info);
        }

        $object->setFullRoles(
            array_values(array_unique($this->roleHierarchy->getReachableRoleNames($object->getRoles()))),
        );
        $data = $this->normalizer->normalize($object, $format, $context);
        return $data;
    }

    public function supportsNormalization($data, string $format = null)
    {
        return  ($data instanceof User);
    }
}

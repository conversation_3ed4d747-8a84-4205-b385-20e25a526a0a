<?php

namespace App\Listeners;

use App\Entity\User;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\HttpFoundation\Cookie;
use Symfony\Component\HttpKernel\KernelEvents;
use Symfony\Component\Security\Http\Event\InteractiveLoginEvent;
use Symfony\Component\Security\Http\SecurityEvents;

class LocaleListener implements EventSubscriberInterface
{
    /** @var string */
    private $defaultLocale;

    /** @var string|null */
    private $locale;

    /** @var string */
    private $location = null;

    /**
     * LocaleListener constructor.
     * @param string $defaultLocale
     */
    public function __construct($defaultLocale = 'en')
    {
        $this->defaultLocale = $defaultLocale;
        $this->locale = null;
    }

    /**
     * Get subscribed events
     *
     * @return array
     */
    public static function getSubscribedEvents()
    {
        return [
            KernelEvents::REQUEST => [['onKernelRequest', 15]],
            KernelEvents::RESPONSE => [['onKernelResponse', 15]],
            SecurityEvents::INTERACTIVE_LOGIN => [['onInteractiveLogin', 15]],
        ];
    }

    /**
     * OnKernelRequest event
     */
    public function onKernelRequest($event)
    {
        $request = $event->getRequest();
        $session = $request->getSession();

        if (!$request->hasPreviousSession()) {
            if ($locale = $request->get('locale')) {
                // If yes, then set locale in current request,
                // session and mark for save it later as cookie
                $session->set('_locale', $locale);
                $request->setLocale($locale);
                $this->locale = $locale;
            }
            return;
        }

        if ($this->location) {
            date_default_timezone_set($this->location);
        }

        // Check if locale is defined explicitly as routing parameter (in url)
        if ($locale = $request->get('locale')) {
            // If yes, then set locale in current request,
            // session and mark for save it later as cookie
            $session->set('_locale', $locale);
            $request->setLocale($locale);
            $this->locale = $locale;

            // Check if locale is set in cookie
            // If yes, then set locale from cookie
        } elseif ($locale = $request->cookies->get('_locale')) {
            $session->set('_locale', $locale);
            $request->setLocale($locale);

            // Default policy - set locale from current session (required for subRequests)
        } else {
            $request->setLocale($session->get('_locale', $this->defaultLocale));
            $session->set('_locale', $this->defaultLocale);
        }
    }

    /**
     * OnKernelResponse event
     */
    public function onKernelResponse($event)
    {
        // Save locale to cookie
        if ($locale = $this->locale) {
            $event->getResponse()
                ->headers
                ->setCookie(new Cookie('_locale', $locale));
        }
    }

    /**
     * OnInteractiveLoginEvent
     */
    public function onInteractiveLogin(InteractiveLoginEvent $event)
    {
        /** @var User $user */
        $user = $event->getAuthenticationToken()->getUser();
        $session = $event->getRequest()->getSession();

        // Get locale from user profile
        $language = $user->getLanguage();
        $session->set('_locale', $language->getLocale());
        $this->locale = $language->getLocale();
    }
}

<?php

namespace App\Entity\Subscription;

use I2m\StandardTypes\Enum\CMSubscription;
use App\Entity\Subscribers;
use Doctrine\ORM\Mapping as ORM;
use I2m\StandardTypes\Enum\Currency;
use Symfony\Component\Serializer\Annotation\Groups;

#[ORM\Entity(repositoryClass: 'App\Repository\Subscription\SubscriptionPackagesRepository')]
#[ORM\Table(name: 'owner_subscription_packages')]
class OwnerSubscriptionPackages
{
    #[ORM\Id]
    #[ORM\GeneratedValue(strategy: 'AUTO')]
    #[ORM\Column(type: 'integer')]
    #[Groups(["plans:read", "subscription:read"])]
    protected $id;

    #[Groups(["plans:read", "subscription:read"])]
    #[ORM\Column(enumType: Currency::class, length: 3)]
    private ?Currency $currency = null;

    #[ORM\Column(type: 'string', enumType: CMSubscription::class)]
    #[Groups(["plans:read", "subscription:read"])]
    private CMSubscription $code;

    #[ORM\Column(type: 'float')]
    #[Groups(["plans:read", "subscription:read"])]
    private float $value;

    #[ORM\Column(name: 'month_length', type: 'integer')]
    #[Groups(["plans:read", "subscription:read"])]
    private int $monthsLength;

    #[ORM\Column(name: 'status', type: 'string', length: 1)]
    private string $status = 'a';
    #[ORM\ManyToOne]
    private ?Subscribers $dealer = null;

    /**
     * @return mixed
     */
    public function getId()
    {
        return $this->id;
    }
    public function getCurrency(): Currency
    {
        return $this->currency;
    }

    #[Groups(["plans:read", "subscription:read"])]
    public function getCurrencySymbol(): string
    {
        return $this->currency->symbol();
    }

    public function setCurrency(Currency $currency): OwnerSubscriptionPackages
    {
        $this->currency = $currency;
        return $this;
    }

    public function getCode(): CMSubscription
    {
        return $this->code;
    }

    public function setCode(CMSubscription $code): static
    {
        $this->code = $code;
        return $this;
    }
    /**
     * @return mixed
     */
    public function getValue()
    {
        return $this->value;
    }
    /**
     * @param mixed $value
     * @return OwnerSubscriptionPackages
     */
    public function setValue($value)
    {
        $this->value = $value;
        return $this;
    }
    public function getStatus(): string
    {
        return $this->status;
    }
    public function setStatus(string $status): OwnerSubscriptionPackages
    {
        $this->status = $status;
        return $this;
    }
    /**
     * @return mixed
     */
    public function getMonthsLength()
    {
        return $this->monthsLength;
    }
    /**
     * @param mixed $monthsLength
     * @return OwnerSubscriptionPackages
     */
    public function setMonthsLength($monthsLength)
    {
        $this->monthsLength = $monthsLength;
        return $this;
    }

    public function getDealer(): ?Subscribers
    {
        return $this->dealer;
    }
    public function setDealer(?Subscribers $dealer): self
    {
        $this->dealer = $dealer;

        return $this;
    }
}

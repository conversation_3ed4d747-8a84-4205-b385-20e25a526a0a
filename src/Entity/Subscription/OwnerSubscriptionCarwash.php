<?php

namespace App\Entity\Subscription;

use App\Entity\Carwashes;
use App\Entity\Enum\SubscriptionCarwashType;
use I2m\StandardTypes\Enum\CMSubscription;
use App\Entity\Subscribers;
use App\Repository\Subscription\OwnerSubscriptionCarwashRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use I2m\StandardTypes\Enum\Currency;
use Symfony\Component\Serializer\Annotation\Groups;

#[ORM\Entity(repositoryClass: OwnerSubscriptionCarwashRepository::class)]
class OwnerSubscriptionCarwash
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private int $id;

    #[ORM\Column(type: Types::DATETIME_MUTABLE)]
    private ?\DateTimeInterface $ctime = null;

    #[ORM\ManyToOne(inversedBy: 'item')]
    #[ORM\JoinColumn(nullable: false)]
    private ?OwnerSubscriptionPayment $subscription = null;

    #[ORM\ManyToOne(inversedBy: 'carwashes')]
    #[ORM\JoinColumn(nullable: false)]
    #[Groups(["subscription:read"])]
    private ?Carwashes $carwash = null;

    #[ORM\Column(length: 32, enumType: SubscriptionCarwashType::class)]
    #[Groups(["subscription:read", "subscription:owner"])]
    private ?SubscriptionCarwashType $type = null;

    #[ORM\Column(type: Types::DATE_MUTABLE)]
    #[Groups(["subscription:read", "subscription:owner"])]
    private ?\DateTimeInterface $startTime = null;

    #[ORM\Column(type: Types::DATE_MUTABLE)]
    #[Groups(["subscription:read", "subscription:owner"])]
    private ?\DateTimeInterface $endTime = null;

    #[ORM\Column(length: 32, enumType: CMSubscription::class)]
    #[Groups(["subscription:read", "subscription:owner"])]
    private ?CMSubscription $code = null;

    #[ORM\Column(type: Types::DECIMAL, precision: 10, scale: 2)]
    #[Groups(["subscription:read", "subscription:owner"])]
    private float $price = 0;

    #[ORM\Column(enumType: Currency::class, length: 3)]
    #[Groups(["subscription:read", "subscription:owner"])]
    private ?Currency $currency = null;
    #[ORM\Column(type: Types::DECIMAL, precision: 10, scale: 2)]
    #[Groups(["subscription:read", "subscription:owner"])]
    private float $quantity = 0;

    #[ORM\Column(type: Types::DECIMAL, precision: 10, scale: 2)]
    #[Groups(["subscription:read", "subscription:owner"])]
    private float $totalPrice = 0;

    #[ORM\ManyToOne]
    private ?Subscribers $dealer = null;

    #[ORM\Column(nullable: true)]
    #[Groups(["subscription:read"])]
    private ?string $invoice = null;

    public function getId(): int
    {
        return $this->id;
    }

    public function getCtime(): ?\DateTimeInterface
    {
        return $this->ctime;
    }

    public function setCtime(\DateTimeInterface $ctime): self
    {
        $this->ctime = $ctime;

        return $this;
    }

    public function getCarwash(): ?Carwashes
    {
        return $this->carwash;
    }

    public function setCarwash(?Carwashes $carwash): self
    {
        $this->carwash = $carwash;

        return $this;
    }

    public function getType(): ?SubscriptionCarwashType
    {
        return $this->type;
    }

    public function setType(SubscriptionCarwashType $type): self
    {
        $this->type = $type;

        return $this;
    }

    public function getStartTime(): ?\DateTimeInterface
    {
        return $this->startTime;
    }

    public function setStartTime(\DateTimeInterface $startTime): self
    {
        $this->startTime = $startTime;

        return $this;
    }

    public function getEndTime(): ?\DateTimeInterface
    {
        return $this->endTime;
    }

    public function setEndTime(\DateTimeInterface $endTime): self
    {
        $this->endTime = $endTime;

        return $this;
    }

    public function getSubscription(): ?OwnerSubscriptionPayment
    {
        return $this->subscription;
    }

    public function setSubscription(?OwnerSubscriptionPayment $subscription): self
    {
        $this->subscription = $subscription;

        return $this;
    }

    public function getCode(): ?CMSubscription
    {
        return $this->code;
    }

    public function setCode(CMSubscription $code): self
    {
        $this->code = $code;

        return $this;
    }

    public function getPrice(): ?float
    {
        return $this->price;
    }

    public function setPrice(float $price): self
    {
        $this->price = round($price, 2);
        $this->setTotalPrice($this->quantity * $this->price);
        return $this;
    }

    public function getCurrency(): ?Currency
    {
        return $this->currency;
    }

    public function setCurrency(?Currency $currency): self
    {
        $this->currency = $currency;

        return $this;
    }

    public function getQuantity(): ?float
    {
        return $this->quantity;
    }

    public function setQuantity(float $quantity): self
    {
        $this->quantity = round($quantity, 2);
        $this->setTotalPrice($this->quantity * $this->price);

        return $this;
    }

    public function getTotalPrice(): ?float
    {
        return $this->totalPrice;
    }

    public function setTotalPrice(float $totalPrice): self
    {
        $this->totalPrice = round($totalPrice, 2);
        return $this;
    }

    #[Groups(["subscription:read", "subscription:owner"])]
    public function getPosition()
    {
        return $this->getCarwash()->getSerialNumber() . " - " . $this->getCarwash()->getName();
    }

    public function getDealer(): ?Subscribers
    {
        return $this->dealer;
    }

    public function setDealer(?Subscribers $dealer): self
    {
        $this->dealer = $dealer;
        return $this;
    }

    public function getInvoice(): ?string
    {
        return $this->invoice;
    }

    public function setInvoice(?string $invoice): OwnerSubscriptionCarwash
    {
        $this->invoice = $invoice;
        return $this;
    }

    public function addInvoice(?string $invoice): OwnerSubscriptionCarwash
    {
        if (is_null($this->invoice)) {
            $this->invoice = $invoice;
            return $this;
        }

        $this->invoice .= " ,$invoice";

        return $this;
    }
}

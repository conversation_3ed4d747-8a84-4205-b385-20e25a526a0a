<?php

namespace App\Entity;

use DateTime;
use <PERSON>trine\ORM\Mapping as ORM;
use Symfony\Component\Serializer\Annotation\Groups;

/**
 * Timezone
 *
 *
 *
 */
#[ORM\Table(name: 'timezones')]
#[ORM\Entity(repositoryClass: 'App\Repository\TimezonesRepository')]
#[ORM\HasLifecycleCallbacks]
class Timezones
{
    #[ORM\Column(name: 'id', type: 'integer')]
    #[ORM\Id]
    #[ORM\GeneratedValue(strategy: 'AUTO')]
    #[ORM\SequenceGenerator(sequenceName: 'timezone_id_seq', allocationSize: 1, initialValue: 1)]
    #[Groups(["timezone:basic"])]
    private $id;

    /**
     * @var string
     */
    #[ORM\Column(name: 'status', type: 'string', length: 1)]
    private $status;
    /**
     * @var string
     */
    #[ORM\Column(name: 'location', type: 'string', length: 64)]
    #[Groups(["timezone:basic"])]
    private $location;
    /**
     * @var string
     */
    #[ORM\Column(name: 'gmt', type: 'string', length: 64)]
    private $gmt;
    /**
     * @var integer
     */
    #[ORM\Column(name: 'time_offset', type: 'integer')]
    private $timeOffset;

    /**
     * Get id
     *
     * @return integer
     */
    public function getId()
    {
        return $this->id;
    }

    public function setId(?int $id): self
    {
        $this->id = $id;
        return $this;
    }

    /**
     * Set status
     *
     * @param string $status
     * @return Timezones
     */
    public function setStatus($status)
    {
        $this->status = $status;

        return $this;
    }
    /**
     * Get status
     *
     * @return string
     */
    public function getStatus()
    {
        return $this->status;
    }
    /**
     * Set location
     *
     * @param string $location
     * @return Timezones
     */
    public function setLocation($location)
    {
        $this->location = $location;

        return $this;
    }
    /**
     * Get location
     *
     * @return string
     */
    public function getLocation()
    {
        return $this->location;
    }
    /**
     * Set gmt
     *
     * @param string $gmt
     * @return Timezones
     */
    public function setGmt($gmt)
    {
        $this->gmt = $gmt;

        return $this;
    }
    /**
     * Get gmt
     *
     * @return string
     */
    public function getGmt()
    {
        return $this->gmt;
    }
    /**
     * Set timeOffset
     *
     * @param integer $timeOffset
     * @return Timezones
     */
    public function setTimeOffset($timeOffset)
    {
        $this->timeOffset = $timeOffset;

        return $this;
    }
    /**
     * Get timeOffset
     *
     * @return integer
     */
    public function getTimeOffset()
    {
        return $this->timeOffset;
    }

    public function getDateTimeZone(): \DateTimeZone
    {
        return new \DateTimeZone($this->location);
    }

    public function __toString(): string
    {
        return $this->location;
    }
}

<?php

namespace App\Entity;

use App\Repository\UserReportsRepository;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Serializer\Annotation\Groups;

#[ORM\Table(name: 'cm_users_reports')]
#[ORM\Entity(repositoryClass: UserReportsRepository::class)]
class UserReports
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private int $id;

    #[ORM\Column(name: 'programs_usage_settings', type: 'string', nullable: true)]
    #[Groups(["user:reports"])]
    private ?string $programsUsageSettings = null;

    #[ORM\Column(name: 'turnover_total_settings', type: 'string', nullable: true)]
    #[Groups(["user:reports"])]
    private ?string $turnoverTotalSettings = 'monthly';

    #[ORM\Column(name: 'mobile_payments_settings', type: 'string', nullable: true)]
    #[Groups(["user:reports"])]
    private ?string $mobilePaymentsSettings = null;

    #[ORM\Column(name: 'alarms_settings', type: 'string', nullable: true)]
    #[Groups(["user:reports"])]
    private ?string $alarmsSettings = null;

    #[ORM\Column(name: 'alarms_mobile_settings', type: 'string', nullable: true)]
    #[Groups(["user:reports"])]
    private ?string $alarmsMobileSettings = null;

    #[ORM\Column(name: 'money_collect_alarms', type: 'boolean', nullable: true)]
    #[Groups(["user:reports"])]
    private ?bool $moneyCollectAlarms = false;

    #[ORM\Column(name: 'fiscal_transactions_settings', type: 'string', nullable: true)]
    #[Groups(["user:reports"])]
    private ?string $fiscalTransactionsSettings = null;

    #[ORM\Column(type: 'string', nullable: true)]
    #[Groups(["user:reports"])]
    private ?string $loyaltyReport = null;

    #[ORM\OneToOne(inversedBy: 'reportSettings', cascade: ['persist', 'remove'])]
    #[ORM\JoinColumn(nullable: false)]
    private ?User $user = null;

    public function getId(): int
    {
        return $this->id;
    }

    public function setProgramsUsageSettings(?string $programsUsageSettings): self
    {
        $this->programsUsageSettings = $programsUsageSettings;

        return $this;
    }

    public function getProgramsUsageSettings(): ?string
    {
        return $this->programsUsageSettings;
    }

    public function setTurnoverTotalSettings(?string $turnoverTotalSettings): self
    {
        $this->turnoverTotalSettings = $turnoverTotalSettings;

        return $this;
    }

    public function getTurnoverTotalSettings(): ?string
    {
        return $this->turnoverTotalSettings;
    }

    public function setMobilePaymentsSettings(?string $mobilePaymentsSettings): self
    {
        $this->mobilePaymentsSettings = $mobilePaymentsSettings;

        return $this;
    }

    /**
     * Get mobilePaymentsSettings
     *
     * @return string
     */
    public function getMobilePaymentsSettings(): ?string
    {
        return $this->mobilePaymentsSettings;
    }

    public function setAlarmsSettings(?string $alarmsSettings): self
    {
        $this->alarmsSettings = $alarmsSettings;

        return $this;
    }

    public function getAlarmsSettings(): ?string
    {
        return $this->alarmsSettings;
    }

    public function setMoneyCollectAlarms(?bool $moneyCollectAlarms): self
    {
        $this->moneyCollectAlarms = $moneyCollectAlarms;

        return $this;
    }

    public function getMoneyCollectAlarms(): ?bool
    {
        return $this->moneyCollectAlarms;
    }

    /**
     * Set user
     *
     *
     * @return UserReports
     */
    public function setUser(User $user = null)
    {
        $this->user = $user;

        return $this;
    }

    /**
     * Get user
     *
     * @return User
     */
    public function getUser(): ?User
    {
        return $this->user;
    }

    public function getAlarmsMobileSettings(): ?string
    {
        return $this->alarmsMobileSettings;
    }

    public function setAlarmsMobileSettings(?string $alarmsMobileSettings): self
    {
        $this->alarmsMobileSettings = $alarmsMobileSettings;

        return $this;
    }

    /**
     * @return string
     */
    public function getFiscalTransactionsSettings(): ?string
    {
        return $this->fiscalTransactionsSettings;
    }

    /**
     * @param string $fiscalTransactionsSettings
     */
    public function setFiscalTransactionsSettings(?string $fiscalTransactionsSettings): UserReports
    {
        $this->fiscalTransactionsSettings = $fiscalTransactionsSettings;
        return $this;
    }

    public function getLoyaltyReport(): ?string
    {
        return $this->loyaltyReport;
    }

    public function setLoyaltyReport(?string $loyaltyReport): UserReports
    {
        $this->loyaltyReport = $loyaltyReport;
        return $this;
    }
}

<?php

namespace App\Entity;

use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Serializer\Annotation\Groups;

/**
 * Country
 *
 */
#[ORM\Table(name: 'country')]
#[ORM\Entity(repositoryClass: 'App\Repository\CountryRepository')]
class Country
{
    /**
     * @var integer
     */
    #[ORM\Column(name: 'id', type: 'integer', nullable: false)]
    #[ORM\Id]
    #[ORM\GeneratedValue(strategy: 'AUTO')]
    #[ORM\SequenceGenerator(sequenceName: 'country_id_seq', allocationSize: 1, initialValue: 1)]
    #[Groups(["country:basic"])]
    private $id;
    #[Groups(['admin:list',"user_invoice_data","settings","country:basic"])]
    #[ORM\Column(name: 'short_name', type: 'string', length: 2, nullable: true)]
    private $shortName;
    #[ORM\Column(name: 'name', type: 'string', length: 60, nullable: true)]
    #[Groups(['admin:list',"user_invoice_data","settings","carwash:list","country:basic"])]
    private $name;
    /**
     * @var bool
     */
    #[ORM\Column(name: 'in_european_union', type: 'boolean', nullable: true, options: ['default' => false])]
    private $inEuropeanUnion;
    /**
     * Get id
     *
     * @return integer
     */
    public function getId()
    {
        return $this->id;
    }

    public function setId(int $id): Country
    {
        $this->id = $id;
        return $this;
    }


    /**
     * Set shortName
     *
     * @param string $shortName
     *
     * @return Country
     */
    public function setShortName($shortName)
    {
        $this->shortName = $shortName;

        return $this;
    }
    /**
     * Get shortName
     *
     * @return string
     */
    public function getShortName()
    {
        return $this->shortName;
    }
    /**
     * Set name
     *
     * @param string $name
     *
     * @return Country
     */
    public function setName($name)
    {
        $this->name = $name;

        return $this;
    }
    /**
     * Get name
     *
     * @return string
     */
    public function getName()
    {
        return $this->name;
    }
    /**
     * Flag returned true if country is in Union European
     */
    public function isInEuropeanUnion(): bool
    {
        return $this->inEuropeanUnion;
    }
    /**
     * Set flag indicated if country is in Union European
     *
     * @return $this
     */
    public function setInEuropeanUnion(bool $inEuropeanUnion)
    {
        $this->inEuropeanUnion = $inEuropeanUnion;

        return $this;
    }

    public function __toString(): string
    {
        return $this->name;
    }
}

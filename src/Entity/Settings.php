<?php

namespace App\Entity;

use App\Repository\SettingsRepository;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Serializer\Annotation\Groups;

#[ORM\Table(name: 'settings')]
#[ORM\Entity(repositoryClass: SettingsRepository::class)]
class Settings
{
    #[Groups(['settings_list'])]
    #[ORM\Column(name: 'id', type: 'integer')]
    #[ORM\Id]
    #[ORM\GeneratedValue(strategy: 'AUTO')]
    private int $id;

    #[ORM\ManyToOne(targetEntity: 'App\Entity\User')]
    #[ORM\JoinColumn(name: 'user_id', referencedColumnName: 'id')]
    private User $user;

    #[Groups(['settings_list'])]
    #[ORM\Column(name: 'namespace', type: 'string', length: 255)]
    private string $namespace;

    #[Groups(['settings_list'])]
    #[ORM\Column(name: 'value', type: 'json')]
    private $value;

    public function getId(): int
    {
        return $this->id;
    }

    public function setId(int $id): Settings
    {
        $this->id = $id;
        return $this;
    }

    public function getUser(): User
    {
        return $this->user;
    }

    public function setUser(User $user): Settings
    {
        $this->user = $user;
        return $this;
    }

    public function getNamespace(): string
    {
        return $this->namespace;
    }

    public function setNamespace(string $namespace): Settings
    {
        $this->namespace = $namespace;
        return $this;
    }

    public function getValue()
    {
        return $this->value;
    }

    public function setValue($value): Settings
    {
        $this->value = $value;
        return $this;
    }
}

<?php

namespace App\Entity;

use I2m\StandardTypes\Enum\CMSubscription;
use App\Repository\UserRepository;
use DateTimeImmutable;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\Common\Collections\ReadableCollection;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use I2m\StandardTypes\Enum\Country;
use I2m\StandardTypes\Interface\IUser;
use Symfony\Component\Security\Core\User\PasswordAuthenticatedUserInterface;
use Symfony\Component\Security\Core\User\UserInterface;
use Symfony\Component\Serializer\Annotation\Groups;

#[ORM\Entity(repositoryClass: UserRepository::class)]
#[ORM\Table(name: '`users`')]
#[ORM\HasLifecycleCallbacks]
class User implements UserInterface, PasswordAuthenticatedUserInterface, IUser
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    #[Groups(["user:details", "user:list", 'default:basic'])]
    private ?int $id = null;

    #[ORM\Column(length: 180, unique: true)]
    #[Groups(["user:details", "user:list", 'default:basic'])]
    private ?string $email = null;

    #[ORM\Column]
    #[Groups(["user:details"])]
    private array $roles = [];

    #[ORM\Column(nullable: true)]
    private ?string $password = null;

    #[ORM\Column(length: 255, nullable: true)]
    #[Groups(["user:details", "user:list"])]
    private ?string $firstname = null;

    #[ORM\Column(length: 255, nullable: true)]
    #[Groups(["user:details", "user:list"])]
    private ?string $lastname = null;

    #[ORM\Column(nullable: true)]
    private ?DateTimeImmutable $ctime = null;

    #[ORM\Column(nullable: true)]
    private ?DateTimeImmutable $mtime = null;

    #[ORM\ManyToOne]
    #[ORM\JoinColumn(nullable: false)]
    #[Groups(["user:details"])]
    private Languages $language;

    #[ORM\ManyToOne]
    #[ORM\JoinColumn(nullable: false)]
    #[Groups(["user:details"])]
    private Timezones $timezone;

    #[ORM\Column(nullable: true)]
    #[Groups(["user:details"])]
    private ?array $assignedCarwashes = [];

    #[ORM\ManyToOne(targetEntity: self::class)]
    private ?self $createdBy = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    #[Groups(["user:admin"])]
    private ?string $comment = null;

    #[ORM\Column(length: 30, nullable: true)]
    #[Groups(["user:details", "user:list"])]
    private ?string $phone = null;

    #[ORM\Column(length: 1)]
    private string $status = 'a';

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $passwordResetToken = null;

    #[ORM\Column(nullable: true)]
    private ?DateTimeImmutable $passwordRequestedAt = null;

    #[ORM\Column(nullable: true)]
    #[Groups(["user:list", "user:details"])]
    private ?DateTimeImmutable $lastLogin = null;

    #[ORM\Column]
    #[Groups(["user:details"])]
    private bool $privacyPolicyAccepted = false;

    #[ORM\Column(nullable: true)]
    private ?DateTimeImmutable $privacyPolicyAcceptedAt = null;

    #[ORM\OneToMany(mappedBy: 'user', targetEntity: ApiAccessToken::class, orphanRemoval: true)]
    private Collection $apiAccessTokens;

    #[ORM\ManyToOne(targetEntity: Subscribers::class, cascade: ['persist'], inversedBy: 'users')]
    #[Groups(["user:list", "user:details"])]
    private ?Subscribers $subscriber = null;

    #[ORM\Column(nullable: true)]
    private ?array $loyaltyApp = [];

    #[ORM\Column(type: 'boolean', nullable: true)]
    #[Groups(["user:list", "user:details"])]
    private ?bool $isDealer = false;

    #[ORM\Column(type: 'boolean', nullable: true)]
    #[Groups(["user:list", "user:details"])]
    private ?bool $isAdmin = false;

    #[ORM\OneToMany(mappedBy: 'user', targetEntity: MobileToken::class)]
    private Collection $mobileTokens;

    #[Groups(["user:list", "user:details"])]
    private ?array $details = null;

    #[Groups(["user:info"])]
    private ?array $info = null;

    #[ORM\OneToOne(mappedBy: 'user', cascade: ['persist', 'remove'])]
    private ?UserReports $reportSettings = null;

    #[ORM\Column(nullable: true)]
    #[Groups(["user:list", "user:details"])]
    private ?string $mobileAppVer = null;

    public function __construct(array $roles = [])
    {
        if (count($roles)) {
            $this->setRoles($roles);
        }
        $this->apiAccessTokens = new ArrayCollection();
        $this->mobileTokens = new ArrayCollection();
    }

    public function setId(int $id): self
    {
         $this->id = $id;
         return $this;
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getEmail(): string
    {
        return $this->email;
    }

    public function setEmail(string $email): self
    {

        $this->email = $email;

        return $this;
    }

    /**
     * A visual identifier that represents this user.
     *
     * @see UserInterface
     */
    public function getUserIdentifier(): string
    {
        return (string)$this->email;
    }

    /**
     * @deprecated since Symfony 5.3, use getUserIdentifier instead
     *
     */
    #[Groups(["user:details"])]
    public function getUsername(): string
    {
        return (string)$this->email;
    }

    public function getInfo(): ?array
    {
        return $this->info;
    }

    public function setInfo(?array $info): User
    {
        $this->info = $info;
        return $this;
    }

    public function getRoles(): array
    {
        $roles = $this->roles;
        // guarantee every user at least has ROLE_USER
        $roles[] = 'ROLE_USER';

        if ($this->isAdmin()) {
            $roles[] = 'ROLE_SUPERADMIN';
        }

        if ($this->isDealer()) {
            $roles[] = 'ROLE_CM_ADMINISTRATION';
        }

        if ($this->getLoyaltyApp()) {
            $roles[] = 'ROLE_CM_LOYAL_APP';
        }

        if ($this->getSubscriber()?->getSubscription()->isGreaterEqualThan(CMSubscription::PREMIUM)) {
            $roles[] = 'ROLE_SUBSCRIPTION_PREMIUM';
        }

        if ($this->getSubscriber()?->getSubscription()->isGreaterEqualThan(CMSubscription::BASIC)) {
            $roles[] = 'ROLE_SUBSCRIPTION_BASIC';
        }

        if ($this->isOwner()) {
            $roles = array_merge($roles, $this->getRolesList());
        }

        return array_values(array_unique($roles));
    }

    public function setRoles(array $roles): self
    {
        $this->roles = $roles;
        return $this;
    }

    public function setFullRoles(array $roles): self
    {
        $this->roles = [];
        foreach ($roles as $role) {
            $this->addRole($role);
        }
        return $this;
    }


    public function getRolesList()
    {
        return [
            'ROLE_CM_OWNER',
            'ROLE_CM',
            'ROLE_CM_FINANCE',
            'ROLE_CM_SERVICE',
            'ROLE_CM_ALARMS_AND_TECHNICAL_DATA',
            'ROLE_CM_LOYALSYSTEM_KEYLIST_AND_CLIENTS'
        ];
    }
    /**
     * @see PasswordAuthenticatedUserInterface
     */
    public function getPassword(): ?string
    {
        return $this->password;
    }

    public function setPassword(string $password): self
    {
        $this->password = $password;

        return $this;
    }

    /**
     * Returning a salt is only needed, if you are not using a modern
     * hashing algorithm (e.g. bcrypt or sodium) in your security.yaml.
     *
     * @see UserInterface
     */
    public function getSalt(): ?string
    {
        return null;
    }

    /**
     * @see UserInterface
     */
    public function eraseCredentials(): void
    {
        // If you store any temporary, sensitive data on the user, clear it here
        // $this->plainPassword = null;
    }

    public function getFirstname(): ?string
    {
        return $this->firstname;
    }

    public function setFirstname(?string $firstname): self
    {
        $this->firstname = $firstname;

        return $this;
    }

    public function getLastname(): ?string
    {
        return $this->lastname;
    }

    public function setLastname(?string $lastname): self
    {
        $this->lastname = $lastname;

        return $this;
    }

    public function getCtime(): ?DateTimeImmutable
    {
        return $this->ctime;
    }

    public function setCtime(?DateTimeImmutable $ctime): self
    {
        $this->ctime = $ctime;

        return $this;
    }

    public function getMtime(): ?DateTimeImmutable
    {
        return $this->mtime;
    }

    public function setMtime(?DateTimeImmutable $mtime): self
    {
        $this->mtime = $mtime;

        return $this;
    }

    public function getCurrency(): ?Currency
    {
        return $this->getSubscriber()->getCurrency();
    }

    public function getLanguage(): Languages
    {
        return $this->language;
    }

    public function setLanguage(Languages $language): self
    {
        $this->language = $language;

        return $this;
    }

    public function getTimezone(): Timezones
    {
        return $this->timezone;
    }

    public function setTimezone(Timezones $timezone): self
    {
        $this->timezone = $timezone;

        return $this;
    }

    public function getAssignedCarwashes(): ?array
    {
        return $this->assignedCarwashes;
    }

    public function setAssignedCarwashes(?array $assignedCarwashes): self
    {
        $this->assignedCarwashes = $assignedCarwashes;

        return $this;
    }

    #[Groups(['admin:list',"carwash:list",'user:admin'])]
    public function getOwnerBkf(): ?int
    {
        return $this->getSubscriber()?->getOwnerBkf();
    }

    public function getCreatedBy(): ?self
    {
        return $this->createdBy;
    }

    public function setCreatedBy(?self $createdBy): self
    {
        $this->createdBy = $createdBy;

        return $this;
    }

    public function getComment(): ?string
    {
        return $this->comment;
    }

    public function setComment(?string $comment): self
    {
        $this->comment = $comment;

        return $this;
    }

    public function getPhone(): ?string
    {
        return $this->phone;
    }

    public function setPhone(?string $phone): self
    {
        $this->phone = $phone;

        return $this;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(string $status): self
    {
        $this->status = $status;

        return $this;
    }

    public function getPasswordResetToken(): ?string
    {
        return $this->passwordResetToken;
    }

    public function setPasswordResetToken(?string $passwordResetToken): self
    {
        $this->passwordResetToken = $passwordResetToken;

        return $this;
    }

    public function getPasswordRequestedAt(): ?DateTimeImmutable
    {
        return $this->passwordRequestedAt;
    }

    public function setPasswordRequestedAt(?DateTimeImmutable $passwordRequestedAt): self
    {
        $this->passwordRequestedAt = $passwordRequestedAt;

        return $this;
    }

    #[Groups(["user:details"])]
    public function isPrivacyPolicyAccepted(): ?bool
    {
        return $this->privacyPolicyAccepted;
    }

    public function setPrivacyPolicyAccepted(bool $privacyPolicyAccepted): self
    {
        $this->privacyPolicyAccepted = $privacyPolicyAccepted;

        return $this;
    }

    public function getPrivacyPolicyAcceptedAt(): ?DateTimeImmutable
    {
        return $this->privacyPolicyAcceptedAt;
    }

    public function setPrivacyPolicyAcceptedAt(?DateTimeImmutable $privacyPolicyAcceptedAt): User
    {
        $this->privacyPolicyAcceptedAt = $privacyPolicyAcceptedAt;
        return $this;
    }

    /**
     * @return Collection<int, ApiAccessToken>
     */
    public function getApiAccessTokens(): Collection
    {
        return $this->apiAccessTokens;
    }

    public function addApiAccessToken(ApiAccessToken $apiAccessToken): self
    {
        if (!$this->apiAccessTokens->contains($apiAccessToken)) {
            $this->apiAccessTokens->add($apiAccessToken);
            $apiAccessToken->setUser($this);
        }

        return $this;
    }

    public function removeApiAccessToken(ApiAccessToken $apiAccessToken): self
    {
        if ($this->apiAccessTokens->removeElement($apiAccessToken)) {
            // set the owning side to null (unless already changed)
            if ($apiAccessToken->getUser() === $this) {
                $apiAccessToken->setUser(null);
            }
        }

        return $this;
    }
    #[Groups(['admin:list'])]
    public function getLatestTokens(): ?DateTimeImmutable
    {
        if ($this->getApiAccessTokens()->isEmpty()) {
            return null;
        }

        $first = $this->getApiAccessTokens()->last();
        $last = $this->getApiAccessTokens()->first();

        if ($first->getExpiresAt() > $last->getExpiresAt()) {
            return $first->getExpiresAt();
        }

        return $last->getExpiresAt();
    }

    public function getValidTokens(): ReadableCollection
    {
        $now = new DateTimeImmutable();

        return $this->getApiAccessTokens()->filter(function (ApiAccessToken $token) use ($now): bool {
            return $token->getExpiresAt() > $now;
        });
    }

    public function getValidTokensList(): ReadableCollection
    {
        return $this->getValidTokens()->map(function (ApiAccessToken $token): ?string {
            return $token->getToken();
        });
    }

    /**
     * Get user carwashes objects
     * Scenario 1. User is owner: check if user is owner if yes get his carwases and return them
     * Scenario 2. User is not owner(under them): get column assignedCarwashes where is array with all carwases which
     * user can "see" and get his owner carwashes - merge ids with relation Owner - Carwash
     */
    #[Groups(["user:details"])]
    public function getCarwashes(): Collection
    {
        if (is_null($this->getSubscriber())) {
            return new ArrayCollection();
        }

        $carwashes = $this
            ->getSubscriber()
            ->getActiveCarwashes()
        ;

        // jesli jestem wlascicielem wtedy zwróć wszystkie moje myjnie
        if ($this->isOwner()) {
            return new ArrayCollection($carwashes->getValues());
        }

        $assignedCarwashes = $this->getAssignedCarwashes();
        $filteredCarwashes = $carwashes->filter(
            function ($entry) use ($assignedCarwashes) {
                if (is_array($assignedCarwashes)) {
                    return in_array($entry->getId(), $assignedCarwashes);
                } else {
                    return false;
                }
            }
        );

        return new ArrayCollection($filteredCarwashes->getValues());
    }

    /**
     * Get user carwashes which have their start dates set
     */
    public function getUserStartedCarwashes(): Collection
    {
        if ($this->getCarwashes()->isEmpty()) {
            return new ArrayCollection();
        }
        return new ArrayCollection($this->getCarwashes()->filter(fn(Carwashes $cw) => $cw->isCarWashStarted())->getValues());
    }

    public function hasRole(string $role): bool
    {
        return in_array(strtoupper($role), $this->getRoles(), true);
    }


    public function getCountry(): ?Country
    {
        return $this->getSubscriber()->getCountry();
    }

    public function getNip(): ?string
    {
        return $this->getSubscriber()->getTaxNumber();
    }

    public function getSubscriber(): ?Subscribers
    {
        return $this->subscriber;
    }

    public function setSubscriber(?Subscribers $subscriber): static
    {
        $this->subscriber = $subscriber;
        return $this;
    }

    #[Groups(["user:list", "user:details"])]
    public function isOwner(): ?bool
    {
        return in_array('ROLE_CM_OWNER', $this->roles);
    }

    public function getMobileTokens(): Collection
    {
        return $this->mobileTokens;
    }

    public function setMobileTokens(Collection $mobileTokens): self
    {
        $this->mobileTokens = $mobileTokens;
        return $this;
    }

    public function addMobileToken(MobileToken $mobileToken): self
    {
        if (!$this->mobileTokens->contains($mobileToken)) {
            $this->mobileTokens[] = $mobileToken;
            $mobileToken->setUser($this);
        }

        return $this;
    }

    public function removeMobileToken(MobileToken $mobileToken): self
    {
        if ($this->mobileTokens->removeElement($mobileToken) && $mobileToken->getUser() === $this) {
            $mobileToken->setUser(null);
        }

        return $this;
    }

    public function isActive(): bool
    {
        return $this->getStatus() === 'a';
    }

    public function addRole(string $role): self
    {
        $role = strtoupper($role);

        if (!in_array($role, $this->roles, true)) {
            $this->roles[] = $role;
        }

        return $this;
    }

    public function removeRole(string $role): self
    {
        if (false !== $key = array_search(strtoupper($role), $this->roles, true)) {
            unset($this->roles[$key]);
            $this->roles = array_values($this->roles);
        }

        return $this;
    }

    public function getDetails(): ?array
    {
        return $this->details;
    }
    public function setDetails(?array $details): self
    {
        $this->details = $details;
        return $this;
    }

    public function hasAccessToCarwashBySn(int $sn): bool
    {

        if ($this->isAdmin()) {
            return true;
        }

        return $this->getCarwashes()->exists(function ($key, $element) use ($sn) {
            return $element->getSerialNumber() == $sn;
        });
    }

    public function getLastLogin(): ?DateTimeImmutable
    {
        return $this->lastLogin;
    }

    public function setLastLogin(?DateTimeImmutable $lastLogin): User
    {
        $this->lastLogin = $lastLogin;
        return $this;
    }

    public function isDealer(): ?bool
    {
        return $this->isDealer;
    }

    public function setIsDealer(?bool $isDealer): User
    {
        $this->isDealer = $isDealer;
        return $this;
    }

    public function isAdmin(): ?bool
    {
        return $this->isAdmin;
    }

    public function setIsAdmin(?bool $isAdmin): User
    {
        $this->isAdmin = $isAdmin;
        return $this;
    }

    public function getLoyaltyApp(): ?array
    {
        return $this->loyaltyApp;
    }

    public function setLoyaltyApp(?array $loyaltyApp): User
    {
        $this->loyaltyApp = $loyaltyApp;
        return $this;
    }

    #[ORM\PrePersist]
    #[ORM\PreUpdate]
    public function updatedTimestamps()
    {

        $this->roles = $this->filterRoles($this->roles);
        $this->setMtime(new DateTimeImmutable('now'));
        if (empty($this->ctime)) {
            $this->setCtime(new DateTimeImmutable('now'));
        }
    }

    public function getReportSettings(): ?UserReports
    {
        return $this->reportSettings ?? (new UserReports())->setUser($this);
    }

    public function setReportSettings(UserReports $reportSettings): static
    {
        // set the owning side of the relation if necessary
        if ($reportSettings->getUser() !== $this) {
            $reportSettings->setUser($this);
        }

        $this->reportSettings = $reportSettings;

        return $this;
    }

    public function filterRoles(array $roles): array
    {
        $newRoles = [];
        // w bazie trzymamy tylko te role, reszte ustalamy na podstawie innych zmiennych

        foreach ($roles as $role) {
            if (!in_array($role, $this->getRolesList())) {
                continue;
            }
            if (!in_array($role, $newRoles, true)) {
                $newRoles[] = $role;
            }
        }
        return $newRoles;
    }

    public function getMobileAppVer(): ?string
    {
        return $this->mobileAppVer;
    }

    public function setMobileAppVer(?string $mobileAppVer): User
    {
        $this->mobileAppVer = $mobileAppVer;
        return $this;
    }

    #[Groups(["user:list"])]
    public function isNotificable(): bool
    {
        return !$this->mobileTokens->isEmpty();
    }

    public function getLocale(): string
    {
        return $this->getLanguage()->getLocale();
    }
}

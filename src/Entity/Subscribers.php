<?php

namespace App\Entity;

use I2m\StandardTypes\Enum\CMSubscription;
use App\Repository\SubscribersRepository;
use DateTime;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use I2m\Invoices\Interface\IClient;
use I2m\Invoices\Interface\IIssuer;
use I2m\StandardTypes\Enum\Country;
use Symfony\Component\Serializer\Annotation\Groups;
use Symfony\Component\Serializer\Annotation\MaxDepth;
use Symfony\Component\Validator\Constraints as Assert;

/**
 */
#[ORM\Table(name: 'subscribers_cm')]
#[ORM\Entity(repositoryClass: SubscribersRepository::class)]
#[ORM\HasLifecycleCallbacks]
class Subscribers implements IIssuer, IClient
{
    /**
     * @var integer
     */
    #[ORM\Column(type: 'integer')]
    #[ORM\Id]
    #[ORM\GeneratedValue(strategy: 'AUTO')]
    #[Groups(['subscriber:list','subscriber:details', 'subscriber:basic', 'default:basic'])]
    private $id;

    #[Assert\NotBlank]
    #[ORM\Column(name: 'name', type: 'string', length: 128)]
    #[Groups(['subscriber:details','subscriber:list', 'subscriber:basic', 'default:basic'])]
    protected $name;

    /**
     * @var string
     */
    #[ORM\Column(name: 'status', type: 'string', length: 1)]
    protected $status = 'a';

    /**
     * @var DateTime
     */
    #[ORM\Column(name: 'ctime', type: 'datetime')]
    protected $ctime;

    /**
     * @var DateTime
     */
    #[ORM\Column(name: 'mtime', type: 'datetime', nullable: true)]
    protected $mtime;

    /**
     * @var string
     */
    #[Assert\NotBlank]
    #[ORM\Column(name: 'email', type: 'string', length: 255, nullable: true)]
    #[Groups(['subscriber:details', 'subscriber:edit'])]
    protected $email;

    /**
     * @var string
     */
    #[Assert\NotBlank]
    #[ORM\Column(name: 'address', type: 'string', length: 255, nullable: true)]
    #[Groups(['subscriber:details','subscriber:list', 'subscriber:edit'])]
    protected $address;

    /**
     * @var string
     */
    #[Assert\NotBlank]
    #[ORM\Column(type: 'string', length: 40, nullable: true)]
    #[Groups(['subscriber:details','subscriber:list'])]
    protected $taxNumber;

    /**
     * @var string
     */
    #[Assert\NotBlank]
    #[ORM\Column(name: 'regon', type: 'string', length: 40, nullable: true)]
    #[Groups(['subscriber:details', 'subscriber:edit'])]
    protected $regon;

    /**
     * @var string
     */
    #[ORM\Column(name: 'post_code', type: 'string', length: 12, nullable: true)]
    #[Groups(['subscriber:details', 'subscriber:edit'])]
    protected $postCode;

    #[Assert\Regex(pattern: '/^(([[:alpha:]]|[[:space:]]|(-))*)$|^$/u', match: true, message: 'Only literal characters or empty string allowed')]
    #[ORM\Column(length: 128, nullable: true)]
    #[Groups(['subscriber:details','subscriber:list','subscriber:edit'])]
    protected ?string $city = null;

    #[Groups(['subscriber:details','subscriber:list'])]
    #[ORM\Column(length: 8, enumType: Country::class)]
    protected Country $country;

    /**
     * @var Currency
     */
    #[ORM\ManyToOne(targetEntity: 'App\Entity\Currency')]
    #[ORM\JoinColumn(name: 'currency_id', referencedColumnName: 'id', nullable: false)]
    #[Groups(['subscriber:details','subscriber:list'])]
    protected $currency;

    /**
     * base64 logo
     *
     * @var string
     */
    #[ORM\Column(name: 'logo_file_base', type: 'text', nullable: true)]
    #[Groups(['subscriber:logo','subscriber:edit'])]
    protected $logo;

    /**
     * @var string
     */
    #[Assert\Iban(message: 'This is not a valid International Bank Account Number (IBAN).')]
    #[ORM\Column(name: 'bank_account_number', type: 'string', length: 32, nullable: true)]
    #[Groups(['subscriber:details','subscriber:edit'])]
    protected $bankAccountNumber;

    #[ORM\Column(name: 'invoice_copy_email', type: 'text', nullable: true)]
    #[Groups(['subscriber:details','subscriber:edit'])]
    protected $invoiceCopyEmail;

    #[Groups(['subscriber:extend'])]
    #[ORM\OneToMany(mappedBy: 'subscriber', targetEntity: User::class)]
    private Collection $users;

    #[Groups(['subscriber:extend'])]
    #[ORM\OneToMany(mappedBy: 'subscriber', targetEntity: Carwashes::class)]
    private Collection $carwashes;

    #[ORM\Column(nullable: true)]
    #[Groups(['subscriber:admin'])]
    private ?bool $isDealer = false;

    #[ORM\ManyToOne]
    #[Groups(['subscriber:details','subscriber:list'])]
    #[MaxDepth(1)]
    private ?Subscribers $dealer = null;

    #[ORM\Column(nullable: false)]
    #[Groups(['subscriber:admin','subscriber:basic'])]
    private ?int $ownerBkf = null;

    #[ORM\ManyToOne]
    #[ORM\JoinColumn(nullable: false)]
    #[Groups(['subscriber:details','subscriber:edit'])]
    private Timezones $timezone;

    #[ORM\Column(nullable: true)]
    #[Groups(['subscriber:admin'])]
    private ?int $subscriptionDiscount = null;

    #[Groups(['subscriber:list','subscriber:details'])]
    private ?array $details = null;

    #[Groups(['subscriber:list','subscriber:details'])]
    #[ORM\Column(nullable: false, enumType: CMSubscription::class)]
    private CMSubscription $subscription = CMSubscription::FREE;

    #[ORM\ManyToOne]
    #[ORM\JoinColumn(nullable: false)]
    #[Groups(['subscriber:details'])]
    private Languages $language;

    #[ORM\Column(nullable: true)]
    private ?string $autoRenew = null;

    #[ORM\Column(name: 'phone', type: 'text', nullable: true)]
    protected $phone;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    #[Groups(["subscriber:admin"])]
    private ?string $comment = null;

    #[Groups(["subscriber:info"])]
    protected ?array $info = null;

    public function getUsers(): Collection
    {
        return $this->users;
    }

    public function setUsers(Collection $users): static
    {
        $this->users = $users;
        return $this;
    }

    #[Groups(['subscriber:list','subscriber:details'])]
    public function isInvoiceDataFilled(): bool
    {
        return $this->getName() &&
            $this->getAddress() &&
            $this->getCity() &&
            $this->getPostCode() &&
            $this->getTaxNumber();
    }

    /**
     * Subscribers constructor.
     */
    public function __construct()
    {
        $this->users = new ArrayCollection();
        $this->carwashes = new ArrayCollection();
    }

    /**
     * Get id
     *
     * @return integer
     */
    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(?int $id): static
    {
        $this->id = $id;
        return $this;
    }

    /**
     * Set name
     */
    public function setName(string $name): static
    {
        $this->name = $name;

        return $this;
    }

    public function getName(): string
    {
        return $this->name;
    }

    /**
     * Set status
     */
    public function setStatus(string $status): static
    {
        $this->status = $status;

        return $this;
    }

    /**
     * Get status
     */
    public function getStatus(): string
    {
        return $this->status;
    }

    /**
     * Set ctime
     */
    public function setCtime(DateTime $ctime): static
    {
        $this->ctime = $ctime;

        return $this;
    }

    /**
     * Get ctime
     *
     * @return DateTime
     */
    public function getCtime(): ?DateTime
    {
        return $this->ctime;
    }

    /**
     * Set mtime
     */
    public function setMtime(DateTime $mtime): static
    {
        $this->mtime = $mtime;

        return $this;
    }

    /**
     * Get mtime
     *
     * @return DateTime
     */
    public function getMtime(): ?DateTime
    {
        return $this->mtime;
    }

    /**
     * Get email
     *
     * @return string
     */
    public function getEmail(): ?string
    {
        return $this->email;
    }

    /**
     * Set email
     */
    public function setEmail(?string $email): static
    {
        $this->email = $email;

        return $this;
    }

    /**
     * Set address
     */
    public function setAddress(?string $address): static
    {
        $this->address = $address;

        return $this;
    }

    /**
     * Get address
     *
     * @return string
     */
    public function getAddress(): ?string
    {
        return $this->address;
    }

    /**
     * Set nip
     *
     * @param string $taxNumber
     */
    public function setTaxNumber(?string $taxNumber): static
    {
        $this->taxNumber = $taxNumber;

        return $this;
    }

    /**
     * Get nip
     *
     * @return string
     */
    public function getTaxNumber(): ?string
    {
        return $this->taxNumber;
    }

    /**
     * Get post code
     *
     * @return string
     */
    public function getPostCode(): ?string
    {
        return $this->postCode;
    }

    /**
     * Set post code
     */
    public function setPostCode(?string $postCode): static
    {
        $this->postCode = $postCode;

        return $this;
    }

    /**
     * Set city
     */
    public function setCity(?string $city): static
    {
        $this->city = $city;

        return $this;
    }

    /**
     * Get city
     *
     * @return string
     */
    public function getCity(): ?string
    {
        return $this->city;
    }

    public function setCountry(?Country $country): static
    {
        $this->country = $country;

        return $this;
    }

    public function getCountry(): Country
    {
        return $this->country;
    }

    /**
     * @return Currency
     */
    public function getCurrency(): ?Currency
    {
        return $this->currency;
    }

    /**
     * @return $this
     */
    public function setCurrency(?Currency $currency)
    {
        $this->currency = $currency;

        return $this;
    }

    /**
     * set base64 logo
     */
    public function getLogo(): ?string
    {
        return $this->logo;
    }

    /**
     * Set base64 logo
     *
     * @return $this
     */
    public function setLogo(?string $logo)
    {
        $this->logo = $logo;

        return $this;
    }

    /**
     * Get bank account number
     */
    public function getBankAccountNumber(): ?string
    {
        return $this->bankAccountNumber;
    }

    /**
     * Set bank account number
     *
     * @return $this
     */
    public function setBankAccountNumber(?string $bankAccountNumber)
    {
        $this->bankAccountNumber = $bankAccountNumber;

        return $this;
    }

    public function getDetails(): ?array
    {
        return $this->details;
    }
    public function setDetails(?array $details): static
    {
        $this->details = $details;
        return $this;
    }

    #[ORM\PrePersist]
    #[ORM\PreUpdate]
    public function updatedTimestamps()
    {
        $this->setMtime(new DateTime('now'));
        if ($this->getCtime() == null) {
            $this->setCtime(new DateTime('now'));
        }
    }

    public function getInvoiceCopyEmail(): ?string
    {
        return $this->invoiceCopyEmail;
    }

    /**
     * @return Subscribers
     */
    public function setInvoiceCopyEmail(?string $invoiceCopyEmail)
    {
        $this->invoiceCopyEmail = $invoiceCopyEmail;
        return $this;
    }

    public function getRegon(): ?string
    {
        return $this->regon;
    }

    public function setRegon(?string $regon): static
    {
        $this->regon = $regon;
        return $this;
    }

    public function isDealer(): ?bool
    {
        return $this->isDealer;
    }

    public function setIsDealer(?bool $isDealer): static
    {
        $this->isDealer = $isDealer;
        return $this;
    }

    public function getDealer(): ?Subscribers
    {
        return $this->dealer;
    }

    public function setDealer(?Subscribers $dealer): static
    {
        $this->dealer = $dealer;

        return $this;
    }

    public function getOwnerBkf(): ?int
    {
        return $this->ownerBkf;
    }

    public function setOwnerBkf(?int $ownerBkf): static
    {
        $this->ownerBkf = $ownerBkf;
        return $this;
    }

    public function getTimezone(): Timezones
    {
        return $this->timezone;
    }

    public function setTimezone(Timezones $timezone): static
    {
        $this->timezone = $timezone;
        return $this;
    }

    public function getSubscriptionDiscount(): ?int
    {
        return $this->subscriptionDiscount;
    }

    public function setSubscriptionDiscount(?int $subscriptionDiscount): static
    {
        $this->subscriptionDiscount = $subscriptionDiscount;
        return $this;
    }

    public function getCarwashes(): Collection
    {
        return $this->carwashes;
    }

    public function getActiveCarwashes()
    {
        return $this->getCarwashes()
            ->filter(
                function (Carwashes $entry) {
                    return !$entry->isUnsubscribed();
                }
            );
    }

    public function setCarwashes(Collection $carwashes): static
    {
        $this->carwashes = $carwashes;
        return $this;
    }

    public function getSubscription(): CMSubscription
    {
        return $this->subscription  ?? CMSubscription::FREE;
    }

    public function setSubscription(?CMSubscription $subscription): static
    {
        $this->subscription = $subscription ?? CMSubscription::FREE;
        return $this;
    }

    public function getLanguage(): Languages
    {
        return $this->language;
    }

    public function setLanguage(Languages $language): static
    {
        $this->language = $language;

        return $this;
    }

    public function getAutoRenew(): ?string
    {
        return $this->autoRenew;
    }

    public function isAutoRenew(): bool
    {
        return !is_null($this->autoRenew);
    }

    public function setAutoRenew(?string $autoRenew): Subscribers
    {
        $this->autoRenew = $autoRenew;
        return $this;
    }

    public function getPhone()
    {
        return $this->phone;
    }

    public function setPhone($phone)
    {
        $this->phone = $phone;
        return $this;
    }

    public function getInfo(): ?array
    {
        return $this->info;
    }

    public function setInfo(?array $info): Subscribers
    {
        $this->info = $info;
        return $this;
    }

    public function getComment(): ?string
    {
        return $this->comment;
    }

    public function setComment(?string $comment): Subscribers
    {
        $this->comment = $comment;
        return $this;
    }

    public function __toString(): string
    {
        return $this->name;
    }
}

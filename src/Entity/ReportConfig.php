<?php

namespace App\Entity;

use App\Entity\Enum\ReportPeriod;
use App\Repository\ReportConfigRepository;
use DateTimeImmutable;
use DateTimeInterface;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use I2m\Reports\Enum\FileExtention;
use Symfony\Component\Serializer\Annotation\Groups;

#[ORM\Entity(repositoryClass: ReportConfigRepository::class)]
#[ORM\HasLifecycleCallbacks]
class ReportConfig
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    #[Groups(['report:config:list', 'default:basic'])]
    private int $id;

    #[ORM\ManyToOne]
    #[ORM\JoinColumn(nullable: true)]
    #[Groups(['report:config:list'])]
    private ?User $user = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE)]
    #[Groups(['report:config:list'])]
    private ?DateTimeInterface $ctime = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE)]
    #[Groups(['report:config:list'])]
    private ?DateTimeInterface $mtime = null;

    #[ORM\Column(length: 255)]
    #[Groups(['report:config:list'])]
    private ?string $type = null;

    #[ORM\Column(length: 16)]
    #[Groups(['report:config:list','report:config:user_write'])]
    private FileExtention $ext;

    #[ORM\Column(nullable: true)]
    #[Groups(['report:config:list'])]
    private ?array $criterias = null;

    #[ORM\Column()]
    #[Groups(['report:config:list','report:config:user_write'])]
    private ReportPeriod $period;

    #[ORM\Column()]
    #[Groups(['report:config:list','report:config:user_write'])]
    private bool $enabled = true;

    #[ORM\Column(nullable: true)]
    #[Groups(['report:config:list','report:config:user_write'])]
    private ?array $email = null;

    #[ORM\Column(length: 255, nullable: true)]
    #[Groups(['report:config:list','report:config:user_write'])]
    private ?string $title = null;

    #[ORM\PrePersist]
    #[ORM\PreUpdate]
    public function updatedTimestamps()
    {
        $this->mtime = new DateTimeImmutable('now');
        if (empty($this->ctime)) {
            $this->ctime = new DateTimeImmutable('now');
        }
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function getCtime(): ?DateTimeInterface
    {
        return $this->ctime;
    }

    public function setCtime(DateTimeInterface $ctime): static
    {
        $this->ctime = $ctime;

        return $this;
    }

    public function getMtime(): ?DateTimeInterface
    {
        return $this->mtime;
    }

    public function setMtime(DateTimeInterface $mtime): static
    {
        $this->mtime = $mtime;

        return $this;
    }

    public function getType(): ?string
    {
        return $this->type;
    }

    public function setType(string $type): static
    {
        $this->type = $type;

        return $this;
    }

    public function getExt(): ?FileExtention
    {
        return $this->ext;
    }

    public function setExt(FileExtention $ext): static
    {
        $this->ext = $ext;

        return $this;
    }

    public function getCriterias(): ?array
    {
        return $this->criterias;
    }

    public function setCriterias(?array $criterias): static
    {
        $this->criterias = $criterias;

        return $this;
    }

    public function getPeriod(): ReportPeriod
    {
        return $this->period;
    }

    public function setPeriod(ReportPeriod $period): static
    {
        $this->period = $period;

        return $this;
    }

    public function isEnabled(): ?bool
    {
        return $this->enabled;
    }

    public function setEnabled(bool $enabled): static
    {
        $this->enabled = $enabled;

        return $this;
    }

    public function getEmail(): ?array
    {
        return $this->email;
    }

    public function setEmail(?array $email): static
    {
        $this->email = $email;

        return $this;
    }

    public function getTitle(): ?string
    {
        return $this->title;
    }

    public function setTitle(?string $title): static
    {
        $this->title = $title;

        return $this;
    }

    public function getUser(): ?User
    {
        return $this->user;
    }

    public function setUser(?User $user): static
    {
        $this->user = $user;

        return $this;
    }
}

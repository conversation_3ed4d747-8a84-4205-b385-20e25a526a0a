stages:
#  - lint
  - check_translations
  - test
  - deploy

test:phpunit:
  stage: test
  variables:
   APP_ENV: test
  script:
    - php /usr/bin/composer install
    - ./vendor/bin/phpcs ./src/ --extensions=php --standard=PSR12 --parallel=4 -p -n
    - ./vendor/bin/phpstan analyse -c ./phpstan5.neon --no-progress --memory-limit 1G
    - php bin/console doctrine:database:drop --if-exists --force --env=test
    - php bin/console doctrine:database:create --if-not-exists --env=test
    - php bin/console doctrine:schema:update --force --env=test
    # ustawiam losowo seq. aby testy payU przechodzily poprawnie
    - php bin/console --env=test doctrine:query:sql "SELECT setval('external_payment_id_seq', floor(random() * 1000000)::bigint, false)"
    - php bin/console doctrine:fixtures:load -n --env=test
    - php ./bin/phpunit
  after_script:
    - php bin/console doctrine:database:drop --if-exists --force --env=test
  tags:
    - i2m-v2

# wersja staging CM
cm_api_review:
  variables:
    APP_DIR: /srv/${CI_PROJECT_NAME}
    REV_DIR: ${APP_DIR}/${CI_JOB_ID}
    APP_ENV: prod
  stage: deploy
  script:
    # przygotowanie katalogu
    - mkdir -p ${REV_DIR}
    - rsync -a --delete --exclude .git ./ ${REV_DIR}
    - echo ${REV_DIR}
    - cd ${REV_DIR}
    # yarn
    - yarn
    - yarn encore prod
    # composer php
    - composer install --optimize-autoloader
    - composer dump-env prod
    - composer dump-autoload --optimize
    - php bin/console cache:clear --env=prod --no-debug
    - php bin/console cache:warmup --env=prod
    - bin/console doctrine:cache:clear-metadata
    # przepięcie środowiska
    - chmod -R 777 ${REV_DIR}
    - ln -sfn ${CI_JOB_ID} ${APP_DIR}/review
  tags:
    - cm-web
  allow_failure: true
  when: manual
  needs: []

cm_api_prod:
  variables:
    APP_DIR: /srv/${CI_PROJECT_NAME}
    REV_DIR: ${APP_DIR}/${CI_JOB_ID}
    APP_ENV: prod
  stage: deploy
  script:
    # przygotowanie katalogu
    - mkdir -p ${REV_DIR}
    - rsync -a --delete --exclude .git ./ ${REV_DIR}
    - echo ${REV_DIR}
    - cd ${REV_DIR}
    # yarn
    - yarn
    - yarn encore prod
    # composer php
    - composer install --optimize-autoloader
    - composer dump-env prod
    - composer dump-autoload --optimize
    - php bin/console cache:clear --env=prod --no-debug
    - php bin/console cache:warmup --env=prod
    - bin/console doctrine:cache:clear-metadata
    - bin/console doctrine:migrations:migrate --no-interaction --allow-no-migration
    # przepięcie środowiska
    - chmod -R 777 ${REV_DIR}
    - ln -sfn ${CI_JOB_ID} ${APP_DIR}/latest
    - php ${APP_DIR}/latest/bin/console messenger:stop-workers --env=prod
  tags:
    - cm-web
  only:
    - development
  needs: ["test:phpunit"]

.check_translations_template:
  script:
    - ./checkLang.py $LANG

check_translations:
  stage: test
  allow_failure: true
  parallel:
    matrix:
      - LANG: "pl"
      - LANG: "en"
      - LANG: "cs"
      - LANG: "ru"
      - LANG: "hr"
  extends: .check_translations_template
  tags:
    - i2m-v2

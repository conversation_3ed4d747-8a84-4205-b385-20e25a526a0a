#!/usr/bin/env pwsh

param (

)

./vendor/bin/phpcbf ./src/ --extensions=php --standard=PSR12 --parallel=4 -p -n
./vendor/bin/phpstan analyse -c ./phpstan5.neon
#./vendor/bin/phpcs --exclude=Generic.Files.LineLength ./src/

php bin/console cache:clear
php bin/console doctrine:database:drop --if-exists --force --env=test
php bin/console doctrine:database:create --if-not-exists --env=test
php bin/console doctrine:schema:update --force --env=test
php bin/console doctrine:fixtures:load -n --env=test
php bin/console --env=test doctrine:query:sql "SELECT setval('external_payment_id_seq', floor(random() * 1000000)::bigint, false)"

php ./bin/phpunit

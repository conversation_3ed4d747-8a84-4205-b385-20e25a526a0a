#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import re
import sys
import yaml

# Define the directory to scan for Symfony files
directory = "./src"
templates_directory = "./templates"

# Define the translation directory
translation_directory = "./translations"

# Regular expression to find translation calls
php_re_list = [
    r"\>trans\(\s?['\"](.*?)['\"]\s?[\)]?", # ->trans
    r"['\"]columnName['\"] => ['\"]([^'\"]*)['\"]", # src/Reports columns translations 'columnName' => 'translation_key'
    r"['\"]literal['\"] => ['\"]([^'\"]*)['\"]", # src/Reports columns translations 'literal' => 'translation_key'
]

twig_re_list = [
    r"['\"]([^'\"]*?)['\"]\s?\|\s?trans", # |trans
    r"{%\s?trans[^}]*%}\s*([^\s]*){%\s?endtrans[^}]*%}", # {% trans %} ... {% endtrans %}
]

pattern_php = re.compile( '|'.join(php_re_list) )
pattern_twig = re.compile( '|'.join(twig_re_list) )

# reports title form src/Reports metogd getTitle
missing_keys = {
    'moneycollect.list-period-exchanger',
    'finance.option-turnover-daily',
    'finance.fiscal-transactions',
    'finance.mobile-payments',
    'finance.portal-programsusage-daily',
    'finance.portal-programsusage-total',
    'finance.portal-turnover-total',
    'finance.program-usage',
    'menu.finance-programsusage-daily',
    'finance.option-turnover-monthly',
    'help.turnover-since-last-money-collect',
    'finance.turnover-from',
    'email.report-title-moneycollect',
    'moneycollect.list-period-yeti',
    'subscription.report_owner_subscriptions',
    'dealer_subscription_report.title-main',
    'fiscal.source.CAR_WASH',
    'fiscal.source.DISTRIBUTOR',
    'fiscal.source.MONEY_CHANGER',
    'fiscal.source.UNKNOWN',
    'fiscal.source.VACUUM_CLEANER',
    'cmuser.role_cm_costs',
    'cmuser.role_cm_finance',
    'cmuser.role_invoices',
    'cmuser.role_clients',
    'cmuser.role_subscription_premium',
    'cmuser.role_subscription_basic',
    'cmuser.role_client_invoices_list',
    'cmuser.role_client_create_invoice',
    'cmuser.role_cm_clients',
    'cmuser.role_cm_demo',
    'cmuser.role_cm',
    'cmuser.role_cm_finance_turnover_total',
    'cmuser.role_cm_finance_turnover_daily',
    'cmuser.role_cm_finance_programsusage_total',
    'cmuser.role_cm_finance_programsusage_daily',
    'cmuser.role_cm_finance_mobile_payments',
    'cmuser.role_cm_loyalsystem_keylist',
    'cmuser.role_cm_loyalsystem_keylist_and_clients',
    'cmuser.role_cm_bkfpay_users',
    'cmuser.role_cm_bkfpay_transactions',
    'cmuser.role_user',
    'cmuser.role_superadmin',
    'cmuser.role_cm_monitor',
    'cmuser.role_cm_exchanger',
    'cmuser.role_cm_moneycollect',
    'cmuser.role_cm_settings',
    'cmuser.role_cm_alarms',
    'cmuser.role_cm_alarms_and_technical_data',
    'cmuser.role_cm_protect_invoice_data',
    'cmuser.role_cm_instructions',
    'cmuser.role_cm_owner',
    'cmuser.role_cm_service',
    'cmuser.username_exists',
    'cmuser.email_exists',
    'error_report.problem_types.loyalty_cards',
    'error_report.problem_types.finance_data',
    'error_report.problem_types.client_add',
    'error_report.problem_types.subscirption',
    'error_report.problem_types.other',
    'invoices.payment-method.cash',
    'invoices.payment-method.credit-card',
    'invoices.payment-method.mobile-payment',
    'invoices.payment-method.p24',
    'invoices.payment-method.pay-ex',
    'invoices.payment-method.post-paid',
    'invoices.payment-method.transfer',
    'finance.payment-status-confirmed',
    'finance.payment-status-initiated',
    'finance.payment-status-timeout',
    'finance.payment-status-unknown',
    'finance.payment-status-success',
    'cards_topups_report.statuses.DEFAULT',
    'cards_topups_report.statuses.NOT_FULLY_REFILLED',
    'cards_topups_report.statuses.REFILLED',
    'cards_topups_report.statuses.WAITING',
    'cards_topups_report.sources.CAR_WASH',
    'cards_topups_report.sources.DEFAULT',
    'cards_topups_report.sources.DISTRIBUTOR',
    'cards_topups_report.sources.INTERNET',
    'cards_topups_report.sources.MONEY_CHANGER',
    'cards_topups_report.sources.SCRIPT',
    'cards_topups_report.sources.UNKNOWN',
    'cards_topups_report.sources.VACUUM_CLEANER'
}

ignore_keys = {
    '',
    'fiscal.source.{$source}',
    'cmuser.',
    'error_report.problem_types.',
    'fiscal.',
    'js.',
}

# file_path_preview = '../../Desktop/flat/messages.ru.yml'
# with open(file_path_preview, 'r', encoding='utf-8') as file:
#     yaml_data_preview = yaml.safe_load(file)

# Function to load translations from YAML files for a specific language
def load_translations(language):
    translations = set()
    filename = f"messages.{language}.yml"
    with open(os.path.join(translation_directory, filename), "r") as file:
        content = yaml.safe_load(file)
        return extract_keys_from_yaml(content)

def extract_keys_from_yaml(data, parent_key='', sep='.'):
    """Rekursywne wydobywanie kluczy z zagnieżdżonych struktur YAML."""
    keys = set()
    if isinstance(data, dict):
        for k, v in data.items():
            full_key = f"{parent_key}{sep}{k}" if parent_key else k
            keys.add(full_key)
            keys |= extract_keys_from_yaml(v, full_key, sep=sep)
    return keys

# Function to scan Symfony files for translation usage
def scan_for_translations():
    translation_usage = set()

    for root, _, files in os.walk(directory):
        for filename in files:
            if filename.endswith(".php"):
                with open(os.path.join(root, filename), "r", encoding="utf-8") as file:
                    content = file.read()
                    matches = pattern_php.findall(content)
                    for match in matches:
                        translation_usage.update(match)
            elif filename.endswith(".twig"):
                with open(os.path.join(root, filename), "r", encoding="utf-8") as file:
                    content = file.read()
                    matches = pattern_twig.findall(content)
                    for match in matches:
                        translation_usage.update(match)

    for root, _, files in os.walk(templates_directory):
        for filename in files:
            if filename.endswith(".php"):
                with open(os.path.join(root, filename), "r", encoding="utf-8") as file:
                    content = file.read()
                    matches = pattern_php.findall(content)
                    for match in matches:
                        translation_usage.update(match)
            elif filename.endswith(".twig"):
                with open(os.path.join(root, filename), "r", encoding="utf-8") as file:
                    content = file.read()
                    matches = pattern_twig.findall(content)
                    for match in matches:
                        translation_usage.update(match)

    return translation_usage


langs = ['pl']

# przekazanie parametru z językiem
if len(sys.argv) > 1:
    langs = sys.argv[1:]

def check_translations_by_lang(language):
    dekorator="######################################"

    translations = load_translations(language)
    translation_usage = scan_for_translations().union(missing_keys) - ignore_keys
    missing_translations = sorted(translation_usage - translations)

    unused_translations = translations - translation_usage
    unused_translations = sorted(unused_translations)

    print(dekorator)
    print(" Sprawdzenie tłumaczeń ", lang)
    print("\n Brakujące tłumaczenia: ", len(missing_translations))

#     print("\n".join(missing_translations))

    print("\n Niewykorzystane tłumaczenia: ", len(unused_translations))

    print("\n".join(unused_translations))

#     print(dekorator)
#     print("Used:")
#     translation_usage = sorted(translation_usage)
#     print(dekorator)

#     for key in missing_translations:
#         value = yaml_data_preview.get(key, '')
#         if(len(value) > 0):
#             print(f"{key}: {value}")

#     for key in translation_usage:
#         value = yaml_data_preview.get(key, '')
#         print(f"{key}: {value}")

    if (len(unused_translations) + len(missing_translations) > 0):
        return 1
    return 0

return_error = 0

for lang in langs:
    return_error = check_translations_by_lang(lang) or return_error

sys.exit(return_error)

#* * * * * cd /srv/cm/latest && bin/console scheduler:execute --env=cmprod
*/5 * * * * php /srv/beloyal/latest/bin/console bp:invoice:generate --env=prod >>/var/log/bp_new_invoice.log 2>>/var/log/bp_new_invoice_errors.log
#*/30 * * * * /srv/beloyal/latest/bin/console bkf:carwash:sync --env=prod >>/var/log/beloyal_carwash_command.log 2>>/var/log/beloyal_carwash_command_errors.log
*/5 * * * * php /srv/washstop-staging/latest/bin/console bp:invoice:generate >>/var/log/washstop_staging_invoices.log 2>>/var/log/washstop_staging_invoices_errors.log
* * * * * php /srv/washstop-staging/latest/bin/console washstop:mobile-payments:add-cards >>/var/log/washstop_staging_cards.log 2>>/var/log/washstop_staging_cards_errors.log


5 8 * * * ls -dt /srv/carwash-api/[0-9]* | tail -n +6 | xargs rm -r
10 8 * * * ls -dt /srv/cm/[0-9]* | tail -n +6 | xargs rm -r
15 8 * * * ls -dt /srv/cm-review/[0-9]* | tail -n +6 | xargs rm -r
20 8 * * * ls -dt /srv/beloyal/[0-9]* | tail -n +6 | xargs rm -r
25 8 * * * ls -dt /srv/washstop-staging/[0-9]* | tail -n +6 | xargs rm -r

# CM

*/10 * * * * php /srv/cm/latest/bin/console cm:subscriptions:set-privileges
0 2 * * 1 php /srv/cm/latest/bin/console cm:report:interval --interval=weekly
50 3 1 * * php /srv/cm/latest/bin/console cm:turnover:net_gross:csv -o 2326 -i monthly --emails="<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>" -l pl
0 1 * * * php /srv/cm/latest/bin/console cm:report:interval --interval=daily
30 2 1 * * php /srv/cm/latest/bin/console cm:report:interval --interval=monthly
40 3 * * * php /srv/cm/latest/bin/console cm:turnover:net_gross:csv -o 2326 -i daily --emails="<EMAIL>,<EMAIL>" -l pl
0 3 * * * php /srv/cm/latest/bin/console cm:turnover:csv --owner 1696 --email <EMAIL>

# CM - wykonywany na cm-consumer, ewentualnie mozna tutaj odpalic
#* * * * * php /srv/cm/latest/bin/console bkf:listener:queue:process --env=prod --no-debug
#*/13 0-1,3-22 * * * php /srv/cm/latest/bin/console bkf:synchro-devices --env=prod --no-debug

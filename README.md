instalacja <PERSON>a cm_consumer:

```
sudo add-apt-repository ppa:ondrej/nginx
sudo apt install php-redis php-zip php-gd php-intl php-gd php-zip php-pgsql php-curl php-xml npm certbot
sudo apt update -y && sudo apt upgrade -y && sudo apt dist-upgrade -y
sudo apt install php-cli php-intl php-xml php-curl php-mbstring php-pgsql php-redis php-zip php-gd php-intl php-pgsql php-cli unzip nginx php-fpm postgresql postgresql-contrib certbot python3-certbot-nginx
sudo npm install -g yarn
```

# generowanie pdf

```bash
sudo wget https://github.com/wkhtmltopdf/packaging/releases/download/0.12.6-1/wkhtmltox_0.12.6-1.bionic_amd64.deb
sudo apt install ./wkhtmltox_0.12.6-1.bionic_amd64.deb
whereis wkhtmltopdf
wkhtmltopdf http://www.example.com example.pdf
```

# define your env variables for the test env here
KERNEL_CLASS='App\Kernel'
APP_SECRET='$ecretf0rt3st'
SYMFONY_DEPRECATIONS_HELPER=999999
PANTHER_APP_ENV=panther
PANTHER_ERROR_SCREENSHOT_DIR=./var/error-screenshots

WEB_URL=https://127.0.0.1:8000

DATABASE_URL="postgresql://cm_dev:5fGXxPHkd5Sk@127.0.0.1:5432/cm-${CI_JOB_ID:-local}?serverVersion=12&charset=utf8"

CARDS_API_URL=https://carwash-api.bkf.pl

MAILER_URL="null://null"

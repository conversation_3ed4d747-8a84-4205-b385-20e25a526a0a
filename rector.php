<?php

declare(strict_types=1);

use Rector\Config\RectorConfig;

return static function (RectorConfig $rectorConfig): void {
    $rectorConfig->paths([
        __DIR__ . '/src/'
    ]);

    // register a single rule

//    $rectorConfig->rule(InlineConstructorDefaultToPropertyRector::class);
//    $rectorConfig->rule(FinalizePublicClassConstantRector::class);

    // define sets of rules
    $rectorConfig->sets([
        \Rector\Set\ValueObject\SetList::DEAD_CODE,
    ]);

};
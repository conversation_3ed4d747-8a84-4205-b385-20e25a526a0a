{{ "\xEF\xBB\xBF" }}{% for column in columns %}{{ column.columnName |trans }} {{ column
.columnSuffix ?? '' }}{% if column.units is not empty %}[{{ column.units }}]{% endif %}{{
separator }}{% endfor %}

{% if data.result|length > 0 %}
{% for record in data.result %}
{% for field in record %}{% if field.timestamp is defined %}{{ field|date("Y-m-d H:i:s")}}{% else %}"{{ field }}"{% endif %}{{ separator }}{% endfor %}

{% endfor %}
{% else %}
    {{ 'table.no-data'|trans }}
{% endif %}

<!DOCTYPE html>
<html lang="pl">
    <head>
        <meta charset="UTF-8"/>
        {% block styles %}
        {% include 'Reports/report.styles.html.twig' %}
        {% endblock %}
        {% block additionalstyles %}
        {% endblock %}
        <title>{{ title | trans }}</title>
    </head>
    <body id="cm_report_pdf">
        {{ link }}
        <div class="row">
            <div class="col s12 m12 l12 right-align">
                <img src="{{ app.brand.logo }}" class="thumbnail-200" />
            </div>
        </div>
        {% block title %}
        <div class="row">
            <div class="col s12 m12 l12">
                <h4 class="bold">{{ title | trans }}</h4>
                <h5>{{ formattedCriteria }}</h5>
            </div>
        </div>
        {% endblock %}
        <div class="row">
            {% block preCotent %}
            {% endblock %}
        </div>
        <div class="row">
            <div class="col s12 m12 l12">
                {% block content %}
                    <div id="pdf-turnover-table" class="pdf-table">
                        <table id="display table no-footer">
                            <thead>
                                <tr class="table-header" style="line-height: 1.3;
                                    background-color: #2c9bc7; color: white"
                                >
                                    {% for column in columns %}
                                        <th class="{{ column['class'] ?: 'align-right' }}">
                                            {{ column.columnName |trans }}
                                            {{ column.columnSuffix ?? '' }}
                                        </th>
                                    {% endfor %}
                                </tr>
                            </thead>
                            <tbody>
                            {% if data.result|length > 0 %}
                                {% for record in data.result %}
                                    <tr style="line-height: 1px; border-bottom: solid 1px lightgrey">
                                        {% for key, field in record %}
                                            <td class="{{ columns[key]['class'] ?: 'align-right' }}" style="line-height: 1.0">
                                                {% if field.timestamp is defined %}
                                                    {{ field|date(date_format, timezone) }}
                                                {% else %}
                                                    {{ field }}
                                                {% endif %}
                                            </td>
                                        {% endfor %}
                                    </tr>
                                {% endfor %}
                            {% else %}
                                <tr style="line-height: 1px; border-bottom: solid 1px lightgrey">
                                    <td class="center-align" colspan="{{ columns|length }}">{{ 'monitor.no-data-error' | trans }}</td>
                                </tr>
                            {% endif %}
                            <tr class="bold" style="line-height: 1px; background-color: #E8E8E8">
                                <td class="right-align bold">{{ 'table.sum' |trans }}</td>
                                {% for field in data.sums %}
                                    <td class="right-align bold">{{ field }}</td>
                                {% endfor %}
                            </tr>
                            </tbody>
                        </table>
                    </div>
                {% endblock %}
            </div>
        </div>
        <div class="row">
            <div class="col s12 m12 l12">
                {% block postContent %}
                {% endblock %}
            </div>
        </div>
    </body>
</html>

{% block header %}
    <style>
        a {
            color: #2c9bc7;
        }

        a:visited {
            color: #2c9bc7;
            background-color: transparent;
            text-decoration: none;
        }

        a:hover {
            color: #2c9bc7;
            background-color: transparent;
            text-decoration: underline;
        }

        a:active {
            color: #2c9bc7;
            background-color: transparent;
            text-decoration: underline;
        }
    </style>
{% endblock %}


<b style="display: block; margin-bottom: 20px;">
    {{ 'carwash_alarms.on_your_carwash_occured_alarms'|trans
        ({'%carwash_name%':
            carwashLongName }) }}
    :</b>

<table style="border-collapse: collapse; width:100%; table-layout:fixed; font-size: 13px;
    margin-bottom: 20px;"
>
    <thead style="border-bottom: 1px solid #ccc; padding-bottom: 5px;">
    <th align="left" valign="bottom" style="vertical-align:bottom; width: 20%; text-align:
        left;
        padding: 5px; padding-bottom: 10px;
"
    >{{ 'table.date'|trans }}
    </th>
    <th align="left" valign="bottom" style="vertical-align:bottom; width: 10%; text-align:
        left;
        padding: 5px; padding-bottom: 10px;"
    >{{ 'table.id'|trans }}
    </th>
    <th align="left" valign="bottom" style="vertical-align:bottom; width: 45%; text-align:
        left;
        padding: 5px; padding-bottom: 10px;
"
    >{{ 'table.alarms-description'|trans }}
    </th>
    <th align="left" valign="bottom" style="vertical-align:bottom; width: 15%; text-align: left;
        padding: 5px; padding-bottom: 10px;
"
    >{{ 'alarm-level'|trans }}
    </th>
    </thead>
    <tbody style="padding-top: 5px;">

    <tr>
        <td valign="top" style="width: 20%; vertical-align: top; padding: 5px; padding-top:
            10px;"
        >
            {{ occuredAlarm.createTime|date('Y-m-d H:i') }}
        </td>
        <td valign="top" style="width: 10%; vertical-align: top; padding: 5px; padding-top:
            10px;"
        >
            {{ occuredAlarm.alarmId }}
        </td>
        <td valign="top" style="width: 50%; vertical-align: top; padding: 5px; padding-top:
            10px;"
        >
            {% if occuredAlarm.docUrl is not null %}
                <a href="{{ occuredAlarm.docUrl }}">{{ occuredAlarm.description }}</a><br>
            {% else %}
                {{ occuredAlarm.description }}<br>
            {% endif %}
        </td>
        {% set alarmColor = 'black' %}
        {% if occuredAlarm.level == 'error' %}
            {% set alarmColor = '#f44336' %}
        {% elseif occuredAlarm.level == 'information' %}
            {% set alarmColor = '#2196f3' %}
        {% elseif occuredAlarm.level == 'unspecified' %}
            {% set alarmColor = '#9e9e9e' %}
        {% elseif occuredAlarm.level == 'warning' %}
            {% set alarmColor = '#ff9800' %}
        {% endif %}
        <td valign="top" style="width: 9%; vertical-align: top; color:{{ alarmColor }};
                padding: 5px; padding-top: 10px;"
        >
            {{ occuredAlarm.level }}
        </td>
    <tr>

    </tbody>
</table>

<div>
    <span>{{ 'carwash_alarms.check_current_alarms1'|trans }}</span>
    <a href="https://cm.bkf.pl/{{ alarmHistoryURI }}">
        {{ 'carwash_alarms.check_current_alarms2'|trans }}
    </a>
    <span>{{ 'carwash_alarms.check_current_alarms3'|trans }}</span>
</div>

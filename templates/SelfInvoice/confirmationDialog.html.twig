{% extends 'layout.html.twig' %}

{% block title %}
    <title>{{ 'self-invoice-confirmation.state'|trans }}</title>
{% endblock %}

{% block fos_user_content %}

<div class="p-20 col s12 m12 l12" style="text-align: center;margin: auto;max-width: 400px;">
    <div class="card">
        <div class="title" >
            <h4>BE LOYAL</h4>
        </div>
        <div class="content">
            {% if responseType == 'toConfirm'%}
                {{ 'self-invoice-confirmation.invoice-confirm-dialog-content'|trans({
                    '%invoiceNumber%': invoiceNumber, })|raw }}
                <p style="padding-top: 20px">
                    <a style="background:#2c9bc7;padding:15px 30px;color:#fff;border-radius:10px;text-transform:uppercase"
                       href="{{ path('self_invoice_confirm_by_uuid_and_email', {'uuid': uuid, 'email': 'email'}) }}" type="button">
                        {{ 'self-invoice-confirmation.confirm'|trans }}
                    </a>
                </p>
            {% elseif responseType == 'fail' %}
                {{ 'self-invoice-confirmation.confirmation-fail'|trans }}
            {% elseif responseType == 'alreadyConfirmed' %}
                {{ 'self-invoice-confirmation.already-confirmed'|trans({
                    '%invoiceNumber%': invoiceNumber,
                })|raw }}
            {% endif %}
        </div>
    </div>
</div>

{% endblock %}
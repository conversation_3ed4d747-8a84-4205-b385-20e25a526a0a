{% extends 'Email/base.html.twig' %}

{% block body %}
    <tr>
        <td style="padding: 40px 20px 20px 20px; background-color: #fff; color: #000">
            <p>
                {{ 'email.welcome'|trans({}, null, language) }},
            </p>
            <p>
                {% if data.body is iterable %}
                    <ul>
                    {% for element in body %}
                        <li>{{ element }}</li>
                    {% endfor %}
                    </ul>
                {% else %}
                    {{ data.body|raw }}
                {% endif %}
            </p>
            <p>
                {{ 'email.greetings'|trans({}, null, language) }}
            </p>
        </td>
    </tr>
{% endblock body %}
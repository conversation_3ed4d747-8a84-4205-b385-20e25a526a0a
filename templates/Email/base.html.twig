<html>
    <head>
        <title></title>
    </head>
    {% if primaryColor is defined and primaryColor is not null %}
        {% set footerColor = primaryColor %}
    {% endif %}

    {% set primaryColor = '#2c9bc7' %}
    {% set footerColor = '#263238' %}


    <body style="background-color: #d4d4d4; padding: 0 0 20px 0; margin: 0; font-family: Arial;">
        <table align="center" border="0" cellpadding="0" cellspacing="0" id="wrapper" style="width: 100%; max-width: 740px; margin-top: 20px; line-height: 120%;">
            <tbody>
                <tr>
                    {% block header %}
                        <td style="background-color: {{ primaryColor ?? '#e2001a' }}">
                            <table border="0" cellpadding="0" cellspacing="0">
                                <tbody>
                                <tr>
                                    <td style="padding: 10px 10px 10px 20px">
                                        {% block topLogo %}
                                            <img alt="CM" src="cid:logo.png" style="max-height: {{ logoMaxHeight ?? '45px' }};" />
                                        {% endblock topLogo %}
                                    </td>
                                    <td style="padding: 20px 20px 16px 10px;">
                                        {% block slogan %}
                                            <i style="color: #e6e6e6">{{ 'email.cm-slogan'|trans({}, null, language) }}</i>
                                        {% endblock slogan %}
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                        </td>
                    {% endblock header %}
                </tr>
                {#<tr>#}
                {% block body %}
                    {% block environment %}
                        <strong>Carwash Manager</strong>
                    {% endblock environment %}
                {% endblock body %}
                {% block footer %}

                {# MOBILE APP DOWNLOAD #}
                {% block mobileAppDownload %}
                    <tr>
                        <td style="background: {{ primaryColor }}; padding: 5px 15px 5px 20px; text-align: center;">
                            <table border="0" cellpadding="0" cellspacing="0" style="width: 100%;" width="100%">
                                <tbody>
                                <tr>
                                    <td style="text-align: left;">
                                        <strong style="color: #fff;">{{ 'mobile_app_download'|trans({}, null, language) }}</strong>
                                    </td>
                                    <td style="text-align: right;">
                                        <table style="margin-left: auto;">
                                            <tbody>
                                            <tr style="text-align: right; line-height: 1;">
                                                <td style="text-align: right; padding-right:5px;">
                                                    <a href="{{ assets.apple }}"
                                                       class="mobile-app__link"
                                                    >
                                                        <img
                                                            class="badge-ios"
                                                            style="max-width:200px; max-height: 38px;"
                                                            src="cid:appStoreLogo.png"
                                                            alt="Apple App Store"
                                                        >
                                                    </a>
                                                </td>
                                                <td style="text-align: right;">
                                                    <a
                                                        href="{{ assets.android }}"
                                                        class="mobile-app__link"
                                                    >
                                                        <img
                                                            class="badge-android"
                                                            style="max-width: 200px; max-height: 38px;"
                                                            src="cid:googlePlayLogo.png"
                                                            alt="Google Play"
                                                        >
                                                    </a>
                                                </td>
                                            </tr>
                                            </tbody>
                                        </table>
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                        </td>
                    </tr>
                {% endblock mobileAppDownload %}

                {#  FOOTER  #}
                    <tr>
                        <td>
                            <table border="0" cellpadding="8" cellspacing="0" style="width: 100%;" width="100%">
                                <tbody>
                                <tr>
                                    <td style="background: {{ footerColor ?? '#263238' }}; vertical-align: top; padding-left: 20px; width: 33%; font-size: 10pt; color: #888;" valign="top">
                                        <strong style="color: {{ primaryTextColor ?? '#666666' }}">{{ dealer.name }}</strong><br />
                                        {{ dealer.address }}<br />
                                        {{ dealer.postCode }} {{  dealer.city }}
                                    </td>
                                    <td align="right" style="color: {{ primaryTextColor ??
                                    '#666666' }}; background: {{ footerColor ?? '#263238' }};
                                            vertical-align: middle; padding-right: 10px; font-size:
                                            10pt; text-align: right;"
                                        valign="middle">
                                        {% block footer_info %}
                                            {% if dealer.phone is defined and dealer.phone is not null %}
                                                <p style="margin: 0;"><a href="tel:{{ dealer.phone }}" style="color: {{ primaryTextColor ?? '#888' }};;" title="{{ 'email.call-us'|trans({}, null, language) }}">tel. {{ dealer.phone }}</a></p>
                                            {% endif %}
                                            {% if dealer.email is defined and dealer.email is not null %}
                                                <p style="margin: 0;"><a href="mailto:{{ dealer.email }}" style="color: {{ primaryTextColor ?? '#888' }};;" title="{{ 'email.write-to-us'|trans({}, null, language) }}">{{ dealer.email }}</a></p>
                                            {% endif %}
                                        {% endblock footer_info %}
                                    </td>
                                    <td align="right" style="color: {{ primaryTextColor ??
                                    '#666666' }};; text-align: right; vertical-align: middle;
                                            background: {{ footerColor ?? '#263238' }};
                                            padding-right: 20px; width: 40px;" valign="middle">
                                        {% if dealer.facebookFanpage is defined %}
                                            <a href="{{ dealer.facebookFanpage }}" target="_blank" title="{{ dealer.facebookFanpage }}"><img alt="Facebook" src="cid:fbIcon.png" /></a>
                                        {% endif %}
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                        </td>
                    </tr>
                {% endblock footer %}
            </tbody>
        </table>
    </body>
</html>

{% extends 'Email/base.html.twig' %}

{% block body %}
<tr>
    <td style="padding: 40px 20px 20px 20px; background-color: #fff; color: #000">
        <p>
            {{ 'email.welcome'|trans({}, null, language) }},
        </p>

        <p> {{ 'self-invoice-reminder.content'|trans({
                '%date%': data.invoiceDate|date("Y-m-d"),
                '%invoiceNumber%': data.invoiceNumber,
            }, null, language) |raw|nl2br }}

            {{ 'invoices.check-attachment-document'|trans({}, null, language) |raw|nl2br }}
        </p>

        <p style="padding-top:20px; padding-bottom:20px">
            <a href="{{ data.confirmUrl }}" style="background: #2c9bc7; padding: 15px 30px; color: #fff; text-decoration: none; border-radius: 10px; text-transform: uppercase;"> {{ 'invoices.invoice-confirmation.button'|trans({}, null, language) }} </a>
        </p>

        <p>
            {{ 'email.greetings'|trans({}, null, language) }}
        </p>
    </td>
</tr>
{% endblock body %}
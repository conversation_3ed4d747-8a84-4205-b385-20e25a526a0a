// Module: Helpers
// Define helpers functions and expose them via:
// 1) I2M.Helpers object, ex. I2M.Helpers.funcname(...)
// 2) window object, ex. funcname(...)

var I2M = (function(Scope) {
    // Verify scopes
    Scope.Helpers = Scope.Helpers || {};

    // Mappings
    var Helpers = Scope.Helpers;

    // Extends object with source object functionality
    Helpers.extend = function(destination, source) {
        for (var k in source) {
            if (source.hasOwnProperty(k)) {
                destination[k] = source[k];
            }
        }
        return destination;
    };

    // Expose all helper functions to global scope
    Helpers.expose = function() {
        var excludes = [Helpers.expose];

        for (var func in Helpers) {
            if (excludes.indexOf(Helpers[func]) !== -1) {
                continue;
            }

            if (Helpers.hasOwnProperty(func)) {
                if (typeof window[func] !== 'undefined') {
                    console.warn('Overriding property: ' + window[func]);
                }
                window[func] = Helpers[func];
            }
        }
    };

    // Return scope with additional functionality
    return Scope;
})(I2M || {});

(function () {
    var connections = 0; // count active connections
    var userid = 0;
    var token = '';
    var language = 'en_US';
    var distro = 'cm';
    var alarmSocket;
    var port;

    var waitForConnection = function (interval) {
        if (alarmSocket.readyState === 1) {
            var alarm = {type: 'client', token: token, userid: userid, lang: language, distro: distro};
            alarmSocket.send(JSON.stringify(alarm));
        } else {
            setTimeout(function () {
                waitForConnection(interval);
            }, interval);
        }
    };

    self.addEventListener("connect", function (e) {

    port = e.ports[0];

    connections++;

    port.addEventListener("message", function (e) {
        var args = JSON.parse(e.data);

        if (typeof args.userid != "undefined") {
            userid = args.userid;
        }

        if (typeof args.distro != "undefined") {
            distro = args.distro;
        }

        if (typeof args.token != "undefined") {
            token = args.token;
        }

        if (typeof args.lang != "undefined") {
            language = args.lang;
        }
    });
        port.start();
}, false);

    var timer = setInterval(function() {
        if (token.length > 4) {
            clearInterval(timer);
            if (Notification) {
                alarmSocket = new WebSocket("wss://alarms.ebkf.pl:8081");
                waitForConnection(1000);
                alarmSocket.onmessage = function (event) {
                    var comm = JSON.parse(event.data);
                    var notification = new Notification (comm.title,
                        {
                            body: comm.description,
                            icon: 'http://carwash-alarm.1.v1.bkf.pl/images/alarm.png',
                            data: comm.link,
                            requireInteraction: true
                        });
                    notification.onclick = function(e) {
                        port.postMessage(e.target.data);
                        notification.close();
                    };
                }
            }

        }
    }, 1000);

}());
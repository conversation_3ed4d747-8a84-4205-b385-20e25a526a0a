$(document).ready(function () {
   
    $(document).on('click','#picked-icon', function(){
        var position = $(this).offset();
        $('#simple-icon-picker').css('top', position.top);
        $('#simple-icon-picker').css('left', position.left);
        $('#simple-icon-picker').show();
    });

    $(document).on('mouseout','#simple-icon-picker i', function(){
        $('#simple-icon-picker-view').html('');
    });

    $(document).on('mouseover','#simple-icon-picker i', function(){
        var iconClass = $(this).data('icon');
        $('#simple-icon-picker-view').html('<i class="' + iconClass + '"></i>');
    });

    $(document).on('click','#simple-icon-picker i', function(){
        var iconClass = $(this).data('icon');
        $('#picked-icon').attr('class', iconClass);
        $('.icons').val(iconClass);
    });

    $('*:not(#simple-icon-picker-button, #simple-icon-picker)', 'html').click(function(){
        $('#simple-icon-picker').hide();
    });

})
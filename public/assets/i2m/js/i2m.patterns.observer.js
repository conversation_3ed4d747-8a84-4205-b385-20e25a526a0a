/**
 * Observer Pattern module
 */
var I2M = (function(Scope) {
    //Verify scopes
    Scope.Patterns = Scope.Patterns || {};
    Scope.Patterns.Observer = Scope.Patterns.Observer || constructor;

    // Mappings
    var Observer = Scope.Patterns.Observer;

    /**
     * Create new Observer object
     * @returns {i2m_patterns_observer_L4.constructor}
     */
    function constructor() {
        this.observers = [];
    }

    /**
     * Add observer to observers list
     * @param {object} observer
     */
    Observer.prototype.add = function(observer) {
        this.observers.push(observer);

        return this;
    };

    /**
     * Remover observer from observers list
     * @param {object} observer
     */
    Observer.prototype.remove = function(observer) {
        var observers = this.observers;
        var index = observers.indexOf(observer);
        if (index > -1) {
            observers.splice(index, 1);
        };

        return this;
    };

    /**
     * Notify observers
     * @param {object} sender
     */
    Observer.prototype.notify = function(sender) {
        this.observers.forEach(function(observer) {
            if (typeof observer.update === 'function') {
                observer.update(sender);
            }
        });

        return this;
    };

    /**
     * Count observers
     * @returns {integer} Number of observers
     */
    Observer.prototype.count = function() {
        return this.observer.length;
    };

    return Scope;
})(I2M || {});

var xhr;

/**
 * Show modal with content from given url
 * @param {string} modalName
 * @param {string} url
 * @param {array} params
 * @param {mixed} data to send
 */
function showModal(modalName, url, params, data) {
    var method = "GET";
    var async = true;
    showProgress();
    if (typeof params !== 'undefined' && params !== null) {
        if (params.method) {
            method = params['method'];
        }
        if (params.hasOwnProperty('async')) {
            async = params.async;
        }
    }
    $('#modalsContainer').append('<div class="lean-overlay"></div>');
    $.ajax({
        url: url,
        method: method,
        data: data,
        async: async
    }).done(function(data, status, jqXHR) {
        hideProgress();
        $('.lean-overlay').remove();
        $('#' + modalName).remove();
        $('#modalsContainer').html(data);
        $('#' + modalName).openModal();
        if (typeof params !== 'undefined' && params !== null) {
            if (params.rowIndex) {
                $('#params').attr('data-rowIndex', params['rowIndex']);
            }
        }

        var form = $('#' + modalName + ' form');
        if (form.length) {
            initFormJs(form);
        }
    });

}

/**
 * Toggle progress bar with given selector
 * If selector is no defined, then toggle main progress bar
 * @param {string} selector
 */
function toggleProgress(selector) {
    $(selector || '.progress.main-progress').toggle();
}

/**
 * Show progress bar with given selector
 * If selector is no defined, then show main progress bar
 * @param {string} selector
 */
function showProgress(selector) {
    $(selector || '.progress.main-progress').show();
}

/**
 * Hide progress bar with given selector
 * If selector is no defined, then hide main progress bar
 * @param {string} selector
 */
function hideProgress(selector) {
    $(selector || '.progress.main-progress').hide();
}

/**
 * Initialize Materialize form and several additional plugins in newly loaded modals
 * @param {object} [form=''] form object
 */
function initFormJs(form) {
    initGarlic();
    initPlugins(form);
    initParsley(form);
}

/**
 * Init parsley live validation
 * @param form
 * @returns {boolean}
 */
function initParsley(form) {
    if (!$.fn.parsley) {
        return true;
    }

    $(form).parsley();
}

/**
 * Initialize Garlic, put data from local storage to input fields and add class active to not empty input
 */
function initGarlic() {
    $( '[data-persist="garlic"]' ).each( function () {
        $(this).garlic();
        var $labels = $(this).find("label[for]");
        $(this).find('input').filter(function(){
            return $.trim($(this).val()) != ''
        }).map(function(){
            $('label[for="'+this.id+'"]').first().addClass('active');
        });
    });
}

/**
 * Clear Garlice stored data on form in modal post
 */
function clearGarlic() {
    if (localStorage) {
        for (var key in localStorage) {
            if (key.indexOf('garlic:') === 0) {
                localStorage.removeItem(key);
            }
        }
    }
}

/**
 * Post form data to field {JSON}
 *
 * @param {string} field post field id
 * @param {string} modal modal id to close
 */
function submitField(field, modal) {
    var result = {};
    var re = /[a-z].*\[(.*)\]/i;

    $('#editpar-scheduler-modal').find('input[type="text"]').each(function(t)
    {
        var input = $(this).attr('name');
        var m = re.exec(input);
        var inputVal = $(this).val();
        if (m !== null) {
            result[m[1]] = inputVal;
        }
    });

    $('#'+modal).closeModal();
    $('#'+field).val(JSON.stringify(result));
}

function isParsleyValid(formSelector) {
    if (!$.fn.parsley) {
        return true;
    }

    var $form = $(formSelector || 'form');

    $form.each(function() {
        $(this).parsley().validate();
    });

    if ($form.find('.parsley-error').length) {
        showFirstError();
        return false;
    }

    return true;
}

function showFirstError() {
    // open tab when validation error occured
    var errors = $('.parsley-errors-list li');
    if (errors.length > 0) {
        var tab = $('.parsley-errors-list li').closest('li.tab');
        if (!tab.hasClass('active')) {
            tab.find('.collapsible-header').click();
        }
    }
}

/**
 * Post form data using ajax request and render response
 *
 * @param {string} url - address to post form
 * @param {string} formName - form name to post
 * @param {object} options - additional options:
 *     success - function that is called when data.success is 1 (default: null)
 *     fail - function that is called when data.success is not 1 (default: null)
 *     done - function that is called after all regardless of result (default: null)
 *     replace - selector of DOM element, that content will be replaced with data.result (default: '#modalsContainer')
 *     type - type of form (default: 'modal', other values: 'static')
 */
function postForm(url, formName, options) {
    $('button[name="_new"]').first().attr('disabled', true);
    $('button[name="_new"]').first().addClass('disabled');
    var materialize = window.Materialize || null;
    var datatable = window.dataTable || null;

    options = options || {};
    options.modal = null;
    options.static = null;

    if (!options.type) {
        options.modal = true;
    }

    if (options.type === 'modal') {
        options.modal = true;
    }

    if (options.type === 'static') {
        options.static = true;
    }
    if (!options.replace) {
        options.replace = '#modalsContainer';
    }

    if (typeof window[options.success] !== 'function') {
        options.success = null;
    }

    if (typeof window[options.fail] !== 'function') {
        options.fail = null;
    }

    if (typeof window[options.done] !== 'function') {
        options.done = null;
    }

    // if ckeditor exists, save all values
    if(typeof(CKEDITOR) !== 'undefined') {
        for (instance in CKEDITOR.instances) {
            CKEDITOR.instances[instance].updateElement();
        }
    }

    // check html5 validity if available
    if ( (isParsleyValid("form[name='" + formName +"']") === false) || ($("form[name='" + formName +"']").find('input:invalid').length > 0)) {
        $('button[name="_new"]').first().removeClass('disabled');
        $('button[name="_new"]').first().attr('disabled', false);
        return false;
    }

    // If tinymce exists, save all values
    if ($.fn.tinymce) {
        tinyMCE.triggerSave();
    }

    showProgress();
    $.post(
        url,
        $("form[name='" + formName +"']").serialize()
    ).done(function (data) {
        hideProgress();
        if (data.success === 1) {
            clearGarlic();
            if (data.hasOwnProperty("selectName") && data.selectName !== null) {
                $('#new-' + data.entityName + '-modal').closeModal();
                $('select[name*="' + data.selectName + '"]').append('<option value="' + JSON.parse(data.data)[0] + '">' + JSON.parse(data.data)[1] + '</option>');
                $('select[name*="' + data.selectName + '"]').select2('data', {'id' : JSON.parse(data.data)[0], 'text' : JSON.parse(data.data)[1]});
                materialize && materialize.toast(data.message, data.timeout);
            } else {
                options.modal && $('.modal').closeModal();

                materialize && materialize.toast(data.message, data.timeout);

                if (data.redirect === true) {
                    setTimeout(function() {
                        window.location.replace(data.data.id)
                    }, data.timeout);
                }

                if (data.redirectToPath) {
                    setTimeout(function() {
                        window.location.replace(data.redirectToPath)
                    }, data.timeout);
                }
                if (datatable) {
                    if (data.mode === 'add' ) {
                        datatable.row.add(JSON.parse(data.data)).draw( false );
                    } else if (data.mode === 'edit') {
			            datatable.row($('#'+datatable.tables().nodes().to$().attr('id')+' .selected')).data(JSON.parse(data.data)).draw( false );
                    } else if (data.mode === 'copy') {
                        var items = JSON.parse(data.data);
                        items.map(function(item){
                            datatable.row.add(item).draw( false );
                        });
                    } else if (data.mode === 'update_row' || data.mode === 'replace') {
                        replace(data);
                    } else if (data.mode === 'new_row') {
                        append(data);
                    }
                }

                /* call existing sync function (flextable) */
                if (window.sync) {
                    window.sync(data);
                }

                if (data.result && options.static) {
                    $(options.replace).html(data.result);
                    initFormJs($("form[name='" + formName + "']"));
                }
            }

            if (data.mode === 'replace') {
                replace(data);
            }

            window[options.success] && window[options.success](data);
        } else {
            options.modal && $('.lean-overlay').remove();

            if (data.result) {
                $(options.replace).html(data.result);
                initFormJs($("form[name='" + formName + "']"));
            }
            options.modal && $('.modal').openModal();

            if (data.message && data.timeout) {
                materialize && materialize.toast(data.message, data.timeout);
            }
            window[options.fail] && window[options.fail](data);
        }

        window[options.done] && window[options.done](data);
    });
    return false;
}


/**
 * Get elements from adress and put in chield select
 *
 * @param {string} url - request url
 * @param {array} data - data to send, like id
 * @param {string} dest - destination element to put data
 */
function getSubSelect2Data(url, data, dest) {

    if(xhr != null) {
        xhr.abort();
        xhr = null;
    }

    xhr = $.ajax({
        method: 'GET',
        url: url,
        data: data,
        async: true
    }).done( function(data) {
        $(dest).val(data).trigger("change");
    });
}

function confirmEvent(url) {
    showProgress();
    navigator.geolocation.getCurrentPosition(
        function(data) {
            $.ajax({
                type: "POST",
                url: url,
                data: {
                    latitude: data.coords.latitude,
                    longitude: data.coords.longitude
                },
                success: function(data) {
                    if ( typeof(window['reloadCalendarEvents']) === 'function') {
                        reloadCalendarEvents();
                    }
                    hideProgress();
                    closeModal();
                    Materialize.toast(data.message, data.timeout);
                }
            });
        },
        function() {
            hideProgress();
            closeModal();
            Materialize.toast(trans('widget.evets.get-position-error'), 4000);
        }
    );

}
/**
 * Post form data using ajax xhr function to send single file
 * @param {string} url
 * @param {string} formName
 * @param {array} typeMatch expected mime types of files
 * @param {string} errorMessage string that will be shown if file validation will fail
 * @param {string} eventsTable string that will specify eventtable to reload icons that shows if file is already uploaded
 */
function postFile(url, formName, typeMatch, errorMessage, eventsTable){
    showProgress();
    var formData = new FormData($('form[name="' + formName + '"]')[0]);

    var fileInput = $('#' + formName + '_file')[0].files;

    if (!fileInput || fileInput.length === 0) {
        return;
    }
    var fileMatch = false;

    /*
        JS file mime type validation - to delete after asserts module in enity start works
        SET TO ACCEPT ALL IMAGES, PDF FILES AND SOME VIDEO FILES
    */
    for(var i = 0; i < typeMatch.length; i++){
        if ( fileInput[0].type.match(typeMatch[i]) ) {
            fileMatch = true;
            break;
        }
    }
    if (fileMatch) {

       $.ajax({
            url: url,
            type: 'POST',
            xhr: function() {
                var myXhr = $.ajaxSettings.xhr();
                return myXhr;
            },
            data: formData,
            cache: false,
            contentType: false,
            processData: false
        }).done(function (data) {
            var response = JSON.parse(data.data);
            var entityId = response[0];
            var hasFile = response[5];
            if (hasFile === true) {
                $('#' + eventsTable + ' #event_' + entityId + ' td button.upload-file').removeClass('red').removeClass('green').addClass('green');
                $('#' + eventsTable + ' #event_' + entityId + ' td button.upload-file > i').removeClass('mdi-file-file-upload').removeClass('mdi-file-file-download').addClass('mdi-file-file-download');
            } else {
                $('#' + eventsTable + ' #event_' + entityId + ' td button.upload-file').removeClass('red').removeClass('green').addClass('red');
                $('#' + eventsTable + ' #event_' + entityId + ' td button.upload-file > i').removeClass('mdi-file-file-upload').removeClass('mdi-file-file-download').addClass('mdi-file-file-upload');
            }
            $('.modal').closeModal();
            hideProgress();
            Materialize.toast(data.message, data.timeout);
        });
    } else {
        hideProgress();
        Materialize.toast(errorMessage, 4000);
    }
    return false;
}

/**
 * Get lattitude and longittude for current typed addres. City field must be completed
 *
 * @param {string} formName - name of a form - this parameter is required
 * @param {string} cityPrefix - prefix for city form input, default 'city'
 * @param {string} streetPrefix - prefix for street form input, default 'street'
 * @param {string} latitudePrefix - prefix for latitude form input, default 'latitude'
 * @param {string} longitudePrefix - prefix for longitude form input, default 'longitude'
 *
 * @returns {object} - location object if address exists, undefined otherwise
 */
function getClientLocation(formName, cityPrefix, streetPrefix, latitudePrefix, longitudePrefix) {
    showProgress();
    cityPrefix =  cityPrefix || 'city';
    if ($('#' + formName + '_' + cityPrefix).val() !== '') {
        var buildedQuery = $('#' + formName + '_' + cityPrefix).val();

        streetPrefix = streetPrefix || 'street';
        latitudePrefix = latitudePrefix || 'latitude';
        longitudePrefix = longitudePrefix || 'longitude';

        if( $('#' + formName + '_' + streetPrefix).val() !== '') {
            buildedQuery += '+' +  $('#' + formName + '_' + streetPrefix).val();
        }

        if(xhr != null) {
            xhr.abort();
            xhr = null;
        }

        xhr = $.ajax({
            url: ' http://nominatim.openstreetmap.org/search?q=' + buildedQuery + '&format=json&polygon=1&addressdetails=1',
            method: "GET"
        }).done(function(data) {
            hideProgress();
            if(typeof data[0] !== 'undefined') {
                $('#' + formName + '_' + latitudePrefix).val(data[0].lat);
                $('#' + formName + '_' + longitudePrefix).val(data[0].lon);
            }
        });
    }
    hideProgress();
}

/**
 * Replace element content with result
 *
 * @param {array} data
 * @returns {undefined}
 */
function replace(data) {
    $(data.selector).html(data.result);
}

/**
 *
 * Sets star rating
 *
 * @param {string} url - path to controller action
 * @param {object} params - object of parameters that will be send to controller i.e. clientId, rating
 * @param {string} elementName - anchor that will handle request
 * @param {bool} resultCallback - if ajax returns any data - default false
 * @param {bool} spinner - if spinner be shown - default false
 * @returns {undefined}
 */
function setAjaxStarRating(url, params, elementName ,resultCallback, spinner) {

    if (typeof params === 'object') {
        spinner = spinner || false;
        resultCallback = resultCallback || false;
        if (elementName === '') {
            console.log('elementName parameter in setAjaxStarRating function cannot be empty');
            return;
        }
        if (spinner === true) {
            $('#' + elementName).parent().css('opacity', 0.3);
            $('#' + elementName).parent().parent().append('<i style="position:absolute; top:20%; left: 50%" class="fa fa-spinner fa-3x fa-spin" id="spinner"></i>');
        } else {
            $('#' + elementName).css('opacity', 0.3);
        }
        var formData = $.param(params);
        if (formData  === '') {
            return;
        }

        if(xhr != null) {
            xhr.abort();
            xhr = null;
        }

        xhr = $.ajax({
            url: url,
            method: 'POST',
            data: formData
        }).done(function(data) {
            if (resultCallback === true) {
                $('#' + elementName).html(data.data);
            }
            if (spinner === true) {
                $('#' + elementName).parent().css('opacity', 1);
                $('#spinner').remove();
            } else {
                $('#' + elementName).css('opacity', 1);
            }
            Materialize.toast(data.message, data.timeout);
        });
    }
}

/**
 * Prepend selector content
 *
 * @param {array} data
 * @returns {undefined}
 */
function prepend(data) {
    $(data.selector).prepend(data.result);
}

/**
 * Append selector content
 *
 * @param {array} data
 * @returns {undefined}
 */
function append(data) {
    $(data.selector).append(data.result);
}

/**
 * Call action from given url
 * @param {string} url
 */
function invokeAction(url, data, method = "GET") {
    showProgress();
    $.ajax({
        url: url,
        method: method,
        data: data
    }).done(function(data){
        hideProgress();
        if (data.message !== undefined) {
            var timeout = 3000;
            if (data.timeout) {
                timeout = data.timeout;
            }
            Materialize.toast(data.message, timeout);
            if (data.redirectToPath) {
                window.location.replace(data.redirectToPath);
            }
            if (data.value) {
                $(data.selector).focus();
                $(data.selector).val(data.value);
            }
        }
    });
}

/**
 * Call remote function to delete an entity with id defined in url
 *
 * @param {string} url
 * @param {html id} elementToRemove - used when element not in dataTable
 * @param {object} options - additional options:
 *     success - function that is called when data.success is 1 (default: null)
 *     fail - function that is called when data.success is not 1 (default: null)
 *     done - function that is called after all regardless of result (default: null)
 *     replace - selector of DOM element, that content will be replaced with data.result (default: '#modalsContainer')
 *     type - type of form (default: 'modal', other values: 'static')
 */
function deleteEntity(url, elementToRemove, options) {
    options = options || {};

    showProgress();
    $.ajax({
        url: url,
        method: "DELETE"
    }).done(function (data) {
        hideProgress();
        closeModal();
        Materialize.toast(data.message, data.timeout);

        if (data.error == 1) {
            return;
        }

        // if element not in dataTable
        if (elementToRemove !== '' && elementToRemove !== 'all') {
            $(elementToRemove).remove();
        }

        // If dataTable is present in current view
        // delete selected row. If multiple entities were deleted,
        // delete all rows by using DataTables API.
        if (dataTable) {
            if (data.ids) {
                var indexes = dataTable.rows().eq(0).filter(function(index) {
                    var id = parseInt(dataTable.cell(index, 0).data());
                    return data.ids.indexOf(id) > -1 ? true : false;
                });
                dataTable.rows(indexes).remove().draw(false);
            } else {
                // if remove all elements of dataTable
                if (elementToRemove === 'all') {
                    dataTable.clear().draw(false);
                } else {
                    dataTable.row('.selected').remove().draw(false);
                }
            }
        }

        /* Call sync method if exists (provided for flextable */
        if (window.sync) {
            window.sync(data);
        }

        if (data.mode === 'deletecomment') {
            $('#comments_table tr#comment_' + data.commentId).remove();
        }

        if (options.success) {
            window[options.success] && window[options.success](data);
        }
    });
}

/**
 * Close all modals
 */
function closeModal() {
    $('.modal').closeModal();
}

/**
 * Initialize export dropdown
 *
 * @param {string} insertAfter element after wich export is put
 */
function initExportDropdown(insertAfter) {
    if (typeof insertAfter === 'undefined') {
        insertAfter = '.add-button';
    }
    $(insertAfter).next('.DTTT_container').remove();

    $('.DTTT_container a').addClass('btn dropdown-button');
    $('.DTTT_collection').addClass('dropdown-content');
    $('.DTTT_collection').attr('id', 'DTTT_collection');
    $('.DTTT_container').attr('data-activates', 'DTTT_collection');
    $('.DTTT_container').insertAfter(insertAfter);

    $(insertAfter).next('.dt-buttons').remove();
    $('.dt-buttons a').addClass('waves-effect btn dropdown-button');
    $('.buttons-colections').addClass('dropdown-content');
    $('.dt-button').attr('id', 'dt-button');
    $('.dt-button').addClass('waves-effect btn dropdown-button');
    $('.dt-buttons').attr('data-activates', 'dt-button-collection');
    $('.dt-buttons').insertAfter(insertAfter);
}

/**
 * Change filter from simple to advanced
 */
function switchFilter() {
    $('.dataTables_filter').toggle();
    $('.dataTables_filter label').hide();
    $('#table-header').toggle();
    $('#table-advanced-filter').toggle();
    $("#table-advanced-filter > td > input").val('');
    fnResetAllFilters();
}

function fnResetAllFilters() {
    var oSettings = dataTable.settings();
    for(iCol = 0; iCol < oSettings[0].aoPreSearchCols.length; iCol++) {
      oSettings[0].aoPreSearchCols[ iCol ].sSearch = '';
    }
    dataTable.draw(false);
}

/**
 * Get values from all named childs
 * @param {string} id
 * @returns {object}
 */
function getChildData(id) {
    var data = {};
    $(id).find('*[name]').each(function(){
        if ( $(this).val() !== null ) {
            data[$(this).attr('name')] = $(this).val();
        }
    });
    return data;
}

/**
 * Merge two objects into one by using deep copy method
 * @param {object} first
 * @param {object} second
 * @returns {Object}
 */
function mergeObjects(first, second) {
    var new_object = jQuery.extend(true, {}, first);
    return jQuery.extend(true, new_object, second);
}

/**
 * Compare two literal Objects by recursivly checking their properties
 * excluding prototype ones (like inherited properties from other objects)
 * If properties parameter is defined,
 * then limit comparing object properties to only these ones
 *
 * @param {Object} first
 * @param {Object} second
 * @param {Array|null} properties
 * @returns {Boolean}
 */
function compareObjects(first, second, properties) {
    var firstNames = Object.getOwnPropertyNames(first);
    var secondNames = Object.getOwnPropertyNames(second);
    var limit = Array.isArray(properties) ? true : false;

    if ( !limit && firstNames.length !== secondNames.length) {
        return false;
    }

    for (var name in first) {
        if (first.hasOwnProperty(name)) {
            if ( limit && properties.indexOf(name) === -1 ) {
                continue;
            }
            if (second.hasOwnProperty(name)) {
                if ( typeof first[name] === "object" ) {
                    if ( typeof second[name] !== "object" ) {
                        return false;
                    }
                    var result = compareObjects(first[name], second[name]);
                    if ( result === false ) {
                        return false;
                    }
                } else if (first[name] !== second[name]) {
                    return false;
                }
            } else {
                return false;
            }
        }
    }
    return true;
}

/**
 * Get all same objects of object in defined array
 * If properties parameter is defined,
 * then limit comparing object properties to only these ones

 * @param {Object} object
 * @param {Array} array
 * @param {Array|null} properties
 * @returns {Array}
 */
function getSameObjectsFromArray(object, array, properties) {
    var objects = [];

    array.forEach(function(element){
        if (compareObjects(object, element, properties) === true) {
            objects.push(element);
        }
    });
    return objects;
}

/**
 * Remove same objects of defined object in array
 * If properties parameter is defined,
 * then limit checking object properties to only these ones
 *
 * @param {Object} object
 * @param {Array} array
 * @param {Array|null} properties
 * @returns {undefined}
 */
function removeSameObjectsFromArray(object, array, properties) {
    getSameObjectsFromArray(object, array, properties).forEach(function(element){
        var index = array.indexOf(element);
        if (index > -1) {
            array.splice(index, 1);
        }
    });
}

/**
 * Get intersection elements of multiple arrays
 * ex. for: [[1,2,3], [2,3,4], [2,3,5]] it returns an array intersection: [2,3]
 * Elements of arrays can also be array type, ex.:
 * [[[a,b],[a,c],[b,c]], [[a,d],[a,d],[b,c]]]
 *
 * @param {Array} arrays
 * @returns {Array} array with intersected elements
 */
function getIntersectionOfArrays(arrays) {
    if ( arrays.length === 0 ) {
        return [];
    }
    return arrays.shift().filter(function(v) {
        return arrays.every(function(a) {
            return (getSameObjectsFromArray(v, a).length > 0) === true;
        });
    });
}

/**
 * Get unique elements from array
 *
 * @param {Array} array
 * @returns {Array} array of unique values
 */
function getUniqueFromArray(array) {
    return array.filter(function(elem, pos, array) {
        return array.indexOf(elem) === pos;
    });
}

/**
 * Restore deleted entity in database
 *
 * @param {string} url - restore action
 * @param {integer} id - entity id in database
 * @param {string} lastStatus - last status to restore
 * @param {string} appendix - element added after restore string in url
 * @param {function} func - callback function
 * @returns {Boolean}
 */
function restoreEnitity(url, id, lastStatus, appendix, func) {
    $("a.link-restore").parent().css({"display": "none"});
    if ( !appendix ) {
        appendix = '';
    }
    $.ajax({
        url: url + id + '/restore' + appendix,
        data: $("a.link-restore").data(),
        type: 'POST'
    }).done(function(resp) {
        Materialize.toast(resp.message, resp.timeout,'',function(){
            $('#toast-container').remove();
        });

        if (!resp.mode) {
            resp.mode = 'restore';
        }

        // if element not in dataTable
        if (resp.mode === 'restore_row') {
            append(resp);

            return false;
        }

        if (typeof func === 'function') {
            if (func(resp) === false) {
                return;
            }
        }

        if (dataTable) {
            if (resp.data instanceof Object) {
                dataTable.row.add(resp.data).draw( false );
            } else {
                dataTable.row.add(JSON.parse(resp.data)).draw( false );
            }
        }

        if (typeof variable !== 'undefined' && func.success) {
            window[func.success] && window[func.success](resp);
        }
    });
    return false;
}

/**
 * Translate message if Translator exists (simplified method)
 *
 * @param {string} key
 * @returns {string}
 */
function trans(key) {
    if (typeof Translator !== 'undefined') {
        return Translator.trans( 'js.'+key);
    } else {
        return key;
    }

}

/**
 * Render html template by replacing variables
 * default variable format: {[ name ]}
 *
 * @param {string} template - html template
 * @param {object} data - literal object with named fields that will be searched in template
 * @param {object} params - parameters for rendering method
 * @returns {string}
 */
function renderTemplate(template, data, params) {
    var prefix = '{[ ';
    var postfix = ' ]}';

    if (typeof params !== 'undefined') {
        if (params.prefix) {
            prefix = params['prefix'];
        }

        if (params.postfix) {
            postfix = params['postfix'];
        }
    }
    Object.keys(data).forEach(function(key){
        var variable = prefix + key + postfix;
        variable = variable.replace(/([.*+?^=!:${}()|\[\]\/\\])/g, "\\$1");
        template = template.replace(new RegExp(variable, 'g'), data[key]);
    });

    return template;
}

/**
 * Delete table row
 *
 * @param {string} element in row
 * @param {function} func that will be invoked (optional)
 * @returns {undefined}
 */
function deleteTableRow(element, func) {
    var row = $(element).closest('tr');

    if ( typeof func === 'function' ) {
        func(row);
    }
    $(element).closest('tr').remove();
}

/**
 * Delete duplicated options from select tag
 *
 * @param {Object} select jQuery object
 * @returns {undefined}
 */
function deleteDuplicatedOptions(select) {
    var found = [];
    select.find('option').each(function() {
      if ($.inArray(this.value, found) !== -1) {
          $(this).remove();
      }
      found.push(this.value);
    });
}

/**
 * Adds button to specified selects placed in forms.
 * To determinate selects put them into modals object behind keys,
 * and specify urls to open new modals as a value of those keys.
 * i.e. {
 *      'sensorType' : '/app_dev.php/sensortypes/new'
 * }
 *
 * @param {array} urls to each form that is connected to specifed forms selects
 * @param {array} modals to each form that will be displayed in submodal container
 * @param {string} modalContainer  handler for submodal form
 */
function addButtonsToFormSelects(urls, modals, modalContainer) {
    for (var key in urls) {
            $('form select[name*="' + key + '"]').parent().addClass('rela');
            $('form select[name*="' + key + '"]').parent().append('<a class="btn btn-small waves-effect addSelectButton tooltipster" onclick="showSubmodalForm(\'' + key + '\', \'' + urls[key] + '\', \'' + modals[key] + '\',\'' + modalContainer + '\')" data-select="' + key + '" ><i class="ion-plus"></i></a>');
        $( ".tooltipster" ).tooltipster({
            content: trans('add')
        })
    }
}

function showSubmodalForm(selectName, url, submodal, modalContainer)
{
    $.ajax({
        url: url + '?&selectName=' + selectName,
        method: 'GET'
    }).done(function(data) {
        $('#' + submodal).remove();
        $('.' + modalContainer).html(data);
        $('#' +  submodal).openModal();
        initFormJs($('#' +  submodal + ' form'));
    });
    return true;
}

function toHex(valueStr)
{
  var loAZ = "abcdefghijklmnopqrstuvwxyz";
  var symbols = " !\"#$%&'()*+,-./0123456789:;<=>?@";
  symbols+= loAZ.toUpperCase();
  symbols+= "[\\]^_`";
  symbols+= loAZ;
  symbols+= "{|}~";
  var hexChars = "0123456789abcdef";
  var text = "";
  for( i=0; i<valueStr.length; i++ )
  {
    var oneChar = valueStr.charAt(i);
    var asciiValue = symbols.indexOf(oneChar) + 32;
    var index1 = asciiValue % 16;
    var index2 = (asciiValue - index1)/16;
    if ( text !== "" ) {
        text += "";
    }
    text += hexChars.charAt(index2);
    text += hexChars.charAt(index1);
  }
  return text;
}

/**
 * Switch tooltip content
 * @param {object} element
 * @param {string} content
 */
function switchTooltipContent(element, content)
{
    var data = 'tooltip-content';
    var option = 'content';
    var item = $(element);

    if (!item.data(data)) {
        item.data(data, item.tooltipster(option));
    }

    if (item.tooltipster(option) === content) {
        item.tooltipster(option, item.data(data));
    } else {
        item.tooltipster(option, content);
    }
}

function initPlugins (context) {
    // Get current context like form, layer, etc.
    // if context is undefined, then set is as body
    var $context = $(context || document.body);

    if ($.fn.collapsible) {
        $context.find('.collapsible:not(.no-autoinitialize)').collapsible();
    }

    if ($.fn.tooltipster) {
        $context.find('.tooltip:not(.no-autoinitialize)').tooltipster();
    }

    if ($.fn.select2) {
        $context.find('select.select2:not(.no-autoinitialize)').select2();
    }

    if ($.fn.pikaday) {
        $context.find('.pikaday:not(.no-autoinitialize)').pikaday();
    }

    if ($.fn.pickadate) {
        $context.find('.datepicker:not(.no-autoinitialize)').pickadate();
    }

    if ($.fn.clockpicker) {
        $context.find('.clockpicker:not(.no-autoinitialize)').clockpicker();
    }

    if ($.fn.tabs) {
        $context.find('ul.tabs:not(.no-autoinitialize)').tabs();
    }

    if ($.fn.tinymce) {
        $context.find('.tinymce:not(.no-autoinitialize)').tinymce();
    }

    if ($.fn.spectrum) {
        $context.find('.spectrum:not(.no-autoinitialize)').spectrum();
    }
}


var didScroll;
var lastScrollTop = 0;
var delta = 5;
var navbarHeight = $('.nav-wrapper').outerHeight();

$(window).scroll(function(event){
    didScroll = true;
});

setInterval(function() {
    if (didScroll) {
        hasScrolled();
        didScroll = false;
    }
}, 250);

function hasScrolled() {
    var st = $(this).scrollTop();

    if(Math.abs(lastScrollTop - st) <= delta)
        return;

    if (st > lastScrollTop && st > navbarHeight){
        // Scroll Down
        $('.nav-wrapper').removeClass('nav-down').addClass('nav-up');
        $('#menu').removeClass('nav-down').addClass('nav-up');
        $('nav').addClass('nav-up');
        $('.top').removeClass('nav-down').addClass('nav-top-up');
    } else {
        // Scroll Up
        if(st + $(window).height() < $(document).height()) {
            $('.nav-wrapper').removeClass('nav-up').addClass('nav-down');
            $('#menu').removeClass('nav-up').addClass('nav-down');
            $('nav').removeClass('nav-up');
            $('.top').addClass('nav-down').removeClass('nav-top-up');
        }
    }

    lastScrollTop = st;
}

function reloadpage() {
    window.location.reload();
}

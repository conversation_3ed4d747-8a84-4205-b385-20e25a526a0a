/**
 * Card module
 * Extended implementation of Card class (_con material admin)
 * Added initiliazation and maximize behaviour
 */
var I2M = (function(Scope) {
    //Verify scopes
    Scope.UI = Scope.UI || {};
    Scope.UI.Card = Scope.UI.Card || constructor;

    // Mappings
    var Card = Scope.UI.Card;

    // Default options
    var options = {
        content: '> .content',
        fullscreenButton: '.fullscreen.button',
        fullscreenExitButton: '.fullscreen-exit.button',
        minimizeButton: '> .title > .minimize',
        closeButton: '> .title > .close',
        parent: 'widget-container',
        minimized: 'minimized',
        maximizedClass: 'maximized',
        minimizedClass: 'minimized',
        title: '.title',
        context: 'body',
        contextClass: 'body-overflow-x-hidden'
    };

    // Card custom events
    var events = {
        closed: 'i2m:card:closed',
        created: 'i2m:card:created',
        minimized: 'i2m:card:minimized',
        maximized: 'i2m:card:maximized',
        unmaximized: 'i2m:card:unmaximized'
    };

    /**
     * Create instance of Card.
     *
     * @constructor
     * @this {Card}
     * @param {string} card - card selector
     */
    function constructor(card) {
        var _this = this;
        var card = {$parent: $(card)};
        var $parent = card.$parent;
        card.$content = $parent.find(options.content);
        card.$closeButton = $parent.find(options.closeButton);
        card.$minimizeButton = $parent.find(options.minimizeButton);
        card.$fullscreenButton = $parent.find(options.fullscreenButton);
        card.$fullscreenExitButton = $parent.find(options.fullscreenExitButton);
        card.$content = $parent.find(options.content);
        card.$title = $parent.find(options.title);
        card.$context = $(options.context);
        card.$minimizeButton.off('click');
        card.$closeButton.off('click');
        card.$content.css('display', 'block');
        card.events = events;
        card.options = options;
        this.card = card;

        // Card Events
        card.$minimizeButton.click(function() {
            _this.minimize();
        });

        card.$closeButton.click(function() {
            _this.close();
        });

        card.$fullscreenButton.click(function() {
            _this.maximize();
        });

        card.$fullscreenExitButton.click(function() {
            _this.unmaximize();
        });

        $parent.trigger(events.created, this);
    }

    /**
     * Maximize Card.
     *
     * @this {Card}
     * @returns {this}
     */
    Card.prototype.maximize = function() {
        var card = this.card;
        var $parent = card.$parent;

        card.$fullscreenButton.hide();
        card.$fullscreenExitButton.show();
        card.$minimizeButton.hide();
        card.$closeButton.hide();
        card.$context.addClass(options.contextClass);
        card.$title.find('a').addClass('ml-5');
        $parent.addClass(options.maximizedClass);

        if ($parent.hasClass(options.minimizedClass)) {
            $parent.removeClass(options.minimizedClass);
            card.$content.css('display', 'block');
        }
        $parent.trigger(events.maximized, this);

        return this;
    };

    /**
     * Unmaximize Card.
     *
     * @this {Card}
     * @returns {this}
     */
    Card.prototype.unmaximize = function() {
        var card = this.card;
        var $parent = card.$parent;

        card.$fullscreenButton.show();
        card.$fullscreenExitButton.hide();
        card.$minimizeButton.show();
        card.$closeButton.show();
        card.$context.removeClass(options.contextClass);
        card.$title.find('a').removeClass('ml-5');
        $parent.removeClass(options.maximizedClass);
        $parent.trigger(events.unmaximized, this);

        return this;
    };

    /**
     * Minimize Card.
     *
     * @this {Card}
     * @returns {this}
     */
    Card.prototype.minimize = function() {
        var card = this.card;
        var $parent = card.$parent;
        var $content = card.$content;
        $content.slideToggle();
        $parent.toggleClass(options.minimized);
        $parent.trigger(events.minimized, this);

        return this;
    };

    /**
     * Close Card.
     *
     * @this {Card}
     * @returns {this}
     */
    Card.prototype.close = function() {
        var $parent = this.card.$parent;
        var $outer = $parent.parent();

        $parent.velocity({
            opacity: 0,
            height: 0
        }, function() {
            $parent.trigger(events.closed, this);
            $outer.hasClass(options.parent) ? $outer.remove() : $parent.remove();
        });

        return this;
    };

    return Scope;
})(I2M || {});
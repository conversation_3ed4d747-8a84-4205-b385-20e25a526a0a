$(document).ready(function () {
    $(document).on('click','#picked-color', function() {
        var position = $(this).offset();
        $('#simple-color-picker').css('top', position.top);
        $('#simple-color-picker').css('left', position.left);
        $('#simple-color-picker').show();
    });

    $(document).on('mouseover','#simple-color-picker .color', function() {
        var color = $(this).data('color');
        $('#simple-color-picker-view').html('<div class="col s12 m12 l12 btn btn-floating btn-small ' + color + '"></div>');
    });

    $(document).on('mouseout','#simple-color-picker .color', function() {
        $('#simple-color-picker-view').html('');
    });

    $(document).on('click','#simple-color-picker .color', function() {
        var color = $(this).data('color');
        $('#picked-color').attr('class', 'col s3 m3 l3 btn btn-floating btn-small ' + color);
        $('.colors').val(color);
    });

    $('*:not(#simple-color-picker-button, #simple-color-picker)', 'html').click(function() {
        $('#simple-color-picker').hide();
    });
})
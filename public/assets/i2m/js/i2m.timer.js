/**
 * Timer module
 * This is high precision timer based on requestAnimationFrame
 * for better time management.
 */
var I2M = (function(Scope) {
    //Verify scopes
    Scope.Timer = Scope.Timer || constructor;

    // Mappings
    var Timer = Scope.Timer;

    // Default options
    var options = {
        fps: {
            normal: 60,
            maximum: 60,
            minimum: 1
        }
    };

    /**
     * Create instance of Timer.
     *
     * @constructor
     * @this {Timer}
     * @param {object} params - optional params: fps, callback
     */
    function constructor(params) {
        params = params || {};
        /** @private */ this._callbacks = [];
        /** @private */ this._fps = params.fps || options.fps.normal;
        /** @private */ this._run = false;
        /** @private */ this._delta = 0;
        /** @private */ this._last = 0;
        /** @private */ this._accumulator = 0;
        /** @private */ this._resolution = 0;

        if (params.callback) {
            if (params.callback instanceof Array) {
                params.callback.forEach(function(func) {
                    this._callbacks.push(func);
                }.bind(this));
            } else {
                this._callbacks.push(params.callback);
            }
        }
    }

    /**
     * Set FPS (frames per second) - timer resolution for invoking callbacks
     *
     * @this {Timer}
     * @param {number} fps - in range between 1 to 60
     * @returns {this}
     */
    Timer.prototype.setFPS = function(fps) {
        fps = Math.min(options.fps.maximum, Math.max(options.fps.minimum, fps));
        this._fps = fps;
        this._resolution = this._round2(1 / this._fps);

        return this;
    };

    /**
     * Get FPS (frames per second) - timer resolution for invoking callbacks
     *
     * @this {Timer} 
     * @returns {Number}
     */
    Timer.prototype.getFPS = function() {
        return this._fps;
    };

    /**
     * Add callback to Timer callbacks array
     *
     * @this {Timer}
     * @param {function} callback
     * @returns {this}
     */
    Timer.prototype.addCallback = function(func) {
        if (typeof func !== 'function') {
            throw 'wrong function format';
        }
        if (this._callbacks.indexOf(func) === -1) {
            this._callbacks.push(func);
        }

        return this;
    };

    /**
     * Remove callback from Timer callbacks array
     *
     * @this {Timer}
     * @param {function} callback
     * @returns {this}
     */
    Timer.prototype.removeCallback = function(func) {
        var index = this._callbacks.indexOf(func);
        if (index > -1) {
            this._callbacks.splice(index, 1);
        }

        return this;
    };

    /**
     * Get Timer callbacks array
     *
     * @this {Timer}
     * @returns {array}
     */
    Timer.prototype.getCallbacks = function() {
        return this._callbacks;
    };

    /**
     * Get current delta
     *
     * @this {Timer}
     * @returns {number} delta
     */
    Timer.prototype.getDelta = function() {
        return this._delta;
    };

    /**
     * Start Timer
     *
     * @this Timer
     * @returns {this}
     */
    Timer.prototype.start = function() {
        if (!this._run) {
            this._run = true;
            this._delta = 0;
            this._last = 0;
            this._accumulator = 0;
            this._resolution = this._round2(1 / this._fps);
            this._step();
        }

        return this;
    };

    /**
     * Stop Timer
     *
     * @this Timer
     * @returns {this}
     */
    Timer.prototype.stop = function() {
        this._run = false;
    };
 
    /**
     * Round number to two decimal places
     *
     * @private
     * @param {number} number
     * @returns {number}
     */
    Timer.prototype._round2 = function(number) {
        return Math.round(number * 100) / 100;
    };

    /**
     * Invoke all callbacks when accumulator achieved defined resolution
     * This is internal loop function.
     *
     * @private
     * @this Timer
     */
    Timer.prototype._step = function() {
        if (this._run) {
            var perf = window.performance.now();
            this._delta = (perf - this._last) / 1000;
            this._last = perf;
            this._accumulator += this._delta;

            if (this._accumulator > this._resolution) {
                this._accumulator -= this._resolution;

                for (var i in this._callbacks) {
                    this._callbacks[i]();
                }
            }
            window.requestAnimationFrame(this._step.bind(this));
        }
    };

    return Scope;
})(I2M || {});

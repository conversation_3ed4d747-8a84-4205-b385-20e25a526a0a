var I2M = (function(Scope) {
    // Verify scopes
    Scope.Chat = Scope.Chat || {};

    var par = JSON.parse($('#mainChatButton').find('script').eq(0).html());
    var myinterval;

    function flushUsers(users)
    {
        var online = new Array();
        var offline = new Array();
        var all = new Array();

        $('.usersonline').children().each(function(t)
        {
            var id = $(this).attr('alt-data');
            online[id] = 1;
            all.push(id);
        });

        $('.usersoffline').children().each(function(t)
        {
            var id = $(this).attr('alt-data');
            offline[id] = 1;
            all.push(id);
        });

        for (var i = 0; i < all.length; i++) 
        {
            if (users[all[i]] == 1)
            {
                if (offline[all[i]] == 1)
                {
                    $('.useractive_'+all[i]).appendTo('.usersonline').find('.status').eq(0).html('online');
                    $('.useractive_'+all[i]).find('.fa-circle').eq(0).removeClass('black-text').addClass('green-text');
                }
            } else {
                if (online[all[i]] == 1)
                {
                    $('.useractive_'+all[i]).appendTo('.usersoffline').find('.status').eq(0).html('online');
                    $('.useractive_'+all[i]).find('.fa-circle').eq(0).removeClass('green-text').addClass('black-text');
                }
            }
        }
    }

    function sendMessage(who)
    {
        var message = $('#chat-message').val();
        $.post(par.route + '/send/' + who, {message: message}, function(data)
        {
            if (data.status == 'OK')
            {
                selectedPos++;
            }
        },'json')
        .fail(function()
        {
            window.alert('Failed');
        });
    }

    function addMessage(who, messages, id)
    {
        for (var i = 0; i < messages.length; i++) {
            if (messages[i][0] == id) {
                var newMsg = $('<div class="from-me">' + messages[i][2] + '</div>');
                $('#chatmessagewindow').append('<div class="clear"></div>').append(newMsg);
            } else {
                var newMsg = $('<div class="from-them">' + messages[i][2] + '</div>');
                $('#chatmessagewindow').append('<div class="clear"></div>').append(newMsg);
            }

            selectedPos++;
        }

        $('#chatmessagewindow').scrollTop($('#chatmessagewindow').height());
    }

    function lastMessages(who)
    {
        $('#chatmessagewindow').html('');

        $.get(par.route + '/last/' + who, function(data)
        {
            if (data.status == 'OK')
            {
                selectedPos = 0;
                var messages = data.messages;
                addMessage(who, messages, data.id);
            }
        },'json')
        .fail(function()
        {

        });
    }

    function flushMessages(lastmsg, lastopen, lastcdx)
    {
        var warning = 0;
        for (idx in lastmsg) {
            if ((lastmsg[idx] > lastopen[idx] || (lastmsg[idx] > 0 && lastopen[idx] == false)) && selectedUser != idx) {
                $('.useractive_' + idx).find('.name').eq(0).css('color', 'yellow');
                $('.useractive_' + idx).find('.messagecdx').eq(0).html('[' + lastcdx[idx] + ']');
                warning++;
            } else {
                $('.useractive_' + idx).find('.name').eq(0).css('color', 'white');
                $('.useractive_' + idx).find('.messagecdx').eq(0).html('');
            }
        }

        if (warning > 0) {
            $('#mainChatButton').find('a').eq('0').removeClass('z-depth-0').addClass('z-depth-3');
        } else {
            $('#mainChatButton').find('a').eq('0').removeClass('z-depth-3').addClass('z-depth-0');
        }

    }

    function pingChat()
    {
        $.get(par.route+'?who=' + selectedUser + '&start=' + selectedPos, function(data)
        {
            if (data.status == 'OK')
            {
                $('#mainChatButton').find('a').eq('0').removeClass('green-text').removeClass('yellow-text').addClass('green-text');
                flushUsers(data.users);
                flushMessages(data.lastmsg, data.lastopen, data.lastcdx);
                if (data.messages.length > 0) {
                    addMessage(data.who, data.messages, data.id);
                }
            } else {
                $('#mainChatButton').find('a').eq('0').removeClass('green-text').removeClass('yellow-text').addClass('red-text');
                window.clearInterval(myinterval);
            }
        },'json')
        .fail(function(data)
        {
            $('#mainChatButton').find('a').eq('0').removeClass('yellow-text').addClass('red-text');
            window.clearInterval(myinterval);
            if (data.responseText.search('login-text') > 0) {
              window.location.reload();
            }
        });

    }

    var selectedUser = 0;
    var selectedPos = 0;

    $('#mainChatButton').find('a').eq('0').removeClass('grey-text').addClass('yellow-text');
    pingChat();
    myinterval = window.setInterval(function(){
        pingChat();
    }, 5000);

    $('.chat').eq(0).on('click', '.contacts .user', function(e) {
        selectedUser = $(this).attr('alt-data');
        lastMessages(selectedUser);
    });

    $('.chat-back').bind('click',function() {
        selectedUser = 0;
    });

    var elm = $('.chat').find('.send > form').eq(0);

    $(elm).on('submit', function(e) {
        sendMessage(selectedUser);
    });

    var events = $._data($(elm)[0]).events;
    var submits = events.submit;
    var t = submits[0];
    submits[0] = submits[1];
    submits[1] = t;

    // Return scope with additional functionality
    return Scope;
})(I2M || {});
var I2M = (function(Scope) {
    // Verify scopes
    Scope.Data = Scope.Data || {};
    Scope.Data.Provider = Scope.Data.Provider || constructor;

    // Mappings
    var Provider = Scope.Data.Provider;

    // Types
    var types = {
        remote: 'remote',
        static: 'static'
    };

    // Default options
    var defaultOptions = {
        dataType: 'json',
        shared: true,
        type: types.remote,
        url: null,
        method: 'GET',
        async: true
    };

    // Active providers
    var providers = [];

    /**
     * Constructor
     * @param {object} params
     * @returns {i2m_data_provider_L1.constructor}
     */
    function constructor(params) {
        this.promise = null;
        this.data = null;
        this.options = {
            dataType: defaultOptions.dataType,
            shared: defaultOptions.shared,
            type: defaultOptions.type,
            url: defaultOptions.url,
            method: defaultOptions.method,
            async: defaultOptions.async
        };
        this.setParams(params);
        providers.push(this);
        this.refresh();
    }

    /**
     * Set parameters
     * @param {object} params
     */
    Provider.prototype.setParams = function(params) {
        if (params instanceof Object) {
            this.options = mergeObjects(this.options, params);
        }
    };

    /**
     * Get data
     * @returns {object} data
     */
    Provider.prototype.getData = function() {
        var options = this.options;

        if (options.type === types.static) {
            return this.data;
        }

        if (this.promise) {
            return this.promise;
        } else {
            return this.data;
        }
    };

    /**
     * Reload data
     * (this is alias for refresh method)
     * @returns {object} promise
     */
    Provider.prototype.reload = function() {
        return this.refresh();
    };

    /**
     * Refresh data
     * @returns {object} promise
     */
    Provider.prototype.refresh = function() {
        var options = this.options;

        if (options.type === types.static) {
            this.promise = null;
            return;
        }

        if (options.shared) {
            for (var key in providers) {
                var provider = providers[key];

                if (provider === this) {
                    continue;
                } else if (provider.options.type !== types.remote) {
                    continue;
                } else if (provider.options.url !== this.options.url) {
                    continue;
                }

                if (provider.promise) {
                    this.promise = provider.promise;

                    this.promise.done(function(){
                        this.data = provider.data;
                        this.promise = null;
                    }.bind(this));
                    return provider.promise;
                } else {
                    this.data = provider.data;
                    return;
                }
            }
        }

        if (this.promise) {
            return this.promise;
        }

        this.promise = $.ajax({
            dataType: options.dataType,
            method: options.method,
            async: options.async,
            url: options.url
        }).done(function(result) {
            this.data = result;
            this.promise = null;
        }.bind(this));

        return this.promise;
    };

    return Scope;
})(I2M || {});
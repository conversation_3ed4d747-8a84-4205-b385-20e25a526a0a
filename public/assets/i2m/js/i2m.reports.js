$(document).ready(function() {
    $('.progress').css('visibility', 'hidden'); 
    $('input[name=startDate]').on('change', function() {
        setDate();
    });
    $('input[name=endDate]').on('change', function() {
        setDate(true);
    });
});

var tableId;

/**
 * Generate report and related chart
 * @param {string} path - url path
 * @param {string} id - id of report
 * @param {object} [options] - options for chart
 * @returns {undefined}
 */
function generateReport(path, id, options) {
    if ( $('#generateButton').hasClass('disabled') ) {
        return;
    }

    tableId = '#table-' + id;
    $(tableId).remove();
    $('.progress').css('visibility', 'visible');
    
    $.get(
        path + '?' + $('#report-input-data').serialize(),
        function (report) {        
            if(report.error){
                Materialize.toast(report.message, report.timeout, report.ClassName);
                $('.progress').css('visibility', 'hidden');
                return false;
            }
            $('#report-table').html(report.table);
            $('#spinner-progress').removeClass('active');
            $('.content-wrap').removeClass('opacity-50');
            
            // Draw chart for report
            $('#chart-'+id).height(300);
            drawChart(id, convertToFlot(report.chartData), options ? options : {} );  
                
            $(tableId).DataTable({
                scrollCollapse: true,
                bSortCellsTop: true,
                dom: 'Tl<"top"i f>rtip',
                tableTools: {
                    sSwfPath: '../../assets/dataTables/extensions/TableTools/swf/copy_csv_xls_pdf.swf',
                    aButtons: [{
                        sExtends: "collection",
                        sButtonText: '<i class="mdi-action-input"></i> ' + trans('export'),
                        aButtons: [{
                            sExtends: 'xls',
                            sFileName: '*.xls',
                            mColumns: 'sortable',
                            bSelectedOnly: true
                            },
                            {
                                sExtends: 'pdf',
                                mColumns: 'sortable',
                                bSelectedOnly: true
                            },
                            {
                                sExtends: 'csv',
                                mColumns: 'sortable',
                                bSelectedOnly: true
                            }
                        ]
                    }]
                },
                paging: false,
                language: {
                    search: trans('search'),
                    info : trans('info')
                }
            });

            $('.progress').css('visibility', 'hidden');
            initExportDropdown('#generateButton');
            $('#exportButton').removeClass('disabled');
    });  
}

/**
 * Export report to pdf and open it
 * @param {string} path
 * @param {string} id
 * @param {object} options
 * @returns {undefined}
 */
function exportReport(path, id, options) {
    if ( $('#exportButton').hasClass('disabled') ) {
        return;
    }
    $('.progress').css('visibility', 'visible');
    var params = $('#report-input-data').serialize();
    window.location.href = path + '?' + params + '&reportType='+id;
}

function setDate() {
    var firstInput = $('input[name=startDate]');
    var secondInput = $('input[name=endDate]');
    if (moment(firstInput.val()) > (moment(secondInput.val()))) {
        var tmp = firstInput.val();
        firstInput.val(secondInput.val());
        Materialize.toast(trans('date-range-corrected'), 8000, 'red');
    }
};

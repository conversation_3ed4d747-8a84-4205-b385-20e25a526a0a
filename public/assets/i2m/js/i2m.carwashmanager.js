
/**
 * Gets all data loaded after choosing single carwash. Function used when there is more tha one select loading data on page
 *
 * @param {string} url - link to controller action
 * @param {integer} id - current carwash identy
 * @param {integer} serial - current carwash serial number
 * @param {string} dataHandler - current div identy to reload ajax data
 * @param {string} serialField - current span identy to reload carwash serial number
 * @param {string} dateFromField - dateFrom selector
 * @param {string} dateToField - dateTo selector
 * @returns {undefined}
 */
function getClientModuleExtendedData(url, serial, dataHandler, serialField, dateFromField, dateToField)
{
    getAjaxData(url, serial, dataHandler, serialField, dateFromField, dateToField, '');
}

var xhr;

/**
 * Gets data from controller for chosen loading type
 *
 * @param {string} url
 * @param {string|integer} id
 * @param {integer} serial
 * @param {string} dataHandler
 * @param {string} serialField
 * @param {string} dateFromField
 * @param {string} dateToField
 * @returns {undefined}
 */
function getAjaxData(url, serial, dataH<PERSON>ler, serial<PERSON>ield, date<PERSON>rom<PERSON>ield, dateTo<PERSON>ield, extra)
{
    var handler = $('#' + dataHandler);
    $('#spinner-progress').addClass('active');
    $('body .content-wrap').addClass('opacity-50');
    var link = url + '?&serial=' + serial;

    if (dateFromField !== '' && dateToField !== '') {
        switchdates('#'+dateFromField, "input[name='" +$('#' + dateFromField).attr("name") + "Date']",'#'+dateToField, "input[name='" +$('#' + dateToField).attr("name") + "Date']");
        var dateFrom = $('#' + dateFromField).val();
        var dateFromValObj = $( "input[name='"+$('#' + dateFromField).attr("name") + "Date']");
        if(dateFromValObj.val()) {
            dateFrom = dateFromValObj.val();
        }
        var dateTo = $('#' + dateToField).val();
        var dateToValObj = $( "input[name='"+$('#' + dateToField).attr("name") + "Date']");
        if(dateToValObj.val()) {
            dateTo = dateToValObj.val();
        }
        $('#' + dateFromField).removeClass('invalid').addClass('valid');
        $('#' + dateToField).removeClass('invalid').addClass('valid');
        link += '&dateFrom=' + dateFrom + '&dateTo=' + dateTo;
    }

    link += extra;

    if(xhr != null) {
        xhr.abort();
        xhr = null;
    }
    xhr = $.ajax({
        url: link,
        method: 'get'
    }).done(function(data) {
        if (data.error) {
            $('#just_on_mobile_no_data').removeClass('hide');
            Materialize.toast(data.message, data.timeout);
            if(data.messageAdd) {
                $('#just_on_mobile_no_data').addClass('hide');
                handler.html('<p class="col s12 m12 l12 p-15 red message-add center-align">' + data.messageAdd + '</p>');
            } else {
                handler.html('');
            }
        } else {
            handler.html(data.result);
            $('#just_on_mobile_no_data').addClass('hide');
        }
        if (typeof serialField === 'string' && serialField.length > 0) {
          $('#' + serialField).html(serial);
        }
        $('#spinner-progress').removeClass('active');
        $('body .content-wrap').removeClass('opacity-50');
    });
}

/**
 * Update label state correspondig to given select element
 * (fix for missing Select2 label state support)
 *
 * @param {object} $select
 */
function updateSelect2Label($select) {
    if (($select.val() === null || $select.val() === '')) {
        var $option = $select.find('option:selected');
        if ($option.length === 0) {
            $select.siblings('label').removeClass('active');
            return;
        }

        if ($option.html() === '') {
            $select.siblings('label').removeClass('active');
        } else if ($option.html() === '-') {
            $select.val([]).trigger('change');
            $select.siblings('label').removeClass('active');
        }
    } else {
        $select.siblings('label').addClass('active');
    }
}

$(document).ready(function() {
    $('body .progress-content').find('.preloader-wrapper').removeClass('active');
    $('body .card-panel').removeClass('opacity-75');

    var selectTimer;
    var doneSelectInterval = 500;

    $(document).on('change', '.select2.single-select', function() {
        clearTimeout(selectTimer);
        var selectId = $(this).attr('id');
        if (selectId !== 'g__i2m__set_myjnia_osw_iTryb' && selectId !== 'hardware') {
            selectTimer = setTimeout(function() {
                getClientModuleData(
                    selectId,
                    'masked-date-from',
                    'masked-date-to'
                );
            }, doneSelectInterval);
        };
    });


    $(document).on('change', '#masked-date-from.single-date-from, #masked-date-to.single-date-to', function() {
        clearTimeout(selectTimer);
        if(typeof $(this).data('select') !== 'undefined') {
            var selectId = $(this).data('select');
        } else {
            var selectId = $('.select2.single-select').attr('id');
        }
        selectTimer = setTimeout(function() {
            getClientModuleData(
                selectId,
                'masked-date-from',
                'masked-date-to'
            );
        }, doneSelectInterval);
    });

    $('#spinner-progress').removeClass('active');
    // This was done with previous pickaday
    // if on screen is datepicker trigger change to request ajax on start
    $("#masked-date-from.datepicker").trigger("change");

});

/**
 * Gets all data loaded after choosing single carwash. Used when there is only one select loading data on page
 *
 * @param {string} carwashSelectId - current select identy
 * @param {string} dateFromField - dateFrom selector
 * @param {string} dateToField - dateTo selector
 * @returns {undefined}
 */
function getClientModuleData(carwashSelectId, dateFromField, dateToField)
{
    var extra = '';
    if ($('.card').find('#' + carwashSelectId).length > 0) {
        var selectVals = $('#' + carwashSelectId).val().split(',');
        var serial = selectVals[0];
        if (typeof extraParametersForPage !== 'undefined') {
            extra = extraParametersForPage();
        }
    } else {
        var serial = '';
    }
    getAjaxData(carwashSelectId, serial, 'div_' + carwashSelectId, '', dateFromField, dateToField, extra);
}

/**
 *
 * Get keyusage reload list after choosing reload list tab
 *
 * @param {string} url - link to controller action
 * @param {integer} serial - current carwash serial number
 * @param owner
 * @param {string} dataHandler - current div identy to reload ajax data
 * @param dateFrom
 * @param dateTo
 * @returns {undefined}
 */
function getKeyUsageReloadList(url, serial, owner, dataHandler, dateFrom, dateTo)
{
    var handler = $('#' + dataHandler);
    $('#spinner-progress').addClass('active');
    $('body .content-wrap').addClass('opacity-50');

    if (!dateFrom) {
        dateFrom = $('input[name="startDate"]').val();
    }

    if (!dateTo) {
        dateTo = $('input[name="endDate"]').val();
    }

    var link = url +'?&cardserial=' + serial + '&owner=' + owner + '&dateFrom=' + dateFrom + '&dateTo=' + dateTo;

    if(xhr != null) {
        xhr.abort();
        xhr = null;
    }

    xhr = $.ajax({
        url: link,
        method: 'get'
    }).done(function(data) {
        if (data.error){
            handler.html(data.message);
        } else {
            handler.html(data.result);
        }
        $('#spinner-progress').removeClass('active');
        $('body .content-wrap').removeClass('opacity-50');
    });
}

/**
 * Refill key
 *
 * @param {string} url - link to controller action
 * @param {integer} serial - current carwash serial number
 * @param {string} dataHandler - current div identy to reload ajax data
 * @param {string} value - refill value
 * @param {string} reloadUrl
 * @returns {undefined}
 */
function refillKey(url, serial, owner, dataHandler, value, reloadUrl)
{
    $('#' + dataHandler);
    $('#spinner-progress').addClass('active');
    $('body .content-wrap').addClass('opacity-50');

    $.ajax({
        url: url + '?&cardserial=' + serial + '&value=' + value + '&owner=' + owner,
        method: 'get'
    }).done(function(data) {
        if (data.error){
            $('#' + dataHandler).html(data.message);
        } else {
            getKeyUsageReloadList(
                reloadUrl,
                serial,
                owner,
                dataHandler
            );
        }
        $('#spinner-progress').removeClass('active');
        $('body .content-wrap').removeClass('opacity-50');
    });
}

/**
 * Delele key supplement, and reload key supplements list
 *
 * @param {string} url - link to controller action
 * @param {integer} entityId - current seplement identy to remove
 * @param {integer} serial - current carwash serial number
 * @param {string} dataHandler - current div identy to reload ajax data
 * @param {string} reloadUrl - link to controller reload action
 * @returns {undefined}
 */
function deleteRefill(url, entityId, serial, owner, dataHandler, reloadUrl)
{
    $('#' + dataHandler);
    $('#spinner-progress').addClass('active');
    $('body .content-wrap').addClass('opacity-50');
    $.ajax({
        url: url + '?&cardserial=' + serial + '&owner=' + owner + '&entityId=' + entityId,
        method: 'get'
    }).done(function(data) {
        if (data.error){
            console.log(data.message);
        } else {
            $('.modal').closeModal();
            Materialize.toast(data.message, data.timeout);
            getKeyUsageReloadList(
                reloadUrl,
                serial,
                owner,
                dataHandler
            );
        }
        $('#spinner-progress').removeClass('active');
        $('body .content-wrap').removeClass('opacity-50');
    });
}

function isParsleyValid(formSelector) {
    if (!$.fn.parsley) {
        return true;
    }

    var $form = $(formSelector || 'form');

    $form.each(function() {
        $(this).parsley().validate();
    });

    if ($form.find('.parsley-error').length) {
        showFirstError();
        return false;
    }

    return true;
}

/**
 * Render html template by replacing variables
 * default variable format: {[ name ]}
 *
 * @param {string} template - html template
 * @param {object} data - literal object with named fields that will be searched in template
 * @param {object} params - parameters for rendering method
 * @returns {string}
 */
function renderTemplate(template, data, params) {
    var prefix = '{[ ';
    var postfix = ' ]}';

    if (typeof params !== 'undefined') {
        if (params.prefix) {
            prefix = params['prefix'];
        }

        if (params.postfix) {
            postfix = params['postfix'];
        }
    }
    Object.keys(data).forEach(function(key){
        var variable = prefix + key + postfix;
        variable = variable.replace(/([.*+?^=!:${}()|\[\]\/\\])/g, "\\$1");
        template = template.replace(new RegExp(variable, 'g'), data[key]);
    });
    return template;
}

/**
* Show modal with content from given url
* @param {string} modalName
* @param {string} url
* @param {array} params
 *      method - method to be executed
 *      dataClass - css class on input or checkbox which tells that value
 *                  from this element should be append as data and send with ajax request
* @param {mixed} data to send
*/
function showModal(modalName, url, params, data)
{
    var method = "GET";

    if (params) {
        if (params.method) {
            method = params['method'];
        }
        if (params.dataClass && !data) {
            var dataClass = params['dataClass'];
            if(!data) {
                data = [];
            }
            $('.' + dataClass).each(function () {
                var val = $(this).val();
                if($(this).attr("type") == 'checkbox') {
                    val = $(this).is(":checked") ? 1 : 0
                }
                data.push({
                    name: $(this).attr("name"),
                    value: val
                });
            });
        }
    }
    $('#spinner-progress').addClass('active');
    $.ajax({
        url: url,
        method: method,
        data: data,
        async: true
    }).done(function(data) {
        $('.lean-overlay').remove();
        $('#spinner-progress').removeClass('active');
        $('#' + modalName).remove();
        if (data.success === 0) {
            $('.modal').closeModal();
            Materialize.toast(data.message, data.timeout);
        } else {
            if (data.result) {
                $('#modalsContainer').html(data.result);
            } else {
                $('#modalsContainer').html(data);
            }
            $('#' + modalName).openModal();
        }
    });
}

/**
 * serialize forms with multiselect support
 * returns params for post request
 *
 * @param {string} url
 * @param {string} formName
 */
function serializeForm(formName) {
    //fixes problems with multiselect
    var formParameters = {};
    $($("form[name='" + formName +"']").serializeArray()).each(function(){
        if (formParameters[$(this)[0].name]) {
            formParameters[$(this)[0].name] = formParameters[$(this)[0].name].concat(','+$(this)[0].value);
        } else {
            formParameters[$(this)[0].name] = $(this)[0].value;
        }
    });

    var postParams = '';
    for (var key in formParameters) {
        postParams = postParams + key + '=' + encodeURI(formParameters[key]) + '&';
    }

    return postParams.slice(0, -1);
}

/**
 * Post form data using ajax request and render response
 *
 * @param {string} url
 * @param {string} formName
 * @param {object} options
 */
function postForm(url, formName, options) {
    options = options || {};
    options.modal = null;
    options.static = null;

    var datatable = window.dataTable || null;
    var successCallback = null;

    if (!options.type) {
        options.modal = true;
    }

    if (options.type === 'modal') {
        options.modal = true;
    }

    if (options.type === 'static') {
        options.static = true;
    }
    if (!options.replace) {
        options.replace = '#modalsContainer';
    }

    successCallback = (typeof window[options.success] === 'function') ? options.success : 'successCallback';

    if (typeof window[options.fail] !== 'function') {
        options.fail = null;
    }

    if (typeof window[options.done] !== 'function') {
        options.done = null;
    }
    // if ckeditor exists, save all values
    if(typeof(CKEDITOR) !== 'undefined') {
        for (instance in CKEDITOR.instances) {
            CKEDITOR.instances[instance].updateElement();
        }
    }

    if ((isParsleyValid("form[name='" + formName +"']") === false) || ($("form[name='" + formName +"']").find('input:invalid').length > 0)) {
        $('button[name="_new"]').first().removeClass('disabled');
        $('button[name="_new"]').first().attr('disabled', false);
        return false;
    }

    if ($.fn.tinymce) {
        tinyMCE.triggerSave();
    }

    $('#spinner-progress').addClass('active');
    $('body .content-wrap').addClass('opacity-50');
    $.post(
        url,
        $("form[name='" + formName +"']").serialize()
    ).done(function (data) {
        if (data.success) {
            $('.modal').closeModal();
            $('.lean-overlay').remove();
            Materialize.toast(data.message, data.timeout);
            if (data.mode && datatable) {
                if (data.mode === 'add' ) {
                    datatable.row.add(JSON.parse(data.data)).draw( true );
                } else if (data.mode === 'edit') {
                    datatable.row('.selected').data(JSON.parse(data.data)).draw( true );
                } else if (data.mode === 'copy') {
                    var items = JSON.parse(data.data);
                    items.map(function(item){
                        datatable.row.add(item).draw( true );
                    });
                } else if(data.mode === 'invitations') {
                    loadInvitations();
                } else if(data.mode === 'reload') {
                    dataTable.draw()
                }
            } else {
                if (data.mode === 'keyalias_edit') {
                    //$('#key-alias' + data.plain_sn).html(data.keyalias);
                } else if(data.mode === 'key_remove') {
                    $('#key-alias' + data.plain_sn).parent().parent().remove();
                } else if(data.mode === 'key_create') {
                    $('#div_carwash_keyusage').html(data.result);
                } else if (data.mode === 'key_lock') {
                    $('#key-locking' + data.sn).html(data.icon);
                    $('#key-locking-info' + data.sn).html(data.icon_info);
                } else if(data.mode === 'transaction_create') {
                    $('#div_carwash_bkfpay_user_transactions').html(data.result);
                } else if(data.mode === 'invitations') {
                    loadInvitations();
                } else if (data.mode === 'report-config') {
                    $('#' + data.name + '_settings_summary').html(data.summary);
                }
            }

            if (data.redirectToPath) {
                window.location.replace(data.redirectToPath);
            }

            window[successCallback] && window[successCallback](data);
        } else {
            $('.lean-overlay').remove();
            if (data.result) {
                $(options.replace).html(data.result);
                if (data.mode !== 'userprofile-edit') {
                    $('.modal').openModal();
                }
            }
            Materialize.toast(data.message, data.timeout);
        }
        $('#spinner-progress').removeClass('active');
        $('body .content-wrap').removeClass('opacity-50');
    });
    return false;
}

/**
 * Call remote function to delete an entity with id defined in url
 *
 * @param {string} url
 * @param {html id} elementToRemove - used when element not in dataTable
 */
function deleteEntity(url, elementToRemove) {
    $('#spinner-progress').addClass('active');
    $('body .content-wrap').addClass('opacity-50');
    $.ajax({
        url: url,
        method: "DELETE"
    }).done(function (data) {
        Materialize.toast(data.message, data.timeout);

        // if element not in dataTable
        if (elementToRemove !== '' && elementToRemove !== 'all') {
            $(elementToRemove).remove();
        }

        // If dataTable is present in current view
        // delete selected row. If multiple entities were deleted,
        // delete all rows by using DataTables API.
        if (dataTable) {
            if (data.ids) {
                var indexes = dataTable.rows().eq(0).filter(function(index) {
                    var id = parseInt(dataTable.cell(index, 0).data());
                    return data.ids.indexOf(id) > -1 ? true : false;
                });
                dataTable.rows(indexes).remove().draw();
            } else {
                // if remove all elements of dataTable
                if (elementToRemove === 'all') {
                    dataTable.clear().draw();
                } else {
                    dataTable.row('.selected').remove().draw(false);
                }
            }
        }
        $('.modal').closeModal();
        $('#spinner-progress').removeClass('active');
        $('body .content-wrap').removeClass('opacity-50');
    });
}

/**
 * Change filter from simple to advanced
 */
function switchFilter() {
    $('.dataTables_filter').toggle();
    $('#table-header').toggle();
    $('#table-advanced-filter').toggle();
    $("#table-advanced-filter > td > input").val('');
    fnResetAllFilters();
}

function fnResetAllFilters() {
    var oSettings = dataTable.settings();
    for(iCol = 0; iCol < oSettings[0].aoPreSearchCols.length; iCol++) {
      oSettings[0].aoPreSearchCols[ iCol ].sSearch = '';
    }
    dataTable.draw();
}

/**
 * Restore deleted entity in database
 * @param {string} url - restore action
 * @param {integer} id - entity id in database
 * @param {string} lastStatus - last status to restore
 * @param {string} appendix - element added after restore string in url
 * @param {function} func - callback function
 * @returns {Boolean}
 */
function restoreEnitity(url, id, lastStatus, appendix, func) {
    $('#spinner-progress').addClass('active');
    $('body .content-wrap').addClass('opacity-50');
    $("a.link-restore").parent().css({"display": "none"});
    if ( !appendix ) {
        appendix = '';
    }
    $.ajax({
        url: url + id + '/restore' + appendix,
        data: $("a.link-restore").data(),
        type: 'POST'
    }).done(function(resp) {
        Materialize.toast(resp.message, resp.timeout,'',function(){
            $('#toast-container').remove();
        });

        if (!resp.mode) {
            resp.mode = 'restore';
        }

        // if element not in dataTable
        if (resp.mode === 'restore_row') {
            append(resp);

            return false;
        }

        if (typeof func === 'function') {
            if (func(resp) === false) {
                return;
            }
        }

        if (dataTable) {
            if (resp.data instanceof Object) {
                dataTable.row.add(resp.data).draw( true );
            } else {
                dataTable.row.add(JSON.parse(resp.data)).draw( true );
            }
        }
        $('#spinner-progress').removeClass('active');
        $('body .content-wrap').removeClass('opacity-50');
    });
    return false;
}

/**
 * Generate csv file
 *
 * @param {string} url
 * @param {string} dateFromField
 * @param {string} dateToField
 * @param {integer} carwashId
 * @returns {undefined}
 */
function generateCSV(url, dateFromField, dateToField, carwashId)
{
    var link = url;
    if (dateFromField && dateToField) {
        carwashId = carwashId | 0;
        var dateFrom = $('#' + dateFromField).val();
        var dateTo = $('#' + dateToField).val();
        link += '?&dateFrom=' + dateFrom + '&dateTo=' + dateTo + '&carwashId=' + carwashId;
        window.location.href = link;
    }
}

/**
 * Merge two objects into one by using deep copy method
 * @param {object} first
 * @param {object} second
 * @returns {Object}
 */
function mergeObjects(first, second) {
    new_object = jQuery.extend(true, {}, first);
    return jQuery.extend(true, new_object, second);
}

/**
 * Translate message if Translator exists (simplified method)
 *
 * @param {string} key
 * @returns {string}
 */
function trans(key) {
    if (typeof Translator !== 'undefined') {
        return Translator.trans( 'js.'+key);
    } else {
        return key;
    }
}

/**
 * Change checkbox value
 *
 * @param {object} checkbox
 * @returns {undefined}
 */
function changeCheckedState(elm) {
    'use strict';
    if (elm.checked) {
        elm.value = 1;
    } else {
        elm.value = 0;
    }
}

/**
 * Show/Hide inputs
 *
 * @param {none}
 * @returns {undefined}
 */

function changeLightMode() {
    'use strict';
    var select = $('#g__i2m__set_myjnia_osw_iTryb'),
        field1 = $('#lTFrom'),
        field2 = $('#lTTo'),
        row  = $('#light-timespan'),
        disabled = row.hasClass('disabled');

    var val = +select.val();
    if (val === 3) {
        field1.removeAttr('disabled');
        field2.removeAttr('disabled');
        row.removeClass('disabled');
        disabled = false;
    } else if (val !== 3) {
        field1.attr('disabled', 'disabled');
        field2.attr('disabled', 'disabled');
        row.addClass('disabled');
        disabled = true;
    }
}
function hideProgress(selector) {
    $(selector || '.progress.main-progress').hide();
}

/**
 * Call action from given url
 * @param {string} url
 */
function invokeAction(url, data, method = "GET") {
    showProgress();
    $.ajax({
        url: url,
        method: method,
        data: data
    }).done(function(data){
        hideProgress();
        if (data.message !== undefined) {
            var timeout = 3000;
            if (data.timeout) {
                timeout = data.timeout;
            }
            Materialize.toast(data.message, timeout);
        }
    });
}

/**
 * Change checkbox value
 *
 * @param {object} checkbox
 * @returns {undefined}
 */
function disableSettingsWatchDog(elm) {
    'use strict';
    try {
        if (elm.checked) {
            $('#disable-settings-warning').removeClass('visible');
        } else {
            if (!confirm(trans('settings.reallydisable'))) {
            // canceled, revert state
                elm.checked = true;
                elm.value = 1;
            } else {
                $('#disable-settings-warning').addClass('visible');
            }
        }
    } catch (error) {
    }
}

/**
 * bind cancel and save button for car wash settings
 *
 * @param {undefined}
 * @returns {undefined}
 */
function bindsavebutton() {
    $('#cancelcarwashallsettings').bind('click',function(t) {
        location.href = globalData.start;
    });

    $('#savecarwashallsettings').bind('click',function(t) {

        $('#cancelcarwashallsettings').attr('disabled',true);

            var json = {},
                dFrom,
                dTo;
                $('.inputset').each(function(t)
                {
                    var name = $(this).attr('name').replace('__', '|');
                    var value = $(this).val();
                    if (name == 'g|i2m|lFrom') {
                        dFrom = splitTimeString(value);
                        if (dFrom) {
                            json['g|i2m|set_myjnia_osw_od_iGodz'] = dFrom[0];
                            json['g|i2m|set_myjnia_osw_od_iMin'] = dFrom[1];
                        }
                    } else if (name == 'g|i2m|lTo') {
                        dTo = splitTimeString(value);
                        if (dTo) {
                            json['g|i2m|set_myjnia_osw_do_iGodz'] = dTo[0];
                            json['g|i2m|set_myjnia_osw_do_iMin'] = dTo[1];
                        }
                    } else {
                        json[name] = value;
                    }
                });

        var carwashid = $('#carwash_settings').val();
        var carwashName = $('#setting_carwash_name').val();

        // validate light hours
        if (+json['g|i2m|set_myjnia_osw_iTryb'] === 3) {
            if (json['g|i2m|set_myjnia_osw_od_iGodz'] === undefined ||
                json['g|i2m|set_myjnia_osw_od_iMin'] === undefined ||
                json['g|i2m|set_myjnia_osw_do_iGodz'] === undefined ||
                json['g|i2m|set_myjnia_osw_do_iMin'] === undefined
            ) {
                Materialize.toast(trans('settings.invalid-period'), 4000);
                return false;
            } else if (
                json['g|i2m|set_myjnia_osw_od_iGodz'] === json['g|i2m|set_myjnia_osw_do_iGodz'] &&
                json['g|i2m|set_myjnia_osw_od_iMin'] === json['g|i2m|set_myjnia_osw_do_iMin']
            ) {
                Materialize.toast(trans('settings.invalid-period'), 4000);
                return false;
            }
        }

        if (json['g|i2m|set_myjnia_doz_rProszek'] !== undefined) {
            json['g|i2m|set_myjnia_doz_rProszek'] = (+json['g|i2m|set_myjnia_doz_rProszek']).toFixed(2);
            if (json['g|i2m|set_myjnia_doz_rProszek'] < 0) {
                Materialize.toast(trans('settings.invalid-dosage-value'), 4000);
                return false;
            }
        }

        $('#spinner-progress').addClass('active');
        $('.content-wrap').addClass('opacity-50');
        $.post(globalData.path,{ settings: json, carwash: carwashid, name: carwashName },function(t) {
            $('#spinner-progress').removeClass('active');
            $('.content-wrap').removeClass('opacity-50');
            Materialize.toast(trans('settings.newsettings'), 4000);
        },'json');

        return false;
    });
}

function splitTimeString(timeString) {
    'use strict';
    var ans = null,
    l = 0;

    if (isValidTimeString(timeString)) {
        ans = timeString.split(':', 2);
        l = ans.length;

        if (l === 1) {
            ans[1] = 0;
        }

        if (ans.length > 1) {
            ans[0] = +ans[0];
            ans[1] = +ans[1];

            if (!(ans[0] >= 0 && ans[0] <= 23 && ans[1] >= 0 && ans[1] <= 59)) {
                ans = null;
            }
        }
    }

    return ans;
}

function isValidTimeString(timeString) {
    'use strict';
    var regex = /^\d{1,2}(?:\:\d{1,2})?(?:\:\d{1,2})?$/;
    return (timeString ? regex.test(timeString) : false);
}

function setDate() {
    var firstInput = $('input[name*=startDate]');
    var secondInput = $('input[name*=endDate]');
    if (moment(firstInput.val()) > (moment(secondInput.val()))) {
        firstInput.val(secondInput.val());
        Materialize.toast(trans('date-range-corrected'), 8000, 'red');
    }
}
function dateValidate(e) {
    var name = $(e[0]._o.field).attr('name');
    var isStart = name.indexOf('start') === 0 ? true : false;
    var postName = name.replace(isStart ? 'startDate' : 'endDate', '');
    var startName = 'startDate' + postName;
    var endName = 'endDate' + postName;
    var firstInput = $('input[name='+startName+']');
    var secondInput = $('input[name='+endName+']');
    if (moment(firstInput.val()) > (moment(secondInput.val()))) {
        firstInput.val(secondInput.val());
        Materialize.toast(trans('date-range-corrected'), 8000, 'red');
    }
}

function initExportDropdown()
{

}

function initPlugins(context)
{
    var $context = $(context || document.body);
    if ($.fn.collapsible) {
        $context.find('.collapsible:not(.no-autoinitialize)').collapsible();
    }

    if ($.fn.tooltipster) {
        $context.find('.tooltip:not(.no-autoinitialize)').tooltipster();
    }

    if ($.fn.select2) {
        $context.find('.select2:not(.no-autoinitialize)').select2();
    }

    if ($.fn.pikaday) {
        $context.find('.pikaday:not(.no-autoinitialize)').pikaday({
            maxDate: moment().toDate(),
            onSelect: function() {
                dateValidate($(this));
            }
        });
    }

    if ($.fn.pickadate) {
        $('.datepicker:not(.no-autoinitialize)', $context).pickadate();
    }

    if ($.fn.clockpicker) {
        $context.find('.clockpicker:not(.no-autoinitialize)').clockpicker();
    }

    if ($.fn.tabs) {
        $context.find('ul.tabs:not(.no-autoinitialize)').tabs();
    }

    if ($.fn.tinymce) {
        $context.find('.tinymce:not(.no-autoinitialize)').tinymce();
    }
}

var didScroll;
var lastScrollTop = 0;
var delta = 5;
var navbarHeight = $('.nav-wrapper').outerHeight();

$(window).scroll(function(event){
    didScroll = true;
});

setInterval(function() {
    if (didScroll) {
        hasScrolled();
        didScroll = false;
    }
}, 250);

function hasScrolled() {
    var st = $(this).scrollTop();

    if(Math.abs(lastScrollTop - st) <= delta)
        return;

    if (st > lastScrollTop && st > navbarHeight && !$('.nano').hasClass('has-scrollbar')){
        // Scroll Down
        $('.nav-wrapper').removeClass('nav-down').addClass('nav-up');
        $('#menu').removeClass('nav-down').addClass('nav-up');
        $('nav').addClass('nav-up');
    } else {
        // Scroll Up
        if(st + $(window).height() < $(document).height()) {
            $('.nav-wrapper').removeClass('nav-up').addClass('nav-down');
            $('#menu').removeClass('nav-up').addClass('nav-down');
            $('nav').removeClass('nav-up');
        }
    }

    lastScrollTop = st;
}

function showFirstError() {
    // open tab when validation error occured
    var errors = $('.parsley-errors-list li');
    if (errors.length > 0) {
        var tab = $('.parsley-errors-list li').closest('li.tab');
        if (!tab.hasClass('active')) {
            tab.find('.collapsible-header').click();
        }
    }
}

/**
 * Mark AppNotifications as read for logged user
 *
 * @param url
 */
function markNotificationAsRead(url) {
    if ($("#news-counter").html() > 0) {
        $.ajax({
            url: url,
            type: 'POST',
            data: {type: 'news'}
        }).done(function (data) {
            if (data.success) {
                $("#news-counter").hide();
            }
        });
    }
}

/**
 * Get keyusage reload list after choosing reload list tab
 *
 * @param {string} url - link to controller action
 * @param {integer} serial - current carwash serial number
 * @param owner
 * @param {string} dataHandler - current div identy to reload ajax data
 * @param dateFrom
 * @param dateTo
 * @returns {undefined}
 */
function getKeyTransactionList(url, clientId, dataHandler, dateFrom, dateTo, transactionType, carwash) {
    var handler = $('#' + dataHandler);
    $('#spinner-progress').addClass('active');
    $('body .content-wrap').addClass('opacity-50');

    var link = url + '?&clientId=' + clientId + '&dateFrom=' + dateFrom + '&dateTo=' + dateTo + '&transactionType=' + transactionType + '&carwash=' + carwash;

    if (xhr != null) {
        xhr.abort();
        xhr = null;
    }

    xhr = $.ajax({
        url: link,
        method: 'get'
    }).done(function (data) {
        if (data.error) {
            handler.html(data.message);
        } else {
            handler.html(data.result);
        }
        $('#spinner-progress').removeClass('active');
        $('body .content-wrap').removeClass('opacity-50');
    });
}

/**
 * Make radio switchable
 *
 * @param {string} id - radio button id
 * @returns {undefined}
 */
function switchableData(myid)
{
    $('#' + myid).on('click', function()
    {
        $('.datarefresh').each(function(t)
        {
            var field = 'data-' + myid;
            var data = $(this).attr(field);
            $(this).html(data);
        })
    })
}

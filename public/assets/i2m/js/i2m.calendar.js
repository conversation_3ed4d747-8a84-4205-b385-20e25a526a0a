// Initialize Namespaces
var I2M = I2M || {};

// Calendar module
I2M.Calendar = I2M.Calendar || function(params) {
    // Mappings
    var Routing = window.Routing;
    var moment = window.moment;
    var $ = window.jQuery;

    // Calendar data
    var calendar = {
        object : $('#calendar'),
        params : null,
        hoverTrashcan : false,
        hoverEventsList : false,
        filters: null,
    };

    // UI elements
    var ui = {
        externalEvents : $('#externalItems'),
        rightPanel : null,
        trashcan : null,
        trashcanSelector : '#calendarTrashcan',
        eventsList : null,
        eventsListSelector : '#items',
        eventSelector : '.fc-event'
    };

    // Templates
    var templates =  {
        trashcan: $('#template-trashcan').html(),
        copyIcon: $('#template-copy').html(),
        eventTooltipContent: $('#template-event-tooltip').html(),
        shiftTooltipContent: $('#template-shift-tooltip').html()
    };

    // Params for external draggable events
    var draggableParams = {
        zIndex: 999,
        revert: true,
        revertDuration: 100
    };

    // Default Fullcalendar params
    var defaultParams = {
        height : 700,
        lang : 'en',
        firstDay : 1,
        editable : true,
        droppable : true,

        header : {
            left : 'prev,next,today',
            right : 'month,agendaWeek,agendaDay',
            center : 'title'
        },

        eventConstraint: {
            start: moment().toISOString(),
            end: moment().add(1, 'y').toISOString()
        },

        dragRevertDuration: 0,
        forceEventDuration: false,
        defaultTimedEventDuration: '08:00:00',
        allDaySlot: false,

        drop: drop,
        eventResize: handleEvent,
        eventDrop: eventDrop,
        eventReceive: eventReceive,
        eventRender: eventRender,
        eventDragStop: eventDragStop,
        events: null,

        // Custom parameters

        // Set to true if trashcan must be enabled
        allowRemove : false,

        // URI to remove event on backend (revert to externalItems container)
        eventRemove : null,

        // Set to true if copy icon must be enabled
        allowCopy : false,

        // URI to copy event on backend
        eventCopy : null
    };

    init();

    //Initialize module
    function init() {
        calendar.params = mergeObjects(defaultParams, params);
        calendar.object.fullCalendar(calendar.params);
        ui.rightPanel = calendar.object.find('.fc-right');

        setEventsDraggable();
        setEvents();

        calendar.params.allowRemove && setTrashcan();
        setEventsList();
    }

    // Set default events
    function setEvents() {
        if ( calendar.params.allowCopy ) {
            calendar.object.on('mouseover', 'a', function() {
                var eventId = $(this).attr('id');
                $(this + ' i[id=' + eventId + ']').css('display','block');
            });

            calendar.object.on('mouseout', 'a', function() {
                var eventId = $(this).attr('id');
                $(this + ' i[id=' + eventId + ']').css('display','none');
            });
        }
    }

    // Drop item from calendar
    function drop() {
        $(this).remove();
    }

    function eventReceive(event) {
        handleEvent(event);
    }

    function eventDrop(event) {
        handleEvent(event);
    }

    // Default event handler
    function handleEvent(event, revertFunc) {
        var eventData = {};

        eventData['id'] = event.id;
        eventData['title'] = event.title;
        eventData['start'] = event.start.format('YYYY-MM-DD HH:mm:ss');

        if (event.end !== null) {
            eventData['end'] = event.end.format('YYYY-MM-DD HH:mm:ss');
        }

        $.ajax({
            url: calendar.params['dropUrl'],
            data: eventData,
            type: 'POST'
        }).done(function(data) {

            if ( data.status === "failure" ) {
                Materialize.toast(data.message, 3000);

                if (typeof revertFunc === 'function') {
                    revertFunc();
                } else {
                    calendar.object.fullCalendar('removeEvents', event.id);
                    $.ajax({
                        url: calendar.params.eventRemove,
                        data: {'id': event.id},
                        type: 'POST',
                    }).done(function(resp) {
                        ui.externalEvents.append(resp.response);
                        setEventsDraggable();
                    });
                }

            }

        });
    }

    // eventDragStop handler
    function eventDragStop(event) {
        // drop on trashcan
        if (event.start > Date.now() && calendar.hoverTrashcan) {
            calendar.object.fullCalendar('removeEvents', event.id);
            $.ajax({
                url: calendar.params.eventRemove,
                data: { 'id': event.id },
                type: 'POST'
            }).done(function(resp) {
                ui.externalEvents.append(resp.response);
                setEventsDraggable();
                Materialize.toast(resp.message, resp.timeout);
            });
        }

        // drop on eventsList
        if (event.start > Date.now() && calendar.hoverEventsList) {
            calendar.object.fullCalendar('removeEvents', event.id);
            $.ajax({
                url: calendar.params['eventClear'],
                data: { 'id': event.id },
                type: 'POST'
            }).done(function(result) {
                ui.externalEvents.empty();
                ui.externalEvents.append(result['itemsList']);
                setEventsDraggable();
                ui.eventsList.find('#externalItems').css('opacity', '1');
            });

            params.removeItem();
        }
    }

    // eventRender handler
    function eventRender(event, element) {
        // hide events that are not in filter table
        if (Array.isArray(calendar.filters)) {
            if (calendar.filters.indexOf(event.id) == -1) {
                element.hide();
            }
        }

        var ntoday = new Date().getTime();
        var start = moment(event.start);
        var end = moment(event.end);

        if (end && end < ntoday) {
            element.removeClass("pink");
            element.children().removeClass("pink");
            element.addClass("past-event");
            element.children().addClass("past-event");
        }

        // render date of the event
        element.find('span.fc-time').remove();
        element.addClass('pt-2 pb-2 pl-10 pr-10 tooltip');
        var formatDate = start.format('YYYY MM DD') === end.format('YYYY MM DD') ? '' : ' (YYYY-MM-DD)';
        var formatTime = start.seconds() === end.seconds() ? 'H:mm' : 'H:mm:ss';

        element.find('span.fc-title').after( '<p></p>' );
        element.find('p').addClass('m-0').text(
            start.format(formatTime ) + ' - ' + end.format(formatTime + formatDate)
        );

        if ( calendar.params.allowCopy ) {
            var renderedIcon = renderTemplate(templates.copyIcon, {
                eventId : event.id,
                backgroundColor : event.backgroundColor,
                copyPath : calendar.params.eventCopy + event.id + '/copy'
            });

            element.append(renderedIcon);
        }

        var eventTooltipData = {
            eventId: event.id,
            eventShow: Routing.generate('events_show',{id: event.id}),
            eventEntityName: calendar.params.eventEntityName,
            eventTitle: event.title,
            startTime: start.format(formatTime),
            endTime: end.format(formatTime + formatDate),
            homepage: Routing.generate('homepage'),
            eventDone: calendar.params.eventDone
        };

        element.attr('id', event.id).tooltipster({
            content: renderTemplate(eventTooltipData.eventDone ? templates.eventTooltipContent : templates.shiftTooltipContent, eventTooltipData),
            interactive: true,
            contentAsHTML: true,
            hideOnClick: true,
            touchDevices: false
        });
    }

    // Set external events draggable to calendar
    function setEventsDraggable() {
        ui.externalEvents.find(ui.eventSelector).each(function(){
            $(this).draggable(draggableParams);
        });
    }

    // Get trashcan position
    function getTrashcanPosition() {
        var offset = ui.trashcan.offset();

        return {
            x1 : offset.left,
            x2 : offset.left + ui.trashcan.outerWidth(true),
            y1 : offset.top,
            y2 : offset.top + ui.trashcan.outerHeight(true)
        };
    }

    // Set trashcan for calendar
    function setTrashcan() {
        ui.rightPanel.append(templates.trashcan);
        ui.trashcan = $(ui.trashcanSelector);

        calendar.object.on('mousemove', function(event) {
            var pos = getTrashcanPosition();

            calendar.hoverTrashcan = !(pos.x1 > event.pageX || event.pageX > pos.x2);

            if (pos.y1 > event.pageY || event.pageY > pos.y2) {
                calendar.hoverTrashcan = false;
            }

            if ( calendar.hoverTrashcan ) {
                ui.trashcan.find('i').removeClass('fa-trash');
                ui.trashcan.find('i').addClass('fa-trash-o');
            } else {
                ui.trashcan.find('i').removeClass('fa-trash-o');
                ui.trashcan.find('i').addClass('fa-trash');
            }
        });
    }

    // Get list of events position
    function getEventsListPosition() {
        var offset = ui.eventsList.offset();
        if (offset) {
            return {
                x1 : offset.left,
                x2 : offset.left + ui.eventsList.outerWidth(true),
                y1 : offset.top,
                y2 : offset.top + ui.eventsList.outerHeight(true)
            };
        }
    }

    // Set list of events
    function setEventsList() {
        ui.eventsList = $(ui.eventsListSelector);

        calendar.object.on('mousemove',function(event){
            var pos = getEventsListPosition();

            calendar.hoverEventsList = true;

            if (pos) {
                if (pos.x1 > event.pageX || event.pageX > pos.x2) {
                    calendar.hoverEventsList = false;
                }

                if (pos.y1 > event.pageY || event.pageY > pos.y2) {
                    calendar.hoverEventsList = false;
                }
            }
            if ( calendar.hoverEventsList ) {
                ui.eventsList.find('#externalItems').css('opacity', '0.7');
            } else {
                ui.eventsList.find('#externalItems').css('opacity', '1');
            }
        });
    }

    // Reload all events
    function reloadEvents() {
        calendar.object.fullCalendar('removeEvents');
        calendar.object.fullCalendar('refetchEvents');
    }

    function setEventsFilter(events) {
        calendar.filters = events;
    }

    // Public interface
    return {
        reloadEvents : reloadEvents,
        setEventsDraggable: setEventsDraggable,
        setEventsFilter: setEventsFilter
    };
};

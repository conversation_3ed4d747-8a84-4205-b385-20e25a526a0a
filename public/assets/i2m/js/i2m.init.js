
var dataTable;

(function() {
    if (window.conApp) {
        var defaults = {
            select2: {
                width: '100%'
            },
            pickadate: {
                firstDay: 1,
                selectMonths: true,
                selectYears: 10,
                formatSubmit: 'yyyy-mm-dd',
                hiddenSuffix: 'Date',
                max: true,
                onRender: function ( arg ) {
                    const node = $(this.$node);
                    if (node.hasClass('no-clear')){
                        node.next('.picker')
                            .find('button.picker__clear')
                            .remove();
                    }
                },
                onSet: function ( arg ) {
                    var currentName = this.get('id').replace('Date', '');
                    var isStart = (currentName.indexOf('start') !== -1);
                    var isEnd = (currentName.indexOf('end') !== -1);
                    var startDateName = isStart ? currentName : currentName.replace('end', 'start');
                    var endDateName = isEnd ? currentName : currentName.replace('start', 'end');
                    var startDate = $("input[name='" + startDateName + "'].datepicker").pickadate('picker');
                    var endDate = $("input[name='" + endDateName + "'].datepicker").pickadate('picker');

                    if ('select' in arg) { //prevent closing on selecting month/year
                        if (isStart) {
                            endDate.set('min', startDate.get('select'));
                        }
                        if (isEnd) {
                            startDate.set('max', endDate.get('select'));
                        }
                        this.close();
                    }
                    else if ( 'clear' in arg ) {
                        if (isStart) {
                            endDate.set('min', false);
                        }
                        if (isEnd) {
                            startDate.set('max', false);
                        }
                    }
                },
                onClose: function(){
                    $('.datepicker').blur();
                    $('.picker').blur();
                },
                trans: {
                    labelMonthNext: 'nextmonth',
                    labelMonthPrev: 'prevmonth',

                    // Months and weekdays
                    monthsFull: [
                        'january', 'february', 'march', 'april', 'may', 'june', 'july',
                        'august', 'september', 'october', 'november', 'december'
                    ],
                    monthsShort: [
                        'january', 'february', 'march', 'april', 'may', 'june', 'july',
                        'august', 'september', 'october', 'november', 'december'
                    ],
                    weekdaysFull: [
                        'sunday', 'monday', 'tuesday', 'wednesday', 'thursday',
                        'friday', 'saturday'
                    ],
                    weekdaysShort: ['sun', 'mon', 'tue', 'wed', 'thu', 'fri', 'sat'],

                    weekdaysLetter: ['sun', 'mon', 'tue', 'wed', 'thu', 'fri', 'sat'],

                    // Today and clear
                    today: 'today',
                    clear: 'clear',
                    close: 'close'
                }
            },
            clockpicker: {
                align: 'left',
                placement: 'middle',
                donetext: 'Ok'
            },
            tooltipster: {
                arrow: true,
                delay : 600,
                speed : 300,
                position : 'top',
                animation : 'grow',
                multiple : 'true'
            },
            collapsible: {
                accordion : false
            },
            pikaday: {
                firstDay: 1,
                format: 'YYYY-MM-DD',
                minDate: new Date('2000-01-01'),
                maxDate: new Date('2040-12-31'),
                yearRange: [2000, 2040],
                i18n: {
                    previousMonth : 'prevmonth',
                    nextMonth: 'nextmonth',
                    months: [
                        'january', 'february', 'march', 'april', 'may', 'june', 'july',
                        'august', 'september', 'october', 'november', 'december'
                    ],
                    weekdays: [
                        'sunday', 'monday', 'tuesday', 'wednesday', 'thursday',
                        'friday', 'saturday'
                    ],
                    weekdaysShort : ['sun', 'mon', 'tue', 'wed', 'thu', 'fri', 'sat']
                }
            },
            tinymce: {
               skin: 'lightgray',
               height: 400,
               toolbar: 'undo redo styleselect bold italic alignleft'
                   + 'alignright bullist numlist outdent indent code pagebreak'
                   + 'preview',
               plugins: 'code pagebreak preview',
               browser_spellcheck: true
            },
            spectrum: {
                howPalette: true,
                chooseText: trans('choose'),
                cancelText: trans('cancel'),
                preferredFormat: 'rgb',
                palette: [
                    ['#f44336', '#e91e63'],
                    ['#9c27b0', '#673ab7'],
                    ['#3f51b5', '#2196f3'],
                    ['#03a9f4', '#009688'],
                    ['#4caf50', '#8bc34a'],
                    ['#cddc39', '#ffeb3b'],
                    ['#ffc107', '#ff9800'],
                    ['#ff5722', '#795548'],
                    ['#9e9e9e', '#607d8b'],
                ]
            }
        };
        // Translate pikadate date definitions
        var pickadateTrans = defaults.pickadate.trans;
        Object.keys(pickadateTrans).forEach(function(key) {
            if (pickadateTrans[key] instanceof Array) {
                for (var subkey in pickadateTrans[key]) {
                    if (typeof (defaults.pickadate[key]) === 'undefined') {
                        defaults.pickadate[key] = [];
                    }
                    defaults.pickadate[key][subkey] = trans(pickadateTrans[key][subkey]);
                }
            } else {
                defaults.pickadate[key] = trans(pickadateTrans[key]);
            }
        });

        // Translate pikaday date definitions
        var i18n = defaults.pikaday.i18n;

        Object.keys(i18n).forEach(function(key) {
            if (i18n[key] instanceof Array) {
                for (var subkey in i18n[key]) {
                    if (i18n[key].hasOwnProperty(subkey)) {
                        i18n[key][subkey] = trans(i18n[key][subkey]);
                    }
                }
            } else {
                i18n[key] = trans(i18n[key]);
            }
        });

        // Hide original _con plugin initializer
        window.conApp._initPlugins = window.conApp.initPlugins;

        // Replace _con plugin initializer with custom initializer
        window.conApp.initPlugins = function() {
            // hide some plugins from _con
            if ($.fn.select2) {
                $.fn._select2 = $.fn.select2;
                $.fn.select2 = undefined;
            }

            if ($.fn.pikaday) {
                $.fn._pikaday = $.fn.pikaday;
                $.fn.pikaday = undefined;
            }

            if ($.fn.pickadate) {
                $.fn._pickadate = $.fn.pickadate;
                $.fn.pickadate = undefined;
            }

            if ($.fn.clockpicker) {
                $.fn._clockpicker = $.fn.clockpicker;
                $.fn.clockpicker = undefined;
            }

            if ($.fn.collapsible) {
                $.fn._collapsible = $.fn.collapsible;
                $.fn.collapsible = undefined;
            }

            if ($.fn.tooltipster) {
                $.fn._tooltipster = $.fn.tooltipster;
                $.fn.tooltipster = undefined;
            }

            if ($.fn.tinymce) {
               $.fn._tinymce = $.fn.tinymce;
               $.fn.tinymce = undefined;
            }

            if ($.fn.spectrum) {
                $.fn._spectrum = $.fn.spectrum;
                $.fn.spectrum = undefined;
            }

            // Call original _con plugin initializer
            window.conApp._initPlugins();

            // Reveal hidden plugins in overlayed mode
            // and safer definitions thanks to using iterator
            if ($.fn._pickadate) {
                $.fn.pickadate = function(options) {
                    if (options instanceof Object) {
                        options = mergeObjects(defaults.pickadate, options);
                    }

                    return this._pickadate(options || defaults.pickadate);
                };
            }

            if ($.fn._pikaday) {
                $.fn.pikaday = function(options) {
                    if (options instanceof Object) {
                        options = mergeObjects(defaults.pikaday, options);
                    }

                    return this._pikaday(options || defaults.pikaday);
                };
            }

            if ($.fn._clockpicker) {
                $.fn.clockpicker = function(options) {
                    this.change(function() {
                        if ($(this).val()) {
                            $(this).siblings('label').addClass('active');
                        }
                    });

                    if (options instanceof Object) {
                        options = mergeObjects(defaults.clockpicker, options);
                    }

                    return this._clockpicker(options || defaults.clockpicker);
                };
            }

            if ($.fn._tooltipster) {
                $.fn.tooltipster = function(options) {
                    if (options instanceof Object) {
                        options = mergeObjects(defaults.tooltipster, options);
                    }

                    return this._tooltipster(options || defaults.tooltipster);
                };
            }

            if ($.fn._collapsible) {
                $.fn.collapsible = function(options) {
                    if (options instanceof Object) {
                        options = mergeObjects(defaults.collapsible, options);
                    }

                    return this._collapsible(options || defaults.collapsible);
                };
            }

            if ($.fn._select2) {
                $.fn.select2 = function(options) {
                    this.conselect();
                    if (options instanceof Object) {
                        options = mergeObjects(defaults.select2, options);
                    }

                    return this.map(function() {
                        var _options = options || defaults.select2;
                        if ($(this).attr('tags')) {
                          _options.tags = true;
                        }

                        return $(this)._select2(_options);
                    });
                };
            }

            if ($.fn._tinymce) {
               $.fn.tinymce = function(options) {
                   if (options instanceof Object) {
                       options = mergeObjects(defaults.tinymce, options);
                   }
                   return this.each(function(){
                       var id = $(this).attr('id');
                       tinymce.get(id) && tinymce.remove('#' + id);
                       $(this)._tinymce(options || defaults.tinymce);
                   });
               };
            }

            if ($.fn._spectrum) {
                $.fn.spectrum = function(options) {
                    if (options instanceof Object) {
                        options = mergeObjects(defaults.spectrum, options);
                    }

                    return this._spectrum(options || defaults.spectrum);
                };
            }

            // ConSelect
            // Provides behaviours to selects like Select2
            // that are responsible for interaction with sibling icon and label
            if (!$.fn.conselect) {
                $.fn.conselect = function() {
                    this.each(function() {
                        var $select = $(this);
                        var updateState = function() {
                            var val = $select.val();

                            // activate label when select has selected value or text
                            if (val || (typeof $select[0].options != 'undefined') && $($select[0].options[0]).text()) {
                                $select.siblings('label').addClass('active');

                                return;
                            }

                            // open select when click on label
                            $select.siblings('label').click(function() {
                                $select.select2('open');
                                $(this).addClass('active');
                            });

                            $select.siblings('label').removeClass('active');
                        };

                        // remove value on click on it
                        if (I2M.Globals.mobile) {
                            $('body').click(function (event) {
                                if ($(event.target).hasClass('select2-selection__choice')) {
                                    $(event.target).find('span.select2-selection__choice__remove').click();
                                }
                            });
                        }

                        $select.on('select2:open', function() {

                            // hide search field on mobile devices
                            if (I2M.Globals.mobile) {
                                $select.siblings('.select2-container')
                                    .find('.select2-search, .select2-focusser').remove();
                            }
                            $select.siblings('label,i')
                                .addClass('active color-label');
                        }).on('select2:close', function() {
                            $select.siblings('label,i')
                                .removeClass('active color-label');
                            updateState();

                            // hide search field on mobile devices
                            if (I2M.Globals.mobile) {
                                $select.siblings('.select2-container')
                                    .find('.select2-search, .select2-focusser').remove();
                            }

                            // activate label when select has text
                            if ($($select[0].options[0]).text()) {
                                $select.siblings('label')
                                    .addClass('active');
                            }
                        }).on('change', function() {
                            updateState();
                        });

                        updateState();
                    });
                    return this;
                };
            }
        };
    }
})();

/**
 * action executed on newly loaded index file
 */
$(document).ready(function () {
    // Make Datatable rows selectable
    $('table tbody').on( 'click', 'tr', function () {
        $('tr.selected').removeClass('selected');
        $(this).addClass('selected');
    });

    $('li.active').parent().parent().addClass('open');

    if ($('.dataTable').length > 0) {
        dataTable = $('.dataTable').DataTable();
        // Prevent calling ajax multiple times
        $('.dataTable').on('preXhr.dt', function (e, settings) {
            if (settings.jqXHR) {
                settings.jqXHR.abort();
            }
        });
    }
    //TODO dodać sprawdzenie czy jest potrzeba inicjalizacji
    initExportDropdown();

    $('.card-panel').show();

    $('.column-filter').on('change keyup', function () {
        dataTable.column( $(this).attr('data-colindex') ).search( this.value ).draw();
    });

    // helpbar
    var helpBar = $('.help-bar:eq(0)');

    if (helpBar.length > 0) {
        helpBar.MDLayer({
            duration: 500,
            fixScrollbar: true,
            onshow: function() {
                var par = JSON.parse($('#helperRoute').find('script').eq(0).html());
                $('#helperwindow').load(par.route+'?'+toHex(window.location.pathname));
            }
        });

        $(document).on('click', '.help-bar-toggle', function(e) {
            e.preventDefault();
            e.stopPropagation();
            helpBar.MDLayer();
        });

        // close search bar on ESC press
        $(document).on('keyup', function(e) {
            if (e.keyCode === 27) {
                helpBar.MDLayer('hide');
            }
        });
    }

    // Init plugins
    initPlugins();

    // Add to all date inputs always active labels
    $('input[type="date"]').siblings('label').addClass('active');

    // Make icons that are siblings to input active only on focus
    $(document).on("focus", 'input', function() {
        $(this).siblings('i').addClass('active');
    }).on("blur", 'input', function() {
        $(this).siblings('i').removeClass('active');
    });

    // KnpMenu Bundle - Third level menu fix
    var element = $('body .nano-content').find('.current_ancestor');
    element.each(function(){
        $(this).removeClass('active')
            .removeClass('open')
            .addClass('active')
            .addClass('open');
    });

    $('body .nano-content').find('.current')
        .removeClass('active')
        .addClass('active');

    $('body .progress-content').find('.preloader-wrapper').removeClass('active');
    $('body .card-panel').removeClass('opacity-75');

    // // Hide progress in mobile devices
    // if (I2M.Globals.mobile || I2M.Globals.tablet) {
    //     $(window).load(function() {
    //         $('#status').fadeOut();
    //         $('#preloader').delay(350).fadeOut('slow');
    //         $('body').delay(350).css({'overflow':'visible'});
    //     });
    //
    //     $('body').addClass('yay-hide');
    // }

    // Save sidebar hidden state
    $('.yay-toggle').on('click', function() {
        window.sessionStorage.setItem('menu-hide', $('body').hasClass('yay-hide'));
    });

    // Ajax prefilter
    $.ajaxPrefilter(
        function (options, localOptions, jqXHR) {
            jqXHR.done(function (data, textStatus, jqXHR) {
                hideProgress();
                if (jqXHR.getResponseHeader('X-Login-Form') == 'true') {
                    location.reload();
                    jqXHR.reject();
                }

                if (jqXHR.getResponseHeader('X-Access-Denied') == 'true') {
                    Materialize.toast(trans('access-denied-to-requested-action'), 4000);
                    jqXHR.reject();
                }
            });
            jqXHR.fail(function (jqXHR) {
                if (jqXHR.getResponseHeader('X-Access-Denied') == 'true') {
                    Materialize.toast(trans('access-denied-to-requested-action'), 4000);
                    jqXHR.reject();
                }
            });
        }
    );
    $("table[id$='-table']").on('order.dt', function () {
            $('.sorting').children().children().removeClass('ion-android-arrow-up').removeClass('ion-android-arrow-down');
            $('.sorting_asc').children().children().removeClass('ion-android-arrow-down').addClass('ion-android-arrow-up');
            $('.sorting_desc').children().children().removeClass('ion-android-arrow-up').addClass('ion-android-arrow-down');
    });
});

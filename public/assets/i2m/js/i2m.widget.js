/**
 * Widget module
 * with tirggered custom events
 */
var I2M = (function(Scope) {
    //Verify scopes
    Scope.Widget = Scope.Widget || constructor;

    // Mappings
    var Card = Scope.UI.Card;
    var Widget = Scope.Widget;
    var Timer = Scope.Timer;

    // Default options
    var options = {
        type: 'generic',
        inputs: 'input,select',
        progress: '.fade-on-progress',
        progressBar: '.progress-bar',
        progressLine: '.progress-line',
        pendingClass: 'opacity-25',
        activeClass: 'opacity-100',
        refresh: '.refresh.button',
        pause: '.pause.button',
        unpause: '.unpause.button'
    };

    // Widget custom events
    var events = {
        closed: 'i2m:widget:closed',
        changed: 'i2m:widget:changed',
        created: 'i2m:widget:created',
        resized: 'i2m:widget:resized',
        animated: 'i2m:widget:animated',
        refreshed: 'i2m:widget:refreshed',
        unpaused: 'i2m:widget:unpaused',
        paused: 'i2m:widget:paused'
    };

    /**
     * Create instance of Widget
     *
     * @constructor
     * @this {Widget}
     * @param {string} widget - widget selector
     */
    function constructor(widget) {
        Card.call(this, widget);

        var _this = this;
        var widget = {$parent: $(widget)};
        widget.type = options.type;
        widget.$refresh = widget.$parent.find(options.refresh);
        widget.$pause = widget.$parent.find(options.pause);
        widget.$unpause = widget.$parent.find(options.unpause);
        widget.$inputs = widget.$parent.find(options.inputs);
        widget.events = events;
        widget.options = options;
        this.widget = widget;

        // NEED REFACTOR - move to helpers
        if (this.messages) {
            Object.keys(this.messages).forEach(function(key) {
                this.messages[key] = trans(this.messages[key]);
            }.bind(this));
        }

        // NEED REFACTOR - move to helpers
        if (this.templates) {
            Object.keys(this.templates).forEach(function(key) {
                this.templates[key] = widget.$parent.find(this.templates[key]).html();
            }.bind(this));
        }

        // Widget (self) events
        $(window).resize(function(event) {
            _this.resize(event);
        });

        widget.$refresh.click(function(event) {
            _this.refresh(event);
        });

        widget.$pause.click(function(event) {
            _this.pause(event);
        });

        widget.$unpause.click(function(event) {
            _this.unpause(event);
        });

        widget.$inputs.change(function(event) {
            setTimeout(function(){
                _this.widget.$parent.trigger(events.changed, {
                    object: _this,
                    input: event.currentTarget
                });
            }, 10);
        });

        // Card (children) events handling
        widget.$parent.on(this.card.events.maximized, function() {
            _this.widget.$parent.trigger(events.resized, _this);
        });

        widget.$parent.on(this.card.events.unmaximized, function() {
            _this.widget.$parent.trigger(events.resized, _this);
        });

        widget.$parent.on(this.card.events.minimized, function() {
            _this.widget.$parent.trigger(events.resized, _this);
        });

        widget.$parent.on(this.card.events.closed, function() {
            _this.widget.$parent.trigger(events.closed, _this);
        });

        widget.$parent.trigger(events.created, this);
    };

    // Inherit methods from Card
    Widget.prototype = new Card();

    /**
     * Get Widget type
     * NEED REFACTOR - vocabulary of widget types
     *
     * @this {Wdiget}
     * @returns {this}
     */
    Widget.prototype.getType = function() {
        return this.widget.type;
    };

    /**
     * Refresh Widget
     * NEED REFACTOR - to event onclick refresh button
     *
     * @this {Widget}
     * @returns {this}
     */
    Widget.prototype.refresh = function() {
        return this.widget.$parent.trigger(events.refreshed, this);
    };

    /**
     * Pause Widget autorefreshing
     *
     * @this {Widget}
     * @returns {this}
     */
    Widget.prototype.pause = function() {
        this._setPause(true);
        return this.widget.$parent.trigger(events.unpaused, this);
    };

    /**
     * Unpause Widget autorefreshing
     *
     * @this {Widget}
     * @returns {this}
     */
    Widget.prototype.unpause = function() {
        this._setPause(false);
        return this.widget.$parent.trigger(events.paused, this);
    };

    /**
     * Resize Widget
     * NEED REFACTOR - move this to Card functionality
     *
     * @this {Widget}
     * @returns {this}
     */
    Widget.prototype.resize = function() {
        return this.widget.$parent.trigger(events.resized, this);
    };

    /**
     * Set Progress state Widget
     *
     * @this {Widget}
     * @param {boolean} condition - is progress active
     * @param {string} animate - optional - duration in seconds to set progess state
     * @returns {this}
     */
    Widget.prototype.setProgress = function(condition, animate) {
        var widget = this.widget;
        var active = options.activeClass;
        var pending = options.pendingClass;
        var $progress = widget.$parent.find(options.progress);
        var $progressBar = widget.$parent.find(options.progressBar);
        var $progressLine = $progressBar.find(options.progressLine);
        var _this = this;

        if (condition) {
            $progressLine.addClass('indeterminate teal');
            $progressLine.removeClass('animate grey');
            $progress.removeClass(active);
            $progress.addClass(pending);
            $progressBar.css('visibility', 'visible');
        } else {
            $progress.removeClass(pending);
            $progress.addClass(active);
            if (!animate) {
                $progressBar.css('visibility', 'hidden');
                return;
            }

            $progressLine.removeClass('indeterminate')
                .addClass('animate grey')
                .removeClass('teal')
                .css('animation-duration', animate)
                .on('animationend', function() {
                    $(this).removeClass('animate');
                    widget.$parent.trigger(widget.events.animated, _this);
            });
        }

        return this;
    };

    /**
     * Set Pause Widget state
     *
     * @private
     * @this {Widget}
     * @param {boolean} state
     * @returns {this}
     */
    Widget.prototype._setPause = function(state) {
        var widget = this.widget;
        var $progressBar = widget.$parent.find(options.progressBar);
        var $progressLine = $progressBar.find(options.progressLine);
        var cssProperty = 'animation-play-state';

        if (state) {
            $progressLine.css(cssProperty, 'paused');
            widget.$pause.hide();
            widget.$unpause.show();
        } else {
            $progressLine.css(cssProperty, 'running');
            widget.$pause.show();
            widget.$unpause.hide();
        }

        return this;
    };

    return Scope;
})(I2M || {});

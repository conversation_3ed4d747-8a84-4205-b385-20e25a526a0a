/*
 * Flot Charts module
 */
var Charts = (function () {
    var charts = {};
    var resizeTimer = null;
    var tooltipTimer = null;
    var tooltipShow = false;

    // Default options for overriding from template
    var default_options = {
        series: {
            pie: {
                show: false,
                innerRadius: 50
            },
            bars: {
                show: false,
                lineWidth: 1,
                barWidth: 0.9,
                align: "center"
            },
            lines: {
                show: true,
                lineWidth: 1,
                fill: true
            },
            points: {
                show: true,
                lineWidth: 2,
                radius: 3
            },
            shadowSize: 1,
            stack: true
        },
        grid: {
            hoverable: true,
            clickable: true,
            tickColor: "#ddd",
            borderWidth: 0
        },
        legend: {
            show: true,
            backgroundOpacity: 0,
            labelBoxBorderColor: "#fff"
        },
        xaxis: {
            mode: "time",
            minTickSize: [1, "minute"],
            ticks: 24,
            calculateTicks: true,
            font: {
                family: "Roboto,sans-serif",
                color: "#999"
            },
            monthNames: [
                trans('january'), trans('february'), trans('march'), trans('april'), trans('may'), trans('june'),
                trans('july'), trans('august'), trans('september'), trans('october'), trans('november'), trans('december')
            ],
            dayNames: [
                trans('sunday'), trans('monday'), trans('tuesday'), trans('wednesday'), trans('thursday'), trans('friday'), trans('saturday')
            ]
        },
        yaxis: {
            ticks: 7,
            tickDecimals: 0,
            font: {
                color: "#999"
            },

        },
        zoom: {
            interactive: true,
            amount: 1.1
        },
        pan: {
            interactive: true,
            cursor: 'move',
            framerate: 60
        },
        colors: ["#0091ea", "#9575cd", "#1b5e20", "#00bfa5", "#33691e", "#827717"],
        parts: 12,

        // Tooltip module (use it only for pie charts)
        tooltipOpts: {
            content: "%s: %p.0% (%y.3)",
            shifts: {
                x: 20,
                y: 0
            },
            defaultTheme: false
        },
        // Labels module (custom flot plugin)
        labels: {
            fontSize: 12,
            fontFamily: 'Arial'
        },
        tooltip: false,
        // Custom setting:
        // mode: "single" - single data tooltip,
        // mode: "multi" - multiple data tooltip (show all series from same x)
        tooltipMode: "single",
        // Custom setting - default template
        tooltipTemplate: '"{[ label ]}" {[ of ]} {[ x ]}, {[ value ]}: {[ y ]}{[ unit ]}',
        // Custom setting - callback function that will be invoked for generating custom tooltip
        tooltipRenderer: null
    };

    // Debounce resize event
    $(window).on('resize', function(e) {
        clearTimeout(resizeTimer);
        resizeTimer = setTimeout(function() {
            Charts.redraw();
        }, 250);
    });

    // Hide progress bars
    $('.gadget-chart').find('.progress').css('visibility', 'hidden');

    // Initialize flot gadget
    function initFlot(id, data, options) {
        var chart = $('#chart-' + id);

        if ( options.chart_id !== undefined ) {
            chart = $(options.chart_id);
        }

        if (options.xaxis.mode === "time" && options.xaxis.calculateTicks === true) {
            options.xaxis.ticks = Math.floor(chart.width() / 100);

            // Reduce from unnecessary too many intervals
            if (data[0].data.length <= 4) {
                options.xaxis.ticks = data[0].data.length;
            }
        }

        var plot = $.plot(chart, data, options);
        cacheChart(id, data, options, plot);
        chart.find('.legend table').css('width', 'auto').find('td').css('padding', 5);

        if ( options.tooltip !== true ) {
            bind(chart, plot);
        }

        return plot;
    }

    // Bind mouse actions to flot
    function bind(chart, plot) {
        var previousPoint = null;
        var mouseX = 0;
        var mouseY = 0;

        chart.on("mousemove", function(event) {
            mouseX = event.clientX;
            mouseY = event.clientY;
        });

        chart.on("plothover", function (event, pos, item) {
            var $tooltip = $('#charts-tooltip');
            var vector = {
                x: mouseX + window.scrollX + 24,
                y: mouseY + window.scrollY - 8
            };

            if (item) {
                if (previousPoint !== item.dataIndex) {
                    previousPoint = item.dataIndex;

                    // Get numerical id of current chart
                    var name = chart[0].id.split('-')[1];
                    var id = typeof name === 'number' ? Number(name) : name;
                    var dataset = charts[id].plot.getData();
                    var options = charts[id].options;
                    var timestamp = item.datapoint[0];

                    // Bugfix for orderBars plugin
                    if ( item.series.datapoints.root !== undefined ) {
                        timestamp = item.series.datapoints.root[item.dataIndex * item.series.datapoints.pointsize];
                    }

                    var string = [];
                    var template = plot.getOptions().tooltipTemplate;
                    var x = moment.utc(timestamp.toFixed(0),"x").utcOffset(0).format("YYYY-MM-DD HH:mm");

                    if (options.tooltipMode === "single") {
                        dataset = [dataset[item.seriesIndex]];
                    }

                    dataset.forEach(function(data){
                        var pointsize = data.datapoints.pointsize;
                        var y = data.datapoints.points[item.dataIndex * pointsize + 1];
                        var unit = item.series.unit || '';

                        if (unit === 'on/off') {
                            unit = '';
                            if (y > 0) {
                                y = 'on';
                            } else {
                                y = 'off';
                            }
                        }

                        var data = {
                            label : data.label,
                            value : trans('value'),
                            unit: unit,
                            of : trans('of'),
                            x : x,
                            y : y
                        };

                        if (options.tooltipRenderer instanceof Function) {
                            string.push(options.tooltipRenderer(data));
                        } else {
                            string.push(renderTemplate(template, data));
                        }
                    });
                    showTooltip(vector.x, vector.y, string.join('<br/>'));
                }
                clearTimeout(tooltipTimer);
                tooltipShow = true;
                $tooltip.fadeIn(500);

                $tooltip.css({
                    left: vector.x,
                    top: vector.y
                });
            }
            else {
                if (tooltipShow === true) {
                    tooltipShow = false;
                    tooltipTimer = setTimeout(function() {
                        $tooltip.fadeOut(500);
                    }, 500);
                }

                previousPoint = null;
            }
         });
    }

    // Show tooltip
    function showTooltip(x, y, content) {
        var $tooltip = $('#charts-tooltip');
        if ($tooltip.length === 0) {
            $tooltip = $('<div id="charts-tooltip"></div>');
            $tooltip.css({
                top: y,
                left: x,
                position: 'absolute',
                display: 'none',
                'z-index': '3000'
            });
            $tooltip.appendTo("body").fadeIn(500);
            tooltipShow = true;
        }

        $tooltip.css({
            'display': 'block'
        }).html(content);
    }

    // Cache chart
    function cacheChart(id, data, options, plot) {
        charts[id] = {
            id: id,
            data: data,
            options: options,
            plot: plot
        };
    }

    return {
        draw: function(id, data, options) {
            if ( options === undefined ) {
                options = {};
            }
            var _options = {};

            _options = mergeObjects(default_options, options);

            // bugfix for jQuery not performing deep copy properly
            if ( options.xaxis && options.xaxis.ticks ) {
                _options.xaxis.ticks = options.xaxis.ticks;
            }

            return initFlot(id, data.flot, _options);
        },
        redraw: function() {
            for ( var key in charts ) {
                var chart = charts[key];
                initFlot(chart.id, chart.data, chart.options);
            }
        },
        create: function(id, data) {

        },
        update: function(id, url, options) {
            // Get data from inputs
            var data = getChildData('#'+id);

            // Default value of options
            if ( options === undefined ) {
                options = {};
            }

            data['options'] = options;

            // Show progress bar
            chart.find('.progress').css('visibility', 'visible');

            $.ajax({
                dataType: "json",
                url: url,
                data: data,
                method: 'POST',
                error: function(){
                    // Hide progress bar and show error
                    chart.find('.progress').css('visibility', 'hidden');
                    Materialize.toast( trans('ajax.wrong-response'), 3000);
                }
            }).done(function(data){
                chart.find('.progress').css('visibility', 'hidden');
                Charts.draw(id, data, options);
            });
        },
        colorElements: function(selector, colors, foreground) {
            var idx = 0;
            var type = foreground === true ? 'color' : 'background-color';
            var _colors = colors === undefined || colors === null ? default_options.colors : colors;
            $(selector).each(function(){
                $(this).css(type, _colors[idx++ % _colors.length]);
            });
        },
        getOptions: function() {
            return default_options;
        }
    };
})();

/**
 * Update chart with remote data
 * @param {string} id
 * @param {string} url
 * @param {object} options
 */
function updateChart(id, url, options) {
    Charts.update(id, url, options);
}

/**
 * Update all charts
 */
function updateCharts() {
    Charts.redraw();
}

/**
 * Create chart
 */
function createChart(id, data) {
    Charts.create(id, data);
}

/**
 * Draw static chart
 * @param {type} id
 * @param {type} data
 * @param {type} options
 */
function drawChart(id, data, options) {
    return Charts.draw(id, data, options);
}

/**
 * Color elements given by selector and optional array of colors
 * @param {string} selector - css selector
 * @param {array} colors (optional)
 * @param {boolean} foreground - true if color text else background
 */
function colorElements(selector, colors, foreground) {
    Charts.colorElements(selector, colors, foreground);
}

/**
 * Get Chart default options
 * @returns {object}
 */
function getChartDefaultOptions() {
    return mergeObjects({}, Charts.getOptions());
}

/**
 * Convert entities to flot format and populate array with missing zeros
 * @param {type} entities
 * @returns {object}
 */
function convertToFlot(entities) {
    var datetimes = [];
    var labels = [];
    var values = [];

    // extract datetimes, labels and values from entities
    for ( date in entities) {
        if ( datetimes.indexOf(date) === -1 ) {
            datetimes.push(date);
        }

        for ( label in entities[date] ) {
            if ( labels.indexOf(label) === -1 ) {
                labels.push(label);
            }
            values.push([label, date, entities[date][label]]);
        }
    }

    // prepare data in indirect format
    var preflot = {};
    for ( label in labels ) {
        if ( preflot[labels[label]] === undefined ) {
            var _datas = {};
            for ( date in datetimes ) {
                _datas[datetimes[date]] = 0;
            }
            preflot[labels[label]] = { 'label' : labels[label], data : _datas };
        }
    }

    for ( var value in values ) {
        preflot[values[value][0]]['data'][values[value][1]] = values[value][2];
    }

    // prepare flot format
    var flot = [];
    for ( var label in preflot ) {
        var series = { 'label' : label, data : [] };
        for ( var date in preflot[label]['data'] ) {
            series.data.push( [ moment.utc(date).utcOffset(0).format("x"), parseFloat(preflot[label]['data'][date]) ] );
        }
        flot.push(series);
    }

    var data = {};
    data.flot = flot;

    // get startdate and enddate
    data.start = moment.utc(datetimes[0]).utcOffset(0).format("x");
    data.end = moment.utc(datetimes[0]).utcOffset(0).format("x");
    data.startDateTime = datetimes[0];
    data.endDateTime = datetimes[0];

    for ( date in datetimes ) {
        var epoch = moment.utc(datetimes[date]).utcOffset(0).format("x");

        if ( epoch < data.start ) {
            data.start = epoch;
            data.startDateTime = datetimes[date];
        }

        if ( epoch > data.end ) {
            data.end = epoch;
            data.endDateTime = datetimes[date];
        }
    }
    return data;
}

/**
 * Truncate flot data with given only one label
 * @param {type} label
 * @param {type} chartData
 * @returns {undefined}
 */
function truncateFlotData(label, chartData) {
    data = jQuery.extend({}, chartData);

    for ( col in data.flot ) {
        if ( data.flot[col].label == label ) {
            data.flot = [ data.flot[col] ];
            break;
        }
    }
    return data;
}

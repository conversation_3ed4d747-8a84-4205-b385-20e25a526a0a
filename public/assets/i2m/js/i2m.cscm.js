/**
 * bind cancel and save button for car wash settings
 * 
 * @param {undefined}
 * @returns {undefined}
 */
function bindsavebutton() {
    $('#cancelcarwashallsettings').bind('click',function(t) {
        location.href = globalData.start;
    });

    $('#savecarwashallsettings').bind('click',function(t) {

        $('#cancelcarwashallsettings').attr('disabled',true);

            var json = {},
                dFrom,
                dTo;
                $('.inputset').each(function(t)
                {
                    var name = $(this).attr('name').replace('__', '|');
                    var value = $(this).val();
                    if (name == 'g|i2m|lFrom') {
                        dFrom = splitTimeString(value);
                        if (dFrom) {
                            json['g|i2m|set_myjnia_osw_od_iGodz'] = dFrom[0];
                            json['g|i2m|set_myjnia_osw_od_iMin'] = dFrom[1];
                        }
                    } else if (name == 'g|i2m|lTo') {
                        dTo = splitTimeString(value);
                        if (dTo) {
                            json['g|i2m|set_myjnia_osw_do_iGodz'] = dTo[0];
                            json['g|i2m|set_myjnia_osw_do_iMin'] = dTo[1];
                        }
                    } else {
                        json[name] = value;
                    }
                });

        var carwashid = $('#carwash_settings').val();
        var carwashName = $('#setting_carwash_name').val();

        // validate light hours
        if (+json['g|i2m|set_myjnia_osw_iTryb'] === 3) {
            if (json['g|i2m|set_myjnia_osw_od_iGodz'] === undefined ||
                json['g|i2m|set_myjnia_osw_od_iMin'] === undefined ||
                json['g|i2m|set_myjnia_osw_do_iGodz'] === undefined ||
                json['g|i2m|set_myjnia_osw_do_iMin'] === undefined
            ) {
                Materialize.toast(trans('settings.invalid-period'), 4000);
                return false;
            } else if (
                json['g|i2m|set_myjnia_osw_od_iGodz'] === json['g|i2m|set_myjnia_osw_do_iGodz'] &&
                json['g|i2m|set_myjnia_osw_od_iMin'] === json['g|i2m|set_myjnia_osw_do_iMin']
            ) {
                Materialize.toast(trans('settings.invalid-period'), 4000);
                return false;
            }
        }

        if (json['g|i2m|set_myjnia_doz_rProszek'] !== undefined) {
            json['g|i2m|set_myjnia_doz_rProszek'] = (+json['g|i2m|set_myjnia_doz_rProszek']).toFixed(2);
            if (json['g|i2m|set_myjnia_doz_rProszek'] < 0) {
                Materialize.toast(trans('settings.invalid-dosage-value'), 4000);
                return false;
            }
        }

        $('#spinner-progress').addClass('active');
        $('.content-wrap').addClass('opacity-50');
        $.post(globalData.path,{ settings: json, carwash: carwashid, name: carwashName },function(t) {
            $('#spinner-progress').removeClass('active');
            $('.content-wrap').removeClass('opacity-50');
            Materialize.toast(trans('settings.newsettings'), 4000);
        },'json');

        return false;
    });
}

/**
 * Switch dates if wrong period
 *
 */
function switchdates(from1,from2,to1,to2) {
    if (from2.length > 1) {    
        var from = new Date($(from2).val()).getTime();
        var to = new Date($(to2).val()).getTime();
        if (from > to) {
            var from = $(from1).val(); 
            $(from1).val($(to1).val());
            $(to1).val(from);
            from = $(from2).val();
            $(from2).val($(to2).val());
            $(to2).val(from);
            Materialize.toast(trans('carwashmanager.period.replaced'), 5000, 'infored');
        }
    } else {
        var from = new Date($(from1).val()).getTime();
        var to = new Date($(to1).val()).getTime();
        if (from > to) {
            var from = $(from1).val(); 
            $(from1).val($(to1).val());
            $(to1).val(from);
            Materialize.toast(trans('carwashmanager.period.replaced'), 5000, 'infored');
       }
    }
}

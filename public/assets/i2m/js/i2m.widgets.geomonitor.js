var I2M = (function(Scope) {
    // Verify scopes
    Scope.Widgets = Scope.Widgets || {};
    Scope.Widgets.GeoMonitor = Scope.Widgets.GeoMonitor || constructor;

    // Mappings
    var Widget = Scope.Widget;
    var Helpers = Scope.Helpers;
    var Monitor = Scope.Widgets.GeoMonitor;
    var Provider = Scope.Data.Provider;

    var modes = {
        normal: 'normal',
        presentation: 'presentation'
    };

    var signals = {
        gps: 'GPS',
        agps: 'AGPS'
    };

    /**
     * Create Widget GeoMonitor
     * @param {object} state
     */
    function constructor(state) {
        this.uid  = state.uid;

        this._widget = {
            uid : state.uid,
            id : '#widget-' + state.uid,
            object : $('#widget-' + state.uid),
            state : state,
            data : null,
            type : 'geomonitor',
            size: 'unmaximized'
        };

        // Messages (that are autotranslated)
        this.templates = {
            title : '#template_title',
            location : '#template_location',
            titleDate: '#template-title-date'
        };

        // Templates (that are autogenerated)
        this.messages = {
            nodevices : 'widget.there-are-no-devices-selected',
            noparams: 'widget.there-are-no-positional-parameters',
            noinformation : 'widget.no-information',
            wrongResponse : 'ajax.wrong-response'
        };

        this.uris = {
            dataProvider : Routing.generate('monitor_devices_location_ajax'),
            deviceProvider: Routing.generate('monitor_devices_ajax')
        };

        this.markers = [
            "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0"
            + "AAAABmJLR0QA/wD/AP+gvaeTAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAB3RJTUUH"
            + "3wsaCTACWVHu4AAAAXhJREFUWMPt171rFEEYx/HPxpBYaKHFQiCkTKEpjIWSaGOT"
            + "UogJiOLfkAVLA0EbC8FiunQWKVL4dkErq3QHAasYJWWwm8LCIvgCGZuNHnqce3e6"
            + "1T7NDM8Mz+/78MzwzGT6sRDPYhrf8UGRHxrSsm7OpcX55ymZI62+aLWfCPE01nET"
            + "J8ptX/Ea9xX5u0EBRv4UnzuFG1lmAkulex23O8RhvFx/K8S7Qhz9JwCSzkDHgtd7"
            + "xBjDY+wJ8U5PkBCnhLgmxJ+6oz2L8mteJbtpbOCREJ9iB59wBudxDZfLpB/iqGrg"
            + "fm0CK4OXoGZrABqABqABaAD66QW72CzHDBexjJk6AG5hS5GnDt8rPBDiAtZwpWKs"
            + "/eNOWL0ERd76Tbxz7Y0iv1q222fo9kw7xBYWcU6RHw1Sgr9BbmNbiCcxi8ny7fgR"
            + "u4r827BnoCrIF7Sba9gANADDfEy6z+sCSDUjdAP4nFI6gCzzvnaAl612wqWU0oWU"
            + "3PvfAD8A0Bth1ORLqS0AAAAASUVORK5CYII=",
            "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAMAAABEpIrG"
            + "AAAAzFBMVEUAAABRUDmVdc2Vdc2Vdc1SUDmVdc2Vdc2Vdc1TUTmVdc1SUDlTUDlT"
            + "UTmVdc1SUDmVdc2Vdc1SUDmVdc2Vdc2Vdc2Vdc2Vdc2Vdc2Vdc2Vdc2Vdc2Vdc2V"
            + "dc2Vdc2Vdc1SUDmVdc2Vdc2Vdc2Vdc2Vdc1TUTmVdc2Vdc2Vdc2Vdc1TUTqVdc1S"
            + "UDlSUDlSUDlSUTmVdc2Vdc2Vdc2Vdc2Vdc2Vdc1SUDlSUDqVdc2Vdc2Vdc2Vdc2V"
            + "dc2Vdc2Vdc2Vdc1TUTqVdc3///9EspC1AAAAQXRSTlMAAQECBAYGCAoMDQ8QEBIW"
            + "HiwvMzg5QEZJTE1QVVpbXGFyeX5/hYyRpausrbC0tre3xMbS09jZ4Obm8/X3+Pn8"
            + "/UeLVlEAAACcSURBVHgB7dPFjsIAFEbhf6adwQWKFxco7gJFuPf9HwpompsmlS0J"
            + "4Vuf7YFDMqeF4W18aCJq3Jgvoww8RIimMNhybalwiRPNcWLbrmEn6Y5iBwmiBc4s"
            + "joN6qdZb3Vn1CcQ3eFOwbpcr3Y1/oP/ipbhksVWcgSgMTX4yJ7oCd2AJ5au69g8h"
            + "gdvHBDJOkBjRDEF+9tRHoFT2Dz4emkNo/hlEz7QAAAAASUVORK5CYII=",
            "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0"
            + "AAAABmJLR0QA/wD/AP+gvaeTAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAB3RJTUUH"
            + "3wsaChoPSs3gLAAAAYJJREFUWMPt1z1rFEEYAOBnNaiIFtqFEZkqjRZqoUQbbSws"
            + "BI0giswfECwsFYI2FoJ9OmUtLERzflSxSXcgWMUP7FZkWwuL4AdkbDZ66HHe5fSq"
            + "fZsdZob3fYbZZXYKI0RIcTdm8B3v6rJaNWYU/Trnzhx9lLNZ8vXHne7dkOJOLOA8"
            + "NjfTvuI5btRl9XqjgE1/Fp/dgbNFYRpzTfcCLvYUh63N+KuQ4tWQ4tQ/Ach6E60X"
            + "PD0gxxbcwZuQ4qVBkJDi3pDifEjxZ92pgZvyqz3M6mZwH7dDig/xEp+wC/twAkea"
            + "Rd/C2rCJR41pXNn4Fkw4WkALaAEtoAWMchas4EHzLHAI57B/EoALeFKXVe7pe4ab"
            + "IcWTmMexIXO9Xz8JhwbUZdUZMLaEpZDicVzGKWz/bdoqXuAentZltbaRLfgbchnL"
            + "IcVtOIg9zb/jR6zUZfVt3HdgWMgXdNvPsAW0gHEuJv3bkwLkCRP6AT7nnD9AUXg7"
            + "ccBip5txOOd8IGfX/jfgBxjlZzuJM6MvAAAAAElFTkSuQmCC",
            "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0"
            + "AAAABmJLR0QA/wD/AP+gvaeTAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAB3RJTUUH"
            + "3wsaCh8RzbUpCgAAAXRJREFUWMPt179rFEEUAOBvNUSRWMQuECzTaJFYKNHGbSxS"
            + "BDSC6OY/EFJYKgRtLIT06RTCYCGa01jFYtIdCFbxB5Zib2ERNELGZqOHHufendlq"
            + "X7PDzPDeN8wus5PpJ2I4gSn8wAd5sWPIyLp1Llw+/zQls6Q7z1rth2I4jlVcw+Fy"
            + "2ne8xF158XZQwKG/i8+O4UqWmcBC2b2KGx3F4Ug5/kYMt8Qw8l8Aks5E+wXne+QY"
            + "xQreiWGxJySGk2JYFsOvuiM9N+V3u8rqprCGB2J4gtf4gnGcQo5z5aLvY69q4n5j"
            + "AkuDb0HN0QAaQANoAA2gn7NgG4/LZ4YzuIrTdQCu47m8SB19G7gnhktYxoWKuT7u"
            + "n4TVAXnR6jG2iU0xXMRNzOHYH7N28AqP8EJe7A2yBf9CbmFLDEcxg8ny3/EztuXF"
            + "7rDvQFXIN7Sbz7ABNIBhLibd23UBUs2EboCvKaVPkGXe1w5Yb7UTzqaUplNy+6AB"
            + "PwHoB2FnUk6JvAAAAABJRU5ErkJggg==",
            "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0"
            + "AAAABmJLR0QA/wD/AP+gvaeTAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAB3RJTUUH"
            + "3wsaCiMdt/Ac3gAAAX9JREFUWMPt1z1rFEEYAOBnNaiIFtoFZMo0WqgDSrTRxsJC"
            + "0Aii+AcGwcJSIWhjIVhvOgULC1EvahWbdAfCVvEDS9fewiL4ARmbjR56nPehV+3b"
            + "7DAzvO8zzC6zUxghYgp7MYfveFeV9boJo+jXuXD22OOczZNvPOl078UUdmMJF7C1"
            + "mfYVL3CzKuvX4wK2/Fl8fhfOFYVZLDTdS7jUUxy2N+NVTOFaTGHmnwBkvYk2C54Z"
            + "kGMb7uJNTOHyIEhMIcQUFmMKP+vODNyUX+1hVjeHB7gTU3iEV/iEPdiPkzjaLPo2"
            + "NoZNPGrM4ur4WzDlaAEtoAW0gBYwylmwhofNs8BhnMeBaQAuYrkq69zT9xy3Ygqn"
            + "sIjjQ+Z6v3kSDg2oyrozYGwFKzGFE7iC09j527R1vMR9PKvKemOcLfgbchWrMYUd"
            + "OIR9zb/jR6xVZf1t0ndgWMgXdNvPsAW0gEkuJv3b0wLkKRP6AT7nnD9AUXg7dcDT"
            + "TjfjSM75YM6u/2/AD90yZ79ek/e5AAAAAElFTkSuQmCC",
            "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAMAAABEpIrG"
            + "AAAAzFBMVEUAAABRUDmCdxeCdxeCdxdSUDmCdxeCdxeCdxdTUTmCdxdSUDlTUDlT"
            + "UTmCdxdSUDmCdxeCdxdSUDmCdxeCdxeCdxeCdxeCdxeCdxeCdxeCdxeCdxeCdxeC"
            + "dxeCdxeCdxdSUDmCdxeCdxeCdxeCdxeCdxdTUTmCdxeCdxeCdxeCdxdTUTqCdxdS"
            + "UDlSUDlSUDlSUTmCdxeCdxeCdxeCdxeCdxeCdxdSUDlSUDqCdxeCdxeCdxeCdxeC"
            + "dxeCdxeCdxeCdxdTUTqCdxf////zeIxvAAAAQXRSTlMAAQECBAYGCAoMDQ8QEBIW"
            + "HiwvMzg5QEZJTE1QVVpbXGFyeX5/hYyRpausrbC0tre3xMbS09jZ4Obm8/X3+Pn8"
            + "/UeLVlEAAACcSURBVHgB7dPFjsIAFEbhf6adwQWKFxco7gJFuPf9HwpompsmlS0J"
            + "4Vuf7YFDMqeF4W18aCJq3Jgvoww8RIimMNhybalwiRPNcWLbrmEn6Y5iBwmiBc4s"
            + "joN6qdZb3Vn1CcQ3eFOwbpcr3Y1/oP/ipbhksVWcgSgMTX4yJ7oCd2AJ5au69g8h"
            + "gdvHBDJOkBjRDEF+9tRHoFT2Dz4emkNo/hlEz7QAAAAASUVORK5CYII="
        ];

        this.ui = {
            mapSelector : 'map-' + this._widget.uid,
            titleLayer : this.find('div.title > h5'),
            devicesSelect : this.find('select[name="devices"]'),
            devicesButton : this.find('button[name="devices-switcher"]'),
            deviceTypeSelect : this.find('select[name="device-type"]'),
            devicesInstancesLayer : this.find('div[name="devices-instances"]'),
            devicesTypesLayer : this.find('div[name="devices-types"]'),
            devicesLocationsSelect : this.find('select[name="devices-locations"]'),
            alertMessage : this.find('div[name="alert"]'),
            startDateInput : this.find('input[name="start-date"]'),
            endDateInput : this.find('input[name="end-date"]'),
            locations : this.find('ul[name="locations"]'),
            allLocations : this.find('input[name="all-locations"]'),
            showAccuracy : this.find('input[name="show-accuracy"]'),
            progressContentLayer: this.find('div[name="progress-content"]'),
            layers: {
                title: this.find('div.title > h5'),
                mainOptions: this.find('div[name="main-options-layer"]'),
                subOptions: this.find('div[name="sub-options-layer"]'),
                progressBar: this.find('div[name="progress-bar-layer"]')
            },
            presentation: {
                selectors: {
                    date: 'input[name="presentation-date"]',
                    dateControl: 'span[name="title-date-control"]',
                    previousDay: 'button[name="title-previous-day"]',
                    nextDay: 'button[name="title-next-day"]'
                }
            }
        };

        this.request = {
            data : null,
            date : {
                start : moment().utc().startOf('day').subtract(0, 'day'),
                end : moment().utc().startOf('day').add(1, 'day')
            },
            response : null,
            timer : null,
            promise: null
        };

        // Settings of widget
        this.settings = {
            map: null,
            layers: [],
            parameters: {
                selected: {
                    id: null
                }
            },
            format : {
                date : 'YYYY-MM-DD',
                time : 'HH:mm',
                datetime : 'YYYY-MM-DD HH:mm:ss'
            },
            select2 : {
                maximumSelectionLength: 4
            },
            request: {
                delay: 200,
                countdown: 600
            },
            toastTimeout: 2000,
            locations: {
                show: {
                    last: true,
                    accuracy: true
                }
            },
            mode: modes.normal,
            presentation: {
                titleDate: false
            }
        };

        if (state.filter) {
            this.uris.deviceProvider += '/' + state.filter;
        }

        this.devicesProvider = new Provider({
            url: this.uris.deviceProvider
        });

        Widget.call(this, this._widget.object);
        this._widget.data = [];
        this.setUI(true, state);
        this.setEvents();
    };

    Monitor.prototype = new Widget();

    /**
     * Update chart data
     */
    Monitor.prototype.updateData = function() {
        var data = this.request.data;
        var date = this.request.date;
        var format = this.settings.format;
        var messages = this.messages;
        var request = this.request;
        var ui =  this.ui;
        var widget = this._widget;
        var toastTimeout = this.settings.toastTimeout;

        clearTimeout(request.timer);
        request.timer = setTimeout(function() {
            data = getChildData(widget.object);
            this.setProgress(false);

            if (!data.devices) {
                ui.alertMessage.find('span').text(messages.nodevices);
                ui.alertMessage.show();
                ui.progressContentLayer.hide();
                return;
            }

            if (!this.settings.parameters.selected.id) {
                ui.alertMessage.find('span').text(messages.noparams);
                ui.alertMessage.show();
                ui.progressContentLayer.hide();
                return;
            }

            ui.alertMessage.hide();
            ui.progressContentLayer.show();

            request.promise = $.ajax({
                dataType: "json",
                url: this.uris.dataProvider,
                data: mergeObjects(data, {
                    uid : widget.uid,
                    startDate : date.start.format(format.datetime),
                    endDate : date.end.format(format.datetime)
                }),
                method: 'POST',
                beforeSend : function() {
                    this.setProgress(true);
                }.bind(this),
                error: function() { 
                    Materialize.toast(messages.wrongResponse, toastTimeout);
                },
                success: function(result) {
                    request.response = result;
                    this.initLocations();
                }.bind(this),
                complete: function() {
                    this.setProgress(false, '60s');
                }.bind(this)
        });
        }.bind(this), this.settings.request.delay);
    };

    // Set Widget events
    Monitor.prototype.setEvents = function() {
        var ui = this.ui;
        var widget = this.widget;
        var card = this.card;
        var presentation = this.ui.presentation;

        // Event for devices select - colorize, change parameters
        ui.devicesSelect.change(function() {
            this.devicesSelected();
        }.bind(this));

        // Event for devices selection type switcher
        ui.devicesButton.click(function(){
            this.changeDevicesSelector();
        }.bind(this));

        // Event for device type select
        ui.deviceTypeSelect.change(function(){
            this.deviceTypeSelected();
        }.bind(this));

        // Event for changing start date input
        ui.startDateInput.change(function(){
            this.setDate();
        }.bind(this));

        // Event for changing end date input
        ui.endDateInput.change(function(){
            this.setDate(true);
        }.bind(this));

        widget.$parent.on('click', presentation.selectors.previousDay, function() {
            this.setDate({days: -1});
        }.bind(this));

        widget.$parent.on('click', presentation.selectors.nextDay, function() {
            this.setDate({days: 1});
        }.bind(this));

        widget.$parent.on('change', presentation.selectors.date, function(event) {
            this.setDate({start: event.target.value});
        }.bind(this));

        ui.allLocations.change(function(){
            this.toggleAllLocations();
        }.bind(this));

        ui.showAccuracy.change(function(){
            this.toggleShowAccuracy();
        }.bind(this));

        this.ui.devicesLocationsSelect.change(function() {
            this.devicesLocationsChanged();
        }.bind(this));

        // Widget events handling
        widget.$parent.on(widget.events.changed, function(event, params) {
            if (!$(params.input).hasClass('notrigger')) {
                this.updateData();
            }
        }.bind(this));

        widget.$parent.on(widget.events.refreshed, function() {
            this.updateData();
        }.bind(this));

        widget.$parent.on(widget.events.animated, function() {
            this.updateData();
        }.bind(this));

        // Card events handling
        card.$parent.on(card.events.minimized, function() {
            this._widget.size = 'minimized';
            this.updateTitle();
        }.bind(this));

        card.$parent.on(card.events.maximized, function() {
            this._widget.size = 'maximized';
            this.mapAutoSize(true);
            this.ui.titleLayer.find('span').remove();
        }.bind(this));

        card.$parent.on(card.events.unmaximized, function() {
            this._widget.size = 'unmaximized';
            this.mapAutoSize(false);
        }.bind(this));
    };

    /**
     * Set Widget UI Elements states
     * @param {bool} firstTime
     * @param {object} state
     */
    Monitor.prototype.setUI = function(firstTime, state) {
        var format = this.settings.format.date;
        var data = this.devicesProvider.getData();
        var widget = this._widget;
        this.ui.startDateInput.val(this.request.date.start.format(format));
        this.ui.endDateInput.val(this.request.date.end.format(format));

        if (state.size) {
            widget.size = state.size;
            widget.size === 'minimized' && this.minimize();
            widget.size === 'maximized' && this.maximize() && setTimeout(function() {
                this.mapAutoSize(true);
            }.bind(this), 200);
        }

        if (data.then) {
            this.devicesProvider.getData().done(function() {
                this.changeDevicesSelect(state.devices || null);
                this.changeDeviceTypeSelect();
            }.bind(this));
        } else {
            this.changeDevicesSelect(state.devices || null);
            this.changeDeviceTypeSelect();
        }

        if (state.presentation) {
            this.settings.mode = modes.presentation;
            this.setPresentationMode(state.presentation);
        }

        this.ui.devicesLocationsSelect.select2();
        this.widget.$parent.find('.pikaday').pikaday();
    };

    /**
     * Set new date
     * @param {bool} reversed - if defined, check dates in reversed order
     * @returns {undefined}
     */
    Monitor.prototype.setDate = function(params) {
        var date = this.request.date;
        var format = this.settings.format.date;
        var firstInput = this.ui.startDateInput;
        var secondInput = this.ui.endDateInput;
        var first = 'start';
        var second = 'end';

        if (this.settings.mode === modes.presentation) {
            if (params.days) {
                date.start.add({days: params.days});
                date.end.add({days: params.days});
                this.updateTitle();
            }
            if (params.start) {
                date.start = window.moment(params.start);
                date.end = date.start.clone();
                // TODO: check why this is interval 2 days, not 1 day. Currently commented
                //date.end.add({days: 1});
            }
            this.updateData();
        } else {
            if (params) {
                first = 'end';
                second = 'start';
                firstInput = secondInput;
                secondInput = this.ui.startDateInput;
            }

            date[first] = moment(firstInput.val());
            if (date.start > date.end) {
                date[second] = date[first].clone();
                secondInput.val(date[second].format(format));
            }
        }
    };

    /**
     * Find DOM element thas is child of current widget
     * @param {string} selector
     * @returns {object} DOM element
     */
    Monitor.prototype.find = function(selector) {
        return this._widget.object.find(selector);
    };

    /**
     * Get Widget Data
     * @returns {Object}
     */
    Monitor.prototype.getData = function() {
        return {
            uid : this._widget.uid,
            type : this._widget.type
        };
    };

    /**
     * Change method of selecting devices
     */
    Monitor.prototype.changeDevicesSelector = function() {
        this.ui.devicesButton.toggleClass('grey');
        this.ui.devicesTypesLayer.toggle();
        this.ui.devicesInstancesLayer.toggle();
    };

    /**
     * Change selected devices
     * @param {array} selected - devices selected
     */
    Monitor.prototype.changeDevicesSelect = function(selected) {
        var settings = this.settings.select2;
        var options = {};

        this.devicesProvider.getData().forEach(function(item) {
            if(item.parameter_data_type === 'positional') {
                options[item.type_id] = options[item.type_id] || {
                    children: {},
                    text: item.type_name
                };
                options[item.type_id]['children'][item.id] = {
                    name: item.name,
                    description: item.description
                };
            }
        });

        var data = Object.keys(options).map(function(key) {
            return {
                text: trans('type') + ': ' + options[key].text,
                children: Object.keys(options[key].children).map(function(id) {
                    return {
                        id: id,
                        text: options[key].children[id].name,
                        title: options[key].children[id].description
                    };
                })
            };
        });

        this.ui.devicesSelect.select2({
            maximumSelectionLength: settings.maximumSelectionLength,
            data: data
        }).val(selected).trigger('change');

        this.devicesSelected();
    };

    /**
     * Change selected devices to selected group
     * @returns {undefined}
     */
    Monitor.prototype.deviceTypeSelected = function() {
        var id = this.ui.deviceTypeSelect.val();
        var options = {};

        this.devicesProvider.getData().forEach(function(row) {
            if (parseInt(id) === parseInt(row.type_id)) {
                if(row.parameter_data_type === 'positional') {
                    options[parseInt(row.id)] = row.id;
                }
            }
        });

        this.ui.devicesSelect.val(Object.keys(options).map(function(key) {
            return key;
        })).trigger('change');
        this.changeDevicesSelector();
        this.devicesSelected();
    };

    /**
     * Devices selected
     */
    Monitor.prototype.devicesSelected = function() {
        var data = this.devicesProvider.getData();
        var devices = this.ui.devicesSelect.val() || [];
        var selector = this._widget.id + ' li.select2-selection__choice';
        var options = {};

        // Get all parameters of selected devices
        data.forEach(function(item) {
            if (devices.indexOf(String(item.id)) !== -1) {
                if (item.parameter_data_type === 'positional') {
                    options[item.id] = options[item.id] || {};
                    options[item.id][item.parameter_id] = item.parameter_name;
                }
            }
        });

        // Map object structure to array of arrays structure
        var mapped = Object.keys(options).map(function(key) {
            return Object.keys(options[key]).map(function(id) {
                return {id: id, text: options[key][id]};
            });
        });

        // Get intersection of arrays
        var intersection = getIntersectionOfArrays(mapped);

        if (intersection.length > 0) {
            this.settings.parameters.selected.id = Number(intersection[0].id);
        } else {
            this.settings.parameters.selected.id = null;
        }

        setTimeout(function() {
            colorElements(selector);
            $(selector).tooltipster(this.settings.tooltipster);
        }.bind(this), 10);
    };

    /**
     * Change options for Device Type Select
     */
    Monitor.prototype.changeDeviceTypeSelect = function() {
        var options = {};

        this.devicesProvider.getData().forEach(function(item) {
            if (item.parameter_data_type === 'positional') {
                options[item.type_id] = item.type_name;
            }
        });

        this.ui.deviceTypeSelect.select2({
            width: this.settings.select2.width,
            data: Object.keys(options).map(function(key) {
                return {id: key, text: options[key]};
            })
        });

        this.ui.deviceTypeSelect.siblings('label').addClass('active');
    };

    /**
     * Update Title with devices names
     */
    Monitor.prototype.updateTitle = function() {
        var select = this.ui.devicesSelect;
        var template = this.templates.title;
        var title = this.ui.titleLayer;
        var widget = this._widget.object;
        var settings = this.settings;
        var presentation = this.ui.presentation;
        var date = this.request.date;
        var sub = 'span';

        if (settings.mode === modes.presentation) {
            widget.find(presentation.selectors.date).val(
                date.start.format(settings.format.date)
            );
            return;
        }

        var devices = select.select2('data').map(function(item) {
            return item.text;
        }).join(', ');

        if (widget.hasClass('minimized')) {
            title.append(renderTemplate(template, {
                devices : devices
            })).find(sub).fadeIn();
        } else {
            title.find(sub).fadeOut(function(){
                $(this).remove();
            });
        }
    };

    Monitor.prototype.getMap = function() {
        if (this.settings.map) {
            return this.settings.map;
        }

        var ol = window.ol;
        var source = new ol.source.OSM({
            url: 'https://{a-c}.tile.openstreetmap.org/{z}/{x}/{y}.png',
            tileLoadFunction: function(tile, src) {
                tile.getImage().src = src;
            }
        });
        var osmLayer = new ol.layer.Tile({
            source: source,
            preload: 1
        });

        $('#' + this.ui.mapSelector).height(400);

        var scaleLineControl = new ol.control.ScaleLine();
        this.settings.map = new ol.Map({
            target: this.ui.mapSelector,
            controls: ol.control.defaults({
                attributionOptions: ({
                    collapsible: false
                })
            }).extend([
                scaleLineControl
            ]),
            layers: [osmLayer],
            view: new ol.View({
                center: ol.proj.fromLonLat([14.44, 53.433]),
                zoom: 15
            }),
            loadTilesWhileAnimating: true,
            loadTilesWhileInteracting: true
        });

        var map = this.settings.map;

        // Render map with some visual effects
        /// in postproccessing below, map has reduced saturation
        osmLayer.on('postcompose', function(event) {
            var context = event.context;
            var canvas = context.canvas;
            var imageData = context.getImageData(0, 0, canvas.width, canvas.height);
            var dataArray = imageData.data;

            for(var i = 0; i < dataArray.length; i += 4)
            {
                var red = dataArray[i];
                var green = dataArray[i + 1];
                var blue = dataArray[i + 2];
                var avg = (red + green + blue) / 3;
                

                dataArray[i] = (red + avg) / 2;
                dataArray[i + 1] = (green + avg) / 2;
                dataArray[i + 2] = (blue + avg) / 2;
            }

            context.putImageData(imageData, 0, 0);
        });

        return this.settings.map;
    };

    Monitor.prototype.initLocations = function() {
        var data = this.request.response.data;
        var select = this.ui.devicesLocationsSelect;
        var options = data.map(function(item) {
            return {id: item.device.id, text: item.device.name};
        });

        select.select2('destroy').find('option').remove();

        select.select2({
            data: options
        }).trigger('change');

        this.drawLocations();
        this.devicesLocationsChanged();

        this.ui.locations.find('li:first-of-type').trigger('click');
    };

    Monitor.prototype.devicesLocationsChanged = function() {
        var id = Number(this.ui.devicesLocationsSelect.val());
        var data = this.request.response.data;
        var locations = this.ui.locations;
        var template = this.templates.location;

        var html = '';
        var idx = 0;

        data.forEach(function(item, index) {
            var deviceId = item.device.id;

            if (deviceId !== id) {
                return;
            }

            idx = index;

            var last = {
                lon: 9999,
                lat: 9999
            };

            item.data.forEach(function(item, index) {
                //TODO: change back to 100000
                if (item.acc > 640000) {
                    return;
                }

                var lon = Number(item.lon).toFixed(4);
                var lat = Number(item.lat).toFixed(4);

                if (last.lat === lat && last.lon === lon) {
                    return;
                }

                last.lat = lat;
                last.lon = lon;

                html += renderTemplate(template, {
                    time: item.time.substr(0,16),
                    lon: lon,
                    lat: lat,
                    acc: item.acc,
                    type: item.acc > 0 ? signals.agps : signals.gps,
                    id: index,
                    device: deviceId
                });
            });
        });

        locations.html(html || this.messages.noinformation);

        var ol = window.ol;
        var map = this.getMap();
        var view = map.getView();
        var _this = this;

        locations.off('click').on('click', 'li', function(event) {
            var li = $(this);
            li.toggleClass('indigo white-text selected');
            var data = $(this).data();
            var pan = ol.animation.pan({
                duration: 1000,
                source: (view.getCenter())
            });

            _this.drawLocations();

            if (li.hasClass('selected')) {
                map.beforeRender(pan);
                view.setCenter(ol.proj.fromLonLat([Number(data.lon), Number(data.lat)]));
            }
        });

        var color = window.getChartDefaultOptions().colors[idx];

        locations.find('li').hover(function(){
            $(this).css({
                'background': color,
                'color': 'white'
            });
        }, function() {
            $(this).css({
                'background': 'none',
                'color': '#333'
        });
        });

        $('.nano').nanoScroller();
    };

    Monitor.prototype.drawLocations = function() {
        var map = this.getMap();
        var oldLayers = this.settings.layers;
        var locations = this.settings.locations;
        var loc = this.ui.locations;
        var raw = this.request.response.data;

        var ol = window.ol;

        var style = {
            flags: [
                new ol.style.Style({
                    image: new ol.style.Icon({
                        anchor: [0.5, 1],
                        src: this.markers[0]
                    })
                }),
                new ol.style.Style({
                    image: new ol.style.Icon({
                        anchor: [0.5, 1],
                        src: this.markers[1]
                    })
                }),
                new ol.style.Style({
                    image: new ol.style.Icon({
                        anchor: [0.5, 1],
                        src: this.markers[2]
                    })
                }),
                new ol.style.Style({
                    image: new ol.style.Icon({
                        anchor: [0.5, 1],
                        src: this.markers[3]
                    })
                })
            ]
        };

        Object.keys(oldLayers).forEach(function(key) {
            map.removeLayer(oldLayers[key].accuracies);
            map.removeLayer(oldLayers[key].markers);
            map.removeLayer(oldLayers[key].types);
        });

        var layers = {};

        raw.forEach(function(data, index) {
            var deviceId = data.device.id;
            var accuracies = new ol.source.Vector();
            var markers = new ol.source.Vector();
            var types = new ol.source.Vector();

            var accLayer = new ol.layer.Vector({
                source: accuracies,
                style: new ol.style.Style({
                    fill: new ol.style.Fill({
                        color: window.getChartDefaultOptions().colors[index],
                        opacity: 1.0
                    })
                }),
                opacity: 1.0,
                updateWhileAnimating: true,
                updateWhileInteracting: true
            });

            var markersLayer = new ol.layer.Vector({
                source: markers,
                style: style.flags[index],
                updateWhileAnimating: true,
                updateWhileInteracting: true
            });

            var typesLayer = new ol.layer.Vector({
                source: types,
                updateWhileAnimating: true,
                updateWhileInteracting: true
            });

            layers[deviceId] = {
                accuracies: accLayer,
                markers: markersLayer,
                types: typesLayer
            };

            var mapData = null;

            accLayer.on('precompose', function(event) {
                var context = event.context;
                var canvas = context.canvas;
                mapData = context.getImageData(0, 0, canvas.width, canvas.height);
            });

            accLayer.on('postcompose', function(event) {
                var context = event.context;
                var canvas = context.canvas;
                var imageData = context.getImageData(0, 0, canvas.width, canvas.height);
                var dataArray = imageData.data;
                var mapArray = mapData.data;

                for (var i = 0; i < dataArray.length; i += 4)
                {
                    dataArray[i] = (dataArray[i] + mapArray[i] + mapArray[i]) / 3;
                    dataArray[i + 1] = (dataArray[i + 1] + mapArray[i + 1] + mapArray[i + 1]) / 3;
                    dataArray[i + 2] = (dataArray[i + 2] + mapArray[i + 2] + mapArray[i + 2]) / 3;
                }

                context.putImageData(imageData, 0, 0);
            });

            var iterator = function(location) {
                if (!location) {
                    return;
                }
                var lat = Number(location.lat);
                var lon = Number(location.lon);
                var acc = Number(location.acc);

                //TODO: change back to 100000
                if (lat < -90 || lat > 90 || lon < -180 || lon > 180 || acc > 640000) {
                    return;
                }

                var marker = new ol.Feature({
                    geometry: new ol.geom.Point(ol.proj.fromLonLat([lon, lat]))
                });

                var type = new ol.Feature({
                    name: acc > 0 ? signals.agps : signals.gps,
                    geometry: new ol.geom.Point(ol.proj.fromLonLat([lon, lat]))
                });

                type.setStyle(new ol.style.Style({
                    text: new ol.style.Text({
                        font: 'bold 14px helvetica, sans-serif',
                        text: (acc > 0 ? 'A' : 'G'),
                        fill: new ol.style.Fill({
                            color: acc > 0 ? '#FFF' : '#FFF'
                        }),
                        stroke: new ol.style.Stroke({
                            color: 'rgba(0,0,0,0.5)',
                            width: 1
                        }),
                        offsetY: -20,
                        offsetX: 3
                    })
                }));

                markers.addFeature(marker);
                types.addFeature(type);

                if (false === locations.show.accuracy) {
                    return;
                }

                var accuracy = new ol.Feature({
                    geometry: new ol.geom.Circle(ol.proj.fromLonLat([lon, lat]), Math.max(10, acc))
                });

                accuracies.addFeature(accuracy);
            };

            loc.find('li.selected').each(function() {
                var data = $(this).data();

                if (raw[index].device.id === data.device) {
                    iterator(raw[index].data[Number(data.id)]);
                }
            });

            if (locations.show.last === false) {
                data.data.forEach(iterator);
            }
        });

        Object.keys(layers).forEach(function(key) {
            map.addLayer(layers[key].accuracies);
        });

        Object.keys(layers).forEach(function(key) {
            map.addLayer(layers[key].markers);
        });

        Object.keys(layers).forEach(function(key) {
            map.addLayer(layers[key].types);
        });
        this.settings.layers = layers;
    };

    Monitor.prototype.toggleAllLocations = function() {
        var allLocations = this.ui.allLocations;
        this.settings.locations.show.last = !allLocations[0].checked;
        this.drawLocations();
    };

    Monitor.prototype.toggleShowAccuracy = function() {
        var showAccuracy = this.ui.showAccuracy;
        this.settings.locations.show.accuracy = showAccuracy[0].checked;
        this.drawLocations();
    };

    Monitor.prototype.mapAutoSize = function(maximized) {
        var resizeTo = maximized ? (this._widget.object.height() - 200) : 400;
        $('#' + this.ui.mapSelector).height(resizeTo);
        this.settings.map && this.settings.map.updateSize();
    };

    Monitor.prototype.setPresentationMode = function(options) {
        var ui = this.ui;
        var title = ui.layers.title;
        var settings = this.settings;
        var templates = this.templates;

        if (options.progressBar === false) {
            ui.layers.progressBar.hide();
        }

        if (options.mainOptions === false) {
            ui.layers.mainOptions.hide();
        }

        if (options.titleDate === true) {
            if (settings.presentation.titleDate === false) {
                settings.presentation.titleDate = true;
                title.html(window.renderTemplate(templates.titleDate, {
                    current: title.html()
                }));
            }

            this.updateTitle();
        }

        if (options.widgetButtons === false) {
            this._widget.object.find('.title a.button').remove();
            ui.layers.title.addClass('fluid-width');
        }
    };

    /**
     * Get Widget Data
     * @returns {Object}
     */
    Monitor.prototype.getData = function() {
        return {
            uid : this._widget.uid,
            size : this._widget.size,
            type : this._widget.type,
            devices : this.ui.devicesSelect.val()
        };
    };

    return Scope;
})(I2M || {});

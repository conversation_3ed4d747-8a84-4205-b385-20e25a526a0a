var I2M = (function(Scope) {
    // Verify scopes
    Scope.Widgets = Scope.Widgets || {};
    Scope.Widgets.Monitor = Scope.Widgets.Monitor || constructor;

    // Mappings
    var Widget = Scope.Widget;
    var Helpers = Scope.Helpers;
    var Monitor = Scope.Widgets.Monitor;
    var Provider = Scope.Data.Provider;

    // Monitor custom events
    var events = {
        control: 'i2m:monitor:control',
        increased: 'i2m:monitor:increased',
        decreased: 'i2m:monitor:decreased',
        reset: 'i2m:monitor:reset',
        wheel: 'i2m:monitor:wheel',
        shift: {
            changed: 'i2m:monitor:shift:changed',
            switched: 'i2m:monitor:shift:switched'
        },
        date: {
            changed: 'i2m:monitor:date:changed'
        },
        parameter: {
            selected: 'i2m:monitor:parameter:selected'
        },
        parameters: {
            changed: 'i2m:monitor:parameters:changed'
        },
        indicators: {
            changed: 'i2m:monitor:indicators:changed'
        },
        indicator: {
            selected: 'i2m:monitor:indicator:selected'
        }
    };

    var modes = {
        normal: 'normal',
        presentation: 'presentation'
    };

    /**
     * Create Widget Monitor
     * @param {object} state
     * @returns {i2m_widgets_monitor_L1.constructor}
     */
    function constructor(state) {
        this.uid = state.uid;

        this._widget = {
            uid: state.uid,
            id: '#widget-' + state.uid,
            object: $('#widget-' + state.uid),
            state: state,
            data: null,
            type: 'monitor',
            size: 'unmaximized',
            events: events
        };

        // Messages (that are autotranslated)
        this.messages = {
            nosensor: 'widget.some-of-selected-devices-has-no-plugged-sensor',
            nosensors: 'widget.none-of-selected-devices-has-plugged-sensors',
            nosharedparams: 'widget.there-are-no-parameter-for-selected-devices',
            noparams: 'widget.there-are-no-parameters-to-choose',
            nodevices: 'widget.there-are-no-devices-selected',
            noinformation: 'widget.no-information',
            wrongResponse: 'ajax.wrong-response',
            sum: 'sum',
            avg: 'avg',
            count: 'count',
            efficiency: 'efficiency'
        };

        // Templates (that are autogenerated)
        this.templates = {
            indicatorsignalquality: '#template_indicator_signal_quality',
            indicator: '#template-indicator',
            shift: '#template-shift',
            title: '#template-title',
            titleDate: '#template-title-date',
            warning: '#template-warning-message',
            tooltip: '#template-tooltip',
            tooltipEfficiency: '#template-tooltip-efficiency'
        };

        this.uris = {
            deviceProvider: window.Routing.generate('monitor_devices_ajax'),
            dataProvider: window.Routing.generate('monitor_devices_production_ajax')
        };

        this.request = {
            data: null,
            date: {
                first: true,
                start: window.moment().utcOffset(0).startOf('day'),
                end: window.moment().utcOffset(0).startOf('day'),
                shift: {
                    start: window.moment().utcOffset(0).startOf('day')
                }
            },
            response: null,
            timer: null,
            delay: 200
        };

        this.chart = {
            id: '#chart-' + this._widget.uid,
            raw: null,
            data: null,
            options: window.getChartDefaultOptions(),
            interval: 'hour',
            type: 'bar',
            steps: false,
            points: true,
            fill: true,
            minutesToExtend: 20,
            plot: null,
            scale: {
                enabled: false,
                progress: null,
                zoom: 1,
                xaxis: {
                    min: null,
                    max: null
                },
                yaxis: {
                    min: null,
                    max: null
                }
            }
        };

        this.ui = {
            hideOnError: this.find('.hide-on-error'),
            refreshButton: this.find('button[name="refresh-button"]'),
            pauseButton: this.find('input[name="pause-button"]'),
            devicesSelect: this.find('select[name="devices"]'),
            deviceTypeSelect: this.find('select[name="device-type"]'),
            devicesSwitcher: this.find('input[name="devices-switcher"]'),
            shiftSwitcher: this.find('input[name="shift-switcher"]'),
            intervalSelect: this.find('select[name="interval"]'),
            parameterSelect: this.find('select[name="parameter"]'),
            indicatorSelect: this.find('select[name="indicator"]'),
            minutesToExtendSelect: this.find('select[name="minutes-to-extend"]'),
            lineChartButton: this.find('button[data-type="line"]'),
            barChartButton: this.find('button[data-type="bar"]'),
            indicatorSum: this.find('span[name="indicator-sum"]'),
            alertMessage: this.find('div[name="alert"]'),
            indicatorMessage: this.find('div[name="indicator-message"]'),
            indicatorTitle: this.find('span[name="indicator-title"]'),
            indicatorSelector: this._widget.id + ' span[name="indicator-sum"] > span',
            devicesInstancesLayer: this.find('div[name="devices-instances"]'),
            devicesTypesLayer: this.find('div[name="devices-types"]'),
            dateCalendarLayer: this.find('div[name="date-calendar"]'),
            dateShiftLayer: this.find('div[name="date-shift"]'),
            shiftNextButton: this.find('button[name="shift-next"]'),
            shiftPrevButton: this.find('button[name="shift-previous"]'),
            dayNextButton: this.find('button[name="day-next"]'),
            dayPrevButton: this.find('button[name="day-previous"]'),
            shiftInfoLayer: this.find('div[name="shift-info"]'),
            startDateInput: this.find('input[name="start-date"]'),
            endDateInput: this.find('input[name="end-date"]'),
            subOptionsLayer: this.find('div[name="sub-options"]'),
            chartIncreaseButton: this.find('button[name="chart-increase"]'),
            chartDecreaseButton: this.find('button[name="chart-decrease"]'),
            chartResetButton: this.find('button[name="chart-reset"]'),
            chartControlButton: this.find('input[name="chart-control"]'),
            chartStepsButton: this.find('input[name="chart-steps"]'),
            chartPointsButton: this.find('input[name="chart-points"]'),
            chartRulesButton: this.find('input[name="chart-rules"]'),
            chartTypeButton: this.find('input[name="chart-type"]'),
            chartLayer: this.find('div.chart-content'),
            chartButtons: this.find('span[name="chart-buttons"]'),
            layers: {
                title: this.find('div.title > h5'),
                mainOptions: this.find('div[name="main-options-layer"]'),
                progressBar: this.find('div[name="progress-bar-layer"]'),
                chartControl: this.find('div[name="chart-control-layer"]'),
                indicatorMessage: this.find('div[name="indicator-message-layer"]'),
                alerts: this.find('div[name="alerts-layer"]')
            },
            presentation: {
                selectors: {
                    date: 'input[name="presentation-date"]',
                    dateControl: 'span[name="title-date-control"]',
                    previousDay: 'button[name="title-previous-day"]',
                    nextDay: 'button[name="title-next-day"]',
                    chartType: 'input[name="chart-type"]'
                }
            }
        };

        this.settings = {
            shiftSwitcher: false,
            format: {
                date: 'YYYY-MM-DD',
                time: 'HH:mm',
                datetime: 'YYYY-MM-DD HH:mm:ss'
            },
            select2: {
                maximumSelectionLength: 4
            },
            tooltipster: {
                interactive: true
            },
            shift: {
                start: {
                    hours: 6,
                    minutes: 0
                },
                duration: {
                    hours: 8,
                    minutes: 0
                }
            },
            parameter: {
                id: null,
                type: null,
                efficiency: null
            },
            indicator: {
                type: null
            },
            mode: modes.normal,
            presentation: {
                titleDate: false,
                color: false
            },
            rules: false,
            toastTimeout: 2000
        };

        if (state.filter) {
            this.uris.deviceProvider += '/' + state.filter;
        }

        this.devicesProvider = new Provider({
            url: this.uris.deviceProvider
        });

        Widget.call(this, this._widget.object);
        this._widget.data = [];
        this.setEvents();
        this.setUI(true, state);
        this.updateData();
    }

    Monitor.prototype = new Widget();

    /**
     * Find DOM element thas is child of current widget
     * @param {string} selector
     * @returns {object} DOM element
     */
    Monitor.prototype.find = function(selector) {
        return this._widget.object.find(selector);
    };

    /**
     * Set Widget UI Elements states
     * @param {bool} firstTime
     * @param {object} state
     */
    Monitor.prototype.setUI = function(firstTime, state) {
        var widget = this._widget;
        var chart = this.chart;
        var ui = this.ui;
        var settings = this.settings;
        var data = this.devicesProvider.getData();

        if (!firstTime) {
            return;
        }

        if (state.hide) {
            widget.object.hide();
        }

        if (state.shift) {
            settings.shift = state.shift;
        }

        if (state.interval) {
            chart.interval = state.interval;
            ui.intervalSelect.val(chart.interval);
        }

        if (state.chartType) {
            chart.type = state.chartType;
            if (chart.type === 'bar') {
                this.ui.chartTypeButton.prop('checked', false);
            }
        }

        if (state.chartSteps) {
            chart.steps = state.chartSteps === 'true';
            this.ui.chartStepsButton.prop('checked', chart.steps);
        }

        if (state.chartPoints) {
            chart.points = state.chartPoints === 'true';
            this.ui.chartPointsButton.prop('checked', chart.points);
        }

        if (state.chartFill) {
            chart.fill = state.chartFill === 'true';
        }

        if (state.rules) {
            settings.rules = state.rules === 'true';
        }

        if (state.indicator) {
            settings.indicator.type = state.indicator;
        }

        if (state.parameter) {
            settings.parameter.id = Number(state.parameter);
        }

        if (state.size) {
            widget.size = state.size;
            widget.size === 'minimized' && this.minimize();
            widget.size === 'maximized' && this.maximize();
        }

        if (state.shifts) {
            ui.shiftSwitcher.prop('checked', state.shifts === 'true');
            this.toggleShiftSelector();
        }

        if (state.presentation) {
            settings.mode = modes.presentation;
            this.setPresentationMode(state.presentation);
        }

        if (data.then) {
            this.devicesProvider.getData().done(function() {
                this.changeDevicesSelect(state.devices || null);
                this.changeDeviceTypeSelect();
            }.bind(this));
        } else {
            this.changeDevicesSelect(state.devices || null);
            this.changeDeviceTypeSelect();
        }

        ui.minutesToExtendSelect.select2();
        ui.parameterSelect.select2();
        ui.intervalSelect.select2({minimumResultsForSearch: Infinity});
        ui.indicatorSelect.select2({minimumResultsForSearch: Infinity});
        this.setChartOptions();
        this.setChartTypeButtons();
        this.widget.$parent.find('.pikaday').pikaday();
    };

    /**
     * Set Widget chart options
     */
    Monitor.prototype.setChartOptions = function() {
        var options = this.chart.options;
        var series = options.series;
        var type = this.chart.type;
        var steps = this.chart.steps;
        var points = this.chart.points;
        var fill = this.chart.fill;

        options.legend.show = false;
        series.lines.fill = fill;

        if (type === 'line') {
            series.lines.show = true;
            series.points.show = points;
            series.bars.show = false;
            series.lines.steps = steps;
            options.tooltipMode = 'multi';
            options.labels.show = false;
        }

        if (type === 'bar') {
            series.lines.show = false;
            series.points.show = false;
            series.bars.show = true;
            series.bars.barWidth = 3600000;
            series.bars.order = 1;
            options.tooltipMode = 'single';
            options.labels.show = true;
        }
    };

    // Set Widget events
    Monitor.prototype.setEvents = function() {
        var chart = this.chart;
        var request = this.request;
        var settings = this.settings;
        var ui = this.ui;
        var widget = this.widget;
        var card = this.card;
        var presentation = this.ui.presentation;

        // Event for device type select
        ui.deviceTypeSelect.change(function() {
            this.deviceTypeSelected();
        }.bind(this));

        ui.shiftSwitcher.change(function() {
            this.toggleShiftSelector();
        }.bind(this));

        // Event for devices select - colorize, change parameters
        ui.devicesSelect.change(function() {
            this.devicesSelected();
        }.bind(this));

        // Parameter changed
        ui.parameterSelect.on('change', function(event) {
            this.selectParameter(Number(event.target.value));
        }.bind(this));

        // Parameters changed
        ui.parameterSelect.on(events.parameters.changed, function() {
            ui.parameterSelect.val(settings.parameter.id).trigger('change');
        });

        // Paremeter selected
        ui.parameterSelect.on(events.parameter.selected, function() {
            this.changeIndicatorSelect();
        }.bind(this));

        // Event for indicator select
        ui.indicatorSelect.on('change', function(event) {
            this.selectIndicator(event.target.value);
        }.bind(this));

        // Indicators changed
        ui.indicatorSelect.on(events.indicators.changed, function() {
            ui.indicatorSelect.val(settings.indicator.type).trigger('change');
        });

        // Indicator selected
        ui.indicatorSelect.on(events.indicator.selected, function() {
            this.redrawChart();
        }.bind(this));

        // Event for interval select
        ui.intervalSelect.change(function() {
            chart.interval = $(this).val();
        });

        ui.minutesToExtendSelect.change(function(event) {
            chart.minutesToExtend = parseInt($(event.currentTarget).val(), 10);
            this.redrawChart();
        }.bind(this));

        ui.refreshButton.click(function() {
            this.refresh();
        }.bind(this));

        ui.pauseButton.change(function(event) {
            event.target.checked === true ? this.pause() : this.unpause();
        }.bind(this));

        // Events for chart type buttons
        ui.chartTypeButton.click(function(event) {
            chart.type = event.currentTarget.checked === true ? 'line' : 'bar';
            this.setChartOptions();
            this.setChartTypeButtons();
            this.setUI();
            this.redrawChart();
        }.bind(this));

        // Enable/disable steps mode for line charts
        ui.chartStepsButton.change(function(event) {
            this.chart.steps = event.target.checked;
            this.setChartOptions();
            this.redrawChart();
        }.bind(this));

        // Enable/disable points on line charts
        ui.chartPointsButton.change(function(event) {
            this.chart.points = event.target.checked;
            this.setChartOptions();
            this.redrawChart();
        }.bind(this));

        // Enable/disable parameter rule
        ui.chartRulesButton.change(function(event) {
            this.settings.rules = event.target.checked;
            this.updateData();
        }.bind(this));

        // Previous day for shift button
        ui.dayPrevButton.click(function(event) {
            this.changeShiftDate({days: -1});
        }.bind(this));

        // Next day for shift button
        ui.dayNextButton.click(function() {
            this.changeShiftDate({days: 1});
        }.bind(this));

        // Previous shift button
        ui.shiftPrevButton.click(function() {
            this.changeShiftDate({
                hours: -this.settings.shift.duration.hours,
                minutes: -this.settings.shift.duration.minutes
            });
        }.bind(this));

        // Next shift button
        ui.shiftNextButton.click(function() {
            this.changeShiftDate({
                hours: this.settings.shift.duration.hours,
                minutes: this.settings.shift.duration.minutes
            });
        }.bind(this));

        // Change start date input
        ui.startDateInput.change(function(event) {
            this.changeDate({start: event.target.value});
        }.bind(this));

        // Change end date input
        ui.endDateInput.change(function(event) {
            this.changeDate({end: event.target.value});
        }.bind(this));

        // Monitor (self) events
        ui.chartIncreaseButton.click(function() {
            this.increaseChartSize();
        }.bind(this));

        // Chart pan & zoom controls
        ui.chartDecreaseButton.click(function() {
            this.decreaseChartSize();
        }.bind(this));

        ui.chartResetButton.click(function() {
            this.resetChartSize();
        }.bind(this));

        ui.chartControlButton.click(function(event) {
            this.chart.scale.enabled = event.target.checked;
            return this._widget.object.trigger(this._widget.events.control, this);
        }.bind(this));

        ui.chartLayer.on('wheel', function(event) {
            this.wheelChart(event);
        }.bind(this));

        ui.chartLayer.on('mousemove', function(event) {
            this.moveChart(event);
        }.bind(this));

        ui.chartLayer.on('mousedown', function(event) {
            this.pressedChart(event);
        }.bind(this));

        ui.chartLayer.on('mouseup', function(event) {
            this.releasedChart(event);
        }.bind(this));

        // Self events handling
        widget.$parent.on(events.shift.switched, function() {
            this.renderShiftInfo();
            this.updateData();
        }.bind(this));

        widget.$parent.on(events.shift.changed, function() {
            this.renderShiftInfo();
            this.updateData();
        }.bind(this));

        widget.$parent.on(events.date.changed, function() {
            this.updateData();
        }.bind(this));

        widget.$parent.on('click', presentation.selectors.previousDay, function() {
            this.changeDate({days: -1});
        }.bind(this));

        widget.$parent.on('click', presentation.selectors.nextDay, function() {
            this.changeDate({days: 1});
        }.bind(this));

        widget.$parent.on('change', presentation.selectors.chartType, function(event) {
            chart.type = event.target.checked === true ? 'line' : 'bar';
            this.setChartOptions();
            this.setChartTypeButtons();
            this.setUI();
            this.redrawChart();
        }.bind(this));

        widget.$parent.on('change', presentation.selectors.date, function(event) {
            this.changeDate({start: event.target.value});
        }.bind(this));

        // Widget events handling
        widget.$parent.on(widget.events.changed, function(event, params) {
            if ($(params.input).hasClass('trigger') === true) {
                this.updateData();
            }
        }.bind(this));

        widget.$parent.on(widget.events.refreshed, function() {
            this.updateData();
        }.bind(this));

        widget.$parent.on(widget.events.animated, function() {
            this.updateData();
        }.bind(this));

        // Card events handling
        card.$parent.on(card.events.minimized, function() {
            this._widget.size = 'minimized';
            if (!this._widget.object.hasClass('minimized')) {
                this._widget.size = 'unmaximized';
            }
            this.updateTitle();
        }.bind(this));

        card.$parent.on(card.events.maximized, function() {
            this.ui.layers.title.find('span').remove();
            this._widget.size = 'maximized';
            this.redrawChart();
        }.bind(this));

        card.$parent.on(card.events.unmaximized, function() {
            this._widget.size = 'unmaximized';
            this.redrawChart();
        }.bind(this));
    };

    /**
     * Set Chart type buttons
     */
    Monitor.prototype.setChartTypeButtons = function() {
        var checked = this.ui.chartTypeButton.prop('checked');
        var siblings = this.ui.chartTypeButton.siblings('label');
        var color1 = 'indigo';
        var color2 = 'teal';

        if (checked) {
            siblings.removeClass(color1);
            siblings.addClass(color2);
        } else {
            siblings.removeClass(color2);
            siblings.addClass(color1);
        }
    };

    /**
     * Change date.
     *
     * @this {Monitor}
     * @param {object} source data object (days, hours, minutes)
     * @returns {this}
     */
    Monitor.prototype.changeDate = function(source) {
        var date = this.request.date;
        var format = this.settings.format.date;
        var selectors = this.ui.presentation.selectors;

        if (this.settings.mode === modes.presentation) {
            if (source.days) {
                date.start.add({days: source.days});
                date.end.add({days: source.days});
                this.updateTitle();
            }
            if (source.start) {
                date.start = window.moment(source.start);
                date.end = date.start.clone();
                // TODO: check why this is interval 2 days, not 1 day. Currently commented
                //date.end.add({days: 1});
            }
        } else {
            if (source.start) {
                date.start = window.moment(source.start);
                if (date.start > date.end) {
                    date.end = date.start.clone();
                    this.ui.endDateInput.val(date.end.format(format));
                }
            } else if (source.end) {
                date.end = window.moment(source.end);
                if (date.start > date.end) {
                    date.start = date.end.clone();
                    this.ui.startDateInput.val(date.start.format(format));
                }
            }
        }

        this._widget.object.trigger(events.date.changed);

        return this;
    };

    /**
     * Change shift date.
     *
     * @this {Monitor}
     * @param {object} delta data object (days, hours, minutes)
     * @returns {this}
     */
    Monitor.prototype.changeShiftDate = function(delta) {
        var date = this.request.date;
        var duration = this.settings.shift.duration;

        date.shift.start.add(delta);
        date.start = date.shift.start.clone();
        date.end = date.shift.start.clone().add({
            hours: duration.hours,
            minutes: duration.minutes
        });

        this._widget.object.trigger(events.shift.changed);

        return this;
    };

    /**
     * Toggle date between shift or normal date.
     *
     * @this {Monitor}
     * @returns {this}
     */
    Monitor.prototype.toggleShiftSelector = function() {
        var date = this.request.date;
        var settings = this.settings;
        var ui = this.ui;

        settings.shiftSwitcher = ui.shiftSwitcher.prop('checked');

        if (settings.shiftSwitcher) {
            var years = date.start.year();
            var months = date.start.month();
            var days = date.start.date();
            date.shift.start.year(years).month(months).date(days);
        } else {
            date.start = date.shift.start.clone().startOf('day');
            date.end = date.start.clone();
            ui.startDateInput.val(date.start.format(settings.format.date));
            ui.endDateInput.val(date.end.format(settings.format.date));
        }

        this._widget.object.trigger(events.shift.switched);

        return this;
    };

    /**
     * Update Title with devices names
     */
    Monitor.prototype.updateTitle = function() {
        var select = this.ui.devicesSelect;
        var title = this.ui.layers.title;
        var template = this.templates.title;
        var widget = this._widget.object;
        var date = this.request.date;
        var settings = this.settings;
        var presentation = this.ui.presentation;

        var sub = 'span';

        if (this.settings.mode === modes.presentation) {
            widget.find(presentation.selectors.date).val(
                date.start.format(settings.format.date)
            );
            return;
        }

        var devices = select.select2('data').map(function(item) {
            return item.text;
        }).join(', ');

        if (widget.hasClass('minimized')) {
            title.append(window.renderTemplate(template, {
                devices: devices
            })).find(sub).fadeIn();
        } else {
            title.find(sub).fadeOut(function() {
                $(this).remove();
            });
        }
    };

    /**
     * Render shift information
     * @param {bool} first - if method is run first time, calculate start shift
     */
    Monitor.prototype.renderShiftInfo = function(first) {
        var format = this.settings.format;
        var shift = this.settings.shift;
        var start = this.request.date.start;
        var template = this.templates.shift;

        if (this.request.date.first) {
            this.request.date.first = false;
            var hour = window.moment().hours();
            var begin = shift.start.hours;
            var duration = shift.duration.hours;

            // Calculate what shift is currently
            start.add({
                hour: Math.floor((hour - begin) / duration) * duration + begin
            });
        }

        var from = start.clone();
        var to  = from.clone().add(shift.duration);
        this.request.date.end = to;

        var data = {
            date: from.format(format.date),
            from: from.format(format.time),
            to: to.format(format.time)
        };

        this.ui.shiftInfoLayer.html(window.renderTemplate(template, data));
    };

    /**
     * Change Devices Select values
     * Devices are grouped by theirs type
     * @param {array} selected
     */
    Monitor.prototype.changeDevicesSelect = function(selected) {
        var settings = this.settings.select2;
        var options = {};

        this.devicesProvider.getData().forEach(function(item) {
            options[item.type_id] = options[item.type_id] || {
                children: {},
                text: item.type_name
            };
            options[item.type_id].children[item.id] = {
                name: item.name,
                description: item.description
            };
        });

        var data = Object.keys(options).map(function(key) {
            return {
                text: window.trans('type') + ': ' + options[key].text,
                children: Object.keys(options[key].children).map(function(id) {
                    return {
                        id: id,
                        text: options[key].children[id].name,
                        title: options[key].children[id].description
                    };
                })
            };
        });

        this.ui.devicesSelect.select2({
            maximumSelectionLength: settings.maximumSelectionLength,
            data: data
        }).val(selected).trigger('change');
    };

    /**
     * Change Device Type Select values
     */
    Monitor.prototype.changeDeviceTypeSelect = function() {
        var options = {};

        this.devicesProvider.getData().forEach(function(item) {
            options[item.type_id] = item.type_name;
        });

        this.ui.deviceTypeSelect.select2({
            width: this.settings.select2.width,
            data: Object.keys(options).map(function(key) {
                return {id: key, text: options[key]};
            })
        });

        this.ui.deviceTypeSelect.siblings('label').addClass('active');
    };

    /**
     * Devices selected
     */
    Monitor.prototype.devicesSelected = function() {
        var selector = this._widget.id + ' li.select2-selection__choice';

        setTimeout(function() {
            window.colorElements(selector);
            $(selector).tooltipster(this.settings.tooltipster);
            this.changeParametersSelect();
        }.bind(this), 10);
    };

    /**
     * Change selected devices to selected group
     * @returns {undefined}
     */
    Monitor.prototype.deviceTypeSelected = function() {
        var id = this.ui.deviceTypeSelect.val();
        var options = {};

        this.devicesProvider.getData().forEach(function(row) {
            if (parseInt(id, 10) === parseInt(row.type_id, 10)) {
                options[parseInt(row.id, 10)] = row.id;
            }
        });

        this.ui.devicesSelect.val(Object.keys(options).map(function(key) {
            return key;
        })).trigger('change');
        this.ui.devicesSwitcher.prop('checked', false);
        this.devicesSelected();
    };

    /**
     * Change parameters available to select
     */
    Monitor.prototype.changeParametersSelect = function() {
        var devices = this.ui.devicesSelect.val() || [];
        var data = this.devicesProvider.getData();
        var parameter = this.settings.parameter;
        var mapped = {};
        var $parameters = this.ui.parameterSelect;
        var parameters = [];

        // Get all parameters associated to given devices
        data.forEach(function(item) {
            if (devices.indexOf(String(item.id)) !== -1) {
                if (item.parameter_data_type === 'positional') {
                    return;
                }
                if (mapped[item.parameter_id]) {
                    if (mapped[item.parameter_id].devices.indexOf(item.id) === -1) {
                        mapped[item.parameter_id].devices.push(item.id);
                    }
                } else {
                    mapped[item.parameter_id] = {
                        id: item.parameter_id,
                        text: item.rule_label || item.parameter_name,
                        devices: [item.parameter_id]
                    };
                }
            }
        });

        // Filter to only parameters that are associated to all given devices
        Object.keys(mapped).forEach(function(key) {
            if (mapped[key].devices.length === devices.length) {
                parameters.push(mapped[key]);
            }
        });

        // Check if last selected option exists in actual options
        if (parameter.id) {
            var exists = false;
            for (var i in parameters) {
                if (parameter.id === Number(parameters[i].id)) {
                    exists = true;
                }
            }
            if (exists === false && parameters.length > 0) {
                parameter.id = parameters[0].id;
            }
        } else {
            if (parameters.length > 0) {
                parameter.id = parameters[0].id;
            }
        }

        // Reinitialize select2 with new options
        $parameters.select2('destroy');
        $parameters.find('option').remove();
        $parameters.select2({
            minimumResultsForSearch: Infinity,
            data: parameters
        });

        $parameters.trigger(events.parameters.changed);
    };

    /**
     * Select Parameter internally
     *
     * @this {Monitor}
     * @param {number} id
     */
    Monitor.prototype.selectParameter = function(id) {
        var $parameters = this.ui.parameterSelect;
        var settings = this.settings;
        var data = this.devicesProvider.getData();

        if (id) {
            for (var i in data) {
                if (id === data[i].parameter_id) {
                    settings.parameter = {
                        id: id,
                        type: data[i].parameter_data_type,
                        efficiency: data[i].parameter_fill_data
                    };
                    break;
                }
            }
        } else {
            settings.parameter = {
                id: null,
                type: null,
                efficency: null
            }
        }
        $parameters.trigger(events.parameter.selected);
    };

    /**
     * Change indicators available to select
     */
    Monitor.prototype.changeIndicatorSelect = function() {
        var $indicators = this.ui.indicatorSelect;
        var messages = this.messages;
        var parameter = this.settings.parameter;
        var indicator = this.settings.indicator;
        var indicators = [];
        var isChoosen = function(item) {
            return item.id === indicator.type;
        };

        $indicators.select2('destroy');
        $indicators.find('option').remove();

        if (parameter.efficiency) {
            indicators.push({id: 'efficiency', text: messages.efficiency});
        }

        if (parameter.id) {
            indicators.push({id: 'avg', text: messages.avg});
            indicators.push({id: 'sum', text: messages.sum});
            indicators.push({id: 'count', text: messages.count});

        }

        $indicators.select2({
            minimumResultsForSearch: Infinity,
            data: indicators
        });

        if (indicators.length > 0) {
            if (indicators.some(isChoosen) === false) {
                indicator.type = indicators[0].id;
            }
            $indicators.val(indicator.type);
        }
        $indicators.trigger(events.indicators.changed);
    };

    /**
     * Select indicator internally
     * @param  {string} type of indicator
     */
    Monitor.prototype.selectIndicator = function(type) {
        var $indicators = this.ui.indicatorSelect;

        if (type) {
            this.settings.indicator.type = type;
        }

        $indicators.trigger(events.indicator.selected);
    };

    /**
     * Update chart data
     */
    Monitor.prototype.updateData = function() {
        var chart = this.chart;
        var data = this.request.data;
        var date = this.request.date;
        var format = this.settings.format;
        var messages = this.messages;
        var request = this.request;
        var ui =  this.ui;
        var widget = this._widget;

        clearTimeout(request.timer);
        request.timer = setTimeout(function() {
            data = window.getChildData(widget.object);
            data.shift = this.settings.shiftSwitcher;

            if (data.hasOwnProperty('parameter')) {
                ui.alertMessage.hide();
                ui.hideOnError.show();
                $(chart.id).show();
            } else {
                var message = messages.nodevices;

                if (data.devices instanceof Array) {
                    if (data.devices.length > 1) {
                        message = messages.nosharedparams;
                    } else {
                        message = messages.noparams;
                    }
                }

                ui.subOptionsLayer.hide();
                ui.alertMessage.find('span').text(message);
                ui.layers.alerts.find('div.warning').remove();
                ui.alertMessage.show();
                ui.hideOnError.hide();
                this.setProgress(false);
                $(chart.id).hide();
                return;
            }

            $.ajax({
                dataType: 'json',
                url: this.uris.dataProvider,
                data: window.mergeObjects(data, {
                    uid: widget.uid,
                    type: chart.type,
                    startDate: date.start.format(format.datetime),
                    endDate: date.end.format(format.datetime),
                    rules: this.settings.rules
                }),
                method: 'POST',
                beforeSend: function() {
                    this.setProgress(true);
                }.bind(this),
                error: function() {
                    window.Materialize.toast(messages.wrogResponse, this.settings.toastTimeout);
                }.bind(this),
                success: function(result) {
                    request.response = result;
                    chart.raw = result;
                    this.redrawChart();
                    this.showMessages();
                }.bind(this),
                complete: function() {
                    this.setProgress(false, '60s');
                }.bind(this)
            });
        }.bind(this), request.delay);
    };

    /**
     * Convert raw data to flot
     * @param {Object} raw
     * @returns {Object}
     */
    Monitor.prototype.convertToFlot = function(raw) {
        var chart = this.chart;
        var parameter = this.settings.parameter;
        var indicator = this.settings.indicator;
        var flot = [];
        var boolean = false;
        var currentTime = window.moment.utc().valueOf();

        if (indicator.type !== 'count' && parameter.type === 'boolean') {
            boolean = true;
        }

        raw.data.forEach(function(series) {
            var _series = [];
            var formatted = {};
            var lastData = {
                value: 0,
                time: 0,
                frame: chart.minutesToExtend * 60000
            };

            indicator.type === 'efficiency' && series.efficiency.forEach(function(element) {
                _series.push([
                    window.moment.utc(element.time).utcOffset(0).format('x'),
                    element.ef ? element.ef : 0
                ]);
            });

            indicator.type !== 'efficiency' && series.data.forEach(function(element) {
                var value = Math.round(parseFloat(element[indicator.type] * 100)) / 100;
                var time = window.moment.utc(element.time).valueOf();
                if (boolean) {
                    value = value > 0 ? 1 : 0;
                }

                if (lastData.frame > 0) {
                    if (element.count > 0) {
                        lastData.value = value;
                        lastData.time = time;
                    } else if (lastData.frame > time - lastData.time) {
                        if (currentTime >= window.moment(element.time).valueOf()) {
                            value = lastData.value;
                        }
                    }
                }

                _series.push([
                    time,
                    value
                ]);
            });

            formatted.data = _series;
            formatted.label = series.device.name;

            if (series.signal) {
                formatted.signal = series.signal;
            }

            flot.push(formatted);
        });

        if (chart.type === 'bar') {
            var cases = {
                minute: 60000,
                day: 86400000
            };
            var timeLength = cases[chart.interval] || 3600000;
            var barWidth = (timeLength / raw.data.length) * 0.8;
            chart.options.series.bars.barWidth = barWidth;
        }

        return {
            flot: flot,
            startDateTime: raw.start,
            endDateTime: raw.end
        };
    };

    /**
     * Get Parameters' signal quality state
     *
     * @param {integer} parameterId
     * @returns {Boolean}
     */
    Monitor.prototype.getParameterSignalState = function(parameterId) {
        var data = this.devicesProvider.getData();
        var row = null;

        for (row in data) {
            if (parameterId == data[row].parameter_id) {
                return data[row].parameter_signal_quality;
            }
        }

        return false;
    };

    /**
     * Redraw chart
     */
    Monitor.prototype.redrawChart = function() {
        var chart = this.chart;
        var messages = this.messages;
        var indicator = this.settings.indicator.type;
        var ui = this.ui;
        var options = chart.options;
        var date = this.request.date;
        var templates = {
            extended: this.templates.indicatorsignalquality,
            simple: this.templates.indicator,
            tooltip: this.templates.tooltip,
            tooltipEfficiency: this.templates.tooltipEfficiency
        };

        if (!chart.raw || !$(chart.id).is(':visible')) {
            return;
        }

        if (this.settings.presentation.color === true) {
            var devicesData = this.devicesProvider.getData();
            for (var index in devicesData) {
                if (devicesData[index].rule_color && this.settings.parameter.id == devicesData[index].parameter_id) {
                    chart.options.colors = [devicesData[index].rule_color];
                }
            }
        }

        chart.data = this.convertToFlot(chart.raw);

        if (chart.raw.data[0].efficiency && indicator === 'efficiency') {
            chart.options.yaxis.tickSize = 10;
            chart.options.yaxis.min = 0;
            chart.options.yaxis.max = 100;
            chart.options.labels.show = true;
            chart.options.labels.renderer = function(value) {
                return Math.round(value * 100) / 100 + '%';
            };

            // Callback for custom tooltip custom content rendering
            chart.options.tooltipRenderer = function(data) {
                data.unit = '%';
                data.time = null;
                data.y = data.y.toFixed(2);
                var seconds = null;
                var minutes = null;
                var hours = null;
                var rest = null;

                if (chart.interval === 'minute') {
                    data.time = Math.round((60 * data.y) / 100) + 's';
                }

                if (chart.interval === 'hour') {
                    seconds = Math.round((3600 * data.y) / 100);
                    data.time = Math.floor(seconds / 60) + 'm ' + (seconds % 60) + 's';
                }

                if (chart.interval === 'day') {
                    seconds = Math.round((86400 * data.y) / 100);
                    hours = Math.floor(seconds / 3600);
                    minutes = Math.floor(seconds / 60) - (hours * 60);
                    rest = seconds - (hours * 3600) - (minutes * 60);
                    data.time = hours + 'h ' + minutes + 'm ' + rest + 's';
                }

                return window.renderTemplate(templates.tooltipEfficiency, data);
            };

            // TODO: check below if utc offset affects to day shift
            // if (this.settings.shiftSwitcher) {
            //     chart.options.xaxis.min = date.shift.start.utcOffset(0).format('x');
            //     chart.options.xaxis.max = date.shift.start.clone().add(8, 'hour').utcOffset(0).format('x');
            // } else {
            //     chart.options.xaxis.min = window.moment(this.ui.startDateInput.val()).utcOffset(0).add({hours:2}).format('x');
            //     chart.options.xaxis.max = window.moment(this.ui.endDateInput.val()).add(1, 'day').utcOffset(0).add({hours:2}).format('x');
            // }
            chart.options.yaxis.tickFormatter = function(val) {
                return val + '%';
            };
        } else {
            delete chart.options.tooltipRenderer;
            delete chart.options.yaxis.tickFormatter;
            delete chart.options.yaxis.tickSize;
            delete chart.options.yaxis.min;
            delete chart.options.yaxis.max;
            delete chart.options.xaxis.min;
            delete chart.options.xaxis.max;
            chart.options.labels.show = false;
            chart.options.tooltipTemplate = templates.tooltip;

            if (this.settings.rules) {
                var parameter = chart.raw.data[0].parameter;

                if (parameter.hasOwnProperty('visibleMinimum')) {
                    chart.options.yaxis.min = parameter.visibleMinimum;
                }
                if (parameter.hasOwnProperty('visibleMaximum')) {
                    chart.options.yaxis.max = parameter.visibleMaximum;
                }

                if (parameter.hasOwnProperty('unit')) {
                    var unit = parameter.unit;

                    if (unit === 'on/off') {
                        chart.options.yaxis.tickFormatter = function (val) {
                            return val > 0 ? 'on' : 'off';
                        };
                    } else {
                        chart.options.yaxis.tickFormatter = function (val) {
                            return val + parameter.unit;
                        };
                    }
                }
            }
        }

        if (this.settings.colors) {
            chart.options.colors = this.settings.colors;
        }

        var template = this.getParameterSignalState(
            ui.parameterSelect.val()
        ) ? templates.extended : templates.simple;

        var plot = drawChart(this._widget.uid, chart.data, options);
        var scale = this.chart.scale;

        this.chart.plot = plot;

        var xaxis = plot.getAxes().xaxis;
        var yaxis = plot.getAxes().yaxis;

        scale.xaxis.smin = scale.xaxis.min = xaxis.min;
        scale.xaxis.smax = scale.xaxis.max = xaxis.max;
        scale.yaxis.smin = scale.yaxis.min = yaxis.min;
        scale.yaxis.smax = scale.yaxis.max = yaxis.max;

        ui.indicatorTitle.text(messages[indicator]);
        ui.indicatorSum.html('');

        this.chart.data.flot.forEach(function(data, index){
            var sum = 0;
            var count = 0;
            var time = 0;

            data.data.forEach(function(serie){
                sum += serie[1];
                count++;
            });

            if (indicator === 'avg') {
                sum = Math.round(sum / count);
            }

            if (indicator === 'efficiency') {
                var ef = 0;
                var pf = 0;
                var nf = 0;
                var compound = true;

                var firstEfficiency = chart.raw.data[0].efficiency[0];

                if (firstEfficiency.hasOwnProperty('pf')) {
                    compound = false;
                }

                chart.raw.data[index].efficiency.forEach(function(element) {
                    if (!compound) {
                        pf += element.pf;
                        nf += element.nf;
                    } else {
                        ef += element.ef;
                    }
                });

                if (pf + nf === 0) {
                    if (compound) {
                        ef = Math.round(ef / count);
                    } else {
                        ef = 0;
                    }
                } else {
                    ef = Math.round(pf / (pf + nf) * 100);
                }
                sum = ef + '%';
            }

            if (data.signal) {
                time = data.signal.time;
            }

            ui.indicatorSum.append(window.renderTemplate(template, {
                device: data.label,
                indicator: sum,
                signalDate: time || messages.noinformation
            }));
        });

        ui.indicatorSum.find('p.cursor').tooltipster({
            contentAsHTML: true
        });

        window.colorElements(ui.indicatorSelector, null, true);
    };

    Monitor.prototype.increaseChartSize = function() {
        this.zoomChart(0.5);
        return this._widget.object.trigger(this._widget.events.increased, this);
    };

    Monitor.prototype.decreaseChartSize = function() {
        this.zoomChart(2.0);
        return this._widget.object.trigger(this._widget.events.decreased, this);
    };

    Monitor.prototype.wheelChart = function(event) {
        if (!this.chart.scale.enabled) {
            return;
        }
        var zoom = event.originalEvent.wheelDelta > 0 ? 0.5 : 2.0;
        this.zoomChart(zoom, {animation: false});
        event.preventDefault();
        this._widget.object.trigger(this._widget.events.wheel, this);
    };

    Monitor.prototype.moveChart = function(event) {
        if (!this.chart.scale.enabled) {
            return;
        }
        if (this.chart.scale.pressed) {
            this.ui.chartLayer.css('cursor', 'move');
        } else {
            this.ui.chartLayer.css('cursor', 'pointer');
            return;
        }

        event.preventDefault();

        var plot = this.chart.plot;
        var scale = this.chart.scale;

        var xaxis = plot.getAxes().xaxis;
        var yaxis = plot.getAxes().yaxis;

        var dx = event.originalEvent.movementX * ((xaxis.max - xaxis.min) / plot.width());
        var dy = event.originalEvent.movementY * ((yaxis.max - yaxis.min) / plot.height());

        xaxis.options.min = scale.xaxis.min -= dx;
        xaxis.options.max = scale.xaxis.max -= dx;
        yaxis.options.min = scale.yaxis.min += dy;
        yaxis.options.max = scale.yaxis.max += dy;

        plot.setupGrid();
        plot.draw();
    };

    Monitor.prototype.pressedChart = function() {
        this.chart.scale.pressed = true;
    };

    Monitor.prototype.releasedChart = function() {
        this.chart.scale.pressed = false;
    };

    Monitor.prototype.zoomChart = function(zoom) {
        var plot = this.chart.plot;
        var scale = this.chart.scale;
        var xaxis = plot.getAxes().xaxis;
        var yaxis = plot.getAxes().yaxis;

        if (scale.progress) {
            scale.progress.velocity('stop');
        }

        scale.xaxis.delta2 = (scale.xaxis.max - scale.xaxis.min) / 2;
        scale.yaxis.delta2 = (scale.yaxis.max - scale.yaxis.min) / 2;

        scale.xaxis.center = scale.xaxis.min + scale.xaxis.delta2;
        scale.yaxis.center = scale.yaxis.min + scale.yaxis.delta2;

        scale.progress = true;
        scale.progress = $('<div>');
        scale.progress.velocity({
            tween: [scale.zoom * zoom, scale.zoom]
        }, {
            easing: 'swing',
            duration: 500,
            progress: function(elements, c, r, s, t) {
                xaxis.options.min = scale.xaxis.center - scale.xaxis.delta2 * t;
                xaxis.options.max = scale.xaxis.center + scale.xaxis.delta2 * t;
                yaxis.options.min = scale.yaxis.center - scale.yaxis.delta2 * t;
                yaxis.options.max = scale.yaxis.center + scale.yaxis.delta2 * t;
                scale.zoom = t;
                plot.setupGrid();
                plot.draw();
            },
            complete: function() {
                scale.xaxis.min = xaxis.options.min;
                scale.xaxis.max = xaxis.options.max;
                scale.yaxis.min = yaxis.options.min;
                scale.yaxis.max = yaxis.options.max;
                scale.zoom = 1;
                scale.progress = null;
            }
        });
    };

    Monitor.prototype.resetChartSize = function() {
        this.redrawChart();
        return this._widget.object.trigger(this._widget.events.reset, this);
    };

    Monitor.prototype.setPresentationMode = function(options) {
        var ui = this.ui;
        var title = this.ui.layers.title;
        var settings = this.settings;
        var templates = this.templates;

        if (options.progressBar === false) {
            ui.layers.progressBar.hide();
        }

        if (options.chartControl === false) {
            ui.layers.chartControl.hide();
        }

        if (options.mainOptions === false) {
            ui.layers.mainOptions.hide();
        }

        if (options.indicatorMessage === false) {
            ui.layers.indicatorMessage.remove();
        }

        if (options.color === true) {
            settings.presentation.color = true;
        }
        if (options.titleDate === true) {
            if (settings.presentation.titleDate === false) {
                settings.presentation.titleDate = true;
                title.html(window.renderTemplate(templates.titleDate, {
                    current: title.html()
                }));
            }

            this.updateTitle();
        }

        if (options.widgetButtons === false) {
            this._widget.object.find('.title a.button').remove();
            ui.layers.title.addClass('fluid-width');
        }
    };

    Monitor.prototype.showMessages = function() {
        var data = this.chart.raw.data;
        var alerts = this.ui.layers.alerts;
        var template = this.templates.warning;

        if (data instanceof Array === false) {
            return;
        }

        alerts.find('div.warning').remove();

        data.forEach(function(item) {
            if (item.message) {
                alerts.append(renderTemplate(template, {
                    id: item.device.id,
                    message: item.message.text
                }));
            }
        });
    };
    /**
     * Get Widget Data
     * @returns {Object}
     */
    Monitor.prototype.getData = function() {
        return {
            uid: this._widget.uid,
            size: this._widget.size,
            type: this._widget.type,
            chartType: this.chart.type,
            chartSteps: this.chart.steps,
            chartPoints: this.chart.points,
            devices: this.ui.devicesSelect.val(),
            parameter: this.settings.parameter.id,
            interval: this.chart.interval,
            indicator: this.settings.indicator.type,
            shifts: this.settings.shiftSwitcher
        };
    };

    return Scope;
})(window.I2M || {});

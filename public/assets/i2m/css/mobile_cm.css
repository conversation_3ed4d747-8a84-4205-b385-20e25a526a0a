.content-wrap {
    padding:70px 0;
}

.yay-hide .yaybar.yay-shrink.yay-hide-to-small~footer {
    margin-left: 0px; 
}

.card {
    padding:0px;
}

html {
    zoom:1!important;
}

.row {
    margin-right:0px;
}

.row .col {
    padding-right:0px;
}

#cm .card .title .button {
    width:25px;
    margin:0 5px;
}

#cm .card .title .button > i {
    font-size: 2rem;
}

#cm .card .title .spacer {
    display:none;
}

footer {
    text-align:center;    
}

td .btn {
    height: 55px;
    line-height:55px;
    margin: 2px;
}

.table {
    overflow-x: auto;
    display: block;
}

.fc-view-container {
    overflow-x: auto;
    display: block;
}

#cm.yay-hide .yaybar.yay-hide-to-small {
    left:-240px;
}

#cm .clients-table_previous {
    display:none;
}

#cm .clients-table_next {
    display:none;
}

.dataTables_wrapper .dataTables_paginate .paginate_button {
    padding: 0.5em 0.5em;
    margin-left:0px;
}

#cm .clients-table_length {
    display:none;
}

#cm .help-fixed {
    display:none;
}

#cm .chat-fixed {
    display:none;
}

#cm .indicator {
    display:none;
}

.yaybar .nano-content>ul li>.sp-cancel, .yaybar .nano-content>ul li>a {
    font-size: 1.5rem;
    padding: 20px;
}

.yaybar {
    width:100%;
}

.yay-toggle {
    height: 90px;
}

.navbar-top {
    height: 90px;
    line-height: 90px;
}

.yay-toggle {
    padding-top:40px;
}

.yay-toggle div {
    width:30px;
}

.yaybar .top {
    height:90px;
}

.content-wrap {
    min-height: -webkit-calc(100vh - 90px);
    min-height: calc(100vh - 90px);
    padding-top: -webkit-calc(90px + 1.3rem);
    padding-top: calc(90px + 1.3rem);
}

.modal {
    width: 100%;
    height:80%;
}

.tw-1 {
    min-width:80px;
}

.tw-2 {
    min-width:80px;
}

.tw-3 {
    min-width:80px;
}

.tw-4 {
    min-width:80px;
}

.tw-5 {
    min-width:80px;
}

.tw-6 {
    min-width:80px;
}

.tw-7 {
    min-width:80px;
}

.tw-8 {
    min-width:80px;
}

button a {
    zoom:1.2;
}

.navbar-top .brand-logo>img {
    max-width:120px;
    height:auto;
    
}

#cm .dataTables_wrapper .dataTables_filter {
    float:left;
    width: 100%;
}

#cm .tooltipster-base {
    display:none;
}

#cm #toast-container {
    display:none;
}

::-webkit-scrollbar {
    -webkit-appearance: none;
    width: 2px;
}

::-webkit-scrollbar-thumb {
    background-color: rgba(66,165,245,1);
    -webkit-box-shadow: 0 0 1px rgba(255,255,255,.2);
}

#cm .tooltipster-base {
    display:none;
}

@media all and (orientation:landscape) {
    html {
        font-size: 0.8rem;
    }
    
    .nano {
        zoom:1.3;
    }

    .dropdown-content li {
        line-height:inherit;
    }
    .content-wrap {
        padding: 71.9px 0 1.3rem;
    }

    #calendarTrashcan {
        display:none;
    }
    
    .navbar-top {
        height: 45px;
        line-height: 45px;
    }
    
    .yay-toggle {
        height:45px;
        line-height:45px;
        padding-top:15px;
    }
    
    .yaybar .top {
        height: 45px;
    }
}

@media all and (orientation:portrait) {
    #cm .brand-logo {
        padding-top: 35px;
    }
    

    #calendarTrashcan {
        display:none;
    }

    #cm .height-fix {
        height:90px;
        line-height:90px;
    }
    
    #cm .content-wrap {
        padding:60px 0;
    }
}

.widget > .title .button {
    opacity: 1;
}

button a {
    zoom: 1;
    color: #fff;
}

#cm .on-mobile {
    visibility: hidden;
}

#cm .content-wrap {
    margin-left:0px;
}

.navbar-top .user {
    margin:0 25px;
}

#finance-charts {
    overflow-x: auto;
}

#finance-charts, #finance-charts-details, #finance-chart-share-payments-detail {
    overflow-x:auto;
}

#user-data .collapsible-header {
    line-height: 5rem;
    height: 5rem;
    border-bottom: 5px solid #f4f4f4;
    padding-left: 5px;
}

#user-data .collapsible-header i {
    line-height: 5rem;
    height: 5rem;
}

.pika-single.is-bound {
    display:none;
}

#cm .tbl {
        display: table;
}

.no-wrap {
 white-space: nowrap;    
}

@media screen and (max-width: 485px) {
    #cm .tab-fix {
        line-height: 24px;
        white-space: pre-wrap
    }
}

div.infored {
    background-color: red;
    color: white;
}

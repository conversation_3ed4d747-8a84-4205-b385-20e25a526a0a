.card > .title .button {
    width: 20px;
    float: right;
    text-align: center;
    color: inherit;
    transition: transform 300ms ease-in-out;
}

.card > .title .fullscreen-exit.button {
    display: none;
}

.card > .title .spacer {
    width: 10px;
    height: 2.0rem;
    display: inline-block;
}

.card.maximized {
    position: absolute;
    width:  100%;
    height:  100%;
    top:  0px;
    z-index: 1100;
    left: 0;
}

.card .title .button > i {
    font-size: 1.6rem;
    height: 2.0rem;
    line-height: 2.0rem;
}

.card.maximized,
.card.maximized h4,
.card.maximized h5,
.card.maximized .title h5,
.card.maximized .input-field > input,
.card.maximized .select-dropdown,
.card.maximized .title .button > i {
    font-size: 2rem;
}

.card.maximized .fs-10 {
    font-size: 18px !important;
}

.card.maximized .fs-12 {
    font-size: 22px !important;
}

.card.maximized .fs-16 {
    font-size: 30px !important;
}

.card.maximized .fs-18 {
    font-size: 36px !important;
}

.card.maximized .fs-20 {
    font-size: 42px !important;
}

.card.maximized .title h5 {
    font-weight: 700;
}

.card.maximized .title .spacer {
    width: 20px;
}

.card.maximized .fs-11r {
    font-size: 2.1rem;
}

.card.maximized .fs-12r {
    font-size: 2.2rem;
}

.card.maximized .fs-13r {
    font-size: 2.3rem;
}

.card.maximized .fs-14r {
    font-size: 2.4rem;
}

.card.maximized .fs-15r {
    font-size: 2.5rem;
}

.card.maximized .fs-16r {
    font-size: 2.6rem;
}

.card.maximized .fs-17r {
    font-size: 2.7rem;
}

.card.maximized .fs-18r {
    font-size: 2.8rem;
}

.card.maximized .h-60 {
    height: 100px !important;
}

.card.maximized h5.mt-4 {
    margin-top: 20px !important;
}

.card.maximized h5.mt-15 {
    margin-top: 30px !important;
}

/* force hiding content if widget is maximized */
.body-overflow-x-hidden {
    overflow-x: hidden;
}

.widget {
    position: relative;
    overflow: hidden;
}

.widget > .content {
    box-shadow: none;
}

.widget-container {
    padding-right: 0px;
}

.card i.bigicon {
    position: absolute;
    font-size: 10rem;
    bottom: -30px;
    right: -30px;
    opacity: 0.5;
}

.card.maximized i.bigicon {
    font-size: 80rem;
}

.widget > .title .button {
    opacity: 0;
    -webkit-transition: opacity 300ms ease-in-out;
    transition: opacity 300ms ease-in-out;
}

.widget:hover > .title .button {
    opacity: 1;
    -webkit-transition: opacity 300ms ease-in-out;
    transition: opacity 300ms ease-in-out;
}

.widget .cursor:hover {
    color: #000;
}

.card.maximized .switch-container.h-60 {
    height: 100px !important;
}

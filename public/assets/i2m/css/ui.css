/*
    This file contains custom UI elements
*/

/*
    Badge element
*/
.i2m-badge {
    color: white;
    display: inline-block;
    border-radius: 2px;
    border: 0;
    font-size: 0.85rem;
    box-sizing: border-box;
    padding: 1px 7px;
    background-color: #42A5F5;
    border-color: #039be5;
}

.i2m-badge i.left {
    margin-right: 5px;
}

.i2m-badge.corner {
    position: absolute;
    top: 0;
    left: 0;
}

.i2m-badge.corner.right {
    right: 0;
    left: auto;
}

.i2m-badge.corner:after {
    content: "";
    border-top: 0;
    border-left: 0;
    border-bottom: 2.0em solid transparent;
    border-right: 2.0em solid transparent;
    border-right-color: inherit;
    position: absolute;
    top: 0;
    right: 0;
}

/*
    Button
*/
.i2m-button,
label.i2m-button {
    display: inline-block;
    margin: 0;
    padding: 0;
    width: 37px;
    height: 37px;
    line-height: 37px;
    font-size: 1.5rem;
    background: #5ab1f6;
    color: white;
    border: 0;
    border-radius: 100px;
    text-align: center;
    cursor: pointer;
    box-shadow: 0 1px 2px rgba(0,0,0,.26);
}

.i2m-button:hover,
label.i2m-button:hover {
    box-shadow: 0 2px 5px rgba(0,0,0,.4);
}

.i2m-button.i2m-tiny,
label.i2m-button.i2m-tiny {
    width: 24px;
    height: 24px;
    line-height: 24px;
    font-size: 1.0rem;
}

.i2m-button.i2m-small,
label.i2m-button.i2m-small {
    width: 29.6px;
    height: 29.6px;
    line-height: 29.6px;
}

.i2m-button.i2m-large,
label.i2m-button.i2m-large {
    width: 55px;
    height: 55px;
    line-height: 55px;
    font-size: 1.8rem;
}

.i2m-button > i {
    vertical-align: baseline;
}

.i2m-zoom,
.i2m-zoom:hover {
    transition: transform 300ms cubic-bezier(.17,.67,.38,2.0);
}

.i2m-zoom:hover {
    z-index: 1500;
}

.i2m-zoom:hover,
.i2m-zoom.z-2x:hover {
    transform: scale(2);
}

.i2m-zoom.z-12x:hover {
    transform: scale(1.2);
}

.i2m-zoom.z-14x:hover {
    transform: scale(1.4);
}

.i2m-zoom.z-16x:hover {
    transform: scale(1.6);
}


.i2m-button.i2m-corner,
label.i2m-button.i2m-corner {
    position: absolute;
    top: 0;
    right: 0;
    bottom: auto;
    left: auto;
    transform-origin: 100% 0;
    border-bottom-left-radius: 65px;
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
    border-bottom-right-radius: 10px;
    line-height: 32px;
    padding-left: 6px;
}

.i2m-button.i2m-corner.i2m-tiny {
    padding-left: 4px;
    line-height: 1.55rem;
}

.i2m-button.i2m-corner.i2m-small {
    padding-left: 4px;
    line-height: 2.0rem;
}

.i2m-button.i2m-corner.i2m-large {
    line-height: 45px;
}

/*
    Switcher
*/
.i2m-switcher {
    position: relative;
}

.i2m-switcher > label::before {
    content: none;
}

.i2m-switcher > input:not(:checked) + label.i2m-disable {
    background: #ccc !important;
}

.i2m-switcher > input:not(:checked) + label.i2m-disable:hover {
    background: #bbb !important;
}

.i2m-switcher > input:checked + label > .i2m-disabled,
.i2m-switcher > input:not(:checked) + label > .i2m-enabled,
.i2m-switcher > input:not(:checked) ~ .i2m-container:not(.i2m-fold):not(.i2m-slide) > .i2m-disabled {
    display: none;
}

.i2m-container,
.i2m-container > .i2m-enabled,
.i2m-container > .i2m-disabled {
    position: relative;
    overflow: hidden;
    visibility: visible;
    transition: all 300ms ease;
}

.i2m-switcher > input:checked ~ .i2m-container.i2m-disabled:not(.i2m-opacity):not(.i2m-fold),
.i2m-switcher > input:not(:checked) ~ .i2m-container.i2m-enabled:not(.i2m-opacity):not(.i2m-fold) {
    display: none;
}

.i2m-switcher > input:checked ~ .i2m-container.i2m-disabled.i2m-opacity,
.i2m-switcher > input:not(:checked) ~ .i2m-container.i2m-enabled.i2m-opacity {
    opacity: 0;
    visibility: hidden;
}

.i2m-switcher > input:checked ~ .i2m-container.i2m-disabled.i2m-fold,
.i2m-switcher > input:not(:checked) ~ .i2m-container.i2m-enabled.i2m-fold {
    height: 0 !important;
}

.i2m-switcher > input:checked ~ .i2m-container.i2m-fold:not(.i2m-disabled):not(.i2m-enabled) > .i2m-disabled {
    height: 0;
}

.i2m-switcher > input:checked ~ .i2m-container.i2m-slide:not(.i2m-disabled):not(.i2m-enabled) > .i2m-disabled,
.i2m-switcher > input:checked ~ .i2m-container.i2m-slide:not(.i2m-disabled):not(.i2m-enabled) > .i2m-enabled {
    transform: translateY(-100%);
}

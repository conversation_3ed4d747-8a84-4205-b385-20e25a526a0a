.btn-floatingx {
    border-radius: 50%;
    border: none;
    color: #fff;
}

@media screen and (max-width: 480px) {
    .yay-hide .yaybar.yay-hide-to-small {
        left:-240px;
    }
    
    .content-wrap {
        margin-left:0!important;
    }
}

@media screen and (min-width: 1101px) {
  .btn-floatingx {
    width:80px;
    height:80px;
    }

.btn-floatingx i {
    font-size: 3rem;

    }
}

@media screen and (max-width: 1100px) {
    .btn-floatingx {
     width:80px;
     height:80px;
    }

    .btn-floatingx i {
        font-size: 3rem;
    }
  }

@media screen and (max-width: 800px) {
    .arrow {
        width: 280px;
        top: 50%;
    }

    .btn-floatingx {
        width:60px;
        height:60px;
    }

    .btn-floatingx i {
        font-size: 3rem;

    }
}

@media screen and (min-width: 1101px) {
  .btn-floatingx i {
      font-size: 3rem;

    }
}

@media screen and (max-width: 1100px) {
    .btn-floatingx {
        width:80px;
        height:80px;
    }
}

@media screen and (max-width: 800px) {
    .btn-floatingx {
        width:60px;
        height:60px;
    }

    .btn-floatingx i {
        font-size: 3rem;

    }
}

/*
*Loader
*/

#loading-spinner {
    z-index: 1000;
}

.spinner-fullscreen {
    z-index: 1000;
    position: fixed;
    top: 50%;
    left: 50%;
}

.page-break {
    page-break-after: always;
}

.absolute {
    position: absolute;
}

.fixed {
    position: fixed;
}

.relative {
    position: relative;
}

.z-100 {
    z-index: 100;
}

.z-500 {
    z-index: 500;
}

.z-1000 {
    z-index: 1000;
}

.chat-fixed {
    position:fixed;
    top:0;
    right:190px;
    height: 65px;
    width: 70px;
    text-align: center;
}

.help-fixed {
    position:fixed;
    top:0;
    right:260px;
    height: 65px;
    width: 70px;
    text-align: center;
}

.help-fixed:hover, .chat-fixed:hover {
    background-color: rgba(0,0,0,.04);
    -webkit-transition-duration: 0.7s; /* Safari */
    transition-duration: 0.7s;
}

.btn-trans {
    background-color: transparent;
    height: 100%;
    line-height: inherit;
    box-shadow: 0 0px 0px rgba(0,0,0,0)!important;
}

.btn-trans:hover {
    background-color:rgba(0,0,0,.1);
}

.form-error {
    margin-top: -10px;
} 

.colorTd {
    text-align: center;
}

.colorTd .btn-floating {
    cursor: auto !important;
}

.bottomOffset {
    padding-bottom: 50px;
}

#menu {
    position: fixed;
    right: 0;
    top: 0;
}

.clickable {
    cursor: pointer;
}

.shift-icon {
    position: absolute;
    top: 4px;
    right: 10px;
    padding-left: 5px;
}

/*
 * CON overriding styles
 */

/* New background color */
.content-wrap {
    background-color: #eaeaea;
}

.bg-white {
    background-color: white;
}

/* Most correct centering horizontally & z-index */
#toast-container {
    z-index: 3000;
    top: 10% !important;
    right: auto !important;
    left: 50%;
    -webkit-transform: translateX(-50%);
    -moz-transform: translateX(-50%);
    -ms-transform: translateX(-50%);
    transform: translateX(-50%);
}

/* Correct rendering full calendar buttons */
.fc button {
    padding-left: 1.5rem !important;
    padding-right: 1.5rem !important;
    height: 2.5rem !important;
    font-size: 0.9rem !important;
}

.fc-copy-icon {
    position:absolute;
    z-index: 2;
    top: 5px;
    right: 5px;
    display: none;
}

.fc-event {
    margin-bottom: 1px;
    border-width: 2px !important;
    height: auto !important;
    min-height: 32px;
}

#externalItems .event-row .fc-event {
    border: 0 !important;
}

/* Correct rendering DataTables header options */
.dataTables_wrapper .dataTables_filter {
    float: none !important;
}

/* Correct rendering DataTables pagination buttons */
.dataTables_wrapper .dataTables_paginate .paginate_button.disabled {
    opacity: 0.3;
}

/* Spectrum overriding styles */
.sp-preview {
    width: 25px !important;
    height: 25px !important;
    border: 0 !important;
}

.sp-replacer {
    background-color: transparent !important;
    border: 0 !important;
}

.sp-preview, .sp-alpha, .sp-thumb-el {
    border: 0 !important;
    background-image: none !important;
}

.sp-container {
    border: 0 !important;
    border-radius: 3px !important;
    background-color: #fff !important;
}

.sp-picker-container, .sp-palette-container {
    border: 0 !important;
}

.sp-choose {
    background-color: #42A5F5 !important;
    background-image: none !important;
    color: #fff !important;
    border: 0 !important;
    border-radius: 3px !important;
    text-shadow: none !important;
    font-size: 1em !important;
    padding: 1px 10px !important;
    text-transform: uppercase !important;
}

.sp-cancel {
    display: none;
}

/* Disable Webkit native datepicker */
input::-webkit-calendar-picker-indicator {
    display: none;
}

input[type="date"]::-webkit-input-placeholder {
    visibility: hidden !important;
}

/* Correct Pikaday rendering styles */
.pika-button {
    background: #fff !important;
    text-align: center !important;
    font-size: 1.1em !important;
}

.is-selected .pika-button {
  background-color: #42A5F5 !important;
}

/* Correct Clockpicker rendering styles */
.clockpicker-button {
    padding: 0 !important;
    color: white !important;
    background-color: #42A5F5 !important;
    border: 0 !important
}

.clockpicker-popover {
    padding: 5px !important;
    position: fixed !important;
    top: 10% !important;
}

/* Sortable rendering styles */
.sortable-ghost {
    opacity: 0.5 !important;
    border: none !important;
}

.sortable-ghost-shadow {
    opacity: 0.1 !important;
    border: none !important;
    background: #000;
    border-radius: 5px;
}

.sortable-ghost-shadow * {
    visibility: hidden;
}

.sortable-fallback {
    opacity: 1.0 !important;
}

.timeline-dragged-content-right {
    float: right;
}

.timeline-dragged-content-right::before {
    left: auto !important;
    right: 100% !important;
    border-color: transparent !important;
    border-right-color: #fff !important;
}

.timeline-dragged-content-right .timeline-date {
    left: auto !important;
    right: 122% !important;
    text-align: right !important;
}

/* Tooltipster rendering styles */
.tooltipster-base {
    border-radius: 3px;
    border: none;
}

/* Shifttypes table column: color styles */
#shifttypes-table tbody tr td:nth-child(4) {
    text-align: center;
}

/* EventTypes simple color picker */
#simple-color-picker{
    position: absolute;
    display: none;
    z-index: 1006;
    width: 280px;
    height: 205px;
    background-color: #fff;
    padding: 5px;
}

#picked-color, #simple-color-picker-button {
    cursor: pointer;
}

#simple-color-picker .color {
    background-position: 50% 50%;
    background-repeat: no-repeat;
    width: 25px;
    height: 25px;
    margin: 7px;
}

#simple-color-picker-view {
    width: 50px !important;
    height: 50px !important;
    border-radius: 50% !important;
}

#simple-color-picker-view > div {
    margin-top: 50px;
    width: 50px !important;
    height: 50px !important;
}

#picked-color {
    margin-left: 30px;
    margin-top: 10px;
    margin-bottom: 10px;
}

/* EventTypes simple icon picker */
#simple-icon-picker {
    position: absolute;
    display: none;
    z-index: 1006;
    width: 836px;
    height: 368px;
    background-color: #fff;
    padding: 5px;
}

#simple-icon-picker .row {
    margin-left: 10px;
}


#simple-icon-picker .row .col .row > i {
    font-size: 32px;
    cursor: pointer;
}

#simple-icon-picker .row .col .row > i:hover:before {
    color: #dadada;
    -webkit-transition: all .3s ease-out;
    transition: all .3s ease-out;
}

#simple-icon-picker-view {
    margin-left: 90px;
    margin-top: 120px;
}

#simple-icon-picker-view > i {
    font-size: 64px;
}

#picked-icon {
    font-size: 3rem;
    margin-left: 2.5rem;
}

/* Event files styling */
.event-image {
    max-width: 100%;
}

.event-video {
    width: 640px;
    height: 480px;
}

/* Slide up btn menu
   to add new item to menu You have to add new classes like here:
.fixed-action-btn.one{ <-- instead of .one You can make .two and set bottom to 130px (add 65px)*/

.fixed-action-btn ul {
 bottom:44px;
}

.fixed-action-btn {
    bottom: 45px;
    right: 24px;
}

.op {
    opacity:0;
}

.fixed-action-btn.one {
    bottom:65px;
    margin-bottom:10px;
    display:none;
}

.change-icon > .fa:nth-child(2) {
  display: none;
}

.change-icon:hover > .fa:nth-child(1) {
  display: none;
}

.change-icon:hover > .fa:nth-child(2) {
  display: inherit;
}

#page-error {
    font-size: 5rem;
    line-height: 5rem;
    text-align: center;
}

#page-error-text {
    font-size: 2rem;
    line-height: 2rem;
    text-align: center;
}

#subtext-error {
    font-size: 2rem;
    line-height: 2rem;
    text-align: center;
}

.error-x .content-wrap {
    padding: 0 1.3rem 1.3rem;
    min-height: -webkit-calc(100vh - 70px);
    min-height: calc(100vh - 70px);
    margin: 0;
    background-color: white;
}

.error-x .search {
    display:none;
}

.error-x .header.clear-fix {
    display: none;
}

.error-x .container {
    padding-top: 10rem;
}

#notPlannedEvents:empty {
    display:none;
}

.hide-me {
    visibility:hidden;
}

.collapse-me {
    display: none
}

.hand-cursor {
  cursor: -webkit-grab;
  cursor: -moz-grab;
  cursor: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAQAAADZc7J/AAAABGdBTUEAALGPC/xhBQAAACBjSFJNAAB6JgAAgIQAAPoAAACA6AAAdTAAAOpgAAA6mAAAF3CculE8AAAAAmJLR0QA/4ePzL8AAABwSURBVEjH7ZJBEsAgCAMT/v/n9NCOSqe2oD2yNx1JggB4BCEFWyFASP2KMQE7ywWhe/tTRGCGogLk02tFctiW/SUgaMyQG4PdPzDn31rQbMb8FiAXgvsEJNax1yVlVGAjA93apP3HFhZTGIqiKH7iADB6HxPlHdNVAAAAJXRFWHRkYXRlOmNyZWF0ZQAyMDE0LTA3LTA3VDEzOjQ5OjEwKzAyOjAwm7WiFAAAACV0RVh0ZGF0ZTptb2RpZnkAMjAxNC0wNy0wN1QxMzo0OToxMCswMjowMOroGqgAAAAASUVORK5CYII="), move;
}

.dt-buttons .dt-button {
    height: 36px;
    padding: 0 2rem;
    line-height: 36px;
}

.dt-buttons {
    display: inline;
}

.buttons-html5 {
    margin-top:4px;
    margin-bottom:4px;
}

button.dt-button, div.dt-button, a.dt-button {
    background-color:transparent;
}

#campaign-table .odd.selected tr {
    background-color:#fff!important;
}

.margin-right {
    margin-right:20px;
}

.margin-left {
    margin-left:20px;
}

.margin-top {
    margin-top:20px;
}

.margin-bottom {
    margin-bottom:20px;
}

.huge-margin-top {
    margin-top:60px;
}

/*
 * Helper classes
 */

.opacity-100 {
    opacity: 1;
}

.opacity-75 {
    opacity: 0.75;
}

.opacity-50 {
    opacity: 0.5;
}

.opacity-25 {
    opacity: 0.25;
}

.opacity-0 {
    opacity: 0;
}

.opacity-100,
.opacity-75,
.opacity-50,
.opacity-25,
.opacity-0 {
    transition: opacity .25s ease-in-out;
    -moz-transition: opacity .25s ease-in-out;
    -webkit-transition: opacity .25s ease-in-out;
}

.fs-08r {
    font-size: 0.8rem;
}

.fs-09r {
    font-size: 0.9rem;
}

.fs-10r {
    font-size: 1.0rem;
}

.fs-11r {
    font-size: 1.1rem;
}

.fs-12r {
    font-size: 1.2rem;
}

.fs-13r {
    font-size: 1.3rem;
}

.fs-14r {
    font-size: 1.4rem;
}

.fs-15r {
    font-size: 1.5rem;
}

.fs-16r {
    font-size: 1.6rem;
}

.fs-17r {
    font-size: 1.7rem;
}

.fs-18r {
    font-size: 1.8rem;
}

.fs-19r {
    font-size: 1.9rem;
}

.fs-20r {
    font-size: 2.0rem;
}

.d-ib {
    display: inline-block !important;
}

.d-b {
    display: block !important;
}

.d-i {
    display: inline !important;
}

.ml-100 {
    margin-left: 100px;
}

.mr-100 {
    margin-right: 100px;
}

.mt-100 {
    margin-top: 100px;
}

.mb-100 {
    margin-bottom: 100px;
}

.m-100 {
    margin: 100px;
}

.ml-180 {
    margin-left: 180px;
}

.mr-180 {
    margin-right: 180px;
}

.mt-180 {
    margin-top: 180px;
}

.mb-180 {
    margin-bottom: 180px;
}

.m-180 {
    margin: 180px;
}

.ml-200 {
    margin-left: 200px;
}

.mr-200 {
    margin-right: 200px;
}

.mt-200 {
    margin-top: 200px;
}

.mb-200 {
    margin-bottom: 200px;
}

.m-200 {
    margin: 200px;
}

.l-auto {
    left: auto;
}

.l-0 {
    left: 0;
}

.l-10 {
    left: 10px;
}

.l-20 {
    left: 20px;
}

.l-30 {
    left: 30px;
}

.l-40 {
    left: 40px;
}

.l-50 {
    left: 50px;
}

.l-60 {
    left: 60px;
}

.l-80 {
    left: 80px;
}

.l-50p {
    left: 50%;
}

.t-50p {
    top: 50%;
}

.r-auto {
    right: auto;
}

.r-0 {
    right: 0;
}

.r-10 {
    right: 10px;
}

.r-20 {
    right: 20px;
}

.r-26 {
    right: 26px;
}

.r-28 {
    right: 28px;
}

.r-30 {
    right: 30px;
}

.r-40 {
    right: 40px;
}

.r-50 {
    right: 50px;
}

.r-60 {
    right: 60px;
}

.r-70 {
    right: 70px;
}


.r-80 {
    right: 80px;
}

.w-10p {
    width: 10%;
}

.w-20p {
    width: 20%;
}

.w-30p {
    width: 30%;
}

.w-40p {
    width: 40%;
}

.w-50p {
    width: 50%;
}

.w-60p {
    width: 60%;
}

.w-70p {
    width: 70%;
}

.w-80p {
    width: 80%;
}

.w-90p {
    width: 90%;
}

.w-100p {
    width: 100%;
}

.h-80 {
    height: 80px;
}

.h-90 {
    height: 90px;
}

.h-120 {
    height: 120px;
}

.h-140 {
    height: 140px;
}

.h-160 {
    height: 160px;
}

.h-180 {
    height: 180px;
}

.over-visible {
    overflow: visible;
}

.over-hidden,
.modal-content {
    overflow: hidden;
}

.valign-top {
    vertical-align: top;
}

.valign-middle {
    vertical-align: middle;
}

.valign-bottom {
    vertical-align: bottom;
}

.postfix ~ input {
    width: calc(100% - 3.0rem);
}

.postfix.long ~ input {
    width: calc(100% - 8.0rem);
}

.postfix {
    right: 0;
    margin-top: 0.6rem;
    position: absolute;
}

.grey-border {
    border: 1px solid #eee;
}

/*
*Mockup styles
*/
@media only screen and (max-width : 1300px) {
    .collapsible-header {
        padding:0;
        line-height:2rem;
        min-height:2rem;
        overflow:hidden;
    }
}

/*.collapsible-header {*/
    /*padding: 0 !important;*/
    /*line-height:1rem;*/
    /*min-height:1rem;*/
    /*overflow:hidden;*/
/*}*/

#modalsContainer .collapsible-header {
    line-height: 3rem;
}

#modalsContainer .collapsible-header i {
    width: 6rem;
}

#modalsContainer .collapsible {
    box-shadow: 0 0 0 !important;
}

#modalsContainer .collapsible-header {
    box-shadow: 0 1px 2px rgba(0, 0, 0, .1);
}

.collapsible-header .col {
    padding:0;
}

.status-work{
    background:#4CAF50!important;
}

.status-warning {
    background:#2196F3!important;
}

.status-alarm {
    background:#F44336!important;
}

.status-table td {
    text-align:center!important;
}


.card i.big-icon {
    position: absolute;
    font-size: 7rem;
    bottom: 10px;
    right: 0;
    opacity: 0.5;
}

.card i.big-iconx {
    position: absolute;
    font-size: 7rem;
    bottom: 10px;
    right: 20px;
    opacity: 0.5;
}

.card i.small-iconx {
    position: absolute;
    font-size: 2.5rem;
    top: 0;
    bottom: 0;
    right: 0;
    opacity: 0.5;
    }

.tabs .tab {
    letter-spacing:0;
}

.collapsible {
   border:0;
   margin:0;
   box-shadow:0 0 0;

}

.tabs-vert {
    display: inline;
    float: none!important;
    text-align: left!important;
    line-height: 20px!important;
    height: 20px!important;
    color: #000 !important;
}

.tabs-vert .indicator {
    display:none;
}

.alarm-icon {
    position: absolute;
    top: 15%;
    left: 2%;
}

.max-width{
    max-width:900px;
}

.small-max-width {
    max-width: 10%;
}

.bold {
    font-weight:600;
}

.mt-minut-45 {
    margin-top:-45px;
}

@media screen and (max-width: 800px) {
    .mt-minut-45 {
        margin-top:0px;
    }
 }

.valign-wrapper {
    display:flex!important;
}

 .cursor-move {
    cursor:move;
}

.cursor-pointer {
    cursor:pointer!important;
}

input[type=file]::-webkit-file-upload-button {
 visibility: hidden;
}

.pickit {
     margin-top: 1rem;
     margin-bottom: -1rem;
}

#traces-text {
   display:block!important;
}

/* Help Bar */
.help-bar {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  height: 100%;
  overflow: hidden;
  z-index: 2000;
}

.help-bar > .layer-overlay {
    background: #fff;
    -webkit-transform: scale(0);
    -ms-transform: scale(0);
    transform: scale(0);
}

.help-bar > .layer-content {
    position: relative;
    padding: 1.3rem 70px;
    background: #fff;
    display: none;
    overflow: auto;
    height: 100%;
}

@media only screen and (max-width : 992px) {
    .help-bar > .layer-content {
        padding: 1.3rem;
    }
}

.help-bar > .layer-content .search-bar-toggle {
    position: absolute;
    font-size: 3rem;
    right: 5rem;
    top: .5rem;
    z-index: 10;
}

.help-bar > .layer-content .search-bar-toggle {
  position: absolute;
  font-size: 3rem;
  right: 5rem;
  top: .5rem;
  z-index: 10;
}

@media only screen and (max-width : 992px) {
    .help-bar > .layer-content .search-bar-toggle {
      right: 1rem;
    }
}

.help-bar > .layer-content .input-field {
    margin-top: 4rem;
}

.help-bar > .layer-content .input-field .prefix {
    text-align: center;
    font-size: 5rem;
    width: 5rem;
}

.help-bar > .layer-content .input-field .prefix ~ input {
    margin-left: 6rem;
    width: 91%;
    width: -webkit-calc(100% - 6rem);
}

.help-bar > .layer-content .input-field input {
    font-size: 5rem;
    height: 7rem;
    margin: 0 0 20px;
}

@media only screen and (max-width : 992px) {
    .help-bar > .layer-content .input-field .prefix {
      font-size: 3rem;
      width: 3rem;
    }


    .help-bar > .layer-content .input-field .prefix ~ input {
      margin-left: 4rem;
      width: 91%;
      width: -webkit-calc(100% - 4rem);
      width: calc(100% - 4rem);
    }

    .help-bar > .layer-content .input-field input {
          font-size: 3rem;
          height: 4rem;
          margin: 0 0 20px;
    }
}

.help-bar > .layer-content .search-results h4 {
    margin-bottom: 2rem;
    text-transform: uppercase;
}

.help-bar > .layer-content .search-results .each-result {
    cursor: pointer;
    padding-top: 5px;
    padding-bottom: 25px;
    padding-right: 10px;
}

.help-bar > .layer-content .search-results .each-result img {
    max-width: 55px;
    vertical-align: middle;
    margin-right: 1rem;
    float: left;
}

.help-bar > .layer-content .search-results .each-result .title {
    display: inline-block;
    font-size: 1.4rem;
    font-weight: bold;
    margin-bottom: .4rem;
}

.help-bar > .layer-content .search-results .each-result .icon {
    width: 55px;
    height: 55px;
    line-height: 55px;
    font-size: 1.2rem;
    text-align: center;
    vertical-align: middle;
    margin-right: 1rem;
    float: left;
}

.help-bar > .layer-content .search-results .each-result .nowrap {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.fc-event-container .past-event {
    background-color: #999!important;
    cursor: not-allowed;
}

.fc-event-container .past-event:parent {
    background-color: #999!important;
    cursor: not-allowed;
}

.btn-dev {
    border: none;
    cursor:pointer!important;
    color: #fff;
    padding: 3px 10px;
    border-radius: 4px;
    cursor: default;
    float: left;
    margin-right: 5px;
    margin-top: 5px;
    background-color: rgb(0, 145, 234);
    -webkit-transition: all .3s ease-out;
    transition: all .3s ease-out;
}

.btn-dev:hover {
    box-shadow: 0 2px 5px rgba(0,0,0,.4);
}

#spinner-cm {
    position:absolute;
    top:80%;
    left: 50%;
}

.circle {
  bottom: 10px;
  -webkit-transition:all 0.2s linear;
  -moz-transition:all 0.2s linear;
  -ms-transition:all 0.2s linear;
  transition:all 0.2s linear;
}

.first_circle {
  z-index: 3;
}

.second_circle {
  z-index: 2;
}

.contener_circle:hover .second_circle {
  bottom:75px;
}

.contener_circle {
  height: 70px;
  width: 60px;
}

.contener_circle:hover {
  height: 200px;
  width: 60px;
}

#externalItems .col {
    padding-bottom: 0;
}

td .btn {
    letter-spacing:0px;
    width:55px;
    padding: 0;
}

.tw-1 {
    width:5%!important;
}

.tw-2 {
    width:10%!important;
}

.tw-3 {
    width:15%!important;
}

.tw-4 {
    width:20%!important;
}

.tw-5 {
    width:25%!important;
}

.tw-6 {
    width:30%!important;
}

.tw-7 {
    width:35%!important;
}

.tw-8 {
    width:40%!important;
}

#eventtypes-table tr td:nth-child(3) {
    text-align:center
}

#eventtypes-table tr td:nth-child(4) {
    text-align:center
}

.tooltipster-content .fa-pencil:hover {
    color: #009688;
    cursor: pointer;
}

.tooltipster-content .fa-eye:hover {
    color: #ffc107;
    cursor: pointer;
}

.tooltipster-content .fa-check:hover {
    color: #00bcd4;
    cursor: pointer;
}

.tooltipster-content .fa-times:hover {
    color: #EF5350;
    cursor: pointer;
}

.thumbnail-200 {
    max-width: 200px;
    max-height: 200px;
}

.thumbnail-150 {
    max-width: 150px;
    max-height: 150px;
}

.pdf-table {
    page-break-inside: avoid !important;
}

.keyusage-list-buttons {
    cursor: pointer;
    margin-top: 10px;
}

.keyusage-list-buttons .keyusage-icon {
    width: 30px !important;
    height: 30px !important;
    font-size: 16px;
    line-height: 1.75;
    background: #42A5F5;
    padding: 1px !important;
    border-radius: 50%;
    margin: -10px 6px -18px 0;
    color: white;
    transition: .2s ease-out;
    cursor: pointer;
}

.card i.medium-iconx {
    position: absolute;
    font-size: 5rem;
    right: 30px !important;
    bottom: 8px !important;
    opacity: 0.5;
}

.addSelectButton {
    position: absolute;
    right: 0px;
    margin-top: 8px;
}

/* Select2 overriding styles */
.select2-container {
    margin-bottom: 15px;
}

.select2-dropdown {
    z-index: 2000;
}

.select2-selection {
    background: transparent !important;
}

.select2-search__field {
    border: 0px !important;
    padding-left: 0px !important;
    background-color: transparent !important;
}

.select2-results__group {
    font-weight: 900;
}

/* autogenerated form in modals */
form.autogenerated-form .select2-container,
i.prefix + select.select2 + .select2-container {
    left: 3rem;
    width: calc(100% - 3rem) !important;
}

form.autogenerated-form input,
form.autogenerated-form textarea,
form.autogenerated-form table {
    margin-left: 3rem;
    width: calc(100% - 3rem);
}

form.autogenerated-form h2 {
    margin-bottom: 40px;
}

.keyusage-list-buttons .keyusage-icon {
    width: 30px !important;
    height: 30px !important;
    font-size: 16px;
    line-height: 1.75;
    background: #42A5F5;
    padding: 1px !important;
    border-radius: 50%;
    margin: -10px 6px -18px 0;
    color: white;
    transition: .2s ease-out;
    cursor: pointer;
}

.light-grey {
    background-color: #EEEEEE !important;
}

#emptyExternalCalanderItemsList {
    display: none;
}

.sp-preview {
    margin: 0.5rem 0 0.5rem 2.5rem;
}

#div_carwash_keyusage div.sort {
    cursor: pointer;
    position: relative;
}

#div_carwash_keyusage div.sort.active.desc:after {
    position: absolute;
    border-bottom: 5px solid #5F5F5F;
    content: '';
    height: 0;
    width: 0;
    top: 20%;
    right: 10%;
    border: 5px solid transparent;
    border-top: 5px solid #5F5F5F;
}

#div_carwash_keyusage div.sort.active.asc:after {
    content: '';
    position: absolute;
    top: 0%;
    right: 10%;
    height: 0;
    width: 0;
    border: 5px solid transparent;
    border-bottom: 5px solid #5F5F5F;
}

@media screen and (max-width: 1000px) {
    .dataTable {
        overflow-x: auto;
        display: block;
    }
}

button a {
    color: #fff;
}

/* Preloader */

#preloader {
    position:fixed;
    top:0;
    left:0;
    right:0;
    bottom:0;
    background-color:#fff;
    z-index:9999;
}

.loader-position {
    position: absolute;
    left: 45%;
    top: 100px;
}

.progress-content {
    position:relative;
}

.preload-cm {
    position: fixed;
    top: 50%;
    left: 55%;
    margin-top: -50px;
    z-index:0;
}

.preload-cm.active {
    z-index:1040!important;
}

.progress-content-field {
    position: absolute;
}

.preload-cm-field {
    position: absolute;
    left: calc(50% - 30px);
    z-index:0;
}

.preload-cm_field.active {
    z-index:1000 !important;
}

.s2-color-input {
    color:#42A5F5 !important;
}

.color-label {
    color:#42A5F5 !important;
}

/* Dropzone profile image */
.dropzone.profile-image {
    border: none;
    padding: 0px;
    width: 140px;
    height: 140px;
    min-height: 140px;
}

.dropzone.profile-image .dz-preview {
    margin: 0px;
}

.dropzone.profile-image img {
    border-radius: 50% !important;
}

/* Main progress bar */
.progress.main-progress {
    z-index: 10000;
    margin: 0;
    background-color: transparent;
    display: none;
    position: fixed;
    top: 0;
}

.dataTables_filter {
   display: none!important;
}

.dataTables_length {
    display:none!important;
}

.switch {
    padding-top:15px;
}

#searchbox {
    height: 2rem!important;
    line-height: 2rem!important;
}

.search-fix {
    top:0!important;
}

.switch label .lever {
    margin: 0 0px;
}

#loading-spinner .preload-cm {
    left: 40%!important;
    position: absolute!important;
    top: 39% !important;
    margin-top:0px!important;
}

.switch label {
    margin: 0 5px;
}

#preloader .preload-cm {
    left: 0;
    right: 0;
    margin: 5% auto;
    zoom: 2;
    top: 50%;
    margin-top: 0;
}

.remove-tag {
    font-size: 18px;
    float: left;
    margin-right: 10px;
}

.tag-element {
    margin-left: 5px;
}

#filesCollection  li {
    height: 60px;
}

.circle-small {
    height: 25.2px;
    line-height: 25px;
    font-size: .8rem;
}

#filesCollection  li i {
    top: 9px;
}

#filesCollection  li p {
    margin-top: 9px;
}

#formRow form.autogenerated-form table {
    margin-left: 0;
    width: 100%;
}

#formRow .collapsible-header {
    line-height: 3rem !important;
}

@keyframes progress-animation {
    0% {
        width: 0%;
    }
    100% {
        width: 100%;
    }
}

.progress > .animate {
    animation: progress-animation 1s linear 1;
    animation-fill-mode: both;
}

/* Flex styles */
.row.flex {
    display: flex;
}

.yay-hide .yaybar.yay-hide-to-small {
    position:fixed;
}

/*Language flags*/

.fixed-country {
    position: absolute;
    right: 5%;
    top: 5%;
    width: 55px;
}

.flag-img {
    width:40px;
    height:40px;
    margin-top:7px;
    background-color: transparent;
}

.flag-img img {
    width:40px;
    height:40px;
}

.fixed-action-btn.flag-btn {
    position: relative;
    bottom: 0px;
    right: 0px;
    padding: 0px;
    width:100%;
}

.flag-btn.fixed-action-btn ul {
    bottom: auto;
}

.flag-btn.fixed-action-btn ul li {
    margin-bottom:0px;
}

nav .flag-btn.fixed-action-btn ul li.active, nav ul li:hover {
    background-color: rgba(0,0,0,.0)
}

#sign-in button[class*="flag-img-"] {
    width:40px;
}

#modalsContainer #products tbody input {
    margin-left: 0;
}

.top {
    height: 40px;
    padding-bottom: 0.5em;
}

.lean-overlay {
    top:0;
}

.switch.switch-filterable label .lever:after {
    background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA\
        ABkAAAAZAQMAAAD+JxcgAAAABlBMVEUAAAAAAAClZ7nPAAAAAXRSTlMAQObYZgAAAB\
        xJREFUCNdjIALI//8BJRCA/f8DCIEM/kMIogEAtUAJ96V/58IAAAAASUVORK5CYII=');
    background-size: cover;
}

#monitor .tabs {
    overflow: hidden;
}


#monitor .fa-eye:before {
    content: "\f06e";
}

/* Monitor button types */
.device {
    background-color: #7b1fa2;
}

.devicetype {
    background-color: #d32f2f;
}

.sensor {
    background-color: #458EB0;
}

.sensortype {
    background-color: #a1887f;
}

.parameter {
    background-color: #D38531;
}

/* Monitor special date inputs for presentation */
input.pikaday.title {
    padding: 0;
    margin: 0;
    width: 100px;
    height: 1.5rem;
    border: 0;
    box-shadow: none;
    font-size: 16px;
    cursor: pointer;
}

input.pikaday.title:focus {
    box-shadow: none;
    border: 0;
}

#monitor .chat .contacts .label {
    color:#fff;
    background: #35384C;
}

#edit-clients-modal #results {
    display: absolute;
    border: 0px;
}
#edit-clients-modal #results li:hover {
    background-color: #42A5F5;
    color: #fff;
}
#edit-clients-modal .indicator,
#new-clients-modal .indicator {
    right: 797px;
    left: 0px;
}

.width3 {
    width: 33.333%
}

#browser-alert {
    max-width: 600px;
    margin: 0 auto;
}

.hidden {
    display: none;
}
footer {
    text-align:center;    
}

#new-monitorparameters-modal .input-field:nth-child(5) {
    padding-bottom: 20px;
}

#edit-monitorparameters-modal .input-field:nth-child(4) {
    padding-bottom: 20px;
}

#monitor .clockpicker-popover {
    display:none!important;
}

td .btn {
    height: 55px;
    line-height:55px;
    margin: 2px;
}

.collection .collection-item.avatar .secondary-content {
    position:inherit;
}

#filesCollection li {
    height:inherit;
}

.collection .collection-item.avatar {
    padding-left:0px;
}

#filesCollection li i {
    top: 20px;
}

.map {
    display:none;
}

#monitor .card .title .button {
    width:25px;
    margin:0 5px;
}

#monitor .card .title .button > i {
    font-size: 2rem;
}

#monitor .card .title .spacer {
    display:none;
}

.table {
    overflow-x: auto;
    display: block;
}

.fc-view-container {
    overflow-x: auto;
    display: block;
}

#monitor.yay-hide .yaybar.yay-hide-to-small {
    left:-240px;
}

#monitor .clients-table_previous {
    display:none;
}

#monitor .clients-table_next {
    display:none;
}

.dataTables_wrapper .dataTables_paginate .paginate_button {
    padding: 0.5em 0.5em;
    margin-left:0px;
}

#monitor .clients-table_length {
    display:none;
}

#monitor .help-fixed {
    display:none;
}

#monitor .chat-fixed {
    display:none;
}

#monitor .indicator {
    display:none;
}

.yaybar .nano-content>ul li>.sp-cancel, .yaybar .nano-content>ul li>a {
    font-size: 1.5rem;
    padding: 20px;
}

.yaybar {
    width:100%;
    padding-bottom: 120px;
}

.yay-toggle {
    height: 90px;
}

.navbar-top {
    height: 90px;
    line-height: 90px;
}

.yay-toggle {
    padding-top:40px;
}

.yay-toggle div {
    width:30px;
}

.yaybar .top {
    height:90px;
}

.content-wrap {
    min-height: -webkit-calc(100vh - 90px);
    min-height: calc(100vh - 90px);
    padding-top: -webkit-calc(90px + 1.3rem);
    padding-top: calc(90px + 1.3rem);
    padding-left: 0px;
    padding-right:0px;
}

.modal {
    width: 100%;
    height:80%;
}

.tw-1 {
    min-width:80px;
}

.tw-2 {
    min-width:80px;
}

.tw-3 {
    min-width:80px;
}

.tw-4 {
    min-width:80px;
}

.tw-5 {
    min-width:80px;
}

.tw-6 {
    min-width:80px;
}

.tw-7 {
    min-width:80px;
}

.tw-8 {
    min-width:80px;
}

button a {
    zoom:1.2;
}

.navbar-top .brand-logo>img {
    max-width:120px;
    height:auto;
    
}

#monitor .dataTables_wrapper .dataTables_filter  {
    float:left;
    width: 100%;
}

#monitor .tooltipster-base {
    display:none;
}

#monitor #toast-container {
    display:none;
}

::-webkit-scrollbar {
    -webkit-appearance: none;
    width: 2px;
}

::-webkit-scrollbar-thumb {
    background-color: rgba(66,165,245,1);
    -webkit-box-shadow: 0 0 1px rgba(255,255,255,.2);
}

#monitor .tooltipster-base {
    display:none;
}

@media all and (orientation:landscape) {
    html {
        font-size: 0.8rem;
    }
    
    .yay-hide .yaybar.yay-hide-to-small {
        left:-240px;
    }

    .dropdown-content li {
        line-height:inherit;
    }

    .content-wrap {
        padding: 71.9px 0 1.3rem;
    }

    #calendarTrashcan {
        display:none;
    }

    .navbar-top {
        height: 45px;
        line-height: 45px;
    }

    .yay-toggle {
        height:45px;
        line-height:45px;
        padding-top:15px;
    }

    .yaybar .top {
        height: 45px;
    }

    .yaybar {
        padding-bottom: 55px;
    }
}

@media all and (orientation:portrait) {
    #monitor .brand-logo {
        padding-top: 35px;
    }

    #calendarTrashcan {
        display:none;
    }

    #monitor .height-fix {
        height:90px;
        line-height:90px;
    }
}

.widget > .title .button {
    opacity: 1;
}

button a {
    zoom: 1;
    color: #fff;
}

#monitor .content-wrap {
    margin-left:0px;
}

.navbar-top .user {
    margin:0 25px;
}

#finance-charts {
    overflow-x: auto;
}

#finance-charts, #finance-charts-details, #finance-chart-share-payments-detail {
    overflow-x:auto;
}

#user-data .collapsible-header {
    line-height: 5rem;
    height: 5rem;
    border-bottom: 5px solid #f4f4f4;
    padding-left: 5px;
}

#user-data .collapsible-header i {
    line-height: 5rem;
    height: 5rem;
}

.pika-single.is-bound {
    display:none;
}

#monitor .tbl {
    display: table;
}

.no-wrap {
    white-space: nowrap;    
}

.input-field .prefix {
    left:0;
}

.modal .modal-footer {
    padding: 5px 25px;
}

.content-wrap .page-title {
    margin:0px;
}

.rating {
    zoom: 0.7;
}

#edit-clients-modal, #new-clients-modal {
    overflow-x: hidden
}

.timeline-block:nth-child(even) .timeline-content .timeline-date {
    left: auto;
    right: auto;
    text-align: left;
}

#tooltip {
    bottom: 20%;
    top: inherit!important;
    left:50px!important;
}
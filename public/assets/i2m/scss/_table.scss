#cm,
#monitor,
#carwash {
    table.dataTable thead {
        .left-sorting {
            &.sorting {
                &:after,
                &:before {
                    content: '';
                    position: absolute;
                    top: 18px;
                    left: 7px;
                    height: 0;
                    width: 0;
                    border: 5px solid transparent;
                    border-top: 5px solid #5F5F5F;
                }
            }
            &.sorting_asc:after,
            &.sorting_asc_disabled:after,
            &.sorting_desc:after,
            &.sorting_desc_disabled:after {
                content: '';
                position: absolute;
                top: 18px;
                left: 7px;
                height: 0;
                width: 0;
                border: 5px solid transparent;
                border-top: 5px solid #5F5F5F;
            }
        }
    }
    textarea, input, th, button {
        outline: none;
    }
    .dataTables_wrapper {
            margin:-2em;

        .dataTables_info {
            padding: 1em;
        }
    }
    .dataTables_length {
        display: $none;
    }
    .dataTables_filter {
        display: $none;
    }
    tr.odd > .sorting_1.sorting_1,
    tr.even > .sorting_1.sorting_1 {
        background-color: inherit;
    }
    tr {
        height: 48px;
    }
    table {
        th,
        td {
            padding: 15px 26px;
            border-bottom: 1px;
        }
        th:last-child {
            padding-right: 40px;
        }
        td:last-child {
            padding-right: 40px;
        }
        &.dataTable {
            th {
            }
            &.no-footer {
                border-bottom:none;
            }
            tbody {
                color: rgba(black, 0.87);
                tr.selected {
                    background-color: $weak-grey-clr;
                }
                tr:hover {
                    background-color: $light-grey-clr;
                }
            }
            thead {
                font-size:12px;
                color: rgba(black, 0.54);
                .sorting {
                    &:after {
                        display:none;
                    }
                    &:before {
                        display:none;
                    }
                }
                .sorting_desc:after,
                .sorting_desc_disabled:after {
                    display:none;
                }
                .sorting_asc:after,
                .sorting_asc_disabled:after {
                    display:none;
                }
            }
        }
    }
    .sort-icon {
        position: absolute;
        font-size: 20px;
        right: -20px;
    }
    .pika-table {
        td,
        th {
        padding: 0px;
            &:last-child {
                padding-right: 0px;
            }
        }
    }
}

table.invoice {
    thead { display: table-header-group }
    tfoot { display: table-row-group }
    tr { page-break-inside: avoid }
}


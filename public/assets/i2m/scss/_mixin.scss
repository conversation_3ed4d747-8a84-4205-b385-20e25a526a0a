@mixin opacity-styles {
   transition: opacity .25s ease-in-out;
   -moz-transition: opacity .25s ease-in-out;
   -webkit-transition: opacity .25s ease-in-out;
}

@mixin mobile-height {
  min-height: -webkit-calc(100vh - 90px);
  min-height: calc(100vh - 90px);
}

@mixin padding-l-r-null {
  padding-left: 0px;
  padding-right: 0px;
}

@mixin content-wrap-mobile-padding-top {
  padding-top: -webkit-calc(90px + 1.3rem);
  padding-top: calc(90px + 1.3rem);
}

@mixin circle-styles {
   -webkit-transition:all 0.2s linear;
   -moz-transition:all 0.2s linear;
   -ms-transition:all 0.2s linear;
   transition:all 0.2s linear;
}

@mixin postfix-styles {
  right: 0;
  margin-top: 0.6rem;
  position: $absolute;
}

@mixin translateX-50 {
  -webkit-transform: translateX(-50%);
  -moz-transform: translateX(-50%);
  -ms-transform: translateX(-50%);
  transform: translateX(-50%);
}

@mixin fixed-style-center {
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

@mixin fixed-style-center-with-menu {
  top: 150px;
  left: 240px;
  right: 0;
  bottom: 0;
}
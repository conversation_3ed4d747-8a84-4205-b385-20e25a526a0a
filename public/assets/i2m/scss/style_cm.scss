@import '_mixin';
@import '_helpers';
@import '_flags';
@import '_colors';
@import '_select2';

@media screen and (max-width: 480px) {
    #cm .yay-hide .yaybar.yay-hide-to-small {
        left: -240px;
    }

    .m-negative {
        margin-top: -45px;
    }
}

@media screen and (max-width: 1024px) {
    #cm .content-wrap {
        padding: 60px 0rem 0rem;
    }
}

@media screen and (min-width: 1025px) {
    .arrow {
        position: absolute;
        width: 400px;
        top: 35%;
    }
    #cm .btn-floatingx {
        width: 60px;
        height: 60px;
        i {
            font-size: 3rem;
        }
    }

    #cm .content-wrap {
        padding: 60px 2rem 2rem;
    }
}

@media screen and (min-width: 520px) {
    .m-negative {
        margin-top: -50px;
    }
}

#transactions-table_wrapper .dt-buttons,
#cm-user-table_wrapper .dt-buttons,
#balance-table_wrapper .dt-buttons {
    display: none;
}

#cm .dataTables_length {
    display: none;
}

#cm .dataTables_filter label {
    display: none;
}

#cm_report_pdf {
    background-color: $white-clr;
    table {
        font-size: 10px;
    }
}
// *Loader
#loading-spinner {
    position: absolute;
    left: 45%;
    top: 10%;
    z-index: 2000;
    color: $white-clr;
}
.spinner-fullscreen {
    z-index: 2000;
    position: fixed !important;
    top: 50%;
    left: 50%;
}
.page-break {
    page-break-after: always;
}
.rela {
    position: relative;
}
.chat-fixed {
    position: fixed;
    top: 0;
    right: 180px;
}
.help-fixed {
    position: fixed;
    top: 0;
    right: 280px;
}
.btn-trans {
    background-color: transparent;
    height: 55px;
    line-height: 55px;
    &:hover {
        background-color: rgba(0, 0, 0, 0.1);
    }
}
form {
    padding: 2px;
}
.form-error {
    margin-top: -10px;
}
#cm .colorTd {
    text-align: center;
    .btn-floating {
        cursor: auto;
    }
}
.bottomOffset {
    padding-bottom: 50px;
}
#menu {
    position: fixed;
    right: 0;
    top: 0;
}
.clickable {
    cursor: pointer;
}
.shift-icon {
    position: absolute;
    top: 4px;
    right: 10px;
    padding-left: 5px;
}
// Custom badge ui-element
.i2m-badge {
    color: white;
    display: inline-block;
    border-radius: 2px;
    border: 0;
    font-size: 0.85rem;
    box-sizing: border-box;
    padding: 1px 7px;
    background-color: $light-blue-clr;
    border-color: #039be5;
    &.corner {
        position: absolute;
        top: 0px;
        left: 0px;
        &.right {
            right: 0px;
            left: auto;
        }
        &:after {
            content: "";
            border-top: 0;
            border-left: 0;
            border-bottom: 2.0em solid transparent;
            border-right: 2.0em solid transparent;
            border-right-color: inherit;
            position: absolute;
            top: 0px;
            right: 0px;
        }
    }
    i.left {
        margin-right: 5px;
    }
}
// Custom event/odd row color helper
#cm .i2m-evenodd:nth-child(even) {
    background: #f9f9f9;
}
#cm .i2m-evenodd:nth-child(odd) {
    background: #fff;
}
// Most correct centering horizontally & z-index
#toast-container {
    z-index: 3000;
    top: 10%;
    right: auto;
    left: 50%;
    @include translateX-50;
    min-width: auto !important;
}

.toast {
    word-break: break-word !important;
}

// Correct rendering full calendar buttons
#cm .fc button {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
    height: 2.5rem;
    font-size: 0.9rem;
}
// New background color
.fc-copy-icon {
    position: absolute;
    z-index: 2;
    top: 5px;
    right: 5px;
    display: none;
}
// Correct rendering event to no show border
#cm .fc-event {
    border: 0;
    margin-bottom: 1px;
    height: auto;
    min-height: 32px;
    // Help Bar
    &-container .past-event {
        background-color: $grey-color-clr;
        cursor: not-allowed;
        // *Mockup styles
        &:parent {
            background-color: $grey-color-clr;
            cursor: not-allowed;
        }
    }
}
// Spectrum overriding styles
#cm .sp-preview {
    width: 25px;
    height: 25px;
    border: 0;
    margin: 0.5rem 0 0.5rem 2.5rem;
    // Correct rendering Datatables header options
    &, .sp-alpha, .sp-thumb-el {
        border: 0;
        background-image: none;
    }
}
// * CON overriding styles
#cm .sp-replacer {
    background-color: transparent;
    border: 0;
}
#cm .sp-container {
    border: 0;
    border-radius: 3px;
    background-color: $white-clr;
}
#cm .sp-picker-container,
.sp-palette-container {
    border: 0;
}
#cm .sp-choose {
    background-color: $light-blue-clr;
    background-image: none;
    color: $white-clr;
    border: 0;
    border-radius: 3px;
    text-shadow: none;
    font-size: 1em;
    padding: 1px 10px;
    text-transform: uppercase;
}
.sp-cancel {
    display: none;
}
// Disable Webkit native datepicker
input::-webkit-calendar-picker-indicator {
    display: none;
}
#cm input[type="date"]::-webkit-input-placeholder {
    visibility: hidden;
}
// Correct Pikaday rendering styles
#cm .pika-button {
    background: $white-clr;
    text-align: center;
    font-size: 1.1em;
}
#cm .is-selected .pika-button {
    background-color: $light-blue-clr;
}
// Correct Clockpicker rendering styles
#cm .clockpicker-button {
    padding: 0;
    color: $white-clr;
    background-color: $light-blue-clr;
    border: 0;
}
#cm .clockpicker-popover {
    padding: 5px;
}
// Sortable rendering styles
#cm .sortable-ghost {
    opacity: 0.5;
    border: none;
    &-shadow {
        opacity: 0.1;
        border: none;
        background: $black-clr;
        border-radius: 5px;
        * {
            visibility: hidden;
        }
    }
}
#cm .sortable-fallback {
    opacity: 1.0;
}
#cm .timeline-dragged-content-right {
    float: right;
    &::before {
        left: auto;
        right: 100%;
        border-color: transparent;
        border-right-color: $white-clr;
    }
    .timeline-date {
        left: auto;
        right: 122%;
        text-align: right;
    }
}
// Tooltipster rendering styles
.tooltipster-base {
    border-radius: 3px;
    border: none;
    max-width: 520px;
}
// Shifttypes table column: color styles
#shifttypes-table tbody tr td:nth-child(4) {
    text-align: center;
}
// EventTypes simple color picker
#cm #simple-color-picker {
    position: absolute;
    display: none;
    z-index: 1006;
    width: 280px;
    height: 205px;
    background-color: $white-clr;
    padding: 5px;
    &-view {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        > div {
            margin-top: 50px;
            width: 50px;
            height: 50px;
        }
    }
    .color {
        background-position: 50% 50%;
        background-repeat: no-repeat;
        width: 25px;
        height: 25px;
        margin: 7px;
    }
}
#picked-color {
    margin-left: 30px;
    margin-top: 10px;
    margin-bottom: 10px;
    &, #simple-color-picker-button {
        cursor: pointer;
    }
}
// EventTypes simple icon picker
#simple-icon-picker {
    position: absolute;
    display: none;
    z-index: 1006;
    width: 836px;
    height: 368px;
    background-color: $white-clr;
    padding: 5px;
    &-view {
        margin-left: 90px;
        margin-top: 120px;
        > i {
            font-size: 64px;
        }
    }
    .row {
        margin-left: 10px;
        .col .row > i {
            font-size: 32px;
            cursor: pointer;
            &:hover:before {
                color: $lighten-grey-color-clr;
                -webkit-transition: all .3s ease-out;
                transition: all .3s ease-out;
            }
        }
    }
}
#picked-icon {
    font-size: 3rem;
    margin-left: 2.5rem;
}
// Event files styling
.event-image {
    max-width: 100%;
}
.event-video {
    width: 640px;
    height: 480px;
}
.fixed-action-btn {
    bottom: 45px;
    right: 24px;
}
.fixed-action-btn ul {
    bottom: 44px;
    bottom: 65px;
    margin-bottom: 10px;
    display: none;
}
.change-icon > .fa:nth-child(2) {
    display: none;
}
.change-icon:hover > .fa:nth-child(1) {
    display: none;
}
.change-icon:hover > .fa:nth-child(2) {
    display: inherit;
}
#page-error {
    font-size: 10rem;
    line-height: 10rem;
    text-align: center;
    &-text {
        font-size: 3rem;
        line-height: 3rem;
        text-align: center;
    }
}
#subtext-error {
    font-size: 2.5rem;
    line-height: 2.5rem;
    text-align: center;
}
.error-x .content-wrap {
    padding: 0px 1.3rem 1.3rem;
    min-height: calc(100vh - 55px);
    margin: 0px;
}
.error-x .search {
    display: none;
}
.error-x .header.clear-fix {
    display: none;
}
.error-x .container {
    padding-top: 10rem;
}
#notPlannedEvents:empty {
    display: none;
}
.hide-me {
    visibility: hidden;
}
.collapse-me {
    display: none;
}
.hand-cursor {
    cursor: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAQAAADZc7J/AAAABGdBTUEAALGPC/xhBQAAACBjSFJNAAB6JgAAgIQAAPoAAACA6AAAdTAAAOpgAAA6mAAAF3CculE8AAAAAmJLR0QA/4ePzL8AAABwSURBVEjH7ZJBEsAgCAMT/v/n9NCOSqe2oD2yNx1JggB4BCEFWyFASP2KMQE7ywWhe/tTRGCGogLk02tFctiW/SUgaMyQG4PdPzDn31rQbMb8FiAXgvsEJNax1yVlVGAjA93apP3HFhZTGIqiKH7iADB6HxPlHdNVAAAAJXRFWHRkYXRlOmNyZWF0ZQAyMDE0LTA3LTA3VDEzOjQ5OjEwKzAyOjAwm7WiFAAAACV0RVh0ZGF0ZTptb2RpZnkAMjAxNC0wNy0wN1QxMzo0OToxMCswMjowMOroGqgAAAAASUVORK5CYII="), move;
}
.dt-buttons {
    display: inline;
    .dt-button {
        position: relative;
        display: inline-block;
        text-decoration: none;
        color: $white-clr;
        background-color: #42A5F5;
        text-align: center;
        letter-spacing: .5px;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.26);
        -webkit-transition: .2s ease-out;
        transition: 0.2s ease-out;
        cursor: pointer;
        height: 36px;
        padding: 0 2rem;
        border-radius: 2px;
        line-height: 36px;
        text-transform: uppercase;
        border: none;
        outline: 0;
        -webkit-tap-highlight-color: transparent;
    }
}
button.dt-button,
div.dt-button,
a.dt-button {
    background-color: $light-blue-clr;
}
.dt- buttons-collection.dropdown-content {
    background-color: rgba(255, 255, 255, .9);
    width: 146px;
    border-radius: 2px;
    overflow: hidden;
}
#cm #campaign-table .odd.selected tr {
    background-color: $white-clr;
}
.postfix {
    right: 0px;
    margin-top: 0.6rem;
    position: absolute;
    &.long ~ input {
        width: calc(100% - 8.0rem);
    }
    ~ input {
        width: calc(100% - 3.0rem);
    }
}
.grey-border {
    border: 1px solid $light-grey-clr;
}
@media screen and (min-width: 1300px) {
    .collapsible-header {
        padding: 0;
        line-height: 2rem;
        min-height: 2rem;
        overflow: hidden;
    }
}
table.rresponsive-table {
    width: 100%;
    border-collapse: collapse;
    border-spacing: 0;
    position: relative;
}
table.rresponsive-table th {
    text-align: right;
    display: block;
    &, table.rresponsive-table td {
        margin: 0;
        vertical-align: top;
    }
    &ead {
        display: block;
        float: left;
        border: 0;
        border-right: 1px solid $grey-bg-clr;
        tr {
            display: block;
            padding: 0 10px 0 0;
            th::before {
                content: "\00a0";
            }
        }
    }
}
table.rresponsive-table tbody {
    display: block;
    width: auto;
    position: relative;
    overflow-x: auto;
    white-space: nowrap;
    tr {
        display: inline-block;
        vertical-align: top;
    }
}
table.rresponsive-table td {
    display: block;
    min-height: 1.25em;
    text-align: left;
}
table.rresponsive-table tr {
    padding: 0 10px;
}
table.rresponsive-table.bordered th {
    border-bottom: 0;
    border-left: 0;
}
table.rresponsive-table.bordered td {
    border-left: 0;
    border-right: 0;
    border-bottom: 0;
}
table.rresponsive-table.bordered tr {
    border: 0;
}
table.rresponsive-table.bordered tbody tr {
    border-right: 1px solid $grey-bg-clr;
}
#cm .status-work {
    background: $work-clr;
}
#cm .status-warning {
    background: $warning-clr;
}
#cm .status-alarm {
    background: $alarm-clr;
}
#cm .status-table td {
    text-align: center;
}
#cm .status-table td {
    text-align: center;
}
.card i.big-icon {
    position: absolute;
    font-size: 7rem;
    bottom: 10px;
    right: 0;
    opacity: 0.5;
    &x {
        position: absolute;
        font-size: 7rem;
        bottom: 10px;
        right: 20px;
        opacity: 0.5;
    }
}
.card i.small-iconx {
    position: absolute;
    font-size: 2.5rem;
    top: 0px;
    bottom: 0px;
    right: 0px;
    opacity: 0.5;
}
.tabs .tab {
    letter-spacing: 0px;
}
#cm .tabs {
    overflow-x: hidden;
}
.collapsible {
    border: 0;
    margin: 0;
    box-shadow: 0 0 0;
    // autogenerated form in modals
    &-header .dropup {
        position: relative;
    }
    &-header .col {
        padding: 0;
    }
}
#cm .tabs-vert {
    display: inline;
    float: none;
    text-align: left;
    line-height: 20px;
    height: 20px;
    color: $black-clr;
    .indicator {
        display: none;
    }
}
#cm .tabsx {
    height: auto!important;
    .tab {
        clear: both;
        color: rgba(0, 0, 0, 0.65);
        cursor: pointer;
        line-height: 1.5rem;
        width: 100%;
        text-transform: none;
        text-align: left;
    }
    .indicator {
        display: none;
    }
}
.alarm-icon {
    position: absolute;
    top: 15%;
    left: 2%;
}
.max-width {
    max-width: 900px;
}
.small-max-width {
    max-width: 10%;
}
.bold {
    font-weight: 600;
}
.mt-minut-45 {
    margin-top: -45px;
}
#cm .valign-wrapper {
    display: flex;
}
.cursor-move {
    cursor: move;
}
#cm .cursor-pointer {
    cursor: pointer;
}
input[type=file]::-webkit-file-upload-button {
    visibility: hidden;
}
.pickit {
    margin-top: 1rem;
    margin-bottom: -1rem;
}
#cm #traces-text {
    display: block;
}
.help-bar {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    height: 100%;
    overflow: hidden;
    z-index: 2000;
    > .layer-overlay {
        background: $white-clr;
        -webkit-transform: scale(0);
        -ms-transform: scale(0);
        transform: scale(0);
    }
    > .layer-content {
        position: relative;
        padding: 1.3rem 70px;
        background: $white-clr;
        display: none;
        overflow: auto;
        height: 100%;
        .search-bar-toggle {
            position: absolute;
            font-size: 3rem;
            right: 5rem;
            top: .5rem;
            z-index: 10;
        }
        .input-field {
            margin-top: 4rem;
            margin-bottom: 4rem;
            .prefix {
                text-align: center;
                font-size: 5rem;
                width: 5rem;
                ~ input {
                    margin-left: 4rem;
                    width: calc(100% - 4rem);
                }
            }
            input {
                font-size: 3rem;
                height: 4rem;
                margin: 0 0 20px;
            }
        }
        .search-results h4 {
            margin-bottom: 2rem;
            text-transform: uppercase;
        }
        .search-results .each-result {
            cursor: pointer;
            padding-top: 5px;
            padding-bottom: 25px;
            padding-right: 10px;
            img {
                max-width: 55px;
                vertical-align: middle;
                margin-right: 1rem;
                float: left;
            }
            .title {
                display: inline-block;
                font-size: 1.4rem;
                font-weight: bold;
                margin-bottom: .4rem;
            }
            .icon {
                width: 55px;
                height: 55px;
                line-height: 55px;
                font-size: 1.2rem;
                text-align: center;
                vertical-align: middle;
                margin-right: 1rem;
                float: left;
            }
            .nowrap {
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }
        }
    }
}
@media only screen and (max-width: 992px) {
    .help-bar > .layer-content {
        padding: 1.3rem;
        .help-bar > .layer-content .search-bar-toggle {
            right: 1rem;
            .help-bar > .layer-content .input-field .prefix {
                font-size: 3rem;
                width: 3rem;
            }
        }
    }
}
.btn-dev {
    border: none;
    cursor: default;
    color: $white-clr;
    padding: 3px 10px;
    border-radius: 4px;
    float: left;
    margin-right: 5px;
    margin-top: 5px;
    background-color: #0091ea;
    -webkit-transition: all .3s ease-out;
    transition: all .3s ease-out;
    &:hover {
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.4);
    }
}

.legend div:first-child:hover {
    opacity: 0!important;

}
#cm #finance-charts-details {
    position: relative;
    .legend table tr {
        float: left;
    }
    .legend div:first-child {
        width: -1px;

    }

}
#chartTurnoverLegend {
    position: absolute;
    top: 0px;
    right: 0px;
}
#chartSharePaymentsDetaillegend {
    position: absolute;
    top: -35px;
    right: 0px;
}
.tooltipster-charts {
    border-radius: 5px;
    background: #4c4c4c;
    color: #fff;
    padding: 5px;
}
.alarms-occured-confirmed {
    background-color: $light-blue-clr;
}
.alarms-occured-not-confirmed {
    background-color: rgb(244, 67, 54);
}
.alarms-not-occured-not-confirmed {
    background-color: $white-clr;
}
#spinner-cm {
    position: absolute;
    top: 80%;
    left: 50%;
}

#cm .fc-time-grid .fc-bgevent,
.fc-time-grid .fc-event {
    border: 3px solid #8c3a65;
    .circle {
        position: absolute;
        bottom: 10px;
        -webkit-transition: all 0.2s linear;
        -moz-transition: all 0.2s linear;
        -ms-transition: all 0.2s linear;
        transition: all 0.2s linear;
    }
}
.first_circle {
    z-index: 3;
}
.second_circle {
    z-index: 2;
}
.contener_circle {
    height: 70px;
    width: 60px;
    &:hover {
        height: 200px;
        width: 60px;
        .second_circle {
            bottom: 75px;
        }
    }
}
#externalItems .col {
    padding-bottom: 0;
}
td .btn {
    font-size: 1.3rem;
    letter-spacing: 0px;
    width: 55px;
    padding: 0;
}
#eventtypes-table tr td:nth-child(3) {
    text-align: center;
}
#eventtypes-table tr td:nth-child(4) {
    text-align: center;
}
.tooltipster-content .fa-pencil-square-o:hover {
    color: #009688;
    cursor: pointer;
}
.tooltipster-content .fa-times:hover {
    color: $carot-clr;
    cursor: pointer;
}
#cm .pdf-table {
    page-break-inside: avoid;
}
#cm .keyusage-icon {
    width: 30px;
    height: 30px;
    font-size: 16px;
    line-height: 1.75;
    background: $light-blue-clr;
    padding: 1px;
    border-radius: 50%;
    margin: -10px 6px -18px 0;
    color: white;
    transition: .2s ease-out;
    cursor: pointer;
}
#cm .card i.medium-iconx {
    position: absolute;
    font-size: 5rem;
    right: 30px;
    bottom: 8px;
    opacity: 0.5;
}
.addSelectButton {
    position: absolute;
    right: 0px;
    margin-top: 8px;
}
// Select2 overriding styles
#cm .select2-container {
    default .select2-selection--multiple .select2-selection__choice {
        border: none;
        color: $white-clr;
        margin-top: 8px;
        padding: 3px 10px;
        background-color: $carot-clr;
    }
}
#cm .select2-selection {
    background: transparent;
    &__choice__remove {
        color: #fff;
    }
}
#cm .select2-search__field {
    border: 0px;
}
#cm form.autogenerated-form .select2-container {
    left: 0rem;
    width: calc(100% - 3rem)!important;
}
.input-field .prefix~.select2 {
    margin-left: 3rem;
    width: calc(100% - 3rem) !important;
}
form.autogenerated-form label {
    left: 3rem;
}
form.autogenerated-form input {
    margin-left: 3rem;
    width: calc(100% - 3rem);
}
form.autogenerated-form label.checkbox-label {
    left: 0;
}
form.autogenerated-form h2 {
    margin-bottom: 40px;
}
#cm .keyusage-list-buttons {
    display: none;
    position: absolute;
    cursor: pointer;
    .keyusage-icon {
        width: 30px;
        height: 30px;
        font-size: 16px;
        line-height: 1.75;
        background: $light-blue-clr;
        padding: 1px;
        border-radius: 50%;
        margin: 0 2px 0 0;
        color: white;
        transition: .2s ease-out;
        cursor: pointer;
    }
}
#cm .light-grey {
    background-color: $light-grey-clr;
}
#cm .light-red {
    background-color: #FFCDD2;
    color: #E53935;
}
#emptyExternalCalanderItemsList {
    display: none;
}
#edit-products-modal .select2-selection__choice {
    border: none;
    color: $white-clr;
    margin-top: 8px;
    padding: 3px 10px;
    background-color: $light-blue-clr;
    span {
        color: #fff;
    }
}
#cm .dataTable {
    overflow-x: auto;
    /*display: block;*/
    &s_filter .switch {
        display: inline;
    }
    &s_wrapper .dataTables_filter {
        float: none;
    }
}
#cm .monitor-status-icon {
    position: relative;
    line-height: 10px;
    font-size: 25px;
    margin-left: 25px;
}
#div_carwash_keyusage div.sort {
    cursor: pointer;
    position: relative;
    &.active.desc:after {
        position: absolute;
        border-bottom: 5px solid $darker-grey-clr;
        content: '';
        height: 0;
        width: 0;
        top: 20%;
        right: 10%;
        border: 5px solid transparent;
        border-top: 5px solid $darker-grey-clr;
    }
    &.active.asc:after {
        content: '';
        position: absolute;
        top: 0%;
        right: 10%;
        height: 0;
        width: 0;
        border: 5px solid transparent;
        border-bottom: 5px solid $darker-grey-clr;
    }
}
#cm .dashboard-history {
    padding-top: 0px;
    .collapsible, #div_carwash_moneycollect .collapsible {
        border: 0px;
    }
    .collapsible-header div.col,
    .dashboard-history .row .col {
        padding: 0px;
    }
}

#cm #div_carwash_moneycollect_exchanger .list-border-bottom {
    border-bottom: 1px solid grey !important;
}

#cm #div_carwash_keyusage .collapsible-header div.col,
#div_carwash_moneycollect .collapsible-header div.col {
    padding: 0px;
}
#cm .dashboard-content ul.collapsible {
    border: 0px;
    div {
        line-height: 1em;
    }
}
#cm .dashboard-content .alert {
    padding: 0px;
}
#cm .header-dashboard-your-carwash .col {
    padding-bottom: 0px;
}
#cm .card .dashboard-content {
    padding: 10px;
}
#cm .count-wash div {
    display: inline;
    margin: 0 5px;
}

.bordered {
    border-radius: 5px;
    border: 1px solid #aaaaaa;
    padding: 5px;
}
.legendColorBox {
    width: 15px;
    height: 15px;
}
#cm #div_carwash_moneycollect table tbody tr td.right-align {
    text-align: right;
}
#cm .preload-cm {
    position: fixed;
    top: 50%;
    left: 55%;
    margin-top: -50px;
    z-index: 0;
    &.active {
        z-index: 1040;
    }
}

#cm .dropzone.profile-image, .dropzone.company-image {
    border: none;
    padding: 0px;
    width: 140px;
    height: 140px;
    min-height: 140px;
    .dz-preview {
        margin: 0px;
    }
}

#cm .dropzone.profile-image img {
    border-radius: 50%;
}

#mobilepayment-carwash .sort {
    cursor: pointer;
    position: relative;
    &.active.desc:after {
        position: absolute;
        border-bottom: 5px solid $darker-grey-clr;
        content: '';
        height: 0;
        width: 0;
        top: 55%;
        right: 10%;
        border: 5px solid transparent;
        border-top: 5px solid $darker-grey-clr;
    }
    &.active.asc:after {
        content: '';
        position: absolute;
        top: 17%;
        right: 10%;
        height: 0;
        width: 0;
        border: 5px solid transparent;
        border-bottom: 5px solid $darker-grey-clr;
    }
}
#forgot-password,
#lock,
#sign-in,
#sign-up {
    background: url(/assets/i2m/images/bg-cm.jpg);
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center;
}

.select-wrapper.filters.initialized ul>li>span {
    font-family: Roboto, sans-serif;
    font-size: 13px;
    line-height: 1.5;
    font-weight: 400;
    color: $black-clr;
    &:hover {
        background-color: #EF5350;
        color: $white-clr;
    }
}
#option_programusage_daily .col {
    width: 50%;
    text-align: left;
}
#chart-monitor-waterpressure {
    width: 100%;
    height: 300px;
    margin-top: 55px;
}
#chart-monitor-dosage {
    width: 100%;
    height: 300px;
    margin-top: 55px;
}
#monitor-chemistry-chart-handler {
    position: relative;
}
#cm #chart-monitor-chemistry div.legend {
    left: 4.2%;
    top: -95px;
    width: 50px;
    position: absolute;
    line-height: 1px;
}
#chartChemistryLegend {
    position: absolute;
    top: 0px;
    right: 0px;
}
#chart-monitor-smartheatingc div.legend > div:nth-child(1) {
    opacity: 0.3;
}

#cm #monitor-temperatures-chart-handler #choices {
    color: $black-clr;
    padding: 35px;
}
#cm .collapsible-header i {
    font-size: 1.3rem;
    line-height: 25px;
    display: block;
    float: left;
    text-align: center;
    margin-right: 3px;
    padding-left: 18px;
    padding-right: 32px;
}
.widget-width {
    min-width: 1000px;
    display: block;
}
.widget-auto {
    overflow-x: auto;
}
#cm .message-add {
    margin-left: -8px;
    color: $white-clr;
    font-size: 14px;
}
.yay-hide .yaybar.yay-hide-to-small {
    position: fixed;
}
.fixed-country {
    position: absolute;
    top: 4%;
    width: 55px;
    z-index: 1005;
}
#cm .monitor-status-icon {
    position: relative;
    line-height: 10px;
    font-size: 25px;
    margin-left: 5px;
}
[id^=monitor] [id$='handler'] {
    opacity: 0;
}
.no-wrap {
    white-space: nowrap;
}
.centered-box-400 {
    max-width: 400px;
    margin: 0 auto;
}
.card .title .minimize>i {
    font-size: 2rem;
}
#cm .fa-eye:before {
    content: "\f06e";
}
#chart-programusage,
#chart-turnover {
    width: 95%;
    height: 300px;
}

#finance-charts, #finance-charts-sums, #finance-charts-details, #finance-chart-share-payments-detail {
  width: 95%;
  height: 450px;
}

#finance-charts-details .legend table, #finance-charts-sums .legend table, #chart-monitor-temperatures .legend table, #chart-monitor-smartheatinga .legend table, #chart-monitor-smartheatingb .legend table, #chart-monitor-smartheatingc .legend table {
  width: auto;
  background-color: rgba(162, 162, 162, 0.6);
  opacity: 1;
}

#chart-monitor-temperatures .legend table tr, #chart-monitor-smartheatinga .legend table tr, #chart-monitor-smartheatingb .legend table tr, #chart-monitor-smartheatingc .legend table tr, #finance-charts-sums .legend table tr {
  float: left;
}

#finance-charts-details .legend table:hover, #finance-charts-sums .legend table:hover {
  background-color: rgba(162, 162, 162, 0.6);
  -webkit-transition-duration: 0.5s;
  /* Safari */
  transition-duration: 0.5s;
  opacity: 0;
}

#finance-charts-details .legend {
  table tr {
    float: left;
  }
  div:first-child {
    width: -1px !important;
  }
}

#chart-turnover-total-sums {
  height: 350px;
}

#finance-charts-sums, #finance-charts-details {
  position: relative;
}

#chartTurnoverLegend {
  position: absolute;
  top: 0px;
  right: 0px;
}

#chartSharePaymentsDetaillegend {
  position: absolute;
  top: -35px;
  right: 0px;
}

#chart-monitor-temperatures, #chart-monitor-chemistry, #chart-monitor-smartheatinga, #chart-monitor-smartheatingb, #chart-monitor-smartheatingc, #chart-monitor-waterpressure #chart-monitor-dosage {
  width: 100%;
  height: 300px;
  margin-top: 85px;
}

#monitor-chemistry-chart-handler {
  position: relative;
}

#chart-monitor-temperatures div.legend, #chart-monitor-smartheatinga div.legend, #chart-monitor-smartheatingb div.legend, #chart-monitor-smartheatingc div.legend {
  left: 4.2% !important;
  top: -95px;
  width: 100% !important;
  position: absolute;
  line-height: 1px !important;
}

#chart-monitor-chemistry div.legend {
  left: 4.2% !important;
  top: -95px;
  width: 50px;
  position: absolute;
  line-height: 1px !important;
}

#chartChemistryLegend {
  position: absolute;
  top: 0px;
  right: 0px;
}

#chart-monitor-temperatures div.legend > div:nth-child(1), #chart-monitor-smartheatinga div.legend > div:nth-child(1), #chart-monitor-smartheatingb div.legend > div:nth-child(1) #chart-monitor-smartheatingc div.legend > div:nth-child(1) {
  opacity: 0.3;
}

#div_carwash_bkfpay_user_transactions div.sort {
    cursor: pointer;
    position: relative;
}

#div_carwash_bkfpay_user_transactions div.sort.active.desc:after {
    position: absolute;
    border-bottom: 5px solid #5f5f5f;
    content: '';
    height: 0;
    width: 0;
    top: 20%;
    right: 10%;
    border: 5px solid transparent;
    border-top: 5px solid #5f5f5f;
}

#div_carwash_bkfpay_user_transactions div.sort.active.asc:after {
    content: '';
    position: absolute;
    top: 0%;
    right: 10%;
    height: 0;
    width: 0;
    border: 5px solid transparent;
    border-bottom: 5px solid #5f5f5f;
}

.invitation-hr {
     border-bottom: 1px solid #111;
}

.yaybar .nano-content>ul li.open>ul li>a>.badge {
  background-color: white;
  color: black;
}

.yaybar .nano-content>ul li>.sp-cancel>.badge, .yaybar .nano-content>ul li.active>a>.badge {
    background-color: white;
    color: black;
}

#cm table.dataTable {
  &.display tbody tr {
    &.even:hover.selected > .sorting_1, &.odd:hover.selected > .sorting_1, &:hover.selected > .sorting_1 {
      background-color: #f5f5f5;
    }
  }
  &.order-column.hover tbody tr {
    &.even:hover.selected > .sorting_1, &.odd:hover.selected > .sorting_1, &:hover.selected > .sorting_1 {
      background-color: #f5f5f5;
    }
  }
  &.display tbody tr.odd.selected > .sorting_1, &.order-column.stripe tbody tr.odd.selected > .sorting_1, &.display tbody tr.odd.selected, &.stripe tbody tr.odd.selected, tbody tr.selected, &.display tbody tr.even.selected > .sorting_1, &.order-column.stripe tbody tr.even.selected > .sorting_1 {
    background-color: #f5f5f5;
  }
  &.display tbody tr.even > .sorting_1, &.order-column.stripe tbody tr.even > .sorting_1, &.display tbody tr.odd > .sorting_1, &.order-column.stripe tbody tr.odd > .sorting_1 {
    background-color: #ffffff;
  }
}

#olmap.map {
    position: relative;
    width: 100%;
    height: 750px;
    border: 1px solid black;
    background: gray;
}

#info {
    position: absolute;
    background: #FFF;
}

button a {
    color: white !important;
    text-decoration: none !important;
}

a {
    text-decoration: none !important;
}

.width3 {
    width: 33.333%
}

div.infored {
    background-color: red;
    color: white;
    font-weight: bold;
    text-shadow: 1px 1px 1px #000000;
}

.tab .collapsible-header i {
    width: 6rem !important;
}

.app-notifications {
  width: 56px !important;
  min-width: 300px !important;
}

.app-notification-items {
  left: 0 !important;
  top: 70px !important;
  z-index: 10000 !important;
}

.app-notification-item-mobiles {
  position: fixed !important;
}

.btn-transparent {
    color: #000;
    min-width: 100px;
    background-color: transparent !important;
    border: 1px solid lightgrey;
}

.btn-transparent i {
    font-size: 13px;
    padding-right: 10px;
}

[v-cloak] {
    display: none;
}

.dashboard-widget .title {
    background-color: #6F7D95;
    color: white;
}

.h-150{
    height:150px!important
}

.h-120{
    height:120px!important
}

#cm .notification-item {
    width: 100%;
}

#cm .notification-iframe {
    border: none;
    height: calc(70vh - 104px);
}

.fontRed {
    color: rgba(255, 37, 58, 0.8);
}

#cm .summaryRow {
    border-bottom: 3px double;
}

#news-modal .media-date {
    color: #777;
    float: right;
    font-size: .8em;
}

#news-modal .media-link {
    display: block;
}

#news-modal .media-heading {
    display: block;
    width: 100%;
    padding-bottom: 20px;
    font-size: 1.2em;
}

#news-modal.modal {
    max-height: 80%;
}

.cmrc-beta {
    position: absolute;
    bottom: 25px;
    left: 15px;

    a {
        color: #fff;
    }
}

.mobile-menu-panel {
    position: fixed;
    z-index: 1000;
    background: white;
    min-width: 100vw;
    min-height: 100vh;
    top: 0;
    left:0;
    display: flex;
    flex-direction: column;
    display: none;

    .mobile-row {
        display: flex;
        flex-direction: row;
        flex-wrap: nowrap;
        align-items: center;
        height: 50px;
        width: 100%;
        padding: 0 5px;
        color: #42A5F5;
        border-bottom: 1px solid rgba(0, 0, 0, 0.07);

        .mobile-menu-icon {
            width: 30px;
            margin-right: 20px;
            display: flex;
            justify-content: center;

            i {
                height: auto;
            }
        }

        &.icon-right {
            justify-content: flex-end;
            border-bottom: none;
            color: rgba(0, 0, 0, 0.5);

            .mobile-menu-icon {
                margin-right: 0;
            }
        }
    }
}

// dropzone thumbnail size fix
.dropzone .dz-preview .dz-image {
    width: auto !important;
    height: auto !important;
    border-radius: 0px !important;
}

.dropzone .dz-preview .dz-image img{
    max-width: 100%;
}

@media (max-width: 700px) {
    .item-flags {
        order: 2;
    }
}
.loyalty-beta {
    &:after {
        content: '(BETA)';
        color: #ea1b1b;
        font-size: 10px;
        position: relative;
        top: -6px;
    }

    &--vue {
        color: #ea1b1b;
        font-size: 10px;
    }
}

a.link {
    text-decoration: underline !important;
    &:hover {
        text-decoration: none !important;
    }
}
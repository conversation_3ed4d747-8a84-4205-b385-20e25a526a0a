#cm .card .title {
  background-color: #6E7D96;
  color: #ffffff !important;
  font-weight: bold;

  &.dashboard-title {
    align-items: flex-start;
  }
}

#cm .card .title h5{
  font-weight: bold;
  color: #ffffff;
}

#cm .card .content {
  color: #8995A6;
}

.progress>div {
  background-color: #6E7D96;
}

.cm-v2-green-text {
  color: #7ED321;
}

.dashboardV2Background {
  background-color: #ECF2F6;
}

.cm-v2-green-text {
  color: #7ED321;
}

.cm-v2-green {
  background-color: #7ED321;
}

.cm-v2-blue-dark-grey {
  background-color: #354152;
}

.cm-v2-blue-grey {
  background-color: #6f7d95 !important;
}

.btn-show {
  background-color: #5DB0F4!important;
}

.btn-edit {
  background-color: #6f7d95 !important;
}

.select2-container--default .select2-selection--single .select2-selection__arrow {
  height: 33px;
}

.select2-container--default
.select2-selection--single
.select2-selection__rendered {
  line-height: 32px;
  padding-left: 8px;
}

.card .title {
  .select2-selection__rendered {
    color: #ffffff !important;
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 3px;
    padding-top: 3px;
    margin-top: -4px;
    padding-bottom: 3px;
    margin-bottom: 10px;
  }
  .select2-selection {
    border: 0;
  }
  .pull-right {
    position: absolute;
    right: 0px;
  }
  .input-field {
    margin-top: 0;
    .select2 {
      margin-top: 6px;
    }
  }
  .input-field .prefix.active {
    color: #ffffff;
  }
  .col {
    padding-bottom: 2px;
  }
  .small-label {
    font-size: 1rem;
    padding-top: 10px;
  }
  input {
    background-color: rgba(255, 255, 255, 0.2);
    border: none;
    box-shadow: none;
    border-radius: 4px;
  }
}

.date-range-input {
  input {
    width: 100px;
    display: inline;
  }
  i {
    width: calc(100% - 3rem);
  }
}

.dt-button-collection a, #export-button {
  background-color: #6f7d95 !important;
  color: #FFF !important;
}

#forgot-password,
#lock,
#sign-in,
#sign-up  {
  background: url(/assets/i2m/images/bg-cm.png);
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
  padding-top: 80px;
  padding-bottom: 80px;
}

html {
  background-color: rgb(236, 242, 246);
}

.cm-footer {
  bottom: 0;
  width: 100%;
  left: 0;
  z-index: 2;
}

p.contact-box {
  text-align: left;
  color: rgb(83, 117, 152);
  font-size:10px;
  font-weight:700;
}

a.contact-box {
  font-size:16px;
  font-weight:700;
  ext-decoration: none !important;
  text-decoration-line: none !important;
  text-decoration-style: initial !important;
  text-decoration-color: initial !important;
}

@media screen and (max-width: 576px) {
  #forgot-password,
  #lock,
  #sign-in,
  #sign-up  {
    padding-top: 0px;
  }
}

.card-panel-header {
  padding-top: 1.3rem;
  padding-bottom: 1.3rem;
  padding-left: 2rem;
  padding-right: 2rem;
  border-top-left-radius: 3px;
  border-top-right-radius: 3px;
  border-bottom-left-radius: 0px;
  border-bottom-right-radius: 0px;
  font-weight: bold;
  background-color: rgba(151, 162, 182, .8);
  text-transform: uppercase;
  color: #fff;
}

#forgot-password form,
#lock form,
#sign-in form,
#sign-up form,
#mobileApp {
  max-width: 650px;
}

.card-panel-logo {
  padding: 1.3rem;
}

.card-panel {
  border-top-left-radius: 0px;
  border-top-right-radius: 0px;
  border-bottom-left-radius: 3px;
  border-bottom-right-radius: 3px;
}

.btn, .btn-large, .fc button, .sp-container button {
  text-decoration: none;
  font-weight: bold;
  color: #fff;
  text-align: center;
  letter-spacing: 0.5px;
  transition: 0.2s ease-out;
  cursor: pointer;
}
.fixed-action-btn.flag-btn {
  position: relative;
  bottom: -5px;
}

.avatar {
  align-items: center;
  border-radius: 50%;
  display: inline-flex;
  justify-content: center;
  position: relative;
  text-align: center;
  vertical-align: middle;
  background-color: #26364f;
  border-color: #26364f;
}

.headline {
  color: whitesmoke;
  text-transform: uppercase;
  font-size: 24px;
  font-weight: 400;
  line-height: 32px;
  letter-spacing: normal;
}

.headline-profile {
  color: whitesmoke;
  text-transform: uppercase;
  font-size: 48px;
  font-weight: 400;
  line-height: 32px;
  letter-spacing: normal;
}

.navbar-top .new-brand-logo > img {
  width: auto;
  height: 100%;
  padding: 13px 16px;
  transition: padding .2s linear;
}

.yay-hide .navbar-top .new-brand-logo > img {
  padding-left: 25px;
}

.cmV2 {
  .btn:focus, .btn:hover {
    color: #fff;
  }
}

.dashboardV2Background {
  .btn:focus, .btn:hover {
    color: #fff;
  }

  .card {
    .title {
      min-height: 60px;
      display: flex;
      align-items: center;

      @media screen and ( max-width: 576px ) {
        flex-direction: column;
      }

      .select2-selection--single {
        height: 39px;
      }

      .single-date-from,
      .single-date-to{
        margin-top: 0;
        margin-bottom: 0;
      }

      .select2-container--default {
        margin-top: 0px;
      }

      .select2-selection__rendered {
        margin-top: 0px;
        height: 39px;
        padding-top: 5px;
      }

      .select2-selection__arrow {
        top: 6px;
      }
    }
  }

  .history-alarm-page {
    .datepicker {
      margin-bottom: 0px;
    }
  }
}

.export-menu {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}
#flotTip {
  color: #333;
}

.export-item {
  padding: 0 1rem;
  width: 40%;
  flex-basis: 40%;
  flex-grow: 1;
  margin-bottom: 4px;
}

#modalsContainer {
  .modal-footer {
    .modal-close {
      height: 25.2px;
      background-color: #9e9e9e !important;
      font-weight: bold;
      color: #fff;
      text-align: center;
      letter-spacing: 0.5px;
      transition: 0.2s ease-out;
      cursor: pointer;
      margin: 6px 15px 6px 0px;
      line-height: 25px;
      font-size: .8rem;
      display: flex;
      justify-content: center;
      align-items: center;

    }
  }
}

@media screen and (max-width: 576px) {
  #modalsContainer {
    .modal{
      width: 95%;
    }

    .modal-content {
      padding: 5px;
    }

    .modal-footer {
      display: flex;
      flex-direction: column;
      justify-content: center;
      height: auto;

      .btn-small,
      .modal-close {
        height: 50px;
        margin-right: 0;
      }
    }
  }
}

#modalsContainer {
  .modal-footer {
    .btn {
      &.custom-margin {
        margin-left: 0px !important;
      }
      &.btn-edit {
        @media screen and (min-width: 577px) {
          &.custom-margin {
            margin-left: 15px !important;
          }
        }
      }
    }
  }
}

#carwashes-widget {
  .table{
    tr {
      td {
        cursor: pointer;
      }
      table {
        tr {
          td {
            cursor: default;
          }
        }
      }
    }
  }
}

.not-pointer-table {
  td {
    cursor: default !important;
  }
}

footer {
  .footer-v2 {
    text-align: right;
    display: flex;
    align-items: center;
    justify-content: space-between;

    @media screen and (max-width: 576px) {
      text-align: center;
    }

    img.left {
        height: 40px;
    }
  }
}

.yaybar {
  .top {
    .brand-logo {
      padding: 13px 17px 13px 5px;
    }
  }
}

    // Mobile app buttons on sign in page

  #mobileApp {
    display: flex;
    justify-content: center;
    position: relative;
    margin: 30px auto;

    * {
      box-sizing: border-box;
    }

    .card-panel {
      &__outer {
        display: block;
        width: 100%;
        padding: 0 25px;
      }

      &__inner {
        display: flex;
        justify-content: space-between;
        flex-wrap: nowrap;
      }

      .mobile-app {
        &__link {
          display: inline-flex;
          align-items: center;
          justify-content: center;
          flex: 1;
          width: 100%;
          margin: 0 5px;
          max-height: 50px;

          img {
            display: block;
            height: 100%;
            max-width: 100%;
            width: auto;
          }
        }
      }
    }


  }


$absolute:absolute;
$relative:relative;
$block:block;
$none:none;
$inherit:inherit;
$inline:inline;
$fixed:fixed;
$flex:flex;
$hidden:hidden;
$auto:auto;

.opacity-100 {
  opacity: 1;
  @include opacity-styles;
}

.opacity-75 {
  opacity: 0.75;
  @include opacity-styles;
}

.opacity-50 {
  opacity: 0.5;
  @include opacity-styles;
}

.opacity-25 {
  opacity: 0.25;
  @include opacity-styles;
}

.opacity-0 {
  opacity: 0;
  @include opacity-styles;
}

/* transitions */
@mixin transition($delay) {
  transition: all $delay linear;
}

.tr-100 {
  @include transition(0.1s);
}

.tr-200 {
  @include transition(0.2s);
}

.tr-300 {
  @include transition(0.3s);
}

.tr-400 {
  @include transition(0.4s);
}

.tr-500 {
  @include transition(0.5s);
}

.tr-600 {
  @include transition(0.6s);
}

.tr-700 {
  @include transition(0.7s);
}

.tr-800 {
  @include transition(0.8s);
}

.tr-900 {
  @include transition(0.9s);
}

.tr-100 {
  @include transition(1s);
}


.fs-08r {
  font-size: 0.8rem;
}

.fs-09r {
  font-size: 0.9rem;
}

.fs-10r {
  font-size: 1.0rem;
}

.fs-11r {
  font-size: 1.1rem;
}

.fs-12r {
  font-size: 1.2rem;
}

.fs-13r {
  font-size: 1.3rem;
}

.fs-14r {
  font-size: 1.4rem;
}

.fs-15r {
  font-size: 1.5rem;
}

.fs-16r {
  font-size: 1.6rem;
}

.fs-17r {
  font-size: 1.7rem;
}

.fs-18r {
  font-size: 1.8rem;
}

.fs-19r {
  font-size: 1.9rem;
}

.fs-20r {
  font-size: 2.0rem;
}

.fs-21r {
  font-size: 2.1rem;
}

.fs-22r {
  font-size: 2.2rem;
}

.fs-23r {
  font-size: 2.3rem;
}

.fs-24r {
  font-size: 2.4rem;
}

.fs-25r {
  font-size: 2.5rem;
}

.d-ib {
  display: inline-block !important;
}

.d-b {
  display: block !important;
}

.d-i {
  display: inline !important;
}

.a-c {
    text-align:center;
}
.margin-right {
    margin-right:20px;
}

.margin-left {
    margin-left:20px;
}

.margin-top {
    margin-top:20px;
}

.margin-bottom {
    margin-bottom:20px;
}

.huge-margin-top {
    margin-top:60px;
}

.margin-right {
  margin-right: 20px;
}

.margin-left {
  margin-left: 20px;
}

.margin-top {
  margin-top: 20px;
}

.margin-bottom {
  margin-bottom: 20px;
}

.huge-margin-top {
  margin-top: 60px;
}

.fs-11r {
  font-size: 1.1rem;
}

.fs-12r {
  font-size: 1.2rem;
}

.fs-13r {
  font-size: 1.3rem;
}

.fs-14r {
  font-size: 1.4rem;
}

.fs-15r {
  font-size: 1.5rem;
}

.fs-16r {
  font-size: 1.6rem;
}

 #cm .d-b {
  display: block;
}

#cm .d-i {
  display: inline;
  &b {
    display: inline-block;
  }
}

.l-h-18 {
    line-height: 18px;
}

.l-h-24 {
    line-height: 24px;
}

.l-h-36 {
    line-height: 36px;
}

.ml-100 {
  margin-left: 100px;
}

.mr-100 {
  margin-right: 100px;
}

.mt-100 {
  margin-top: 100px;
}

.mb-100 {
  margin-bottom: 100px;
}

.m-100 {
  margin: 100px;
}

.ml-180 {
  margin-left: 180px;
}

.mr-180 {
  margin-right: 180px;
}

.mt-180 {
  margin-top: 180px;
}

.mb-180 {
  margin-bottom: 180px;
}

.m-180 {
  margin: 180px;
}

.ml-200 {
  margin-left: 200px;
}

.mr-200 {
  margin-right: 200px;
}

.mt-200 {
  margin-top: 200px;
}

.mb-200 {
  margin-bottom: 200px;
}

.m-200 {
  margin: 200px;
}

.l-auto {
  left: auto;
}

.l-0 {
  left: 0;
}

.l-10 {
  left: 10px;
}

.l-20 {
  left: 20px;
}

.l-30 {
  left: 30px;
}

.l-40 {
  left: 40px;
}

.l-50 {
  left: 50px;
}

.l-60 {
  left: 60px;
}

.l-80 {
  left: 80px;
}

.l-50p {
  left: 50%;
}

.t-50p {
  top: 50%;
}

.r-auto {
  right: auto;
}

.r-0 {
  right: 0;
}

.r-10 {
  right: 10px;
}

.r-20 {
  right: 20px;
}

.r-26 {
  right: 26px;
}

.r-28 {
  right: 28px;
}

.r-30 {
  right: 30px;
}

.r-40 {
  right: 40px;
}

.r-50 {
  right: 50px;
}

.r-60 {
  right: 60px;
}

.r-70 {
  right: 70px;
}

.r-80 {
  right: 80px;
}

.w-10p {
  width: 10%;
}

.w-15p {
  width: 15%;
}

.w-20p {
  width: 20%;
}

.w-25p {
  width: 25%;
}

.w-30p {
  width: 30%;
}

.w-35p {
  width: 35%;
}

.w-40p {
  width: 40%;
}

.w-45p {
  width: 45%;
}

.w-50p {
  width: 50%;
}

.w-55p {
  width: 55%;
}

.w-60p {
  width: 60%;
}

.w-65p {
  width: 65%;
}

.w-70p {
  width: 70%;
}

.w-75p {
  width: 75%;
}

.w-80p {
  width: 80%;
}

.w-85p {
  width: 85%;
}

.w-90p {
  width: 90%;
}

.w-95p {
  width: 95%;
}

.w-100p {
  width: 100%;
}

.h-80 {
  height: 80px;
}

.h-90 {
  height: 90px;
}

.h-120 {
  height: 120px;
}

.h-140 {
  height: 140px;
}

.w-70 {
  width: 70px;
}

.w-140 {
    width: 140px;
}

.h-160 {
  height: 160px;
}

.h-180 {
  height: 180px;
}

.h-250 {
    height: 250px;
}

.h-350 {
    height: 350px;
}

.h-450 {
    height:450px;
}

.mh-10 {
  min-height: 10px;
}

.mh-20 {
  min-height: 20px;
}

.mh-30 {
  min-height: 30px;
}

.mh-40 {
  min-height: 40px;
}

.tw-1 {
    width:5%!important;
}

.tw-2 {
    width:10%!important;
}

.tw-3 {
    width:15%!important;
}

.tw-4 {
    width:20%!important;
}

.tw-5 {
    width:25%!important;
}

.tw-6 {
    width:30%!important;
}

.tw-7 {
    width:35%!important;
}

.tw-8 {
    width:40%!important;
}

.tw-9 {
    width:45%!important;
}

.absolute {
  position: $absolute;
}

.fixed {
  position: fixed;
}

.relative {
  position: $relative;
}

.thumbnail-200 {
    max-width: 200px;
    max-height: 200px;
}

.thumbnail-150 {
    max-width: 150px;
    max-height: 150px;
}

.over-visible {
  overflow: visible !important;
}

.max-width{
    max-width:900px;
}

.small-max-width {
    max-width: 10%;
}

.bold {
    font-weight:600;
}

.mt-minut-45 {
    margin-top:-45px;
}

.cursor-move {
    cursor:move;
}

.cursor-pointer {
    cursor:pointer;
}

.valign-top {
  vertical-align: top;
}

.valign-middle {
  vertical-align: middle;
}

.valign-bottom {
  vertical-align: bottom;
}

.z-100 {
  z-index: 100;
}

.z-500 {
  z-index: 500;
}

.z-1000 {
  z-index: 1000;
}

.absolute-right {
    position: absolute;
    right: 0;
}

.fl {
    float:left;
}

@media print {
    #cm {
        html, body {
            background-color: #fff !important;
            -webkit-print-color-adjust: exact;
        }

        .card {
            .content {
            box-shadow: 0 0 0 0!important;
            background-color: #fff!important;
            -webkit-print-color-adjust: exact;
            }
            .card-panel {
                transition: .25s;
                border-radius: 0;
            }
        }

        .z-depth-2, .z-depth-2-hover:hover {
           box-shadow: 0 0 0 0!important;
        }

        .collapsible {
            box-shadow: 0 0 0 0!important;
        }

        .hide-on-print {
            display: none;
        }

        input {
            &:not([type]) {
              &.valid, &:focus.valid {
                box-shadow: 0 0 0 0 !important;
              }
            }
            &[type=date] {
              &.valid, &:focus.valid {
                box-shadow: 0 0 0 0 !important;
              }
            }
            &[type=datetime-local] {
              &.valid, &:focus.valid {
                box-shadow: 0 0 0 0 !important;
              }
            }
            &[type=email] {
              &.valid, &:focus.valid {
                box-shadow: 0 0 0 0 !important;
              }
            }
            &[type=number] {
              &.valid, &:focus.valid {
                box-shadow: 0 0 0 0 !important;
              }
            }
            &[type=password] {
              &.valid, &:focus.valid {
                box-shadow: 0 0 0 0 !important;
              }
            }
            &[type=search] {
              &.valid, &:focus.valid {
                box-shadow: 0 0 0 0 !important;
              }
            }
            &[type=tel] {
              &.valid, &:focus.valid {
                box-shadow: 0 0 0 0 !important;
              }
            }
            &[type=text] {
              &.valid, &:focus.valid {
                box-shadow: 0 0 0 0 !important;
              }
            }
            &[type=time] {
              &.valid, &:focus.valid {
                box-shadow: 0 0 0 0 !important;
              }
            }
            &[type=url] {
              &.valid, &:focus.valid {
                box-shadow: 0 0 0 0 !important;
              }
            }
          }

        textarea.materialize-textarea {
          &.valid, &:focus.valid {
            box-shadow: 0 0 0 0 !important;
          }
        }
    }
}

#monitor {
    .sensors_title {
        background-color: rgba(172,92,60,1);
        color: #fff;
    }
    .sensorstypes_title {
        background-color: rgba(117,36,3,1);
        color: #fff;

    }
    .devices_title {
        background-color: rgba(74,110,3,1);
        color: #fff;
    }
    .devicetypes_title {
        background-color: rgba(126,160,56,1);
        color: #fff;
    }
    div[class$="_title"] ul {
        color: #fff;
    }

    .btn-closer {
        width:70px;
    }
}

.btn-revealer {
  background: #564f00;
  border: none;
  line-height: 21px;
  margin: 2px;
  color: #fff;
  padding: 3px 10px;
  border-radius: 4px;
  cursor: pointer;
  letter-spacing: 0px;
  width: 100%;
  max-width: 230px;
  height: auto;
  display: inline-block;
  text-align: center;
  -webkit-transition: all 0.3s ease-out;
  transition: all 0.3s ease-out;
  &:hover {
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.4);
  }
}

.module-reveal {
    position: absolute;
    max-width: 230px;
    height: auto;
    font-size: 14px;
    background: rgba(244,244,244,1);
    z-index: 1200;
    padding: 12px;
    top: 35px;
    margin: 2px;
    border-radius: 4px;
    -webkit-animation-duration: 2s; /* Chrome, Safari, Opera */
    animation-duration: 2s;
}


.module-container {
    position: relative;
    font-size: 26px;
    line-height: 23px;
}


.dt-buttons .dt-button {
    background-color: #EF5350!important;
}


.news-message-show {
    min-height: 400px;
}
.menu-container {
    display: flex;
    position: absolute;
    justify-content: flex-end;
    top: 0;
    right: 0;
}

.navbar-top {
  height: 50px !important;
  line-height: 50px !important;
}

.content-wrap {
  padding-top: 70px !important;
}

nav {
  height: 45px !important;
  line-height: 45px !important;
}

.nav-bottom-margin {
  border-bottom: 1px solid red;
  width: 50%;
}

.dropdown-content li {
  min-width: 280px;
  width: 300px;
}

.yaybar .top {
  height: 50px !important;
}

.yay-toggle {
  height: 50px !important;
  line-height: 50px !important;
  padding-top: 20px !important;
}

.yaybar .nano-content > ul > li > ul > li {
  &.content, &.label {
    padding-left: 35px;
    padding-right: 15px;
  }
  > {
    .sp-cancel, a {
      padding-left: 35px;
      padding-right: 15px;
    }
    ul > li {
      &.content, &.label {
        padding-left: 67.5px;
        padding-right: 15px;
      }
      > {
        .sp-cancel, a {
          padding-left: 67.5px;
          padding-right: 15px;
        }
      }
    }
  }
}
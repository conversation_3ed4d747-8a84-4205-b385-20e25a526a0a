.help-bar {
  display: $none;
  position: $fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  height: 100%;
  overflow: $hidden;
  z-index: 2000;
}

.help-bar > .layer-overlay {
    background: #fff;
    -webkit-transform: scale(0);
    -ms-transform: scale(0);
    transform: scale(0);
}

.help-bar > .layer-content {
    position: $relative;
    padding: 1.3rem 70px;
    background: #fff;
    display: $none;
    overflow: $auto;
    height: 100%;
}

@media only screen and (max-width : 992px) {
    .help-bar > .layer-content {
        padding: 1.3rem;
    }
}

.help-bar > .layer-content .search-bar-toggle {
    position: $absolute;
    font-size: 3rem;
    right: 5rem;
    top: .5rem;
    z-index: 10;
}

.help-bar > .layer-content .search-bar-toggle {
  position: $absolute;
  font-size: 3rem;
  right: 5rem;
  top: .5rem;
  z-index: 10;
}

@media only screen and (max-width : 992px) {
    .help-bar > .layer-content .search-bar-toggle {
      right: 1rem;
    }
}

.help-bar > .layer-content .input-field {
    margin-top: 4rem;
}

.help-bar > .layer-content .input-field .prefix {
    text-align: center;
    font-size: 5rem;
    width: 5rem;
}

.help-bar > .layer-content .input-field .prefix ~ input {
    margin-left: 6rem;
    width: 91%;
    width: -webkit-calc(100% - 6rem);
}

.help-bar > .layer-content .input-field input {
    font-size: 5rem;
    height: 7rem;
    margin: 0 0 20px;
}

@media only screen and (max-width : 992px) {
    .help-bar > .layer-content .input-field .prefix {
      font-size: 3rem;
      width: 3rem;
    }
    .help-bar > .layer-content .input-field .prefix ~ input {
      margin-left: 4rem;
      width: 91%;
      width: -webkit-calc(100% - 4rem);
      width: calc(100% - 4rem);
    }

    .help-bar > .layer-content .input-field input {
          font-size: 3rem;
          height: 4rem;
          margin: 0 0 20px;
    }
}

.help-bar > .layer-content .search-results h4 {
    margin-bottom: 2rem;
    text-transform: uppercase;
}

.help-bar > .layer-content .search-results .each-result {
    cursor: pointer;
    padding-top: 5px;
    padding-bottom: 25px;
    padding-right: 10px;
}

.help-bar > .layer-content .search-results .each-result img {
    max-width: 55px;
    vertical-align: middle;
    margin-right: 1rem;
    float: left;
}

.help-bar > .layer-content .search-results .each-result .title {
    display: inline-block;
    font-size: 1.4rem;
    font-weight: bold;
    margin-bottom: .4rem;
}

.help-bar > .layer-content .search-results .each-result .icon {
    width: 55px;
    height: 55px;
    line-height: 55px;
    font-size: 1.2rem;
    text-align: center;
    vertical-align: middle;
    margin-right: 1rem;
    float: left;
}

.help-bar > .layer-content .search-results .each-result .nowrap {
    white-space: nowrap;
    overflow: $hidden;
    text-overflow: ellipsis;
}
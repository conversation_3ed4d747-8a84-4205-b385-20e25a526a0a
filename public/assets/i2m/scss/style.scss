@import 'mixin';
@import 'helpers';
@import 'flags';
@import 'colors';
@import 'help-bar';
@import 'select2';
@import 'table';
@import 'menu';
@import 'charts';
@import 'heatmap';
@import 'ol-map';
@import 'helpers/flexbox';
@import 'helpers/border';
@import 'vue/vue';

@media screen and (max-width: 480px) {
  .yay-hide .yaybar.yay-hide-to-small {
    left: -240px;
  }
}

@media screen and (min-width: 801px) {
  .btn-floatingx {
    width: 80px;
    height: 80px;
    i {
      font-size: 3rem;
    }
  }
}

@media screen and (max-width: 800px) {
  .btn-floatingx {
    width: 60px;
    height: 60px;
    i {
      font-size: 3rem;
    }
  }
}

.btn-floatingx {
  border-radius: 50%;
  border: $none;
  color: $white-clr;
}

/*
*Loader
*/

#loading-spinner {
  z-index: 2000;
}

.spinner-fullscreen {
  z-index: 2000;
  position: $fixed;
  top: 50%;
  left: 50%;
}

.page-break {
  page-break-after: always;
}

.chat-fixed {
    order: 2;
    top: 0;
    height: 50px;
    width: 70px;
    text-align: center;
    &:hover {
         background-color: rgba(0, 0, 0, 0.04);
  -webkit-transition-duration: 0.7s;
  /* Safari */
  transition-duration: 0.7s;
    }
}

.help-fixed {
  order:1;
  top: 0;
  height: 65px;
  width: 70px;
  text-align: center;
  &:hover {
    background-color: rgba(0, 0, 0, 0.04);
    -webkit-transition-duration: 0.7s;
    /* Safari */
    transition-duration: 0.7s;
  }
}


.btn-trans {
  background-color: $transparent-clr;
  height: 100%;
  line-height: inherit;
  box-shadow: 0 0px 0px $no-color !important;
  &:hover {
    background-color: $btn-trans-hover-clr;
  }
}

.form-error {
  margin-top: -10px;
}

.colorTd {
  text-align: center;
  .btn-floating {
    cursor: $auto;
  }
}

.bottomOffset {
  padding-bottom: 50px;
}

#menu {
  order:3;
  right: 0;
  top: 0;
}

.clickable {
  cursor: pointer;
}

.shift-icon {
  position: $absolute;
  top: 4px;
  right: 10px;
  padding-left: 5px;
}

/*
 * CON overriding styles
 */

/* New background color */

.content-wrap {
  background-color: $light-grey-clr;
}

.bg-white {
  background-color: $white-clr;
}

/* Most correct centering horizontally & z-index */

#toast-container {
  z-index: 3000;
  top: 10% !important;
  right: $auto !important;
  left: 50%;
  @include translateX-50;
}

/* Correct rendering full calendar buttons */

#monitor .fc button {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
  height: 2.5rem;
  font-size: 0.9rem;
}

.fc-copy-icon {
  position: $absolute;
  z-index: 2;
  top: 5px;
  right: 5px;
  display: $none;
}

#monitor {
    .fc-event {
      margin-bottom: 1px;
      border-width: 2px;
      height: $auto;
      min-height: 32px;
    }

    #externalItems .event-row .fc-event {
      border: 0;
    }

    /* Correct rendering DataTables header options */

    .dataTables_wrapper {
      .dataTables_filter {
        float: $none;
      }
      .dataTables_paginate .paginate_button.disabled {
        opacity: 0.3;
      }
    }
}

/* Correct rendering DataTables pagination buttons */

/* Spectrum overriding styles */

.sp-preview {
  width: 25px;
  height: 25px;
  border: 0;
}

#monitor {
    .sp-replacer {
      background-color: $transparent-clr;
      border: 0;
    }

    .sp-preview, .sp-alpha, .sp-thumb-el {
      border: 0 !important;
      background-image: $none;
    }

    .sp-container {
      border: 0;
      border-radius: 3px;
      background-color: $white-clr;
    }

    .sp-picker-container, .sp-palette-container {
      border: 0;
    }

    .sp-choose {
      background-color: $light-blue-clr;
      background-image: $none;
      color: $white-clr;
      border: 0;
      border-radius: 3px;
      text-shadow: $none;
      font-size: 1em;
      padding: 1px 10px;
      text-transform: uppercase;
    }
}

.sp-cancel {
  display: $none;
}

/* Disable Webkit native datepicker */

#monitor input {
  &::-webkit-calendar-picker-indicator {
    display: $none;
  }
  &[type="date"]::-webkit-input-placeholder {
    visibility: $hidden;
  }
}

/* Correct Pikaday rendering styles */

#monitor {
    .pika-button {
      background: $white-clr;
      text-align: center;
      font-size: 1.1em;
    }

    .is-selected .pika-button {
      background-color: $light-blue-clr ;
    }
}

/* Correct Clockpicker rendering styles */

 #monitor, #carwash {
    .clockpicker-button {
      padding: 0;
      color: $white-clr;
      background-color: $light-blue-clr;
      border: 0;
    }

    .clockpicker-popover {
      padding: 5px;
      position: $fixed;
      top: 10%;
    }
}

/* Sortable rendering styles */

#monitor, #carwash {
    .sortable-ghost {
      opacity: 0.5;
      border: $none;
    }

    .sortable-ghost-shadow {
      opacity: 0.1;
      border: $none;
      background: $black-clr;
      border-radius: 5px;
      * {
        visibility: $hidden;
      }
    }

    .sortable-fallback {
      opacity: 1.0;
    }

    .timeline-dragged-content-right {
      float: right;
      &::before {
        left: $auto;
        right: 100%;
        border-color: $transparent-clr;
        border-right-color: $white-clr;
      }
      .timeline-date {
        left: $auto;
        right: 122%;
        text-align: right;
      }
    }
}

/* Tooltipster rendering styles */

.tooltipster-base {
  border-radius: 3px;
  border: $none;
}

/* EventTypes simple color picker */

#simple-color-picker {
  position: $absolute;
  display: $none;
  z-index: 1006;
  width: 280px;
  height: 205px;
  background-color: $white-clr;
  padding: 5px;
}

#picked-color, #simple-color-picker-button {
  cursor: pointer;
}

#simple-color-picker .color {
  background-position: 50% 50%;
  background-repeat: no-repeat;
  width: 25px;
  height: 25px;
  margin: 7px;
}

#monitor, #carwash {
  #simple-color-picker-view {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    > div {
      margin-top: 50px;
      width: 50px;
      height: 50px;
    }
  }
}

#picked-color {
  margin-left: 30px;
  margin-top: 10px;
  margin-bottom: 10px;
}

/* EventTypes simple icon picker */

#simple-icon-picker {
  position: $absolute;
  display: $none;
  z-index: 1006;
  width: 836px;
  height: 368px;
  background-color: $white-clr;
  padding: 5px;
  .row {
    margin-left: 10px;
    .col .row > i {
      font-size: 32px;
      cursor: pointer;
      &:hover:before {
        color: $lighten-grey-color-clr;
        -webkit-transition: all .3s ease-out;
        transition: all .3s ease-out;
      }
    }
  }
}

#simple-icon-picker-view {
  margin-left: 90px;
  margin-top: 120px;
  > i {
    font-size: 64px;
  }
}

#picked-icon {
  font-size: 3rem;
  margin-left: 2.5rem;
}

/* Event files styling */

.event-image {
  max-width: 100%;
}

.event-video {
  width: 640px;
  height: 480px;
}

/* Slide up btn menu
   to add new item to menu You have to add new classes like here:
.fixed-action-btn.one{ <-- instead of .one You can make .two and set bottom to 130px (add 65px)*/

.fixed-action-btn {
  ul {
    bottom: 44px;
  }
  bottom: 44px;
  right: 24px;
}

.fixed-action-btn.one {
  bottom: 65px;
  margin-bottom: 10px;
  display: $none;
}

a.btn.waves-effect.dt-selected i.fa {
    color: $white-clr;
}

a.btn.waves-effect.dt-selected.disabled i.fa {
    color: $grey-color-clr;
}

a.btn i.fa {
    color: $white-clr;
}
.change-icon {
  > .fa:nth-child(2) {
    display: $none;
  }
  &:hover > .fa {
    &:nth-child(1) {
      display: $none;
    }
    &:nth-child(2) {
      display: $inherit;
    }
  }
}

#page-error {
  font-size: 10rem;
  line-height: 10rem;
  text-align: center;
}

#page-error-text {
  font-size: 3rem;
  line-height: 3rem;
  text-align: center;
}

#subtext-error {
  font-size: 2.5rem;
  line-height: 2.5rem;
  text-align: center;
}

.error-x {
  .content-wrap {
    padding: 0 1.3rem 1.3rem;
    min-height: -webkit-calc(100vh - 55px);
    min-height: calc(100vh - 55px);
    margin: 0;
  }
  .search, .header.clear-fix {
    display: $none;
  }
  .container {
    padding-top: 10rem;
  }
}

#notPlannedEvents:empty {
  display: $none;
}

.hide-me {
  visibility: $hidden;
}

.collapse-me {
  display: $none;
}

.hand-cursor {
  cursor: -webkit-grab;
  cursor: -moz-grab;
  cursor: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAQAAADZc7J/AAAABGdBTUEAALGPC/xhBQAAACBjSFJNAAB6JgAAgIQAAPoAAACA6AAAdTAAAOpgAAA6mAAAF3CculE8AAAAAmJLR0QA/4ePzL8AAABwSURBVEjH7ZJBEsAgCAMT/v/n9NCOSqe2oD2yNx1JggB4BCEFWyFASP2KMQE7ywWhe/tTRGCGogLk02tFctiW/SUgaMyQG4PdPzDn31rQbMb8FiAXgvsEJNax1yVlVGAjA93apP3HFhZTGIqiKH7iADB6HxPlHdNVAAAAJXRFWHRkYXRlOmNyZWF0ZQAyMDE0LTA3LTA3VDEzOjQ5OjEwKzAyOjAwm7WiFAAAACV0RVh0ZGF0ZTptb2RpZnkAMjAxNC0wNy0wN1QxMzo0OToxMCswMjowMOroGqgAAAAASUVORK5CYII="), move;
}

.dt-buttons {
    i {
        color: $white-clr;
    }
  .dt-button {
    height: 36px;
    padding: 0 2rem;
    line-height: 36px;
  }
  display: $inline;
}

.buttons-html5 {
  margin-top: 4px;
  margin-bottom: 4px;
}

button.dt-button, div.dt-button, a.dt-button {
  background-color: $transparent-clr;
}

#monitor #campaign-table .odd.selected tr {
  background-color: $white-clr;
}

.over-hidden {
  overflow: $hidden;
}

.postfix {
  ~ input {
    width: calc(100% - 3.0rem);
  }
  &.long ~ input {
    width: calc(100% - 8.0rem);
  }
  @include postfix-styles;
}

.grey-border {
  border: 1px solid $light-grey-clr;
}

/*
*Mockup styles
*/
@media only screen and (max-width: 1300px) {
  .collapsible-header {
    padding: 0;
    line-height: 2rem;
    min-height: 2rem;
    overflow: $hidden;
  }
}

#monitor .collapsible-header {
  padding: 0 ;
  line-height: 1rem;
  min-height: 1rem;
  overflow: $hidden;
}

#modalsContainer {
  .collapsible-header {
    line-height: 3rem;
    i {
      width: 6rem;
    }
  }
  #monitor .collapsible {
    box-shadow: 0 0 0;
  }
  .collapsible-header {
    box-shadow: 0 1px 2px $btn-trans-hover-clr;
  }
}

.collapsible-header .col {
  padding: 0;
}

#monitor {
    .status-work {
      background: $work-clr;
    }

    .status-warning {
      background: $warning-clr;
    }

    .status-alarm {
      background: $alarm-clr;
    }

    .status-table td {
      text-align: center;
    }
}

.card i {
  &.big-icon {
    position: $absolute;
    font-size: 7rem;
    bottom: 10px;
    right: 0;
    opacity: 0.5;
  }
  &.big-iconx {
    position: $absolute;
    font-size: 7rem;
    bottom: 10px;
    right: 20px;
    opacity: 0.5;
  }
  &.small-iconx {
    position: $absolute;
    font-size: 2.5rem;
    top: 0;
    bottom: 0;
    right: 0;
    opacity: 0.5;
  }
}

.tabs .tab {
  letter-spacing: 0;
}

.tabs .tab .i2m-badge {
  display: initial;
}

.collapsible {
  border: 0;
  margin: 0;
  box-shadow: 0 0 0;
}

#monitor .tabs-vert {
  display: $inline;
  float: $none;
  text-align: left;
  line-height: 20px;
  height: 20px;
  color: $black-clr;
  .indicator {
    display: $none;
  }
}

.alarm-icon {
  position: $absolute;
  top: 15%;
  left: 2%;
}

@media screen and (max-width: 800px) {
  .mt-minut-45 {
    margin-top: 0px;
  }
}


input[type=file]::-webkit-file-upload-button {
  visibility: $hidden;
}

.pickit {
  margin-top: 1rem;
  margin-bottom: -1rem;
}

#monitor, #carwash{
    #traces-text {
      display: $block;
    }

    .fc-event-container .past-event {
      background-color: grey-color-clr;
      cursor: not-allowed;
      &:parent {
        background-color: grey-color-clr;
        cursor: not-allowed;
      }
    }
      .valign-wrapper {
        display: $flex;
      }
     .btn-dev {
      border: $none;
      line-height: 21px;
      margin: 2px;
      cursor: pointer;
      color: $white-clr;
      padding: 3px 10px;
      border-radius: 4px;
      cursor: pointer;
      letter-spacing: 0px;
      width: 100%;
      max-width: 230px;
      height: auto;
      display: inline-block;
      text-align: center;
      -webkit-transition: all .3s ease-out;
      transition: all .3s ease-out;
      &:hover {
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.4);
      }
    }
}

#monitor, #carwash {
    .btn {
       &:hover {
           box-shadow: 0 2px 5px rgba(0, 0, 0, 0.4)!important;
       }
   }

   .nano {
       .fa, .mdi, [class*=mdi-], [class^=mdi-] {
           color: inherit;
       }
   }

   button {
       .fa, .mdi, [class*=mdi-], [class^=mdi-] {
           color: $white-clr;
       }
       a {
           color: $white-clr;
       }
   }
}

#spinner-cm {
  position: $absolute;
  top: 80%;
  left: 50%;
}

.circle {
  bottom: 10px;
  @include circle-styles;
}

.first_circle {
  z-index: 3;
}

.second_circle {
  z-index: 2;
}

.contener_circle {
  &:hover {
    .second_circle {
      bottom: 75px;
    }
    height: 200px;
    width: 60px;
  }
  height: 70px;
  width: 60px;
}

#externalItems .col {
  padding-bottom: 0;
}

td .btn {
  letter-spacing: 0px;
  width: 55px;
  padding: 0;
}

.tooltipster-content {
  .fa-pencil:hover {
    color: #009688;
    cursor: pointer;
  }
  .fa-eye:hover {
    color: #ffc107;
    cursor: pointer;
  }
  .fa-check:hover {
    color: #00bcd4;
    cursor: pointer;
  }
  .fa-times:hover {
    color: #EF5350;
    cursor: pointer;
  }
}

.devicetypes {
    background-color: $devicetype-clr;
}

.rules {
    background-color: $work-clr;
}

.rule {
    background-color: $greener-clr;
}

.alarmrule {
    background-color: $alarmRule-clr;
}

#monitor, #carwash {
    .device-color {
        color: $device-clr;
    }

    .card i.medium-iconx {
      position: $absolute;
      font-size: 5rem;
      right: 30px;
      bottom: 8px;
      opacity: 0.5;
    }

    .pdf-table {
        page-break-inside: avoid;
    }
}

.keyusage-list-buttons {
  cursor: pointer;
  margin-top: 10px;
  .keyusage-icon {
    width: 30px !important;
    height: 30px !important;
    font-size: 16px;
    line-height: 1.75;
    background: $light-blue-clr;
    padding: 1px !important;
    border-radius: 50%;
    margin: -10px 6px -18px 0;
    color: $white-clr;
    transition: .2s ease-out;
    cursor: pointer;
  }
}

.addSelectButton {
  position: $absolute;
  right: 0px;
  margin-top: 8px;
}

/* autogenerated form in modals */

#monitor, #carwash {
  form.autogenerated-form .select2-container, i.prefix + select.select2 + .select2-container {
    margin-left: 3rem;
    width: calc(100% - 3rem)!important;
  }

  .keyusage-list-buttons .keyusage-icon {
    width: 30px;
    height: 30px;
    font-size: 16px;
    line-height: 1.75;
    background: $light-blue-clr;
    padding: 1px;
    border-radius: 50%;
    margin: -10px 6px -18px 0;
    color: $white-clr;
    transition: .2s ease-out;
    cursor: pointer;
  }

  .light-grey {
    background-color: $light-grey-clr;
  }
}

form.autogenerated-form {
  input, table {
    margin-left: 3rem;
    width: calc(100% - 3rem);
  }
  h2 {
    margin-bottom: 40px;
  }
}

.input-field label.active {
    margin-left: 2rem;
}

#emptyExternalCalanderItemsList {
  display: $none;
}

.sp-preview {
  margin: 0.5rem 0 0.5rem 2.5rem;
}

#div_carwash_keyusage div.sort {
  cursor: pointer;
  position: relative;
  &.active {
    &.desc:after {
      position: $absolute;
      border-bottom: 5px solid $darker-grey-clr;
      content: '';
      height: 0;
      width: 0;
      top: 20%;
      right: 10%;
      border: 5px solid $transparent-clr;
      border-top: 5px solid $darker-grey-clr;
    }
    &.asc:after {
      content: '';
      position: $absolute;
      top: 0%;
      right: 10%;
      height: 0;
      width: 0;
      border: 5px solid $transparent-clr;
      border-bottom: 5px solid $darker-grey-clr;
    }
  }
}

@media screen and (max-width: 1200px) {
  .overflow-table, .dataTable {
    overflow-x: $auto;
    display: $block;
  }
}



/* Preloader */

#preloader {
  position: $fixed;
  background-color: $white-clr;
  z-index: 9999;
  @include fixed-style-center;
}

#preloader-with-menu {
    position: absolute;
    background-color: $white-clr;
    z-index: 9999;
    @include fixed-style-center-with-menu;
}

.loader-position {
  position: $absolute;
  left: 45%;
  top: 100px;
}

.progress-content {
  position: $relative;
}

#monitor, #carwash {
    .preload-cm {
      position: $fixed;
      top: 50%;
      left: 55%;
      margin-top: -50px;
      z-index: 0;
      &.active {
        z-index: 1000;
      }
    }

    .s2-color-input, .color-label {
      color: $light-blue-clr;
    }

    /* Dropzone profile image */

    .dropzone.profile-image {
      border: $none;
      padding: 0px;
      width: 140px;
      height: 140px;
      min-height: 140px;
      .dz-preview {
        margin: 0px;
      }
      img {
        border-radius: 50%;
      }
    }
}

/* Main progress bar */

.progress.main-progress {
  z-index: 10000;
  margin: 0;
  background-color: $transparent-clr;
  display: $none;
  position: $fixed;
  top: 0;
}

#monitor, #carwash {
    .dataTables_filter, .dataTables_length {
      display: $none;
    }

    .switch {
      padding-top: 15px;
    }

    #searchbox {
      height: 2rem;
      line-height: 2rem;
    }

    .input-field .prefix {
      color: rgba(0,0,0,0.56);
      ~label {
          margin-left: 2rem;
      }
    }

    .search-fix {
      top: 0;
    }
}

.switch label .lever {
  margin: 0 10px;
}

#loading-spinner .preload-cm {
  left: 40%;
  position: $absolute;
  top: 39%;
  margin-top: 0px;
}

.switch label {
  margin: 0 5px;
}

#preloader .preload-cm {
  left: 0;
  right: 0;
  margin: 5% $auto;
  zoom: 2;
  top: 50%;
  margin-top: 0;
}

.remove-tag {
  font-size: 18px;
  float: left;
  margin-right: 10px;
}

.tag-element {
  margin-left: 5px;
}

#filesCollection li {
  height: 60px;
}

.circle-small {
  height: 25.2px;
  line-height: 25px;
  font-size: .8rem;
}

#filesCollection li {
  i {
    top: 9px;
  }
  p {
    margin-top: 9px;
  }
}

#monitor #formRow {
  form.autogenerated-form table {
    margin-left: 0;
    width: 100%;
  }
  .collapsible-header {
    line-height: 3rem ;
  }
}

@keyframes progress-animation {
  0% {
    width: 0%;
  }

  100% {
    width: 100%;
  }
}


.progress > .animate {
  animation: progress-animation 1s linear 1;
  animation-fill-mode: both;
}

/* Flex styles */

.row.flex {
  display: $flex;
}

.yay-hide .yaybar.yay-hide-to-small {
  position: $fixed;
}

#modalsContainer #products tbody input {
  margin-left: 0;
}

.top {
  height: 40px;
  padding-bottom: 0.5em;
}

.lean-overlay {
  top: 0;
}

.switch.switch-filterable label .lever:after {
  background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA\ ABkAAAAZAQMAAAD+JxcgAAAABlBMVEUAAAAAAAClZ7nPAAAAAXRSTlMAQObYZgAAAB\ xJREFUCNdjIALI//8BJRCA/f8DCIEM/kMIogEAtUAJ96V/58IAAAAASUVORK5CYII=');
  background-size: cover;
}

#monitor {
  .tabs {
    overflow: $hidden;
  }
  .fa-eye:before {
    content: "\f06e";
  }
}

/* Monitor button types */

.device {
  background-color: $device-clr;
}

.devicetype {
  background-color: $devicetype-clr;
}

.sensor {
  background-color: $sensor-clr;
}

.sensortype {
  background-color: $sensortype-clr;
}

.parameter {
  background-color: $parameter-clr;
}

.parameter.compound {
    background-color: $parameter-compound-clr;
}

.parameter-type {
  display: inline-block;
  border: 1px solid $parameter-clr;
  border-radius: 4px;
  padding: 2px 4px;
  color: $parameter-clr;
}

.parameter-type.compound {
  border-color: $parameter-compound-clr;
  color: $parameter-compound-clr;
}

/* Monitor special date inputs for presentation */

input.pikaday.title {
  padding: 0;
  margin: 0;
  width: 100px;
  height: 1.5rem;
  border: 0;
  box-shadow: $none;
  font-size: 16px;
  cursor: pointer;
  &:focus {
    box-shadow: $none;
    border: 0;
  }
}

#monitor .chat .contacts .label {
  color: $white-clr;
  background: #35384C;
}

#edit-clients-modal {
  #results {
    display: $absolute;
    border: 0px;
    li:hover {
      background-color: $light-blue-clr;
      color: $white-clr;
    }
  }
  .indicator {
    right: 797px;
    left: 0px;
  }
}

#new-clients-modal .indicator {
  right: 797px;
  left: 0px;
}

.modal{

    .modal-footer {
       padding: 6px 15px;
    }
}

.sensorNoClr {
    background: #EF5350;
}

#monitor {
    .icon-ebkf {
        background:url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADMAAAAzCAYAAAA6oTAqAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAG1JREFUeNrs2kEKwDAIBMBs6f+/nPQF7aUEDbPXnAYJipj5ZPRJ3h6vcVBgYGBgYMrl/qNZqQwMDAwMDAwMzNbZbMfSIyoDAwMDA3NC07TQgIGBgYGBgYGpM5tVueLIFyZNIP4MDAwMTL8sAQYAP0MHb8I12VwAAAAASUVORK5CYII=');
        background-size: cover;
        background-position: center;
        width: 30px;
        height: 30px;
        margin: 0 auto;
        position: Absolute;
        top: 13px;
        left: 13px;
    }
}

th.right-align  td {
    text-align:right;
}

.card-text {
    position: absolute;
    padding: 20px;
    top: 0px;
    width: 100%;
    background: rgba(0,0,0,0.5);
}

.image-container {
    width: 60%;
    min-height: 300px;
    background: $light-grey-clr;
    margin: 0 auto;
    border-radius: 20px;
}
.input-field {
    padding-bottom: 1rem;
    margin-top: 1.6rem;
}

.hint-container {
    position: absolute;
    top: 45px;
    left: 40px;
    color: $light-blue-clr;
    opacity: 0;
    transition: opacity 0.2s linear;
}

input:focus ~.hint-container {
    opacity: 1;
}

a {
  text-decoration: none !important;
}

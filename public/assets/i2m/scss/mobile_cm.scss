
.brand-logo {
  display: flex !important;

}

.navbar-bottom {
  bottom: 0px;
  position: fixed;
}

.dropdown-content {
  margin-top: 3rem;
}

.card i.big-iconx {
  font-size: 2rem;
  right: -5px;
}

//
//.row {
//  margin-right: 0px;
//  .col {
//    padding-right: 0px;
//  }
//}
//
//#cm .card .title {
//  .button {
//    width: 25px;
//    margin: 0 5px;
//    > i {
//      font-size: 2rem;
//    }
//  }
//  .spacer {
//    display: none;
//  }
//}
//
//#cm .content-wrap {
//    padding: 55px 2rem 2rem;
//}
//
//td .btn {
//  height: 55px;
//  line-height: 55px;
//  margin: 2px;
//}
//
//.table, .fc-view-container {
//  overflow-x: auto;
//  display: block;
//}
//
//#cm {
//  .clients-table_previous, .clients-table_next {
//    display: none;
//  }
//}
//
//.dataTables_wrapper .dataTables_paginate .paginate_button {
//  padding: 0.5em 0.5em;
//  margin-left: 0px;
//}
//
//#cm {
//  .clients-table_length, .help-fixed, .chat-fixed, .indicator {
//    display: none;
//  }
//}
//
//.content-wrap {
//  min-height: -webkit-calc(100vh - 90px);
//  min-height: calc(100vh - 90px);
//  padding-top: -webkit-calc(90px + 1.3rem);
//  padding-top: calc(90px + 1.3rem);
//}
//
//.modal {
//  width: 100%;
//  height: 80%;
//}
//
//.tw-1, .tw-2, .tw-3, .tw-4, .tw-5, .tw-6, .tw-7, .tw-8 {
//  min-width: 80px;
//}
//
//button a {
//  zoom: 1.2;
//}
//
//#cm {
//  .dataTables_wrapper .dataTables_filter {
//    float: left;
//    width: 100%;
//  }
//  .tooltipster-base, #toast-container {
//    display: none;
//  }
//}
//
//::-webkit-scrollbar {
//  -webkit-appearance: none;
//  width: 2px;
//}
//
//::-webkit-scrollbar-thumb {
//  background-color: rgba(66, 165, 245, 1);
//  -webkit-box-shadow: 0 0 1px rgba(255, 255, 255, 0.2);
//}
//
//#cm .tooltipster-base {
//  display: none;
//}
//
//@media all and (orientation: landscape) {
//  html {
//    font-size: 0.8rem;
//  }
//  .nano {
//    zoom: 1.3;
//  }
//  .dropdown-content li {
//    line-height: inherit;
//  }
//  .content-wrap {
//    padding: 71.9px 0 1.3rem;
//  }
//  #calendarTrashcan {
//    display: none;
//  }
//  .navbar-top {
//    height: 45px;
//    line-height: 45px;
//  }
//  .yay-toggle {
//    height: 45px;
//    line-height: 45px;
//    padding-top: 15px;
//  }
//  .yaybar .top {
//    height: 45px;
//  }
//}
//
//@media all and (orientation: portrait) {
//
//  #calendarTrashcan {
//    display: none;
//  }
//  #cm {
//    .height-fix {
//      height: 90px;
//      line-height: 90px;
//    }
//    .content-wrap {
//      padding: 90px 0;
//    }
//     .dropdown-content li > {
//      .sp-cancel, a, span {
//        line-height: 48px;
//      }
//    }
//  }
//}
//
//.widget > .title .button {
//  opacity: 1;
//}
//
//button a {
//  zoom: 1;
//  color: #fff;
//}
//
//#cm {
//  .on-mobile {
//    visibility: hidden;
//  }
//  .content-wrap {
//    margin-left: 0px;
//  }
//}
//
//.navbar-top .user {
//  margin: 0 25px;
//}
//
//#finance-charts {
//  overflow-x: auto;
//  overflow-x: auto;
//}
//
//#finance-charts-details, #finance-chart-share-payments-detail {
//  overflow-x: auto;
//}
//
//#user-data .collapsible-header {
//  line-height: 5rem;
//  height: 5rem;
//  border-bottom: 5px solid #f4f4f4;
//  padding-left: 5px;
//  i {
//    line-height: 5rem;
//    height: 5rem;
//  }
//}
//
//.pika-single.is-bound {
//  display: none;
//}
//
//#cm .tbl {
//  display: table;
//}
//
//.no-wrap {
//  white-space: nowrap;
//}
//
//@media screen and (max-width: 485px) {
//  #cm .tab-fix {
//    line-height: 24px;
//    white-space: pre-wrap;
//  }
//}

//#cm {
//    nav {
//        transition: top 0.2s ease-in-out;
//    }
//
//    .nav-down {
//        transition: top 0.2s ease-in-out;
//    }
//
//    .nav-up {
//        top: -90px;
//        transition: top 0.4s ease-in-out;
//    }
//
//    #flot-base, #flot-text {
//        top: 15px;
//    }
//}

@import '_flags';

#chart-monitor-temperatures .legend table tr, #chart-monitor-smartheatinga .legend table tr, #chart-monitor-smartheatingb .legend table tr, #chart-monitor-smartheatingc .legend table tr, #finance-charts-sums .legend table tr {
  float: left;
}
#chart-monitor-temperatures, #chart-monitor-chemistry, #chart-monitor-smartheatinga, #chart-monitor-smartheatingb, #chart-monitor-smartheatingc, #chart-monitor-waterpressure, #chart-monitor-dosage {
  width: 100%;
  height: 300px;
  margin-top: 55px;
}

#monitor-chemistry-chart-handler {
  position: relative;
}

#chart-monitor-temperatures div.legend, #chart-monitor-smartheatinga div.legend, #chart-monitor-smartheatingb div.legend, #chart-monitor-smartheatingc div.legend {
  left: 4.2% !important;
  top: -95px;
  width: 100% !important;
  position: absolute;
  line-height: 1px !important;
}

#chart-monitor-chemistry div.legend {
  left: 4.2% !important;
  top: -95px;
  width: 50px;
  position: absolute;
  line-height: 1px !important;
}

#chart-monitor-dosage {
  width: 100%;
  height: 300px;
  margin-top: 55px;
}

#chartChemistryLegend {
  position: absolute;
  top: 0px;
  right: 0px;
}

#chart-monitor-temperatures div.legend > div:nth-child(1), #chart-monitor-smartheatinga div.legend > div:nth-child(1), #chart-monitor-smartheatingb div.legend > div:nth-child(1) #chart-monitor-smartheatingc div.legend > div:nth-child(1) {
  opacity: 0.3;
}

.legendColorBox {
  width: 15px;
  height: 15px;
}

#finance-charts-details .legend table, #finance-charts-sums .legend table, #chart-monitor-temperatures .legend table, #chart-monitor-smartheatinga .legend table, #chart-monitor-smartheatingb .legend table, #chart-monitor-smartheatingc .legend table {
  width: auto;
  opacity: 1;
}

.centered-box-400 {
  max-width: 400px;
  margin: 0 auto;
}

.fontwhite {
 color: white;
}

.width3 {
  width: 33.333%
}

#monitor-dosage-chart-handler {
  position: relative;
  height: 300px;
}

.card {
  overflow: auto;
}

#cmcarwashes-table.table {
  display: block;
  overflow: auto;
}

.card-panel {
  .mt-0 {
    .col{
      &.right-align {
        display: flex;
        justify-content: flex-start;
        flex-direction: row-reverse;
      }
    }
  }
}

.row {
  .col {
    .dt-buttons {
      float: right;
      margin-left: 5px;
      .dt-button {
        border: none;
      }
    }
  }
}

.last-table-fix {
  min-width: 258px;
}

.mdi-navigation-expand-more {
  line-height: 53px !important;
}

.dropdown-content {
  &.active {
    top: 50px !important;

    li {
      min-width: fit-content;
    }
  }
}

@media screen and (max-width: 420px) {
  .nav-mobile {
    display: flex;
    overflow: auto;
    width: 100%;

    li > a {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
}

#cm .select2-results__option.select2-results__option--highlighted {
    background-color: #ed413d;
}

#cm .select2-container {
    margin-bottom: 15px;
}

.dt-button-collection a, #export-button {
    background-color: #ed413d !important;
    color: #FFF !important;
}

.btn-show {
    background-color: #ffc107!important;
}

.btn-edit {
    background-color: #009688!important;
}

.card i.big-iconx {
    font-size: 3rem;
    right: 0px;
}

.navbar-top .new-brand-logo>img {
    width: auto;
    height: 100%;
    padding: 16px;
}

.photo-wrap {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    width: 20%;

    @media screen and (max-width: 576px) {
        width: 100%;
        align-items: center;
    }
}
@media screen and (max-width: 576px) {
    #modalsContainer{
        .btn {
            width: 100%;
        }
    }
}

@media screen and (min-width: 577px) {
    #modalsContainer {
        .modal-footer {
            .btn {
                &.custom-margin{
                    margin-left: 15px !important;
                }
            }
        }
    }
}

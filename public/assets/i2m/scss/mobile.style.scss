@import '_mixin';
@import '_helpers';

#monitor footer {
  text-align: center;
  margin: 0;
}

.row {
    margin-right:0px;
}

.row .col {
    padding-right:0px;
}

#new-monitorparameters-modal .input-field:nth-child(5), #edit-monitorparameters-modal .input-field:nth-child(4) {
  padding-bottom: 20px;
}

#monitor .clockpicker-popover {
  display: none;
}

#monitor .a-l {
    text-align:left;
}

td .btn {
  height: 55px;
  line-height: 55px;
  margin: 2px;
}

.collection .collection-item.avatar .secondary-content {
  position: inherit;
}

#filesCollection li {
  height: inherit;
}

.collection .collection-item.avatar {
  padding-left: 0px;
}

#filesCollection li i {
  top: 35px;
}

.map {
  display: none;
}

#monitor .card .title {
  .button {
    width: 25px;
    margin: 0 5px;
    > i {
      font-size: 2rem;
    }
  }
  .spacer {
    display: none;
  }
}

.table, .fc-view-container {
  overflow-x: auto;
  display: block;
}

#monitor {
  &.yay-hide .yaybar.yay-hide-to-small {
    left: -240px;
  }
  .clients-table_previous, .clients-table_next {
    display: none;
  }
}

.dataTables_wrapper .dataTables_paginate .paginate_button {
  padding: 0.5em 0.5em;
  margin-left: 0px;
}

#monitor {
  .clients-table_length, .help-fixed, .chat-fixed, .indicator {
    display: none;
  }
}

.yay-toggle {
  height: 90px;
}

.navbar-top {
  height: 90px;
  line-height: 90px;
}

.yay-toggle {
  padding-top: 40px;
  div {
    width: 30px;
  }
}

.yaybar .top {
  height: 90px;
}

.content-wrap {
  @include content-wrap-mobile-padding-top;
  @include padding-l-r-null;
  @include mobile-height;
}

.modal {
  width: 100%;
  height: 80%;
}

.tw-1, .tw-2, .tw-3, .tw-4, .tw-5, .tw-6, .tw-7, .tw-8 {
  min-width: 120px;
}

button a {
  zoom: 1.2;
}

.navbar-top .brand-logo > img {
  max-width: 120px;
  height: auto;
}

#monitor {
  .dataTables_wrapper .dataTables_filter {
    float: left;
    width: 100%;
  }
  .tooltipster-base, #toast-container {
    display: none;
  }
}

::-webkit-scrollbar {
  -webkit-appearance: none;
  width: 2px;
}

::-webkit-scrollbar-thumb {
  background-color: rgba(66, 165, 245, 1);
  -webkit-box-shadow: 0 0 1px rgba(255, 255, 255, 0.2);
}

#monitor .tooltipster-base {
  display: none;
}

@media all and (orientation: landscape) {
    #monitor {
    nav {
            transition: top 0.2s ease-in-out;  
        }

        .nav-down {
            transition: top 0.2s ease-in-out;
        }

        .nav-up {
            top: -45px;
            transition: top 0.4s ease-in-out;
        }

        .nav-top-up {
            height: 0px;
            transition: top 0.4s ease-in-out;

        }
        
        table.dataTable {
            margin-top:30px;
        }
    }

  html {
    font-size: 0.8rem;
  }
  .yay-hide .yaybar.yay-hide-to-small {
    left: -240px;
  }
  #monitor .navbar-top .nav-wrapper > ul i {
    height: 45px;
    line-height: 45px;
  }
  .dropdown-content li {
    line-height: inherit;
  }
  
  #calendarTrashcan {
    display: none;
  }
  
  .navbar-top {
    height: 45px;
    line-height: 45px;
  }
  
  .yay-toggle {
    height: 45px;
    line-height: 45px;
    padding-top: 15px;
  }
  
  .yaybar {
    .top {
      height: 45px;
    }
    padding-bottom: 55px;
  }
  
  #monitor .dropdown-content li > {
    .sp-cancel, a, span {
      line-height: 48px;
    }
  }
  .menu-mobile{
    padding-top: 14px;
    padding-bottom: 14px;
  }
}

@media all and (orientation: portrait) {
    
    #monitor {
        nav {
            transition: top 0.2s ease-in-out;  
        }

        .nav-down {
            transition: top 0.2s ease-in-out;
        }

        .nav-up {
            top: -90px;
            transition: top 0.4s ease-in-out;
        }

        .nav-top-up {
            height: 0px;
            transition: top 0.4s ease-in-out;
        }
        
        .brand-logo {
            padding-top: 35px;
        }
        
        .height-fix {
            height: 90px;
            line-height: 90px;
        }
        
        #calendarTrashcan {
            display: none;
        }
        
        table.dataTable {
            margin-top:30px;
        }
    }
    .menu-mobile{
        padding-top: 36px;
        padding-bottom: 36px;
      }
}

.widget > .title .button {
  opacity: 1;
}

button a {
  zoom: 1;
  color: #fff;
}

#monitor .content-wrap {
  margin-left: 0px;
}

.navbar-top .user {
  margin: 0;
}

#finance-charts {
  overflow-x: auto;
  overflow-x: auto;
}

#finance-charts-details, #finance-chart-share-payments-detail {
  overflow-x: auto;
}

#user-data .collapsible-header {
  line-height: 5rem;
  height: 5rem;
  border-bottom: 5px solid #f4f4f4;
  padding-left: 5px;
  i {
    line-height: 5rem;
    height: 5rem;
  }
}

.pika-single.is-bound {
  display: none;
}

#monitor .tbl {
  display: table;
}

.no-wrap {
  white-space: nowrap;
}

.input-field .prefix {
  left: 0;
}

.modal .modal-footer {
  padding: 5px 25px;
}

.content-wrap .page-title {
  margin: 0px;
}

.rating {
  zoom: 0.7;
}

#edit-clients-modal, #new-clients-modal {
  overflow-x: hidden;
}

.timeline-block:nth-child(even) .timeline-content .timeline-date {
  left: auto;
  right: auto;
  text-align: left;
}

#tooltip {
  bottom: 20%;
  top: inherit !important;
  left: 50px !important;
}

.yaybar {
  .nano-content > ul li > {
    .sp-cancel, a {
      font-size: 1.5rem;
      padding: 20px;
    }
  }
  width: 100%;
  padding-bottom: 120px;
}


#cm table.dataTable thead .sorting:after, #monitor table.dataTable thead .sorting:after {
    top: 13px;
}

#cm table.dataTable thead .sorting:before, #monitor table.dataTable thead .sorting:before {
    top: 25px;
}

#cm table.dataTable thead .sorting_desc:after, #monitor table.dataTable thead .sorting_desc:after, #cm table.dataTable thead .sorting_desc_disabled:after, #monitor table.dataTable thead .sorting_desc_disabled:after {
    top: 24px;
}


#cm table.dataTable thead .sorting_asc:after, #monitor table.dataTable thead .sorting_asc:after, #cm table.dataTable thead .sorting_asc_disabled:after, #monitor table.dataTable thead .sorting_asc_disabled:after {
    top: 16px;
}


.pikaday {
    margin-left: 3rem;
}

.in-content label {
    color: #9e9e9e;
    position: absolute;
    top: .8rem;
    left: 1rem;
    font-size: 1rem;
    cursor: text;
    transition: .2s ease-out;
}

.i2m-corner.i2m-zoom {
    transform: scale(2);
}

.i2m-corner.i2m-zoom:hover {
    transform: scale(2);
}

.i2m-button {
    opacity: 0.5;
}

.i2m-button {
    &:hover,:active{
        opacity: 0.5;
    }
}

.fullscreen, .minimize {
    display:none;
}

.menu-mobile {
    line-height: 16px;
    display: inline;
    cursor: pointer;
    overflow: hidden;
}
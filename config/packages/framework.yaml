# see https://symfony.com/doc/current/reference/configuration/framework.html
framework:
    secret: '%env(APP_SECRET)%'
    ide: phpstorm
    session: ~
    php_errors:
        log: true
    http_client:
        default_options:
            http_version: '1.1'
        scoped_clients:
            notification.client:
                base_uri: '%env(NOTIFICATION_API_URL)%'
                scope: '%env(NOTIFICATION_API_URL)%'
                headers:
                    Accept: 'application/json'
                    ContentType: 'application/json'
                    Authorization: 'Bearer %env(NOTIFICATION_API_TOKEN)%'

#    annotations: false
#    http_method_override: false
#    handle_all_throwables: true
#
#    # Enables session support. Note that the session will ONLY be started if you read or write from it.
#    # Remove or comment this section to explicitly disable session support.
#    session:
#        handler_id: null
#        cookie_secure: auto
#        cookie_samesite: lax
#
#    #esi: true
#    #fragments: true

security:
    enable_authenticator_manager: true

    password_hashers:
        Symfony\Component\Security\Core\User\PasswordAuthenticatedUserInterface: 'auto'

    providers:
        app_user_provider:
            entity:
                class: App\Entity\User
    firewalls:
        dev:
            pattern: ^/(_(profiler|wdt)|css|images|js)/
            security: false
        main:
            stateless: true
            provider: app_user_provider
            custom_authenticator: App\Security\ApiTokenAuthenticator
            json_login:
                check_path: api_login

    # Easy way to control access for large sections of your site
    # Note: Only the *first* access control that matches will be used
    access_control:
        - { path: ^/login_api$, role: PUBLIC_ACCESS }
        - { path: ^/cm/user/mobile_devices$, role: ROLE_CM }
        - { path: ^/cm/user, allow_if: "is_granted('ROLE_SUBSCRIPTION_BASIC') and is_granted('ROLE_CM_OWNER')" }
        - { path: ^/cm/profile, roles: ROLE_CM }
        - { path: ^/external_payment, role: PUBLIC_ACCESS }
        - { path: ^/api/users/password, roles: PUBLIC_ACCESS }
        - { path: ^/api/users, allow_if: "is_granted('ROLE_SUBSCRIPTION_BASIC') and is_granted('ROLE_CM_OWNER')" }
        - { path: ^/api/admin, roles: ROLE_SUPERADMIN }
        - { path: ^/api/service_issues, allow_if: "is_granted('ROLE_CM_OWNER') or is_granted('ROLE_CM_SERVICE')" }
        - { path: ^/api/service, roles: ROLE_SUPERADMIN }
        - { path: ^/api/gateway/bkf-service, roles: ROLE_SUPERADMIN }
        - { path: ^/api/gateway/wla-admin, roles: ROLE_CM_LOYAL_APP }
        - { path: ^/api/doc, role: PUBLIC_ACCESS }
        - { path: ^/api/?, role: ROLE_CM }
        - { path: ^/cm/carwash_finance/turnover, allow_if: "is_granted('ROLE_SUBSCRIPTION_BASIC') and is_granted('ROLE_CM_FINANCE')" }
        - { path: ^/api/loyalty?, allow_if: "is_granted('ROLE_SUBSCRIPTION_BASIC') and is_granted('ROLE_CM_LOYALSYSTEM_KEYLIST_AND_CLIENTS')" }
        - { path: ^/invoices/, allow_if: "is_granted('ROLE_SUBSCRIPTION_BASIC') and is_granted('ROLE_SUPERADMIN')" }
        - { path: ^/cm/alarm, roles: ROLE_CM_ALARMS_AND_TECHNICAL_DATA }
        - { path: ^/cm/privacy_policy, role: PUBLIC_ACCESS }
        - { path: ^/cm/self_invoice?, role: PUBLIC_ACCESS }
        - { path: ^/cm, roles: ROLE_CM }
        - { path: ^/administration, roles: ROLE_CM_ADMINISTRATION }
        - { path: ^/, roles: ROLE_USER }

    role_hierarchy:
        ROLE_CM: ROLE_USER
        ROLE_CM_FINANCE: ROLE_CM

        ROLE_CM_LOYALSYSTEM_KEYLIST_AND_CLIENTS: ROLE_CM

        ROLE_CM_LOYAL_APP: ROLE_CM

        ROLE_CM_SERVICE: ROLE_CM
        ROLE_CM_ALARMS_AND_TECHNICAL_DATA: ROLE_CM

        ROLE_CM_OWNER:
          - ROLE_CM_FINANCE
          - ROLE_CM_SERVICE
          - ROLE_CM_ALARMS_AND_TECHNICAL_DATA
          - ROLE_CM_LOYALSYSTEM_KEYLIST_AND_CLIENTS

        ROLE_SUPERADMIN:
          - ROLE_CM_ADMINISTRATION
          - ROLE_CM_FINANCE
          - ROLE_CM_SERVICE
          - ROLE_CM_ALARMS_AND_TECHNICAL_DATA
          - ROLE_CM_LOYALSYSTEM_KEYLIST_AND_CLIENTS

        ROLE_SUBSCRIPTION_BASIC:
          - ROLE_CM

        ROLE_SUBSCRIPTION_PREMIUM:
          - ROLE_CM
          - ROLE_SUBSCRIPTION_BASIC

when@test:
    security:
        password_hashers:
            # By default, password hashers are resource intensive and take time. This is
            # important to generate secure password hashes. In tests however, secure hashes
            # are not important, waste resources and increase test times. The following
            # reduces the work factor to the lowest possible values.
            Symfony\Component\Security\Core\User\PasswordAuthenticatedUserInterface:
                algorithm: auto
                cost: 4 # Lowest possible value for bcrypt
                time_cost: 3 # Lowest possible value for argon
                memory_cost: 10 # Lowest possible value for argon

sentry:
    dsn: '%env(SENTRY_DSN)%'
    options:
        send_default_pii: true
        default_integrations: true

#    If you are using Monolog, you also need these additional configuration and services to log the errors correctly:
#    https://docs.sentry.io/platforms/php/guides/symfony/#monolog-integration
#    register_error_listener: false

#    monolog:
#        handlers:
#            sentry:
#                type: service
#                id: Sentry\Monolog\Handler

#    services:
#        Sentry\Monolog\Handler:
#            arguments:
#                $hub: '@Sentry\State\HubInterface'
#                $level: !php/const Monolog\Logger::ERROR

old_sound_rabbit_mq:
    connections:
        default:
            url: '%env(RABBITMQ_URL)%'
#    producers:
#        # use 'old_sound_rabbit_mq.task_producer' service to send data.
#        task:
#            connection:       default
#            exchange_options: { name: 'task', type: direct }
#    consumers:
#        task:
#            connection:       default
#            exchange_options: { name: 'task', type: direct }
#            queue_options:    { name: 'task'}
#            callback:         App\Consumer\TaskConsumer

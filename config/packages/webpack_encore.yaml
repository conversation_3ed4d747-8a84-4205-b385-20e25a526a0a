webpack_encore:
    # The path where Encore is building the assets - i.e. Encore.setOutputPath()
    output_path: '%kernel.project_dir%/public/build'
    # If multiple builds are defined (as shown below), you can disable the default build:
    # output_path: false

    # Set attributes that will be rendered on all script and link tags
    script_attributes:
        defer: true
        # Uncomment (also under link_attributes) if using Turbo Drive
        # https://turbo.hotwired.dev/handbook/drive#reloading-when-assets-change
        # 'data-turbo-track': reload
    # link_attributes:
        # Uncomment if using Turbo Drive
        # 'data-turbo-track': reload

    # If using Encore.enableIntegrityHashes() and need the crossorigin attribute (default: false, or use 'anonymous' or 'use-credentials')
    # crossorigin: 'anonymous'

    # Preload all rendered script and link tags automatically via the HTTP/2 Link header
    # preload: true

    # Throw an exception if the entrypoints.json file is missing or an entry is missing from the data
    # strict_mode: false

    # If you have multiple builds:
    # builds:
        # pass "frontend" as the 3rg arg to the Twig functions
        # {{ encore_entry_script_tags('entry1', null, 'frontend') }}

        # frontend: '%kernel.project_dir%/public/frontend/build'

    # Cache the entrypoints.json (rebuild Symfony's cache when entrypoints.json changes)
    # Put in config/packages/prod/webpack_encore.yaml
    # cache: true

framework:
    assets:
        json_manifest_path: '%kernel.project_dir%/public/compiled/pdf-reports/manifest.json'

#when@prod:
#    webpack_encore:
#        # Cache the entrypoints.json (rebuild Symfony's cache when entrypoints.json changes)
#        # Available in version 1.2
#        cache: true

#when@test:
#    webpack_encore:
#        strict_mode: false

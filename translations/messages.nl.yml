dialog.close: Close
dialog.save: Save
dialog.agree: Agree
dialog.disagree: Disagree
dialog.yes: "Yes"
dialog.no: "No"
dialog.cancel: Cancel
dialog.confirm: Confirm
dialog.create: Create
dialog.edit: Edit
dialog.delete: Delete
notify.success: Success
notify.info: Info
notify.warning: Warning
notify.error: Error
notify.something-wrong-here: Something wrong here...
notify.technical-departament-informed: the technical department has already been informed about it
notify.service-request: Submit a service request
notification.details: Notification details
notification.new: NEW NOTIFICATION
notification.description: "Description:"
notificationrules.go-to-rule: Go to Notification Rule
notification.rules-details: Notification rules details
notification.rules-conditions: Notification conditions
notification.rules-confirmation-text: Do you really want to delete notification rule
notification.rules-create: Create notification rule
notification.rules-added: New notification rule added
notification.rules-edit: Edit notification rule
notification.rules-updated: Notification rule has been updated
notification.rules-delete: Delete notification rule
notification.rules-removed: Notification rule has been removed
form.remember-me: Remember me
form.login: Login
form.username: Username
form.password: Pasword
form.forgot-password: Forgot your password?
form.cmrc-beta: CMRC Beta
authentication.access-denied: Access denied.
authentication.access-denied-to-requested-action: Access denied to requested action.
authentication.user-has-no-roles: This email is already used
authentication.subscription-blocked: Your subscription is blocked
security.bad-credentials: Bad credentials
actions.show: Show
companyname.show: eBKF
actions.back: Go back to main page
actions.error: "An error has occured:"
actions.edit: Edit
actions.copy: Copy
actions.assign-workers: Assign a worker
action.assign-subscription: Assign subscription
actions.delete: Delete
actions.history: History
actions.history-of-changes: History of changes
actions.back-to-list: Back to the list
actions.go-back: Return
actions.go-back-2: Back
actions.table-empty: This table is empty.
actions.table-empty-add: to add data.
actions.table.country: Country
actions.table-timeline: This timeline is empty. Please add an event.
actions.empty-table-timeline: This timeline is empty. Would You like to add an event?
actions.empty-table-events-timeline: This timeline is empty
actions.alarm-empty: No alarms to display
actions.device-empty: No linked devices
actions.plan: Plan
actions.unlink: Delete the collection
actions.close-modal: Close modal
actions.confirm: Confirm
actions.confirm-read: Confirm read
actions.cancel: Cancel
actions.save: Save
actions.unassign: Unassign
messages.saved: Saved
messages.error-occurred: Unexpected error occurred. The service was informed.
clients.list: Clients
clients.details: Clients details
clients.primary-data: Client details
clients.invoices-list: Client invoices
clients.invoice-issue: Generate an invoice
clients.missing-invoice-data: "Missing invoice data!"
clients.fill-missing-invoice-data-box: Complete the missing invoice data to generate the invoice
clients.transaction-addition: "BKE Keys Top Up's"
clients.transaction-subtraction: BKF Keys Payments
client.history: Historia klienta
history.changed-email: Changed email
history.changed-phone: Changed telephone numer
history.changed-firstname: Changed first name
history.changed-lastname: Changed surname
history.changed-websited: Changed website
history.changed-country: Changed country
history.changed-fax: Changed fax numer
history.changed-mobile: Changed mobile numer
history.changed-regon: Changed regon
history.changed-nip: Changed TAX ID
history.changed-city: Changed city
history.changed-street: Changed street
history.changed-postcode: Changed postcode
history.changed-province: Changed province
history.changed-shipping: Changed shipping option
changed-shipping_name: Changed name of shipping
client.deleted: Client deleted
client.restored: Client restored
client.created: Client created
clients.groups: Clients groups
clients.create: Create a client
clients.clients-add: Client has been added
clients.removed: Client has been removed
clients.delete: Delete client
clients.delete-confirmation-text: Do you really want to delete a client?
clients.email-duplication: The email is already registered.
clients.edit: Edit client
clients.clients-updated: Client data has been updated
clients.invoice-cannot-be-generated: Invoice cannot be generated for this Client
clients.invoice-no-transactions-selected: No transactions to generate invoice. Assign BKF Key to the customer in tab and try again
clients.restore: Restore
clients.restored: Client has been restored
clients.localization: Client localization
clients.satisfaction-rating: satisfaction rating
clients.set-satisfaction-rating: satisfaction rating of client has been adding
clients.editcard: Edit client card
clients.find-by-nip-or-email: "On the first step, provide the Tax Identification Number and email address. We will check whether such a Client exists."
clients.invoices-select-transactions: "Step 1: Select transactions to invoices"
clients.invoices-put-invoice-number-text: "Step 2: Put invoice number and payment date. Invoice will be generated for selected above transactions"
user.details: User details
user.timezone: Timezone
clientsgroups.list: Clients groups
clientsgroups.create: Adding a group of clients
clientsgroups.clientsgroups-add: Clients group had been added
clientsgroups.clientsgroups-re-add: Clients group had been restored
clientsgroups.delete: Delete clients group
clientsgroups.edit: Edit clients group
clientsgroups.details: Clients details
clientsgroups.clientsgroups-updated: Clients group has been updated
clientsgroups.delete-confirmation-text: Do You really want to delete a clients group?
clientsgroups.removed: Clients groups has been removed
clientsgroups.restore: Restore
clientsgroups.restored: Clients groups has been restored
clientsgroups.delete-has-children-text: You can not delete a parent group
content.dashboard: Dashboard
content.help: Help
content.helppleasewait: Please wait...
content.maximalize: maximalize
content.minimilize: Minimalize
clientsworkers.list: Client workers
clientsworkers.create: Add new client
clientsworkers.details: Workers details
clientsworkers.edit: Edit worker
clientsworkers.delete: Delete worker
clientsworkers.delete-confirmation-text: Do You really want to delete worker?
clientsworkers.removed: Worker has been deleted
clientsworkers.restore: Restore
clientsworkers.restored: Worker has been restored
clientsworkers.clientwsorkers-add: The worker has been added
clientsevents.list: Client events
clientsevents.create: Add new events
clientsevents.details: Event details
clientsevents.edit: Edit event
clientsevents.delete: Delete event
clientsevents.delete-confirmation-text: Are you really want to delete event?
clientsevents.removed: Event has been removed
clientsevents.measureunits-add: Event has been added
clientsevents.create-time: Event creation time
clientsevents.event-time: Event time
clientsevents.measureunits-updated: Event has been updated
clientsevents.restore: Restore
clientsevents.restored: Event has been restored
clientsevents.clientsevents-add: Event has been added to client
clientsevents.add-file: Add file
clientsevents.added-file: File has been added
clientsevents.file-uploaded: File uploaded
clientsevents.invalid-file-type: "Incorrect file type. Upload imahe, pdf or video file"
events.list: Task list
events.create: Create new task
events.details: Task details
events.edit: Edit task
events.events-updated: Task has been updated
events.delete: Delete task
events.delete-confirmation-text: Do you really want to delete task?
events.removed: Task has been removed
events.restore: Restore
events.restored: Task has been restored
events.events-add: Task has been added
events.add-file: Add file
events.event-planned-start-time: Planned start time
events.event-planned-end-time: Planned end time
events.event-done-time: Done
events.event-type: Task type
events.event-campaign: Campaign
events.event-file-name: File
events.event-product: Product
events.event-user: User
events.added-file: File has been added
events.invalid-file-type: "Incorrect file type. Upload imahe, pdf or video file"
events.show-ready: Show done tasks
events.show-planned: Show planned tasks
events.show-unplanned: Show not planned tasks
events.show-overdue: Show overdue tasks
events.done-place: The place of event done
eventtypes.list: Task types list
eventtypes.create: Create new task types
eventtypes.details: Task type details
eventtypes.edit: Edit task type
eventtypes.delete: Delete task type
eventtypes.delete-confirmation-text: Do you really want to delete task type -
eventtypes.removed: Task type has been removed
eventtypes.eventtypes-add: Task type has been added
eventtypes.eventtypes-re-add: Task type has been restored
eventtypes.eventtypes-updated: Task type has been updated
eventtypes.restore: Restore
eventtypes.restored: Task type has been restored
eventtypestocampaign.list: List of event types campaigns
eventtypestocampaign.eventtypestocampaign-set-order: Order has been changed
eventtypestocampaign.timeinterval: "Time period (in days):"
eventtypestocampaign.eventtypestocampaign-add: The event has been added
eventtypestocampaign.eventtypestocampaign-updated: The event has been updated
eventtypestocampaign.delete-confirmation-text: Do You really want to delete this event?
eventtypestocampaign.delete: Delete event
eventtypestocampaign.removed: The event has been deleted
eventtypestocampaign.restore: Restore
eventtypestocampaign.restored: The event has been restored
eventtypestocampaign.edit: Edit event
eventtypestocampaign.create: Create an event
eventtypestocampaign.details: Event details
devices.list: "Device list "
devices.create: New device
devices.devices-added: New device added
devices.devices-updated: Device updated
devices.delete: Device delete
devices.delete-confirmation-text: Are you sure you want to delete device
devices.removed: Device removed
devices.devicetypes-updated: Devices updated
devices.edit: Edit device
devices.details: Device details
devices.restore: Restore
devices.restored: Device has been restored
devices.monitor: Monit
devices.monitorparameters: Measured parameters
devices.default-rules: Default rule
devices.plugged-sensors: Plugged measure modules
devices.events: Connected tasks
devices.missing-plugged-sensor: " No measure modules plugged."
devices.missing-parameters-to-measure: There are no defined parameters for measurements
devices.assigned: Assigned devices
device.assigned: Assigned device
devices.status: Device status
devices.last-sync: Last synchronization
devices.battery-level: Battery level
devices.efficiency: Device efficiency
shifts.create: New shift
shifts.removed: Shift has been deleted
shifts.delete: Delete shift
shifts.edit: Edit shift
shifts.copy: Copy shift
shifttypes.details: Shift details
shifts.delete-confirmation-text: Do you really want to detele shift
shifts.list: Shift list
shifts.calendar: Calendar
shifts.shift: Shift
shifts.shifts: Shifts
shifts.shift-added: Shift added
shifts.shift-updated: Shift updated
shifts.shift-copied: Shift coppied
shifts.scheduler: Shifts
shifts.details: Details
shifts.team-shift-time-intersects-with-another-shift: "Team changes at the indicated time period coincides with another of its change. Interval intersection: from %s to %s"
shifts.team-in-indicated-period-is-busy: The team in the period indicated is already taken
shifts.shiftType: Shift type
shifts.team: Team
shifts.restore: Restore
shifts.restored: Shift restored
shifts.form.shift-day: Shift day
form.port: Port
form.smtp-encryption: Encryption
shifts.form.multiple-copy-shift: Multiple copy shifts
shifts.form.copy-shift-to-each-day-in-given-interval: Copy shift to each day in given interval
shifts.form.day-of-end-interval: The end of the day interval
shifts.form.assign-to-calendar: Assign to calendar
shifts.form.exclude-weekends: Exclude weekends
shifts.shift-copied-multiple-times: Shift copied multiple times
shifttypes.list: Shift types list
shifttypes.create: Create shift type
shifttypes.edit: Edit shift type
shifttypes.shifttypes-updated: Shift type updated
shifttypes.shifttypes-add: Shift type added
shifttypes.shifttypes-re-add: Shift type restored
shifttypes.delete: Delete shift type
shifttypes.delete-confirmation-text: Do you really want to delete shift type -
shifttypes.removed: Shift type has been removed
shifttypes.restore: Restore
shifttypes.restored: Shift type has been restored
calendar.scheduler: Calendar
calendar.events: Events
calendar.create: Create event
calendar.not-planned-tasks: Not planned tasks
devicetypes.list: Devicetypes list
devicetypes.create: New devicetypes
devicetypes.created: New devicetypes created
devicetypes.devicetypes-add: New devicetypes added
devicetypes.devicetypes-re-add: Device restored
devicetypes.delete: Delete devicetypes
devicetypes.delete-confirmation-text: Do you realy want to delete devicetype
devicetypes.removed: Devicetypes removed
devicetypes.devicetypes-updated: Devicetypes updated
devicetypes.edit: Edit devicetypes
devicetypes.details: Devicetypes details
devicetypes.name: Name
devicetypes.restore: Restore
devicetypes.restored: "Devicetypes restored "
devicetypes.delete-has-children-text: "You can't delete the device type that is used"
devicetypes.devices-list-of-type: List of devices assigned to this type of
devicetypes.parameter.active-rule: Active rule parameter
devicetypes.parameter.default-chart: Visible chart
devicetypes.parameter.default-indicator: Default parameter indicator
products.list: Product list
products.create: Create product
products.details: Product details
products.edit: Edit product
products.delete: Delete product
products.delete-confirmation-text: Are you really want to delete product?
products.products-add: Product added
products.added-file: File added
products.products-updated: Product updated
products.removed: Product removed
products.restore: Restore
products.restored: Product restored
product.timeline: Time line
products.delete-has-children-text: You can not delete a product that is a subscription
productscategory.list: Product category list
productscategory.create: Create product category
productscategory.details: Product category details
productscategory.edit: Edit product category
productscategory.delete: Delete product category
productscategory.delete-confirmation-text: Do you really want to delete product category?
productscategory.delete-has-children-text: You can not delete a category that is superior
productscategory.productscategory-add: Product Category has been created
productscategory.productscategory-updated: Product Category has been updated
productscategory.removed: Product Category has been deleted
productscategory.restore: Restore
productscategory.restored: Product Category has been restored
productcategory.timeline: time line
offers.list: Offers list
offers.create: Create an offer
offers.details: Offers details
offers.edit: Edit an offer
offers.offers-add: Offer has been added
offers.offers-updated: Offer updated
offers.delete: Delete an offer
offers.delete-confirmation-text: Do you really want to delete this offer?
offers.removed: An offert has been deleted
offers.restore: Restore
offers.restored: An offert has been restored
offers.invalid-file-type: "Incorrect file type. Upload imahe, pdf or video file"
campaign.list: Campaign list
campaign.create: Create campaign
campaign.details: Campaign details
campaign.edit: Edit campaign
campaign.delete: Delete campaign
campaign.delete-confirmation-text: "Are you really want to delete a campaign - "
campaign.campaign-add: Campaign added
campaign.campaign-updated: Campaign updated
campaign.timeline: Time line
campaign.removed: Campaign removed
campaign.restore: Restore
campaign.restored: Campaign has been restored
central.command-info: Tasks will be sent to carry out the next time you connect the central device to the server.
central.restart-change: "Force restart of central unit has been changed "
central.config-change: Force download of new configuration has been changed
central.force-restart: Force restart
central.force-config: "Force download of new configuration "
central.management: Manage central unit
central.restart-command-send: Restart command has been send by central unit
central.send-command-send: Command to download a new configuration has been downloaded by the central unit
offerstemplate.list: Offer templates
offerstemplate.create: Add offer template
offerstemplate.details: Offer template details
offerstemplate.edit: Edit offer template
offerstemplate.delete: Delete offer template
offerstemplate.delete-confirmation-text: Do you really want to remove offer template
offerstemplate.removed: Offer template has been removed
offerstemplate.offerstemplate-add: Offer template has been added
offerstemplate.offerstemplate-updated: Offer template updated
offerstemplate.restore: Restore
offerstemplate.restored: Offer template restored
languages.list: Languages
languages.edit: Edit language
languages.details: Details
languages.new: New language
languages.create: New language
languages.delete: Delete language
languages.delete-confirmation-text: Do you really want to remove language?
languages.removed: Languge removed
languages.offerstemplate-re-add: Language restored
languages.offerstemplate-updated: Language updated
languages.restore: Restore
languages.restored: Language restored
languages.languages-updated: Language has been updated
filemanager.list: Files
filemanager.ctime: File creation time
filemanager.details: File details
filemanager.delete: Remove file
filemanager.delete-confirmation-text: Do you really want to delete file
filemanager.removed: File removed
filemanager.add-change-file: Add or remove file
"dropzone.drag-and-drop-or-click'": "Drag`n`drop file or click"
dropzone.yours-browser-do-not-support-drag-and-drop: "This browser doesn't support drag`n`drop"
dropzone.drag-and-drop-or-click-to-import-from-zip: "Drag`n`drop file or click, to import from Google Docs ZIP archive. Supported graphic files: png, gif, jpg i jpeg."
dropzone.invalid-file-type: Wrong file error
dropzone.server-error-response: File sending error
dropzone.cancel-upload: Cancel upload
dropzone.upload-canceled: Upload canceled
dropzone.remove-file: Remove file from list
dropzone.max-file-exceeded: Max file count exceeded
dropzone.file-to-big: File is to big - Max 5 MB
email.welcome: Welcome
email.account-created-pt-1: Your account in the system
email.account-created-pt-2: has been created.
email.account-created-pt-3: All you need to do now is activate.
email.password-reset-pt-1: Your password in the system
email.password-reset-pt-2: need to be reset.
email.invitation-pt-1: Welcome to Fleet Program by
email.invitation-pt-2: , your company has invited you to their account.
email.to-invite: "Complete the registration process by clicking the link below:"
email.invite-button: Complete the process
email.login-button: Log in
email.confirm-button: Confirm email
email.reset-button: Reset
email.account: E-mail account
email.cm-account-created: "Your account in Carwash Manager webservice is created. Please login into panel https://cm.bkf.pl"
email.cm-password-reset: "Your password in Carwash Manager webservice has been reset. Please login into panel https://cm.bkf.pl with new password"
email.visit: "Please visit https://cm.bkf.pl/ to login into panel."
email.new-password: "New password (%url%)"
email.reset-password: Password reset
email.invitation-email: Invitation to the fleet program
email.password-reset: "Your password has been reset. Your new password is:"
email.reset-password-text: You can always reset your password by clicking this link
email.to-reset: "Reset your password by clicking the link below:"
email.set-password-fm: Set password in
email.reset-button-fm: Set password
email.greetings: Greetings
email.bank-transfer.title: Bank transfer title
email.team: BKF team
email.ebkfteam: EBKF team
email.cm-slogan: technology created with passion
email.slogan: Technology created with passion
email.to-login: "Activate your account by clicking the link below:"
email.cs-user-create-confirmation: Account creation confirmation
email.send-success: Email was sent successfully
email.send-failure: Sending email failed
email.new-event-assigned: There is a new task assigned to you
email.see-event: See this task
email.user-create-confirmation: "Account creation confirmation (%url%)"
email.call-us: Call Us
email.write-us: Write us
email.sorry_no_invoice: Sorry we can't generate invoice. Please contact with our bookkeeping department.
email.autocreateworker.title: "Created new device operator: %rfid%"
email.autocreateworker.created: "Created new device operator:"
email.autocreateworker.created-time: "Created at:"
email.autocreateworker.rfid-number: "RFID Number:"
email.autocreateworker.device: "Device:"
email.autocreateworker.owner: "Owner:"
footer.all-rights-reserved: i2m All rights reserved
interval.time-interval: Interval time
interval.time-range: Time range
interval.minute: minute
interval.hour: hour
interval.day: day
interval.week: week
interval.month: month
interval.quarter: quarter
interval.year: year
form.smtp-adress: SMTP server address
form.assigned-workers: Assigned workers
form.assigned-carwashes: Assigned car washes
form.assign-with-device: Plug to device
form.systemRoles: System roles
form.smtp-adress.invalid: SMTP cannot establish a remote connection
form.smtp-adress.valid: SMTP connection has been established
form.user-data: Profile settings
form.fileadd: add file
form.content: Content
form.comments: Comment
form.orderNr: Order number
form.bconnections: Flooring
form.deadline: Deadline
form.title: Title
form.destinationI2m: Destination i2m
form.show-x-per-page: Show _MENU_ rows per page
form.config: Configuration
form.schedulerActionType: Type of scheduled tasks
form.interval-seconds: Interval (seconds)
form.interval: Interval
form.dont-assign-with-any-device: Do not assign with any device
form.callTime: Call time
form.params: Parameters
form.contactPerson: Contact person
form.accountManager: Manager account
form.province: Voivodeship
form.assigned-to: assigned to
form.website: website
form.address: Address
form.fax: Fax
form.status: Status
form.shortname: Shortname
form.mobile: Mobile phone
form.latitude: Latitude
form.longitude: Longitude
form.regon: National Business Registry Number
form.nip: Tax identification number
form.city: City
form.street: Street
form.postcode: Post code
form.clientForm: client form
form.comment: comments
form.team: Team
form.template: Template
form.worker: Worker
form.change-to-worker: Change to worker
form.change-to-dealer: Change to dealer
dealer.dealer: Dealer
menu.dealers: Dealers
dealer.dealer-updated: Dealer details updated
form.dealer_name: Dealer name
dealer.create: Dealer create
dealer.edit: Dealer edit
dealer.delete: Dealer delete
dealer.delete-confirmation-text: Do you realy want to delete Dealer?
dealer.removed: Dealer removed
dealer.restored: Dealer restored
dealer.dealer-add: Dealer added
dealer.restore: Dealer restore
dealer.list: Dealers list
dealer.details: Dealer details
form.change-to-user: Change on user
form.percentage-discount: Percentage discount
form.money-discount: Money discount
form.price-after-discount: Price after discount
form.price-without-discount: Price without any discount
form.quantity: Quantity
form.discount: Discount
form.client: Client
form.devices: Devices
form.parameter: Parameter
form.parameters: Parameters
form.parameterType: Parameter type
form.factor: Factor
form.signal-quality: Signal quality
form.fill-data: Time readings data
form.efficiency-reverse: Efficiency reverse
form.info: Show _PAGE_ from _PAGES_
form.unit: Unit
form.parameterId: parameter ID
form.Id: Id
form.default-team: default team
form.defaultconfig: default configuration
form.reset-password: reset password
form.user-roles: User roles
form.name: Name
form.date: Date
form.photo: Profile pictures (*.jpg or *.png)
form.change-photo: Change photo
form.change-logo: Change logo
form.time: time
form.source: source
form.message: message
form.position: Position
form.roleSystemName: Role name
form.roleSystemName-in-format-role-name: Type role name in format ROLE_NAME
form.roleSimpleName: Simple role name
form.roleTranslatedName: Translated name
form.description: Description
form.reporter: Reporter
form.shiftType: Shift type
form.startShift-date: Shift start
form.endShift-date: Shift end
form.color: Color
form.email.login: "Login (eg. <EMAIL>)"
form.email.smtp.check: Check account
form.export: Export
form.add: Add
form.generate: Generate
form.search: Search
form.filter: Switch filter
form.choose-option: Choose option
form.start-time: Start time
form.end-time: End time
form.short-name: Short name
form.privilages: Privilages
form.email: Email address
form.allow-email-notifications: Allow email notifications
form.password-repeat: Repeat password
form.actions: Action
form.edit: Edit
form.create: New
form.delete: Delete
form.delete-all: Delete all
form.www: WWW
form.submit: Save
form.copy: Copy
form.role: Role
form.rfid: Rfid
form.firstname: First name
form.lastname: Last name
form.firstname-and-lastname: First name and last name
form.phone: Phone
form.timeline: Timeline
form.data: data
form.privileges: Privileges
form.ctime: Creation date
form.id: Id
form.ordernr: "Order #"
form.plcserial: PLC Serial number
form.plcmac: PLC MAC
form.serialnumber: Serial Number
form.stand-count: Stand count
form.startdate: Start date
form.warranty: Warranty
form.warrantyvoided: Warranty Voided
form.lastlogin: Last login
form.mtime: Date of last modification
form.generate-password-and-send-to-mail: Generate password and send it to your email address
form.modifications-after-copying: Modifications after copying
form.clientfrom: Client source
form.country: Country
form.accountmanager: Account manager
form.groups: Groups
form.group: Group
form.basic-data: Basic data
form.contact-data: Contact data
form.correspondence-address: Correspondence address
form.location: Localization
form.locale: Language code
form.shipping: Correspondence address
form.shippingName: Name
form.shipping-firstname-and-lastname: First and last name
form.shippingPhone: Phone
form.shippingAddress: address
form.shippingCity: City
form.event-type: Event type
form.event-time: Event time
form.campaign: Campaign
form.event-planned-start-time: Planned event start
form.event-planned-end-time: Planned event end
form.eventDoneTime: Done
form.event-all-day: All day event
form.icon: Icon
form.clients: Client
form.clienttype: Client types
form.user: User
form.file: File
form.filesize: File size
form.filetype: File type
form.file-name: File name
form.parent: Parent
form.parent-group: Parent group
form.event-to-campaing-time-interval: interval (in days)
form.main-monitor-parameter: The main measure module parameter
form.groupid: group ID
form.index: Index
form.index-main: Main index
form.motto: Motto
form.price: Price
form.price-final: Final price
form.parent-products: Related products
form.sell-chance: Transaction chance
form.parent-setting: Parent setting
form.value: Value
form.options-for-choice: Option selection
form.metadata: Metadata
form.enable-option: Enable option
form.enabled: Enabled
form.visible: visibility
form.editable: Editability
form.visible-for-administrator: Visible to administrator
form.editable-for-administrator: Editable to administrator
form.wrong-number-format: Invalid number format
form.default-monitor-parameter: Default monitor parameter
form.category: Category
form.default-sensor-types: Default sensor types
form.clientsWorkers: Clients workers
form.is-contact-person: Contact person
form.trader: Trader
form.usergroup: User group
form.product: Product
form.until-date: Until date
form.supervisor: Supervisor
form.datatype: Data type
form.datatype.scalar: Scalar
form.datatype.value: Value
form.datatype.value-vector: Values vector
form.datatype.efficiency: Efficiency
form.datatype.efficiency-vector: Efficiency vector
form.datatype.average: Average
form.datatype.average-vector: Average vector
form.datatype.counter: Counter
form.datatype.counter-vector: Counter vector
form.datatype.v: Value
form.datatype.v_v: Values vector
form.datatype.e: Efficiency
form.datatype.e_v: Efficiency vector
form.datatype.a: Average
form.datatype.a_v: Average vector
form.datatype.c: Counter
form.datatype.c_v: Counter vector
form.datatype.gd: Positional
form.file-path: File path
form.file-size: File size
form.file-type: File type
form.datatype.boolean: Boolean
form.datatype.positional: Positional
form.available-variables: Available variables
form.variable: Variable
form.offer-template: Template of an offer
form.widget: Widget
tooltip.monitor: Monit
validation.password-too-short-at-least-X: "Password is to short (at least %s characters)"
validation.passwords-are-different: Given passwords are different.
validation.value-too-short-at-least-X: "This value is to short. at least %s characters."
validation.start-date-newer-than-end-date: The end date may not be earlier than the date of commencement.
validation.only-digits-allowed: This value allows only digits.
validation.only-letters-allowed: This value allows only letters.
validation.only-non-empty-allowed: "This value shouldn't be empty."
validation.email-wrong-format: Wrong email format
validation.email-duplicated: This email is already in database.
validation.your-browser-does-not-support-video-tag: Your browser does not support video
validation.phone-not-allowed: Invalid phone number.
validation.invalid-accountmanager: Incorrect username
validation.invalid-clientevent: Wrong event ID.
validation.username-duplicated: Username already exists in database.
notification.info: Info
notification.warning: Warning
notification.error: Alarm
logs.list: System logs
logs.delete: Delete logs
logs.delete-confirmation-text: Do You really want to delete logs
logs.removed: Log has been removed
logs.removed-all: Logs has been removed
menu.home: Home
menu.alerts: Alerts
menu.keys: Keys
menu.notifications: Notifications
menu.notification: Notification
menu.notifications-list: Notifications list
menu.notification-rules: Notification rules
menu.manage: Manage
menu.device-operators: Device operators
menu.alarms: Alarms
menu.alarms-description: Alarms description
menu.alarm-rules: Alarms rules
menu.alarm-conditions: Alarm conditions
menu.alarm-types: Alarms types
menu.alarm-type: Alarm type
menu.settings-metadata: Metadata settings
menu.application-settings: Application settings
menu.dictionaries: Dictionaries
menu.offers: Offers
menu.monitor-parameters: Parameters
menu.compound-parameters: Virtual parameters
menu.parameters: Parameters
menu.user-positions: Position list
menu.users: Users
menu.user-groups: User groups
menu.profile: Profile
menu.workers: Machine operators
menu.devices: Devices
menu.device-groups: Device groups
menu.production: Production
menu.administration: Administration
menu.dashboard: Dashboard
menu.logs: System logs
menu.languages: Languages
menu.monitoring: Monitor
menu.alarm: Alarms
menu.monitor: Diagnostics
menu.advanced-settings: Advanced settings
menu.shifts: Shifts
menu.logout: Logout
menu.device-types: Device types
menu.user-roles: User roles
menu.shifttypes: Shift types
menu.measure-units: Measure units
menu.rules: Rules
menu.rules-constraints: Rules constraints
menu.teams: Teams
menu.reports: Reports
menu.settings: Settings
menu.carwash-settings: Carwash settings
menu.scheduler: Recurring task
menu.scheduler-action-types: Scheduler action types
menu.reports.worker-production: Workers production
menu.reports.shift-production: Shift production
menu.reports.device-break-at-work: Disruption of device
menu.reports.main-devices-parameters: Main measure module parameters
menu.parameters-to-device-report: Device parameters
menu.crm: CRM
menu.filemanager: Browse files
menu.clients: Clients
menu.clients-groups: Client groups
menu.events: Tasks
menu.event-types: Task types
menu.calendar: Calendar
menu.campaign: Campaign
menu.vattax: VAT tax
menu.products: Products
menu.productscategory: Products category
menu.carwash: Carwash
menu.settings-configuration: Settings configuration
menu.access-settings: access settings
menu.dashboard-cm: Dashboard
menu.finance: Finance
menu.loyalsystem: Loyalty system
menu.monitor-cm: Diagnosis
menu.exchanger: Money changer
menu.moneycollect: money collection
menu.central: Central device
menu.shifts-calendar: Shifts calendar
menu.promotions-types: Promotions types
menu.offers-template: Offers template
menu.securitycode: Security code
menu.manager: Manager
menu.client-types: Client types
menu.news: Notifications
menu.news-details: Notification details
descriptionhistory.description: History description
measureunits.list: Measure units
measureunits.create: Adding measure units
measureunits.details: Measure unit details
measureunits.edit: Edit measure unit
measureunits.delete: Delete measure unit
measureunits.delete-confirmation-text: Do you really want to delete measure unit
measureunits.removed: Measure unit has been deleted
measureunits.measureunits-add: Measure unit has been added
measureunits.measureunits-re-add: Measure unit has been restored
measureunits.measureunits-updated: Measure unit has been updated
measureunits.restore: Restore
measureunits.restored: Measure unit has been restored
measureunits.delete-has-children-text: You can not delete measure unit that is in use
dashboard.no-permission: There are no assigned permissions. To assign please contact site administrator.
dashboard.my-dashboard: My dashboard
dashboard.welcome: Welcome
widgets.button.close: Close
widgets.button.minimize: Minimalization
widgets.button.restore: Restore
widgets.button.fullscreen: Full screen
widgets.button.pause: Enable/disable autorefresh
widgets.button.refresh: Refresh
widgets.button.menu: Menu
widgets.button.settings: Settings
widgets.button.maximize: Maximization
widgets.widget.notifications.alerts: Messages
widgets.widget.production.counter: Counter readings
widgets.widget.production.counter-of-amount-data-reads-with-zeros: "Counter amounts of readings, including readings with zero data"
widgets.widget.production.average: Average
widgets.widget.production.sum: Number of total
widgets.widget.production.sum-of-data: Number of total data
widgets.widget.production.average-of-data: Average total data
widgets.widget.production.parameter: Parameter
widgets.widget.production.device: Assigned device
widgets.widget.production.device-production: Device work
widgets.widget.money-changers-list: List of money changers
widgets.widget.money-changer-not-defined: No money chenger is defined
widgets.widget.production.description: Description
widgets.widget.production.actual-monitors: current widget
widgets.widget.production.add-monitor: Add widget
widgets.monitor.add-monitor-geolocalization: Add your location widget
widgets.widget.production.monitor-with-these-options-already-exists: Widget with this settings already exists
widgets.widget.production.no-devices-to-monitor: There are no defined devices to monit
widgets.widget.production.no-devices-or-parameters-in-system: There are no devices or parameters in system that can be added to the widget.
widgets.monitor.selection-of-devices-by-device-type: The choice of multiple devices via device type
widgets.monitor.switch-chart-type: Switch between line and bar chart types
widgets.monitor.show-steps: Show steps
widgets.monitor.apply-rule: Apply rule assigned to parameter
widgets.monitor.minutes: Data interval
widgets.monitor.show-points: Show points on chart
widgets.monitor.decrease-chart-size: Decrease chart size
widgets.monitor.increase-chart-size: Increase chart size
widgets.monitor.toggle-chart-control: Toggle chart view control
widgets.monitor.reset-chart-view: Reset chart view to default state
widgets.monitor.date-start: Start date
widgets.monitor.date-end: End date
widgets.monitor.add-monitor: Add monitor
widgets.monitor.actual-shift: Shift
widgets.monitor.from: from
widgets.monitor.to: to
widgets.monitor.next-shift: Next shift
widgets.monitor.previous-shift: Previous shift
widgets.monitor.next-day: Next day
widgets.monitor.previous-day: Previous day
widgets.monitor.change-date-selection-to-shift: "Change the monitor mode: change - date"
widgets.monitor.signal-power: Signal power
widgets.monitor.none-signal-power-data: No data available for signal power
widgets.monitor.signal-to-noise: Signal - noise ratio
widgets.monitor.last-read: Last reading date
widgets.monitor.locations-list-by-device: Locations list by device
widgets.monitor.show-all-locations: Show all location
widgets.monitor.show-location-accuracy: Show location accuracy
widgets.monitor.tooltip.value: Value
widgets.monitor.tooltip.date: Date
widgets.monitor.tooltip.device: Device
widgets.monitor.tooltip.time: Time
widgets.widget.campaigns.my-campaigns: My campaigns
widgets.widget.campaigns.campaign: Campaigns
widgets.widget.campaigns.created-date: Campaigns date
widgets.widget.campaigns.you-dont-have-active-campaigns: Currently you have no active campaigns
widgets.widget.my-events: My tasks
widgets.widget.events: Tasks
widgets.widget.events.date: Tasks date
widgets.widget.events.done.it: Done it
widgets.widget.events.done: Done
widget.evets.get-position-error: GPS position error. Turn it on in yours browser.
widgets.show-more: More
widgets.widget.you-dont-have-active-events: Currently you have no active events
widgets.widget.events.mark-as-done: Mark as done
widget.evets.try-to-get-position: Trying to get current GPS position.
widgets.widget.my-alarms: My alarms
widgets.widget.lack-of-active-alarms: Lack of active alarms
widgets.widget.alarm: Alarm
indicator.indicator: Indicator
indicator.counter: Indicator counter
indicator.average: Average
indicator.sum: Sum
indicator.efficiency: Efficiency
widget.measure-module-activity: Measure module activity
widget.battery-charge: Battery charge
widget.enabled-efficiency: Average device activity
widget.enabled-time: Device activity time
widget.work-efficiency: Device work efficiency
widget.work-time: Device effective work time
widget.work-efficiency-trend: Device effective work trend
report.data: Data
report.chart: Chart
report.generate: Generate
report.export-to-pdf: Export to PDF
report.break-at-work: Break at work
report.device-is-working: Device is working
report.pdf.error-occurred-while-generating-report: Error occured while generating report
report.pdf.report: Report
report.pdf.ctime: Date of generating report
report.pdf.startdate: Start date
report.pdf.enddate: End date
report.pdf.interval: Interval
report.pdf.device: Device
report.pdf.parameters: Parameters
report.pdf.worker: Worker
report.pdf.shift: Shifts
report.pdf.incorrect-report-parameters: Incorrect report parameters.
report.pdf.report-devices-production: Report devices production
report.pdf.report-devices-production-divided-to-shifts: Report devices production divided to shifts
report.pdf.daily-sum: Daily sum
report.pdf.daily-efficiency: Daily efficiency
profile.my: My profile
profile.updated: Profile updated
status.page: Page not found
status.event: event
status.unknown: Status unknown
status.unassigned: Unassigned
status.assigned: Assigned
status.ready: Ready
status.unplanned: Unplanned
status.overdue: Overdue
status.planned: Planned
status.removed: Removed
status.all: All
status.active: Active
card.active: Active
card.blocked: Blocked
card.removed: Removed
table.id: Id
table.username: User name
table.lastlogin: Last login
table.country: Country
table.campaignname: Campaign name
table.lp: No.
table.blocked: Blocked
table.interval: Interval
table.lasttimeexecuted: last time executed
table.params: Parameters
table.calltime: Call time
table.schedulerActionType: Action type
table.parameterId: "parameter ID "
table.variable: Variable
table.expression: Expression
table.argument: Argument
table.archive: Archive
table.firstname: First name
table.lastname: Last name
table.email: Email
table.website: Website
table.address: Address
table.phone: Phone
table.fax: Fax
table.mobile: Mobile
table.assigned-to: Assigned to
table.comments: Comments
table.userid: User
table.factor: Factor
table.unit: Unit
table.until-date: To date
table.product: Product
table.percentage-discount: Percentage discount
table.status: Status
table.device-type: Device type
table.config: Configuration
table.user: User
table.message: Message
table.source: Source
table.starttime: Start time
table.endtime: End time
table.name: Name
table.startShift: Shift start
table.endShift: Shift end
table.color: Color
table.defaultconfig: Default configuration
table.date: Date
table.ctime: Creation date
table.mtime: Modification date
table.rolesystemname: Role user name
table.rolesimplename: Role name
table.roletranslatedname: translated name
table.description: Description
table.lastUpdate: Last update
table.shift-type: Shift type
table.shift-assigned-in-calendar: Assigned in calendar
table.shortname: Short name
table.worker: Worker
table.device: Device
table.devices: Devices
table.default-team: Default team
table.team: Team
table.yes: "Yes"
table.no: "No"
table.none: None
table.active: Active
table.ordernr: Order no
table.plcserial: PLC
table.plcmac: MAC
table.startdate: Start date
table.warranty: Warranty
table.warrantyvoided: Warranty voided
table.serialnumber: Serial number
table.enabled: Enabled
table.disabled: Disabled
table.icon: Icon
table.timeinterval: timeinterval (in days)
table.groupid: Group ID
table.group: Group
table.index: Index
table.indexmain: Main index
table.price: Price
table.tax-value: "Tax value [%]"
table.parentProductsCategory: Category
table.title: Title
table.changes: Changes
table.destination: Module
table.destination-i2m: Destination i2m
table.created: Created
table.event: Event
table.event-planned-start-time: Event planned start time
table.event-planned-end-time: Event planned end time
table.event-done-time: Done
table.value: Value
table.node: Node
table.parent-setting: Parent setting
table.visible: Visible
table.editable: Editable
table.metadata: Metadata
table.position: Position
table.category: Category
table.sensors: Measure modules
table.assigned-sensors: Assigned measure modules
table.parameters: Parameters
table.owner: Owner
table.parent: Parent
table.client: Client
table.clienttype: Client type
table.language-code: Language code
table.locale: Locale
table.inherited-system-roles: Inherited system roles
table.activity: Activity
table.including: Including
table.in-total: In total
table.parent-products-category: Parent category
table.efficiency: Efficiency
table.efficiency-for: Efficiency for
table.battery: Battery
table.all: all
table.latest-notifications: Latest notifications
table.show: Show
table.new-tasks: New tasks
table.no-data-pagination-info: You are watching (0 to 0) from 0 records
table.key: Card
history.changed-name: Changed name
history.changed-description: Changed description
user.user-add: User added
User: User
user: user
user.removed: User removed
user.list: User list
user.create: Create user
user.edit: Edit user
user.delete-confirmation-text: Are you really want to delete user?
user.delete: Delete user
user.password-reset-done: Password has been resetted and send to user
user.user-updated: "User updated, user login: %email%"
user.restore: Restore
user.restored: User restored
userpositions.list: Position list
userpositions.details: Worker position details
userpositions.create: Create worker position
userpositions.userpositions-add: Worker position has been added
userpositions.userpositions-re-add: Worker position has been restored
userpositions.edit: Edit worker position
userpositions.userpositions-updated: Worker position has been updated
userpositions.delete: Delete worker position
userpositions.delete-confirmation-text: "Do you really want to delete worker position - "
userpositions.removed: Worker position has been deleted
userpositions.restore: Restore
userpositions.restored: Worker position has been restored
userpositions.delete-has-children-text: You can not delete user position that is in use
userroles.details: User role details
userroles.userroles-updated: user role updated
userroles.edit: Edit user role
userroles.create: Create user role
userroles.userroles-add: User role added
userroles.list: User role
userroles.delete-confirmation-text: Do you really want to delete user role -
userroles.delete: Delete user role
userroles.removed: User role has been deleted
userroles.restore: Restore
userroles.restored: User role restored
userroles.already-exist-restored: The role was previously added to the system. The role has been restored
usertoteamtodevice.list: Assigning employees
usertoteamtodevice.create: Assigning an employee
usertoteamtodevice.details: Assignment details
usertoteamtodevice.edit: Edit assignment
usertoteamtodevice.todevice: to device
usertoteamtodevice.delete: Delete assignment
usertoteamtodevice.delete-confirmation-text: Do you really want to delete assignment
usertoteamtodevice.usertoteamtodevice-add: Assignment added
usertoteamtodevice.usertoteamtodevice-updated: Assignment updated
usertoteamtodevice.removed: Assignment deleted
usertoteamtodevice.restored: Assignment has been restored
usertoteamtodevice.restore: Restore Assignment
ROLE_SUPERADMIN: Super Administrator
ROLE_SUPERADMIN_DESCRIPTION: An administrator with access to all system components
ROLE_ADMIN: Administrator
ROLE_ADMIN_DESCRIPTION: The user having access to most of the administrative elements
ROLE_WORKER: Worker
ROLE_WORKER_DESCRIPTION: "Employee without logging capabilities, only basic information (name and position)."
ROLE_FOREMAN: Foreman
ROLE_FOREMAN_DESCRIPTION: "The foreman with the ability to manage employees. Access to log on and use the system i2m Monitor, as well as to bookmarks: Desktop, Monitoring, Reports, equipment, administration (only the ability to edit profile)"
ROLE_I2MUSER: User
ROLE_TRADER: Trader
ROLE_TRADERMANAGER: Trader manager
ROLE_I2MUSER_DESCRIPTION: "A regular user, the basic role to log in to the system"
worker.list: Machine operator
worker.password-reset-done: Your password has been reset
worker.create: Create operator
worker.worker-add: The operator has been added
worker.delete: Delete an operator
worker.delete-confirmation-text: Do you really want to remove an operator
worker.removed: operator has been deleted
worker.worker-updated: Operator updated
worker.edit: Edit operator
worker.details: Operator details
worker.restore: Restore
worker.restored: Operator has been restored
worker.assigned-devices: Assigned devices
worker.devices-unassign: Unassign device
worker.devices-unassign-confirmation-text: Do you really want to unassign device
worker.device-unassigned: Device has been unassigned
clienttypes.list: Client types list
clienttypes.create: Adding client type
clienttypes.details: Client type details
clienttypes.edit: Edit client type
clienttypes.delete: Delete client type
clienttypes.delete-confirmation-text: Do you really want to delete client type
clienttypes.removed: Client type has been deleted
clienttypes.measureunits-add: Client type has been added
clienttypes.clienttypes-updated: Client type has been updated
clienttypes.restore: Restore
clienttypes.restored: Client type has been restored
clienttypes.delete-has-children-text: You can not delete client type that is in use
teams.list: Teams list
teams.create: Create team
teams.teams-add: Team has been added
teams.edit: Edit team
teams.teams-updated: Team has been updated
teams.delete: Delete team
teams.delete-confirmation-text: Do you really want to delete
teams.removed: Team has been deleted
teams.restore: Restore team
teams.restored: Team has been restored
teams.team: Team
scheduleractiontypes.restore: Restore scheluder action
scheduleractiontypes.restored: Scheduler action has been restored
scheduler.list: Schedule tasks list
scheduler.scheduler-add: Scheduled task has been added
scheduler.edit: Edit schedule task
scheduler.details: Schedule task details
scheduler.scheduler-updated: Schedule task updated
scheduler.delete: Delete schedule task
scheduler.delete-confirmation-text: Do you really want to delete schedule task
scheduler.create: Add schedule task
scheduler.removed: Schedule task has been removed
scheduler.restore: Restore
scheduler.restored: Schedule task has been resored
clientscampaign.list: Clients campaign
clientstocampaign.sellchance-rated: The rating has been saved
clientstocampaign.create: Assign campain to client
scheduleractiontypes.list: Scheduler action type
scheduleractiontypes.create: Create scheduler type
scheduleractiontypes.edit: Edit scheduler type
scheduleractiontypes.delete: Delete scheduler type
scheduleractiontypes.scheduleractiontypes-add: Scheduler type has been added
scheduleractiontypes.scheduleractiontypes-updated: Scheduler type has been updated
scheduleractiontypes.removed: Scheduler type has been removed
scheduleractiontypes.details: Scheduler type details
scheduleractiontypes.delete-confirmation-text: Do you really want to delete scheduler types -
applicationsettings.application-settings: Application settings
applicationsettings.descendant-settings: sub settings
applicationsettings.no-settings-available: No settings available
applicationsettings.edit: Edit settings
applicationsettings.enabled: Enabled
applicationsettings.disabled: Disabled
applicationsettings.names.valueconstraints: restrictions values
applicationsettings.descriptions.valueconstraints: Do you want to restrict all variables in monitor?
applicationsettings.names.minimum: Minimum value
applicationsettings.descriptions.minimum: The minimum value to which the input value is accepted
applicationsettings.names.maximum: Maximum value
applicationsettings.descriptions.maximum: The maximum value to which the input value is accepted
applicationsettings.names.duration: Shift duration
applicationsettings.descriptions.duration: "Default shift time duration. recommended value: 8"
applicationsettings.names.shifts: Shifts
applicationsettings.names.monitor: Monitor
applicationsettings.names.main: Settings
applicationsettings.names.roles: Role name
applicationsettings.descriptions.roles: List of enabled user roles. Functionality of disabled roles is unavailable.
applicationsettings.names.reports: Reports
applicationsettings.descriptions.reports: List of enabled user roles. Functionality excluded role is not available.
applicationsettings.names.intervals: Time intervals
applicationsettings.descriptions.intervals: Time intervals available in system reports.
applicationsettings.names.start: Start of first shift
applicationsettings.descriptions.start: The default start time of the first changes every day.
applicationsettings.names.includeshift: Include shift
applicationsettings.descriptions.includeshift: Include the start and duration time of employment change in the selection interval.
applicationsettings.node.main: Main settings
"applicationsettings.node.main:roles": User oles
"applicationsettings.node.description.main:roles": List of enabled user roles. Functionality excluded role is not available.
"applicationsettings.node.main:customlook": Individualization appearance
"applicationsettings.node.description.main:customlook": Displaying an individual logo in the application instead of the default.
"applicationsettings.node.main:customlook:defaultlogo": Default logo
"applicationsettings.node.description.main:customlook:defaultlogo": The default system logo appears when you turn off the mode of individualization appearance.
"applicationsettings.node.main:customlook:logo": Logo
"applicationsettings.node.description.main:customlook:logo": Selecting the logo image file.
applicationsettings.node.reports: Raports
"applicationsettings.node.reports:intervals": Time intervals
"applicationsettings.node.description.reports:intervals": Intervals available for selection in the system reports.
"applicationsettings.node.reports:showerrors": Show errors
"applicationsettings.node.description.reports:showerrors": Displaying Javascript error in report occurred while generating a PDF file
"applicationsettings.node.invoices:settings": Invoice settings
"applicationsettings.node.invoices:settings:prefix": Prefix
"applicationsettings.node.invoices:settings:foreignprefix": Foreign prefix
"applicationsettings.node.invoices:settings:dateformat": Date format
"applicationsettings.node.invoices:company": Company
"applicationsettings.node.invoices:company:bankaccountnumber": Bank account number
"applicationsettings.node.invoices:company:name": Name
"applicationsettings.node.invoices:company:nip": TAX ID
"applicationsettings.node.invoices:company:address": Address
"applicationsettings.node.invoices:company:postcode": Post code
"applicationsettings.node.invoices:company:city": City
applicationsettings.node.shifts: Shifts
"applicationsettings.node.shifts:duration": Shifts duration
"applicationsettings.node.description.shifts:duration": "Default shift time duration. Recommended value: 8."
"applicationsettings.node.shifts:start": The start time of the first shift
"applicationsettings.node.description.shifts:start": The default start time of the first shift of each working day.
applicationsettings.node.monitor: Monitor
"applicationsettings.node.monitor:valueconstraints": Value restrictions
"applicationsettings.node.description.monitor:valueconstraints": Restrictions each input value for monitor.
"applicationsettings.node.monitor:shifts": Changing work iclusion
"applicationsettings.node.description.monitor:shifts": Start time and length work change inclusion during selection monitor time.
"applicationsettings.node.monitor:valueconstraints:minimum": Minimum value
"applicationsettings.node.description.monitor:valueconstraints:minimum": Minimum value to wich input value will be limited .
"applicationsettings.node.monitor:valueconstraints:maximum": Maximum value
"applicationsettings.node.description.monitor:valueconstraints:maximum": Maximum value to which input value will be limited.
menu.invoices: Invoices
invoices.issued: An invoice was issued
invoices.invoice-details: Invoice details
invoices.bank-transfer.details: Bank transfer details
invoices.check-attachment: "On %issue-date% was issued invoice with number <b>%number%</b>, amount <b>%amount%</b>"
invoices.check-attachment-facture: "On %issue-date% was issued invoice with number <b>%number%</b>, amount <b>%amount%</b>"
invoices.check-attachment-payment-date: " and with payment date <b>%payment-date%</b>"
invoices.check-attachment-document: "\n                The document is attached to the e-mail"
invoices.check-attachment-payments-data: " and the transfer data below"
transfer.bank-account.number: Bank account number
transfer.recipient: Recipient of the transfer
transfer.title: Transfer title
transfer.amount: Transfer amount
invoices.issuance-date: Issuance date
invoices.data-needed-for-invoice: Data needed for the invoice
invoices.setup-subscripiton: Set the data for invoicing and subscription in edition car wash
invoices.invoice-issued-in-current-period: "The invoice has been issued in the current period, you can generate again existing invoice"
invoices.regenerate: Regenerate invoice
invoices.currency: Invoice currency
invoices.publish: Publish invoice. The subscriber will receive an e-mail with the invoice and the invoice will be visible for him in system.
invoices.unpublish: Unpublish invoice
invoices.published: Invoice published. The subscriber has received an e-mail with the invoice and the invoice is visible for him in system.
invoices.published-issue: Publish invoice issue
invoices.unpublished: Invoice unpublished
invoices.regenerate-issue: Problem with the invoice generation
invoices.regenerated: The invoice has been generated again
invoices.regenerate-change-dealer: "Can't regenerate invoice because dealer was changed in subscriptions"
invoices.subscription-for: Subscription for
invoices.period: Period
invoices.execution-date: Execution date
invoices.payment-date: Payment date
invoices.pay-date: Date of payment
invoices.bank-account.number: Bank account number (IBAN)
invoices.seller: Seller
invoices.purchaser: Purchaser
invoices.mobile-payment: Mobile Payment
invoices.buy-for-carwash: Payment for carwash
invoices.buy-credits-for-washing: Credits for washing
invoices.buy-credits-for-washing-card-topup: Credits for washing
invoices.buy-beloyal-card: Buying BE LOYAL card
invoices.pay-for-washing: Washing service
invoices.payment-method.transfer: Transfer
invoices.payment-method.credit-card: Credit Card
invoices.payment-method.cash: Cash
invoices.payment-method.mobile-payment: Mobile Payment
invoices.payment-method.p24: Przelewy24
invoices.payment-method.pay-ex: Credit Card
invoices.payment-method.post-paid: Post paid
invoices.number: Invoice no.
invoices.invoice-number: Invoice no.
invoices.number-prefix: Invoice no. prefix
invoices.number-suffix: Invoice no. suffix
invoices.position-name: Positon name
invoices.setup-subscriber-currency: Setup Tax Identification Number and invoice currency to show price
invoices.invoice-ready: Invoicing data is set
invoices.invoice-not-ready: No invoicing data
invoices.payment-term-after-confirmation: "{0}%days% days after acceptance of the invoice|{1}%days% days after acceptance of the invoice|]2,Inf]%days% days after acceptance of the invoice"
invoices.self-invoicing: Samofakturowanie
invoices.invoice-confirmation-request: "Please within 7 days approve invoice by clicking button below "
invoices.invoice-confirmation.button: Confirm invoice
invoices.accounting-mail-title: Carwash Manager Automatyczne fakturowanie - kopie faktur do wydruku
invoices.accounting-mail-content: Faktury wygenerowane automatycznie i opublikowane.
invoices.accounting-mail-content-file: Aby wydrukować ich kopie zapytaj o plik
user.subscriber-not-found-in-accounting-system: "Can't find a customer with given Tax identification number (%nip%) in accounting system"
form.payment-method: Payment method
form.payment-method.transfer: Bank transfer
table.vat: VAT
table.net-price: Net price
table.net-value: Net value
table.vat-tax-value: Vat tax value
table.gross-value: Gross value
table.quantity: Quantity
settings.list: Setting list
settings.application-settings: Application settings
settings.root-setting: main settings
settings.descendant-settings: Sub settings
settings.details: Details settings
settings.delete: Delete settings
settings.deleted: Settings deleted successfully
settings.updated: Settings updated
settings.created: Settings created
settings.create: New settings
settings.edit: Edit settings
settings.delete-confirmation-text: Do You really want to delete the setting
settings.warning-deletion-of-this-setting-will-cause-deleting-all-children-entities: "Warning! Removing this setting will delete all sub-settings!"
settings.no-settings-available: No settings available
settings.main-settings: Main settings
settings.settings-schemas: Definitions settings
settings.names.reports: Reports
settings.names.dashboard: Desktop
settings.names.chat: Chat
settings.names.intervals: Time intervals
settings.names.main: Main settings
settings.names.monitor: Monitor
dropzone.drag-and-drop-or-click: Drag and drop or click
js.of: of
js.widget.there-are-no-parameters-to-choose: There are no parameters to choose
js.widget.there-are-no-positional-parameters: There are no positional parameters
js.date-range-corrected: "Date range has been corrected - beginning date can't be later than end date"
js.value: value
js.ajax.wrong-response: Wrong server response
js.type: Type
js.today: Today
js.clear: Clear
js.close: Close
js.sunday: Sunday
js.monday: Money
js.tuesday: Tuesday
js.wednesday: Wednesday
js.thursday: Thursday
js.friday: Friday
js.saturday: Saturday
js.sun: Sun
js.mon: Mon
js.tue: Tue
js.wed: Wed
js.thu: Thu
js.fri: Fri
js.sat: Sat
js.january: January
js.february: February
js.march: March
js.april: April
js.may: May
js.june: June
js.july: July
js.august: August
js.september: September
js.october: October
js.november: November
js.december: December
js.prevmonth: Previous month
js.nextmonth: Next month
js.choose: Choose
js.cancel: Cancel
js.search: Search
js.info: Info
js.export: Export
js.sum: Sum
js.avg: Average
js.count: Counter
js.efficiency: Efficiency
js.description: Description
js.add: Add
js.sensor: Measure module
js.data-table-row-move-error: "Table row can't be moved"
js.widget.there-are-no-parameter-for-selected-devices: There are no parameters for those devices
widget.there-are-no-positional-parameters: There is no positional parameters for this devices
js.widget.there-are-no-devices-selected: No devices selected
js.widget.this-device-has-no-plugged-sensor: "Device '%s' has no plugged sensor. Measure parameters is actually unavailable."
js.widget.some-of-selected-devices-has-no-plugged-sensor: Some of selected devices has no plugged sensor. Measure parameters of these devices is actually unavailable.
js.widget.none-of-selected-devices-has-plugged-sensors: None of selected devices has plugged sensors. Measure parameters is actually unavailable.
js.widget.no-information: No information
js.widget.evets.get-position-error: "Fail to get GPS position, turn on localization in settings."
js.access-denied-to-requested-action: Access denied to requested action
chat.online: Online
chat.offline: Offline
chat.chat: Chat
tooltip.upload-file: Upload file
tooltip.download-file: Download file
tooltip.edit: Edit
tooltip.send-email: Send an email
pagination.first: First
pagination.last: Last
pagination.next: Next
pagination.previous: Previous
form.scheduler_email: Email
form.scheduler_title: Message title
form.scheduler_body: Message body
form.scheduler_delay: Delay
form.scheduler_workStart: Start
form.scheduler_workEnd: End
form.scheduler_command: Command
form.scheduler_parameters: Parameters
form.scheduler_reportType: Report type
search.label: Search
prev.label: Previous
next.label: Next
show.label: Show
entities.label: Entities
ajaxload.label: "Loading, please wait"
watch.label: You are watching
to.label: to
from.label: from
records.label: records
filter.label: filter
norecords.label: No records to display.
related-files.label: Related files
label.from: From
label.to: to
label.page: Page
label.of: of
usergroups.list: User groups list
usergroups.create: New user group
usergroups.edit: Edit user group
usergroups.usergroups-updated: User gropu has been updated
usergroups.usergroups-add: User group has been added
usergroups.usergroups-re-add: User group has been restored
usergroups.delete: Delete user group
usergroups.delete-confirmation-text: Do you realy want to delete user group
usergroups.removed: User group has been updated
usergroups.restore: Restore
usergroups.restored: User group has been restored
usergroups.details: User group details
usergroups.cannot-remove-group-contains-users: Cannot remove group which contains users
usergroups.cannot-remove-group-contains-subscriber: Cannot remove group which contains subscriber
day.sunday: Sunday
day.monday: Monday
day.tuesday: Tuesday
day.wednesday: Wednesday
day.thursday: Thursday
day.friday: Friday
day.saturday: Saturday
pl: polish
en: english
country.afghanistan: Afghanistan
country.albania: Albania
country.algeria: Algeria
country.andorra: Andora
country.angola: Angola
country.anguilla: Anguilla
country.antarctica: Antarctica
country.antigua-and-barbuda: Antigua and Barbuda
country.netherlands-antilles: Netherlands antilles
country.saudi-arabia: Saudi arabia
country.argentina: Argentina
country.armenia: Armenia
country.aruba: Aruba
country.australia: Australia
country.austria: Austria
country.azerbaijan: Azerbaijan
country.bahamas: Bahamas
country.bahrain: Bahrain
country.bangladesh: Bangladesh
country.barbados: Barbados
country.belgium: Belgium
country.belize: Belize
country.benin: Benin
country.bermudas: Bermudas
country.bhutan: Bhutan
country.belarus: Belarus
country.burma: Burma
country.bolivia: Bolivia
country.botswana: Botswana
country.bosnia-and-herzegovina: Bośnia and Herzegovina
country.brazil: Brazylia
country.brunei-darussalam: Brunei Darussalam
country.british-virgin-islands: British virgin islands
country.burkina-faso: Burkina Faso
country.burundi: Burundi
country.bulgaria: Bulgaria
country.chile: Chile
country.china: China
country.croatia: Croatia
country.cyprus: Cyprus
country.chad: Chad
country.montenegro: Montenegro
country.czech-republic: Czech Republic
country.minor-outlying-islands-united-states: Minor Outlying Islands United States
country.denmark: Denmark
country.democratic-republic-of-kongo: Democratic Republic of Kongo
country.dominica: Dominica
country.djibouti: Djibouti
country.egypt: Egypt
country.ecuador: Ecuador
country.eritrea: Eritrea
country.estonia: Estonia
country.ethiopia: Ethiopia
country.falkland-islands: Falkland islands
country.federated-states-of-micronesia: Federated States of Micronesia
country.philippines: Philippines
country.finland: Finland
country.france: France
country.french-southern-territories: French Southern Territories
country.gabon: Gabon
country.gambia: Gambia
country.south-georgia-and-the-south-sandwich-islands: South Georgia and the South Sandwich Islands
country.ghana: Ghana
country.gibraltar: Gibraltar
country.greece: Greece
country.grenada: Grenada
country.greenland: Grenland
country.georgia: Georgia
country.guam: Guam
country.guiana: Guiana
country.french-guiana: French Guiana
country.guadeloupe: Gwadelupa
country.guatemala: Guadeloupe
country.guinea: Guinea
country.guinea-bissau: Guinea Bissau
country.equatorial-guinea: Equatorial Guinea
country.haiti: Haiti
country.spain: Spain
country.netherlands: Netherlands
country.honduras: Honduras
country.hongkong: Hongkong
country.india: India
country.indonesia: Indonesia
country.irak: Irak
country.iran: Iran
country.ireland: Irland
country.iceland: Iceland
country.israel: Israel
country.jamaica: Jamaica
country.japan: Japan
country.jemen: Jemen
country.jordan: Jordan
country.cayman-islands: Cayman
country.cambodia: Cambodia
country.cameroon: Cameroon
country.canada: Canada
country.qatar: Qatar
country.kazakhstan: Kazakhstan
country.kenya: Kenya
country.kyrgyzstan: Kyrgyzstan
country.kiribati: Kiribati
country.columbia: Columbia
country.comoros: Comoros
country.kongo: Kongo
country.south-korea: South Korea
country.north-korea: North Korea
country.costa-rica: Costa rica
country.cuba: Cuba
country.kuwait: Kuwait
country.laos: Laos
country.lesotho: Lesotho
country.lebanon: Lebanon
country.liberia: Liberia
country.libia: Libia
country.liechtenstein: Liechtenstein
country.lithuania: Lithuania
country.luxembourg: Luxembourg
country.macedonia: Macedonia
country.madagascar: Madagascar
country.mayotte: Mayotte
country.makau: Makau
country.malawi: Malawi
country.maldives: Maldives
country.malaysia: Malaysia
country.mali: Mali
country.malta: Malta
country.northern-mariana-islands: Northern Mariana Islands
country.morocco: Morocco
country.martinique: Martinique
country.mauretania: Mauretania
country.mauritius: Mauritius
country.mexico: Mexico
country.monaco: Monaco
country.mongolia: Mongolia
country.montserrat: Montserrat
country.mozambique: Mozambique
country.moldova: Moldova
country.namibia: Namibia
country.nauru: Nauru
country.nepal: Nepal
country.germany: Germany
country.niger: Niger
country.nigeria: Nigeria
country.nikaragua: Nikaragua
country.niue: Niue
country.norfolk: Norfolk
country.norway: Norway
country.new-caledonia: New Caledonia
country.new-zealand: New Zealand
country.oman: Oman
country.pakistan: Pakistan
country.palau: Palau
country.panama: Panama
country.papua-new-guinea: Papua New Guinea
country.paraguay: Paraguay
country.peru: Peru
country.pitcairn: Pitcairn
country.french-polynesia: French Polynesia
country.poland: Poland
country.puerto-rico: Puerto Rico
country.portugal: Portugal
country.dominican-republic: Dominican Republic
country.south-africa: South Africa
country.cape-verde: Cape Verde
country.central-african-republic: Central African Republic
country.russia: Russia
country.romania: Romania
country.rwanda: Rwanda
country.western-sahara: Western Sahara
country.saint-barthelemy: Saint Barthélemy
country.saint-kitts-and-nevis: Saint Kitts and Nevis
country.saint-lucia: Saint Lucia
country.saint-vincent-and-the-grenadines: Saint Vincent and Grenadyny
country.saint-pierre-and-miquelon: Saint-Pierre and Miquelon
country.salwador: Salwador
country.samoa: Samoa
country.american-samoa: American Samoa
country.san-marino: San Marino
country.senegal: Senegal
country.serbia: Serbia
country.serbi-and-montenegro: Serbia and Czarnogóra
country.seychelles: Seychelles
country.sierra-leone: Sierra Leone
country.singapore: Singapore
country.sint-maarten: Sint Maarten
country.somalia: Somalia
country.sri-lanka: Sri Lanka
country.usa: United States of America
country.suazi: Suazi
country.sudan: Sudan
country.surinam: Surinam
country.svalbard-and-jan-mayen: Svalbard and Jan Mayen
country.syria: Syria
country.switzerland: Switzerland
country.sweden: Sweden
country.slovakia: Slovakia
country.slovenia: Slovenia
country.tajikistan: Tajikistan
country.thailand: Thailand
country.taiwan: Taiwan
country.tanzania: Tanzania
country.palestinian-territories: Palestinian Territories
country.british-indian-ocean-territory: British Indian Ocean Territory
country.east-timor: East Timor
country.togo: Togo
country.tokelau: Tokelau
country.tonga: Tonga
country.tunisia: Tunisia
country.turkey: Turkey
country.turkmenistan: Turkmenistan
country.turks-and-caicos: Turks and Caicos
country.tuvalu: Tuvalu
country.uganda: Uganda
country.ukraine: Ukraine
country.uruguay: Uruguay
country.uzbekistan: Uzbekistan
country.vanuatu: Vanuatu
country.wallis-and-futuna: Wallis and Futuna
country.vatican: Vatican
country.venezuela: Venezuela
country.great-britain: Great Britain
country.vietnam: Vietnam
country.ivory-coast: Ivory coast
country.bouvet-island: Bouvet island
country.christmas-island: Christmas island
country.guernsey: Guernsey island
country.jersey: Jersey island
country.isle-of-man: Isle of Man
country.saint-helena: Sait Helena Island
country.aland-islands: Aland Island
country.cook-islands: Cook Island
country.us-virgin-islands: US Virgin Islands
country.heard-island-and-mcdonald: Heard and McDonalda islands
country.cocos-islands: Cocos Islands
country.marshall-islands: Marshall islands
country.faroe-islands: Faroe islands
country.solomon-islands: Salomon islands
country.sao-tome-and-principe: Sao Time and Principe Islands
country.hungary: Hungary
country.italy: Italy
country.zambia: Zambia
country.zimbabwe: Zimbabwe
country.united-arab-emirates: United Arab Emirates
country.Latvia: Latvia
country.kosovo: Kosovo
country.swaziland: Swaziland
country.runny-nose: Runny Nose
country.serbia-and-montenegro: Serbia and Mntenegro
country.sverige: Sverige
country.nicaragua: Nicaragua
country.libya: Libya
country.afterdamp: Afterdamp
country.united-states: United States
country.el-salvador: El Salvador
country.livery: Livery
country.heard-and-mcdonald-islands: Heard and McDonald Islands
country.hong-kong: Hong Kong
country.suriname: Suriname
country.iraq: Iraq
country.latvia: Latvia
country.yemen: Yemen
country.st.-lucia: St. Lucia
country.macau: Macau
vattax.list: Vattax list
vattax.you-cannot-modify-vat-tax-that-was-used-already: "Vat tax cannot be modified, because it was used already."
vattax.you-cannot-delete-vat-tax-that-was-used-already: "Vat tax cannot be modified, because it was used already."
vattax.delete-confirmation-text: "Do you really want to delete vat tax "
form.vat-tax: VAT tax
vattax.create: Create Tax
vattax.edit: Edit Tax
vattax.delete: Delete Vat Tax
vattax.details: Tax details
form.tax-value: "Digital tax value [%] - use to count e.g. 0, 3, 7, 23"
form.tax-key: "Displayed value e.g. 23, zw., np."
table.tax-key: Displayed value
vue.flextable.rows-per-page: Rows per page
vue.flextable.page: page
vue.flextable.of: of
vue.flextable.search: Search
vue.flextable.items-selected: Items selected
vue.flextable.items-found: Items found
vue.flextable.no-items-found: No items found
vue.flextable.no-limit: All
filter.in_period: In period
message.email-not-activ-check-config: E-mail adress not activated. Check your account settings.
keys.list: Keys list
keys.cardnumber: Card number
keys.owner: Owner
keys.client: Client
keys.enabled: Active
keys.status: Status
keys.change-owner: New owner for
keys.show: Key
key.details: Key details
monitor.software: Software
monitor.plc: PLC
monitor.since: Since
beloyal-tax-income: gross revenue
invoices.not.issued: Invoice not issued
js.ajax.no-company-in-vies-registry: No data about company in VIES regisrty. Fill data manually.
menu.beloyal.payments: Payments
table.last-error-date: Last Error Date
table.payments-available: Payments available
table.actions: Actions
'Your password must be at least {{ limit }} characters long': 'Your password must be at least {{ limit }} characters long'
subscriptions.paid: Paid
subscriptions.canceled: Canceed
subscriptions.to-pay: To pay
subscriptions.processing: Procesing
subscriptions.add: Adding subscription
mobile_app_download: Try our app

# Deprecation notice

deprecation:
    title: Deprecation notice
    content: This view is deprecated and soon will be removed from the site. We advise you used the new view,
    link: which can be found here.

parsley.error-charset: In EU countries, only Latin characters are allowed on invoices. Sorry for the problem.
actions.card-list: Cards list
system-name: Carwash Manager
carwash-manager: Carwash Manager
alarms.no-alarm-type-defined: No alarm type assigned
alarms.no-message-defined: No message defined
alarms.not-started-carwash: Car wash not started
authentication.dealer-accounts-not-supported: Dealer account types are not supported in current context.
authentication.no-carwash-attached: No carwash attached for this user.
table.permissions: Permissions
cmuser.role_cm_costs: Costs
cmuser.role_cm_clients: Customers
cmuser.role_cm_demo: Demo
cmuser.role_cm: Desktop
cmuser.role_subscription_premium: PREMIUM
cmuser.role_subscription_basic: BASIC
cmuser.role_cm_finance_turnover_total: Finance - funds used
cmuser.role_cm_finance_turnover_daily: Finance- turnover daily
cmuser.role_cm_finance_programsusage_total: Finance- programs usage
cmuser.role_cm_finance_programsusage_daily: Finance- daily usage
cmuser.role_cm_finance_mobile_payments: Finance- mobile payments
cmuser.role_cm_loyalsystem_keylist: Keys and loyalty cards- list
cmuser.role_cm_loyalsystem_keylist_and_clients: Keys and loyalty cards- list
cmuser.role_cm_bkfpay_users: BKFPay users
cmuser.role_cm_bkfpay_transactions: BKFPay transactions
cmuser.role_user: CM user
cmuser.role_superadmin: SUPERADMIN
cmuser.role_cm_finance: Finances
cmuser.role_invoices: Invoices
cmuser.role_clients: Clients
cmuser.role_client_invoices_list: Client Invoices List
cmuser.role_client_create_invoice: Client Create Invoice
cmuser.role_cm_monitor: Monitor
cmuser.role_cm_exchanger: Money changer
cmuser.role_cm_moneycollect: Money collect
cmuser.role_cm_moneycollect_stands: Stand money collection
cmuser.role_cm_moneycollect_exchanger: Changer money collection
cmuser.role_cm_settings: Carwash settings
cmuser.role_cm_alarms: Alarm history
cmuser.role_cm_alarms_and_technical_data: Alarm history and Monitor
cmuser.role_cm_instructions: Instructions
cmuser.role_cm_owner: Carwash owner
cmuser.role_cm_protect_invoice_data: Protect invoice data
cmuser.role_cm_service: Service requests
cmuser.username_exists: Username already exists
cmuser.email_exists: User email already exists
cm-settings.floor-heating-option-1: Floor heating switch­ the temperature outside the container, below which the flooring defrost system is enabled.
cm-settings.floor-heating-option-2: Automatic temperature- the temperature which the system will automatically sustain.
cm-settings.light-option-1: OFF- ightening disabled.
cm-settings.light-option-2: ON- lightening enabled.
cm-settings.light-option-3: Automat- lightening works in accordance with the dusk sensor.
cm-settings.light-option-4: Timely- lightening works during the time set in the "from" and "to".
cm-settings.heating-1: Circulation- temperature in which the circulation system is enabled.
cm-settings.heating-2: Brush heating- temperature enabling pouring the hot water to the brush circulation.
cm-settings.heating-3: Central heating- temperature in which the central heating is enabled.
cm-settings.heating-4: Intensity dispensing- the amount of the hot water poured into the brush circulation system.
cm-settings.programs: The “Program rate” section allows to set the washing prices. You can define rates individually for each program. Entering the 0 rate, means charge­free washing for the washing module.
cm-settings.osmosis: Osmosis bypass- activation of the osmosis bypass valve when the osmosis water container is empty.
cm-settings.dosage: The "Dosage" section enables the change of the chemicals dosing settings.
cm-settings.alarm: Alarms- sending the diagnostic information.
cm-settings.monitor: 'Monitor- sending the information about the car wash state (pressure, temperature etc.). '
cm-settings.turnover: Turnover- sending the information about car wash turnover and stands working time.
cm-settings.activities: 'Events- event''s informations are sent login do devices, turnover delete, etc.) '
form.owner: Owner
form.owner-ebkf: Owner - to change the owner, please contact with the EBKF by JR or e-mail <EMAIL>
form.alarmToService: Send alarms to service
form.currency: Currency
form.on: 'On'
form.off: 'Off'
form.dont-send: Don't send
form.reports-type: Reports types
form.interval-in-days: Interval in days
form.timezone: Time zone
form.warning.reload: After click on "save" button, system will log You out to save changes.
form.warning.reload.flag: We need to log You out to change the language.
form.carwash-name: Carwash name
form.all-carwashes: All car washes
form.bkfpayuser_identy: User identy
form.bkfpayuser_email: BKFPAY user email
form.user_email: User email
form.send: Send
form.enableLoyaltyCards: Enable BKF Cards
form.invoice-settings: Invoice settings
form.be-loyal-card-price: Card price
form.payment-period: Payment term
form.payment-period-type: Period unit
form.period-count-up-new-loyalty-card-on-first-invoice: Add the %app_name% Loyalty card price to the invoice for the first top-up
invoices.invoice-data-set-and-invoicing-is-active: Ergänzte Rechnungsdaten. Die Abrechnung erfolgt auf.
invoices.automatic-topup-issuence: Automatische Aufstockungs Fakturierung
invoices.issuence: Fakturierung
invoice_numerator.invoice_numerator: Invoice numbering
invoice_numerator.next_invoice_number: Next invoice number
invoice_numerator.invoice_number: invoice number
invoice_numerator.long_month_format: month in 01 format (for January)
invoice_numerator.short_month_format: month in format 1 (for January)
invoice_numerator.long_year_format: year in 2018 format
invoice_numerator.short_year_format: year in 18 format
invoice.invoice-only-premium: The invoicing option is available only in Premium packages.
invoice.you-can-set-invoice-number-and-payment-term-in-owner-profile: The format of the invoice number and payment date can be set in the invoicing settings in
invoice.in-carwash-owner-profile: the carwash owner's profile
clients.card-list: Clients cards
userprofile.reports-report-settings: Report settings
userprofile.reports-report-type: Report types
userprofile.reports-report-type-daily: Daily
userprofile.reports-report-type-weekly: Weekly
userprofile.reports-report-type-monthly: Monthly
userprofile.reports-start-date: Send from
userprofile.repeat-on: 'Send in '
userprofile.reports-daily-repeat: Send at (days)
userprofile.reports-monthly-repeat: Send at (month)
userprofile.reports-send-alarms-on-email: Send alarm notification to requested email
userprofile.reports-send-alarms-on-mobile: Send alarm notification to mobile app
userprofile.reports-send-moneycollect-on-email: Send money collect notification to requested email
form.user_reports_settings_money_collect: Money collect report
form.user_reports_settings_money_collect_interval: Interval in days
form.user_reports_settings_turnover_total: Used funds report
form.user_reports_settings_turnover_total_interval: Interval in days
form.user_reports_settings_programs_usage: Programs usage report
form.user_reports_settings_programs_usage_interval: Interval in days
form.user_reports_settings_programs_usage_daily: Programs usage daily
form.user_reports_settings_programs_usage_daily_interval: Interval in days
form.user_reports_settings_alarms: Send alarm notifications to your email address
form.user_reports_settings_moneycollect-alarms: Send money collect notifications to your email address
form.user_reports_settings_alarms_interval: Interval in days
form.user_reports_settings_turnover_daily: Tunover daily report
form.user_reports_settings_turnover_daily_interval: Interval in days
form.user_reports_settings_mobile_payments: Mobile payments report
form.user_reports_settings_mobile_payments_interval: Interval in days
form.user_reports_settings_share_payments: Share payments report
form.user_reports_settings_share_payments_interval: Interval in days
form.user_reports_settings_fiscal_transactions: Fiscal transactions report
form.user_reports_settings_fiscal_transactions_interval: Interval in days
userprofile.primary-information: Primary information
userprofile.regional-settings: Regional settings
userprofile.reports-settings: Reports settings
userprofile.form.language: Language
userprofile.reports-settings-updated: Report settings have been updated
profile.password-updated: User password updated
profile.changes-not-detected: Profile changes not detected
profile.updated-error: Something wrong with profile update
userprofile.invitations: Invitations
userprofile.message_invitations_has_been_already_sent: The invitation for this user has already been sent
userinvitation.invitation_send_table: Send invitations
userinvitation.message_no_bkfpayuser_exists: BKFPay user does not exists
userinvitation.message_no_user_exists: User does not exists
userinvitation.message_no_invitation_exists: Invitation does not exists
userinvitation.message_user_already_invited: This user has already been invited
userinvitation.message_invitation_accepted: Invitation has been accepted
userinvitation.message_invitation_refuse: Invitation has been refused
userinvitation.message_unrecoginze_action: Unrecognize action
userinvitation.message_invalid_bkfpayuserid: Invalid BKFPay user identy
userprofile.message_invitations_has_been_already_accepted: This user has already accepted your invitation
userprofile.message_invitations_has_been_sent: Invitation has been send
userinvitations.restored: Invitation has been restored
userprofile.send_invitation_table: Sent invitation
userprofile.message-position-deleted: Position has been removed
userinvitation.restore: Restore
menu.cmdashboard: Desktop
menu.carwash-promotions: Promotions
menu.cleaner: Vacuum cleaner
menu.finance-turnover: Turnover
menu.finance-programsusage: Usage
menu.finance-programsusage-total: Programs usage
menu.finance-programsusage-daily: Daily usage
menu.finance-mobile-payments: Mobile payments
menu.finance-payments-share: Payments share
menu.carwash-turnover: Carwash turnover
menu.program-usage: Carwash program usage
menu.loyalsystem-beta: Loyalty cards
menu.loyalsystem-monitor: Monitor keys and loyalty cards
menu.loyalsystem-keylist: Keys an loyalty cards list
menu.technical-parameters-cm: Technical parameters
menu.moneycollect-yeti: Money collection YETI
menu.moneycollect-exchanger: Money changer collection
menu.carwash-instructions: Instructions
menu.carwash-software: Software
menu.bkfpay: BKFPay
menu.bkfpay-users: Associated accounts
menu.bkfpay-user-transaction: BKFPay transactions
menu.finance-used: Used funds on a car wash
menu.finance-paid: Funds paid
menu.alarm-history: Alarm history
menu.costs: Costs
menu.error-report: Contact
menu.finance-fiscal_transactions: Fiscal transactions
menu.support: Support
menu.service: Service requests
menu.process-data: Process data
date.month.1: January
date.month.2: February
date.month.3: March
date.month.4: April
date.month.5: May
date.month.6: June
date.month.7: July
date.month.8: August
date.month.9: September
date.month.10: October
date.month.11: November
date.month.12: December
software.carwash-software-and-instructions: Software and instructions
software.popular-software: Popular software
carwash.no: Carwash no.
carwash.date-with: Date from
carwash.choice: Chose Your carwash
carwash.date-from: Date from
carwash.date-to: to
actions.settings: Settings
message.cookies-policy: Cookie message
table.distributor: Distributor
table.language: Language
table.invitation-to: Invitation for
table.vacuum-cleaner: Vacuum Cleaner
table.invitation-send-to: Invitation for
table.cashBox: Cash box
table.empty-tank: Empty tank
table.foam: Foam
table.CLEANER: Vacuum cleaner
table.alarm-level: Overflowing!
table.full-level: Full
table.low-level: Low
table.mid-level: Average
table.up-float: Up
table.down-floatl: Down
table.mid-float: Middle
table.open: Open
table.close: Close
table.yeti: Yeti
table.brush: Brush
table.cash: Cash
table.other-payments: Other payments
table.mobile-payments: Non-cash payments
table.serial: Serial number
table.serial-key: BKF Key serial number
table.ping: Ping
table.software: Software
table.alarms: Alarms
table.alarms-no-description: No alarm description
table.alarms-description: Description
table.promotion-type: Type of promotion
table.alarms-group: Group
alarm.details: Alarm details
table.nr: No.
table.coins: Coins
table.bill: Banknotes
table.money: Cash
table.keys: BKF Key and BKF Card
table.keys-recharge: BKF Key and BKF Card recharge
table.carwash-recharge: Carwash recharge
table.cards-buy: BKF Cards sale
table.paid: Paid
table.spent: Spent
table.from-last-reset: from last reset
table.tokens: Tokens
table.skycash: Mobile payments
table.sum: Sum
table.payments-sum: Payments sum
table.whole: Whole
table.hardware: Device
table.hardware-no: Device number
table.service: Service
table.promotion-on-loyal-cards-topup: Promotion for topping up BKF Card
table.promotion: Promotion
table.topup-promotion: Top Up promotion
table.promotion-payment-description: Promotion when washing the car. The customer gets an extra amount when washing. The column does not include promotion of top-ups - these can be found in the "Funds paid" tab.
table.promotion-top-up-description: Promotion when topping up. The customer gets an additional amount when topping up the account or loyalty card.
table.cleaner: Cleaner
table.clients: Clients
table.last-reset: Last reset
table.more-details: See more details
table.last-ping-date: Last signal date
table.lastLogin: Last login
table.work-time: Work time
table.date-from: From
table.cards: Credit cards
table.income: Income
table.skyCash: Mobile payments
table.payments_cards: Credit cards
table.bank_cards: Bank Cards
table.hopperA: Hopper A
table.hopperB: Hopper B
table.paypass: Pay pass
table.online: Online
table.offline: Offline
table.communication: Communication
table.communication-off: Communication error
table.ok: Ok
table.error: Error
table.status-on: 'on'
table.status-off: 'off'
table.stand: Stand
table.turbo: Turbo
table.powder: Powder
table.flushing: Rinsing
table.waxing: Polymer
table.polishing: Polishing
table.wheels: Rims
table.carwash: Car wash
table.lack: No name
table.options: Options
table.promotiontype: Promotion type
table.settings: Settings
table.promotion-rangefrom: Recharge promotion from
table.promotion-rangeto: Recharge promotion to
table.balance: Balance
table.client-counter: Client counter
table.hour: Hour
table.dosage: Dosage
table.payment-type: Payment type
table.stand_code: Stand code
table.transaction-type: Transaction type
table.value-after-discount: Value after discount
table.be-loyal-transaction-summary: BE LOYAL Transaction list
table.car-wash-service: Washing service at the car wash
table.tables: Tables
table.table: Table
table.charts: Chart
table.charts-details: Details chart
table.onstart: Show on start
table.fiscal: Fiscal code
button.ok: OK
button.show: Show
button.close: Close
button.back: Back
button.agree: I agree
button.disagree: I disagree
table.csv-export: Export CSV
table.excel-export: Export XLSX
table.summary-export: Summary export
table.transactions-export: Transactions export
table.pdf-export: Export PDF
table.STAND: Stand
table.POST: Top-ups from money changer
table.no-data: No data
table.added-by: Refilled by
table.added-to: Refill to
table.source-exchanger: money changer
table.source-manual: manual refill
table.last-refill-date: Last refill date
table.export: Export
table.rest: Change
table.bkfKeyRecharge: BKF Card recharge
table.bkfCardSale: BKF Card sale
table.carwashRecharge: Stand top-ups
table.bkfCardSoldCount: Number of BKF Cards Sold
table.program: Program
table.terminalDetails: Payment terminal details
table.programsSale: Program sales
table.bkfCardPay: BKF Card payment
table.terminal: Payment terminal
turnover:
  cash: Cash payments
  cashless: Cashless payments
  prepaid: Prepaid payments
  exchangerHoppers: Money exchanges
  exchangerSale: Money changer sales
dashboard.your-carwash: Your carwashes
dashboard.carwash-counter: No of carwash
dashboard.carwash-connected: Connected to the internet
dashboard.carwash-with-alarms: With alarms
dashboard.table-details: Details of carwash
dashboard.table-details-version: Software version
dashboard.table-details-last-package: Last packet
dashboard.table-details-ip-out: External IP
dashboard.table-details-ip-in: Internal IP
dashboard.network-details: Network details
dashboard.table-details-ip-gateway: IP gateway
dashboard.table-details-mac: MAC
dashboard.table-details-plc: PLC
dashboard.details-protocol: Protocol
dashboard.details-protocols: Protocols
dashboard.last-update: Last update
dashboard.alarms: Alarms
dashboard.alarms-occured-confirmed: Alarm situation does occur, confirmed on the controller
dashboard.alarms-occured-not-confirmed: Alarm situation does occur, not confirmed on the controller
dashboard.alarms-not-occured-not-confiremd: Alarm situation does not occur, not confirmed on the controller
dashboard.last-counter-reading: Last user counter indicator
dashboard.most-trafic: Most traffic
dashboard.most-trafic-tooltip: Most traffic based on programs usage time
dashboard.median-transaction: Average transaction value
dashboard.median-transaction-tooltip: Average transaction value = Income / Clients count
dashboard.profit: Profit
dashboard.trend: Trend
dashboard.customer-distribution: Customer distribution
dashboard.most-clients: Most clients
dashboard.hours: Hours
dashboard.hours-tooltip: Marked in blue are the hours in which the wash is the most customers
dashboard.legend: Legend
dashboard.legend-tooltip: Color tile represents the number of customers in a given hour. The darker the color, the more customers there were in the wash.
bkfpay_user_transactions.new-transaction: New recharge
bkfpay_user_transactions.add-transaction: Recharge
bkfpay_user_transaction.transaction-created: Client account has been refilled
bkfpay_user_transaction.invalid-form-data: All fields should be filled
bkfpay_user_transaction.cant-pay: No carwashes connected or no recharges available
cmmessages.language: Language
cmmessages.type: Type
cmmessages.delete: Delete message
cmmessages.delete-confirmation-text: Are you sure, you want to delete this message?
cmmessages.edit: Edit message
cmmessages.list: Message list
cmmessages.create: Create message
cmmessages.removed: Message has been deleted
cmmessages.restore: Restore message
cmmessages.details: Message details
cmmessages.cmmessages-add: Message added
cmmessages.cmmessages-updated: Message has been updated
cmmessages.restored: Message has been restored
cmmessages.status: Status
cmmessages.title: Title
cmmessages.content: Content
cmmessages.active: Active
cmmessages.inactive: Inactive
exchanger.yeti: YETI
exchanger.exchanger: Money changer
exchanger.turnover: Turnover
exchanger.monitor: Monitor
exchanger.last-turnover: Last turnover
exchanger.turnover-with: Turnover for
exchanger.level: Level
exchanger.level-empty: Empty
exchanger.level-low: Low level
exchanger.level-medium: Middle level
exchanger.level-high: High level
exchanger.message-no-data: There is no data to display
exchanger.message-no-exchanger-exists-message: Selected carwash does not have an money changer.
exchanger.message-exchanger-adv: Selected carwash does not have an money changer.To purchase and install Changer, please contact our sales department BKF.
exchanger.current-state: Current state
exchanger.state-for-period: State for period
exchanger.moneycollect-history: Money collect history
finance.min: min
finance.carwashes-payment-types.cash: Cash
finance.carwashes-payment-types.klu: Keys and loyalty cards
finance.carwashes-payment-types.tok: Tokens
finance.carwashes-payment-types.skyCash: Mobile payments
finance.carwashes-payment-types.card: Cards
finance.carwashes-payment-types.post: Top-ups from money changer
finance.profitability: Profitability
finance.carwash-turnover: Carwash turnover
carwash.interval: Interval
finance.program-usage: Program usage
finance.mobile-payments: Mobile payments
finance.fiscal-transactions: Fiscal Transactions
finance.payment-status-initiated: Started
finance.payment-status-confirmed: Confirmed
finance.payment-status-unknown: Unknown
finance.payment-status-timeout: Timeout
finance.only-confirmed: Only confirmed
finance.daily-turnover: Daily turnover
finance.table: Table
finance.chart: Chart
finance.turnover-from: Turnover from
finance.programusage-hourly: Program usage per hour
finance.message-no-data: There is no data to display
finance.summary: Summary
finance.report-type: Report type
finance.option-turnover-total: Funds used
finance.option-turnover-daily: Daily funds used
finance.option-programsusage-total: Programs usage
finance.option-programsusage-daily: Daily usage
finance.option-mobile-payments: Mobile payments
finance.daily-turnover-data-range: Date range
finance.error-no-data: No data available
finance.option-day: day
finance.option-week: week
finance.option-month: month
finance.option-paytype.skycash: Sky Cash
finance.option-paytype.r2w: Ready2Wash
finance.option-paytype.wwwash: WWWash
finance.option-paytype.udobno: UDOBNO
finance.option-paytype.gazprom: GAZPROM
finance.option-paytype.puto: Puto
finance.option-paytype.pango: Pango
finance.option-paytype.pango_test: Pango Test
finance.option-paytype.rimes: Rimes
finance.option-paytype.autopay: Autopay
finance.option-paytype.autopay_test: Autopay Test
finance.option-paytype.leykaclub: Leykaclub
finance.option-paytype.lwo: LWO
finance.option-paytype.lwo_test: LWO Test
finance.option-paytype.be_loyal: '%app_name%'
finance.option-paytype.posgroup: Pos Group
finance.option-paytype.payosik: Payosik
finance.option-paytype.polskiportfel: Polish Wallet
finance.option-paytype.alpha: Barking
finance.option-paytype.bkfpay: '%app_name%'
finance.option-paytype.credit_card: Credit card
finance.option-paytype.credit_card_i2m: Credit card
finance.option-paytype.p24: Przelewy24
finance.option-paytype.p24_i2m: Przelewy24
finance.option-paytype.scsystems: Smart City Systems
finance.error-no-carwash-selected: No carwash selected
finance.error-undefined-summary: Undefined summary type
finance.charts-sums: Sums charts
finance.message-no-data-to-display: No data to display in a given period
finance.portal-programsusage-total: Total usage of rollover car wash programs
finance.portal-programsusage-daily: Daily usage of rollover car washes
finance:
  payment-source:
    CAR_WASH: Stand
    VACUUM_CLEANER: Vacuum cleaner
    DISTRIBUTOR: Distributor
    MONEY_CHANGER: Money changer
    UNKNOWN: Unknown
  payment-type:
    CASH: Cash
    CASHLESS: Cashless
keyusage.delete_hint: You cannot add a card once removed
keyusage.owner-is-busy: Top-ups are pending.
keyusage.no_or_bad_file: Missing file or bad file type
keyusage.tooltip-with-names: Show only with names
keyusage.tooltip-only-refilled: Show only with transactions
keyusage.tooltip-only-blocked: Show only blocked
keyusage.tooltip-hide-empty: Hide with balance equal to zero
keyusage.tooltip-not-used: Also show with balance equal to zero
keyusage: Key and loyalty cards usage
keyusage-monitor: Monitor użycia kluczy i kart lojalnościowych
keyusage.only-refilled: Only with transactions
keyusage.server-refill: Transaction from server
keyusage.card-lock-transaction: Card lock - download money
keyusage.carwash-refill: Transaction from carwash
keyusage.with-names: Only with names
keyusage.without-names: Only without names
keyusage.hide-without-founds: Hide without funds
keyusage.only-changer-topups: Only with money changer top ups
keyusage.alert.warning: Soon you will surge to a new card view.
keyusage.alert.warning-description: There may be temporary problems with a view of loyalty cards. Please report bugs to <NAME_EMAIL>.
keyusage.alert.warning-link: Loyalty keys
keyusage.not-used: Not used
keyusage.hide-empty: Hide empty
keyusage.creation-hint: In the case of cards and keys being more than 8 digits long you must rewrite the first 8 alphanumeric characters
keyusage.payment: Payment
keyusage.reload: Recharge
keyusage.top-up: Top up
keyusage.balance: Balance
keyusage.start-balance: Start balance
keyusege.no-transactions-in-given-time-range: No transactions in given time range
keyusage.balance_calculated: Recharge - Payment
keyusage.difference: Difference
keyusage.incorect_balance_only: Show incorect only
keyusage.transaction: Transaction
keyusage.payment-list: Payments list
keyusage.carwash-topups-list: Topups from Car Wash
keyusage.internet-topups-list: Topups from the internet
keyusage.message-no-data: There is no data to display
keyusage.add-key: Add BKF Key
keyusage.export-csv: CSV export
keyusage.reload-list: Recharge list
keyusage.send: Send
keyusage.delete: Delete
keyusage.add-reload: Add recharge
keyusage.refill-wrong-value-message: The value must be a positive number
keyusage.delete-refill-key: Remove recharge
keyusage.delete-refill: Remove recharge
keyusage.delete-refill-key-confirmation: Are you sure you want to remove the recharge?
keyusage.key-refill-deleted: BKF Key recharge has been deleted
keyusage.edit-alias: Edit BKF Key
keyusage.delete-key: Delete BKF Key
keyusage.key-alias-edit-success: BKF Key name was changed
keyusage.last-read-card-value: Card
keyusage.last-read-card-value-help: Last information about amount of credits on card send by car wash to the server.
keyusage.delete-key-confirmation: Removing the card will make it disappear from the list on loyalty cards page.
keyusage.delete-key-confirmation-part: Deleted card can not be re-added to the system
keyusage.key-remove-success: BKF Key was deleted
keyusage.key-already-removed: Important! A card that has been removed cannot be re-added.
keyusage.key-remove-fail: There is a problem with key deleting. This key is still not deleted
keyusage.new-key: Create a new BKF Key
keyusage.invalid-key: Wrong characters in the key serial number
keyusage.info-valid-key: You have to use "0" to "9" and letters from "a" to "f". It has to be 8 characters long
keyusage.invalid-hardware-id: Invalid carwash Id for this logged user
keyusage.key-created: BKF Key was created
keyusage.key-exists: BKF Key exists in database
keyusage.awaiting-reload-sum: Sum of pending reacharges for BKF Key
keyusage.invalid-dates: The start date may not be later than the end date
keyusage.find-key-serial: Find BKF Key (at least 3 characters)
keyusage.lock-key: Lock BKF Key
keyusage.lock-key-confirmation: Are you sure you want to lock BKF Key?
keyusage.key-locked: Key has been locked
keyusage.unlock-key: Unlock BKF Key
keyusage.unlock-key-confirmation: Are you sure you want to unlock BKF Key
keyusage.key-unlocked: BKF Key has been unlocked
keyusage.key-restored: You already have a BKF Key with this number. The BKF Key has been restored
keyusage.key-remove-with-reload-list-message: The BKF Key contains a list of top-ups. Removing it will clear this list
keyusage.only-blocked: Blocked only
keyusage.with-transaction: Have been made transactions
keyusage.card-locked: Locked
keyusage.top-up-transaction: Top Up Loyalty Card
keyusage.payment-transaction: Loyalty Card Payment
keyusage.topup-form-carwash: Top up from car wash
keyusage.internet-topup-form-carwash: Top up from the internet
keyusage.transaction-list: Top up list
keyusage.balance_adjustment: Adjustment
keyusage.changer-topup: Money changer top up
keyusage.bonus-for-topup: Top up bonus
moneycollect: Money collect history
moneycollect-exchanger: Money collect changer history
moneycollect.list-period: Money collection from period
moneycollect.list-period-exchanger: Money changer collect for period
moneycollect.message-no-data: There is no data to display
settings.save-timestamp: Last save date
settings.read-timestamp: Last read date
settings: Carwash settings
settings.FloorHeating: Floor Heating
settings.Lighting: Lighting
settings.Heating: Heating
settings.ProgramsRate: Programs rate
settings.Osmosis: Osmosis
settings.Dosage: Dosage
settings.cancel: Cancel
settings.saveSettings: Save settings
settings.FloorHeatingSwitch: Floor heating switch
settings.ManualMode: Manual mode
settings.AutomaticTemperature: Automatic temperature
settings.LightingMode: Lighting mode
settings.ManualLightingMode: The lighting in the timed mode
settings.hh_mm: hh:mm
settings.Circulation: Circulation
settings.BrushHeating: Brush heating
settings.CentralHeating: Central heating
settings.intensityDispensing: Intensity dispensing
settings.Currency: Currency
settings.Turbo: Turbo
settings.Powder: Powder
settings.powder: Powder
settings.WashingOff: Washing off
settings.Waxing: Waxing
settings.Polishing: Polishing
settings.Rims: Rims
settings.Brush: Brush
settings.OsmosisBypass: Osmosis bypass
settings.CommunicationNetworkManagement: Carwash Network Management
settings.Alarms: Alarms
settings.Monitor: Monitor
settings.Parameters: Parameters
settings.Turnover: Turnover
settings.Events: Events
settings.credit: Credit
settings.automatic: Automatic
settings.timely: Timely
js.settings.reallydisable: Really disable settings protocol? This will make future changes impossible from the website
js.settings.newsettings: New settings will be soon transferred to the car wash
js.settings.invalid-period: Enter a valid period
js.settings.invalid-dosage-value: Enter a valid dosage value
technical-parameters: Technical parameters
monitor: Monitor
monitor.powder-tank: Powder tank
monitor.electrovalve: Electrovalve
monitor.dosage: Dosage
monitor.hydrofor: Hydrofor
monitor.read-timestamp: Read date
monitor.carwash-ping-ok: Communications correct
monitor.carwash-one-hour-ping: Communications incorrect over one hour
monitor.carwash-one-day-ping: Communications incorrect over 24 hours
monitor.temperature: Temperature
monitor.temperature-out: Air temperature
monitor.temperature-in: Container temperature
monitor.temperature-heater: Heater temperature
monitor.temperature-3: Temperatura 3
monitor.central-heating: Central heating 1
monitor.central-heating2: Central heating 2
monitor.hot-circulation: Hot circulation valve 2
monitor.oil-level-sensor: Oil level sensor
monitor.circulation: Circulation
monitor.pump: Pomp
monitor.presostat: Pressure Switch
monitor.osmosis: Osmosis
monitor.osmosis-valve: Osmosis valve
monitor.osmosis-drum-pump: Osmosis trommel pump
monitor.osmosis-bypass: 'Osmosis bypass '
monitor.osmosis-water-pump: Osmosis water pump
monitor.osmosis-conductivity: Conductivity
monitor.chemistry: Chemical agents
monitor.chemistry-wax: Wax
monitor.chemistry-brightener: Brightener
monitor.water-supply: Water supply
monitor.water-pressure: Water pressure
monitor.smartheatingA: Smartheating A
monitor.smartheatingB: Smartheating B
monitor.smartheatingC: Smartheating C
monitor.regulator-status: Regulator state
monitor.temperature-remote: Remote temperature
monitor.temperature-read: Read temperature
monitor.mixer: Mixer
monitor.mixer-set: Mixer - set value
monitor.mixer-read: Mixer - read value
monitor.cauldron-power: Boiler- power supply
monitor.cauldron-return: Boiler- return
monitor.temperature-floor: The temperature of the floor
monitor.message-no-data: There is no data to display
monitor.chemistry-turbo: Turbo
monitor.chemistry-spraying: Spray
monitor.chemistry-polishing: Polishing
monitor.chemistry-polymer: Polymer
monitor.chemistry-powder: Powder
carwashpromotionstypes.list: Promotion on carwash
carwashpromotionstypes.carwash-plc: Carwash PLC
carwashpromotionstypes.delete: Removal of promotion for car washes
carwashpromotionstypes.delete-confirmation-text: Are you sure you want to delete a promotion for this carwash?
carwashpromotionstypes.edit: Edit carwash promotions
carwashpromotionstypes.create: Create a promotion for car wash
carwashpromotionstypes.restore: Restore
carwashpromotionstypes.removed: The promotion was deleted
carwashpromotionstypes.details: Promotion details
carwashpromotionstypes.value: Value
carwashpromotionstypes.promotiontype-name: Promotion type name
carwashpromotionstypes.promotion: Promotion
carwashpromotionstypes.promotion-percentage: Percentage
carwashpromotionstypes.promotion-value: Valued
carwashpromotionstypes.carwashpromotionstypes-add: Carwash promotion was created
carwashpromotionstypes.carwashpromotionstypes-updated: Changes have been saved
carwashpromotionstypes.restored: The promotion was restored
carwashpromotionstypes.promotion-from: Recharge from
carwashpromotionstypes.promotion-to: Recharge to
promotionstypes.list: Promotion types list
promotionstypes.create: Create promotion types
promotionstypes.promotionstypes-add: Promotion type has been added
promotionstypes.promotionstypes-updated: Promotion type has been updated
promotionstypes.details: Promotion types details
promotionstypes.restored: Promotion type has been restored
promotionstypes.delete: Delete promotion type
promotionstypes.delete-confirmation-text: Do you realy want to delete promotion type
promotionstypes.edit: Edit promotion type
promotionstypes.removed: Promotion type has been deleted
promotionstypes.restore: Restore
carwashmanager.carwashnr: Carwash no.
carwashmanager.carwashlist: Carwash list
carwashmanager.data: 'Data from: '
services.hardware-entity-not-exists: Couldn't find PLC for the carwash
services.input-data-wrong-structure: The structure of json is invalid
services.keyrevaluequeue-entity-not-exists: No top-up for the BKF Key in the queue
services.invalid-request-method: Improper method for reading data
services.ok-respone: 'OK for a BKF Key: '
errors.database-error: Database error, please try again later
errors.invalid-hardware-owner: No permission for this carwash
error.error.no-device-assigned-to-car-wash: No device assigned to the car wash, contact the service.
filemanager.uploaded: File has been uploaded
filemanager.filemanager-add: File was added
filemanager.filemanager-generated: File was generated
email.report: Report
email.report-title-programusage: Program usage report
email.report-title-turnover: Turnover report
email.report-title-moneycollect: Money collect report
email.report-title-moneycollect-stands: Stands money collection report %date% of car wash %sn%
email.report-title-moneycollect-changer: Money changer collection report %date% of car wash %sn%
reports.report-send: Send
report.reports: Reports
report.create: Create report
report.message-send: Report was send
report.message-no-attach-found: No attachment found
report.period: Time period
report.day: day
report.days: days
report.report-no-selected: No reports selected
report.total: Total
email.report-off: Reports are off
email.report-send-message: Report has been send
reports.email-alarms-title: Alarms at the carwashes
reports.text-message-for-time-period: for the period
reports.text-message-in-attachment: in attachment
reportssettings: Reports setting
reportssettings.details: Report settings detail
reportssettings.report-switcher: Switch on
reportssettings.report-moneycollect: Money collect report
reportssettings.report-programusage: Programs usage report
reportssettings.report-turnover: Turnover report
reportssettings.report-interval: Time interval
reportssettings.report-start-date: Feport from
reportssettings.save-switcher-on: Reports hass been switched on
reportssettings.save-switcher-off: Reports has been switched off
reportssettings.save-switcher-no-user-exists: This user not exists
reportssettings.save-report-success: Data has been saved
reportssettings.save-report-reports-error: No report selected
js.carwashmanager.period.replaced: Incorrect period. The dates have been replaced!
help.turnover-since-last-money-collect: Car wash turnover since last money collect.
help.monitor.inside-temperature: The temperature inside the technical room of the car wash is used to regulate the internal heating of the container in the case of heating using the c.o. boiler.
help.monitor.boiler-temperature: The boiler hot water temperature is controlled by the boiler safety procedures - switching the main wash to cold water with liquid chemicals, turning off the floor heating pumps, displaying the alarms.
help.monitor.circulation-pomp: Operating status of the antifreeze circulation pump (on / off)
help.monitor.circulation-presostat: The status of the pressure switch behind the anti-freeze circulation pump (on / off) is a dry-running protection of the circulation pump, the lack of a pressure switch during operation of the pump means a clogged circulation filter, a pump failure or a pressure switch.
help.monitor.hot-circulation: The state of the hot circulation solenoid valve (open, closed), indicates whether hot water from the boiler is added to the circulation system at the moment, applies to the Hot Circulation option.
help.monitor.powder-tank: Condition of the water level sensors in the water tank with powder (low, high, overflow). On top of that, the tank is filled.
help.monitor.solenoid: Open state of solenoid valves for filling powdered water tank (open / closed). Indicates whether the tank is currently filled.
help.monitor.dosage-pomp: This applies to the VERTICO car wash, the working condition of the powder-water pump (on, off). Indicates whether the water from the powdered water tank is currently fed to the program manifolds.
help.monitor.osmosis-valve: Open state of water valve for water to produce demineralised water (open, closed). When the solenoid valve is open the production is possible.
help.monitor.osmosis-presostat: The state of the pressure switch on the demineralized water system (on / off) is used to protect the osmosis pump before dry operation.
help.monitor.osmosis-drum-pump: Stan pracy pompy produkcji wody zdemineralizowanej (włączona/wyłączona). Pompa załączona jest w celu podwyższenia ciśnienia wody w procesie produkcji wody zdemineralizowanej.
help.monitor.osmosis-bypass: Operating status of demineralized water pump (on / off). The pump is switched on to increase the water pressure in the process of producing demineralized water.
help.monitor.osmosis-water-pump: Operating status of the pump suction water from the demineralized water tank (on / off). The pump is used to feed water to the program manifolds.
help.monitor.osmosis-low-level: State of the bottom level sensor in the demineralized water tank (on / off). Indicates if there is enough water in the tank to allow the pump to operate without running dry.
help.monitor.osmosis-medium-level: Status of medium level sensor in demineralized water tank (on / off). The sensor off signal starts the process of producing demineralized water.
help.monitor.osmosis-high-level: Condition of upper level sensor in demineralized water tank (on / off). It is used to disable the process of producing demineralized water.
help.monitor.osmosis-conductivity: Conductivity value of demineralized water produced. Delivered via a conductivity microcontroller connected to the probe.
help.monitor.water-presure: Water supply pressure value. Transmitted from analogue pressure transducer.
help.monitor.water-presostat: The state of the water supply pressure switch (on / off) is used to protect the entire car wash from running below the minimum operating pressure.
help.monitor.hydrofor: Operating status of the hydrophonic pump supplying the water washer (on / off). The pump starts when the supply pressure drops below the preset level.
help.monitor.smartheating-pomp: Floor heating pump working condition (on / off). The pumps provide a circulation of hot water with glycol in the pipes under the floor of the posts.
cost.type: Cost Type
cost.amount: Amount
cost.created: Cost added
cost_type.created: New cost type added
cost_type.duplicated: Given cost type already exists
dashboard.messages: Messages
dashboard.name: Name
dashboard.time: Time
dashboard.status: Status
dashboard.react: React
dashboard.chemistry-state: Chemistry state
dashboard.internet: Internet
dashboard.amount: Amount
dashboard.clients-count: Clients count
dashboard.percentage: Percentage
dashboard.since-last-collection: Since last money collection
dashboard.today: Today
dashboard.yesterday: Yesterday
dashboard.since_month_start: Current month
dashboard.last-7-days: Last 7 days
dashboard.last-14-days: Last 14 days
dashboard.last-month: Last month
dashboard.cost: Cost
dashboard.no-chemistry-state-sensor: No chemistry state sensor
table.funds_paid_tooltip.cash: Sum of money (banknotes + coins) paid on carwash PLUS funds used in money changer (loyalty cards sell + loyalty card top ups + top up carwash stand from money changer)
table.funds_paid_tooltip.credit_card: Sum of funds from payments/debits cards, paid on carwash PLUS sum of funds from payments/debits cards, paid on money changer
table.funds_paid_tooltip.mobile_payment: Sum of mobile payments made by mobile application, including card payments via a mobile application
table.funds_paid_tooltip.promotion: Amount given to customer as a promotion, by topping up loyalty card and paying using loyalty card. If %app_name% promotion is set on carwash user gets an extra amount by topping up loyalty card or paing using loyalty card (2 different promotions)
terms.cm_title: REGULAMIN APLIKACJI MOBILNEJ CAR WASH MANAGER
form.terms_and_conditions: terms of use
form.accept: Accept
mail.detailsinmail: Details in attachment
mail.report_type: 'Report type:'
mail.text-message-for-time-period: 'For the period:'
report.cash_form_carwash: Cash from car wash
report.turnover-csv-report: Turnover CSV report
report.sorted_funds: Sorted funds
report.Hopper: Hopper
report.amount_sum: Amount sum
report.token_amount: Token value
report.unrecognized_coins_number: Unrecognise coins number
report.number_of_emergency_discharges_to_safe: Number of emergency dischrges to safe
moneycollect-yeti: YETI moneycollect history
moneycollect.list-period-yeti: Yeti money collection for period
subscription-end-in: 'Your subscription to Carwash Manager end in:'
subscription-end: Your subscription to Carwash Manager has expired.
subscription.click: If you want to extend your subscription, click the button below.
subscription.help-text: 'If you have questions, please contact us by e-mail: '
menu.subscription: Subscription
subscription-end-title: Subscription has ended
menu.subscription-to: Subscription due
mail.your-subscription-will-ended-in: On %expiration_date% your subscription for Carwash Manager application will end.
mail.log-in-to-and-pay-subsctiption-fee: Log on %app_url% and pay a subscription to make full use of the system capabilities.
mail.subscription-ending-title: Carwash Manager Subscription Ends
not-supported.your-browser: Your browser is out of date.
alert.supported_browsers: To continue using the application upgrade your browser or install the latest versions of Chrome, Firefox, Edge or Safari.
cmuser.role_cm_loyal_app: Loyalty application
cmuser.role_cm_loyal_app_washstop: Loyalty application Washstop
cmuser.role_cm_loyal_app_be_loyal: Loyalty application BE LOYAL
menu.loyal_app_manager: Loyalty application
subscription: subscription
months-short: Mnt.
discount-for-carwashes-in-warranty: Discount on car washes during the warranty period
subscription.owner-discount: The percentage discount customer
report.no-mobile-payments-in-period: No mobile payment during the given period.
bl-connection-info.no-be-loyal: No mobile payments
bl-connection-info.mobile-payments-active: Mobile payments are available
bl-connection-info.mobile-payments-not-active: Mobile payments are not available
bl-connection-info.no-info: No information about the status of mobile payments
carwash.number-of-available-mobile-payment: Number of carwashes with active mobile payments
cards.error_duplicate: 'Validation error: Dupliated card {numer_karty}. Please remove duplicates.'
cards.error_card_not_exits: 'Validation error: The {numer_karty} card does not exist or is assigned to a different car wash owner'
cards.error_value: 'Validation error: There are zero or negative top-up values in the file'
cards.error_validate: 'Validation error: Incorrect file format'
cards.error_no_cards: 'Validation error: The account does not have assigned loyalty cards.'
error_report:
  problem_types:
    loyalty_cards: Loyalty cards problem
    finance_data: Finance data problem
    client_add: Klient add problem
    subscirption: Subscription problem
    other: Other problem
carwash_alarms:
  on_your_carwash_occured_alarms: On your car wash - %carwash_name% - a new alarm occurred
  actual_alarms_on_this_carwash: Currently, the following alarms occur on this car wash
  check_current_alarms1: You can check the current alarms in
  check_current_alarms2: CarwashManager
  check_current_alarms3: or in the mobile app
alarm-level: Alarm level
alarm-level.information: Information
alarm-level.warning: Warning
alarm-level.error: Error
reporting:
  noreport: Report could not be generated
  datesInvalid: Provided date range is invalid
fakturownia:
  cm-sub-description: 'Carwash Manager Subscription (CM) for CW '
  month-partial: ' month, '
  month: ' month, '
  months: ' months, '
  carwash: ' carwash.'
  carwashes: ' carwashes.'
userprofile.subscription_basic_notice: Available to Basic and Premium plan subscribers
programs:
  prewash: Pre wash
  mainwash: Main wash
  rinsing: Rinsing
  wasxing: Waxing
  glossing: Glossing
  rims: Rims
  brush: Brush
  foam: Foam
  degreaser: Degreaser
  water-usage: Totaal waterverbruik
  water-average: Gemiddeld waterverbruik
fiscal:
  isu: ISU
  serial number: Serial number
  carwash name: Carwash name
  device: Device type
  type: Transaction type
  location: Location
  customer: Customer
  timestamp: Timestamp
  net: Net value
  vat rate: VAT rate
  vat: VAT value
  gross: Gross value
  status: Status
  jir: JIR
  fiscal id: Fiscal ID
  zki: ZKI
  all: All
  source:
    CAR_WASH: Stand
    VACUUM_CLEANER: Vacuum cleaner
    DISTRIBUTOR: Distributor
    MONEY_CHANGER: Money changer
    UNKNOWN: Unknown
notifications:
  money_collect:
    stands_collect: Stand money collection
    changer_collect: Changer money collection
menu.company-data: Company's data

invoice_generator:
  exceptions:
    no-user: No invoice issuer
    no-client-assign-to-card: No client attached to the card
    generate-for-promotional-top-up: An invoice cannot be issued for a promotional top-up
    turn-off: Issuing invoices for this client is turn off
    no-client: No card client was found
    auto-invoice-turn-off: Automatic invoicing for the client is turned off
    already-issued: The invoice has already been issued
    owner-no-country: The owner of the car was not set up the country
    no-privilege-to-card: No permissions for this card
    aggregate-month: The invoice will be generated at the end of the month

topUp:
  date: Top-up date
  exchanger: Diarrhea

form.remember-me: <PERSON><PERSON><PERSON> mich
form.login: Einloggen
form.username: <PERSON><PERSON><PERSON>ame
form.password: Passwort
authentication.user-has-no-roles: <PERSON> hat alternative Konto.
security.bad-credentials: Benutzername oder Passwort falsch
search.label: Suche
show.label: Zeigen
entities.label: Ergebnisse
form.submit: Speichern
form.actions: Aktionen
form.add: Hinzufügen
form.firstname: Vorname
form.lastname: Nachname
form.password-repeat: Wiederholung Passwort
form.phone: Telefon
form.change-photo: Bild ändern
form.change-logo: Logo ändern
form.photo: Profilbild (*.jpg oder *.png)
form.carwash: Autowäsche
form.ctime: Erstellungsdatum
actions.settings: Einstellungen
actions.back-to-list: Zurück zur Liste
actions.delete: Löschen
menu.logout: Abmelden
menu.profile: Mein Profil
menu.home: Hauptseite
menu.application-settings: Anwendungseinstellungen
table.username: Benutzername
table.ctime: Erstellungsdatum
table.phone: Telefon
table.email: E-Mail-Adresse
table.status: Status
table.created: <PERSON>rst<PERSON><PERSON>
table.description: Beschreibung
table.key: Karte
invoices.issued: Rechnung ausgestellt würde
invoices.invoice-details: Rechnungsdetails
invoices.bank-transfer.details: Banküberweisung Details
invoices.check-attachment: "\n                Am %issue-date% wurde die Rechnung (Nummer <b>%number%</b>) für die Summe <b>%amount%</b>.\n                Im Anhang haben wir die Rechnung hinzugefügt und Banküberweisung Details befinden sich unten.\n"
invoices.check-attachment-facture: "\n                Am %issue-date% wurde die Rechnung (Nummer <b>%number%</b>) für die Summe <b>%amount%</b>.\n                Im Anhang haben wir die Rechnung hinzugefügt und Banküberweisung Details befinden sich unten.\n"
invoices.check-attachment-document: "\n                Das Dokument befindet sich im Anhang"
transfer.bank-account.number: Kontonummer
transfer.recipient: Der Empfänger der Übertragung
transfer.title: Titeltransfer
transfer.amount: Transferbetrag
invoices.issuance-date: Ausgabedatum
invoices.data-needed-for-invoice: Die Daten für die Rechnung
invoices.setup-subscripiton: Stell daten für die Rechnung auf und Abonnement
invoices.invoice-issued-in-current-period: "Die Rechnung wurde in der aktuellen Periode ausgegeben, Sie können die vorhandene Rechnung regenerieren"
invoices.regenerate: Regenerieren Rechnung
invoices.currency: Rechnungswährung
invoices.publish: Publiziert die Rechnung. Der Teilnehmer erhält eine E-Mail mit der Rechnung und die Rechnung wird ihm im System angezeigt werden.
invoices.unpublish: Annullier die Rechnung Publikation
invoices.published: Rechnung veröffentlicht. Der Teilnehmer hat eine E-Mail mit der Rechnung erhalten und die Rechnung ist für ihn sichtbar im System
invoices.published-issue: Das Problem mit der Änderung des Status von Publikationen
invoices.unpublished: Widerruf veröffentlichen der Rechnung - Rechnung nicht veröffentlicht
invoices.regenerate-issue: Das Problem mit die Rechnung regenerieren
invoices.regenerated: Die Rechnung generiert nochmals
invoices.regenerate-change-dealer: "Die Rechnung konnte nicht regeneriert werden, da der Händler in den Abonnements geändert wurde"
invoices.subscription-for: Abonnement für
invoices.period: Periode
invoices.execution-date: Ausstellungsdatum
invoices.payment-date: Zahlungsfrist
invoices.pay-date: Datum der Zahlung
invoices.bank-account.number: Bankkontonummer (IBAN)
invoices.seller: Verkäufer
invoices.purchaser: Käufer
invoices.number: Rechnung Nr.
invoices.position-name: Name des Produkts oder der Dienstleistung
invoices.setup-subscriber-currency: Stellen Sie die Währung Preise sehen
invoices.invoice-ready: Daten zur Ausgabe einer Rechnung setzen
invoices.invoice-not-ready: Keine Daten zur Rechnungsstellung
user.subscriber-not-found-in-accounting-system: "Kunde mit der NIP \"%nip%\" nicht gefunden im Buchhaltungssystem"
form.payment-method: Zahlungsweise
form.payment-method.transfer: Banküberweisung
table.no-data-pagination-info: Sie sehen (0-0) mit 0 Datensätzen
table.vat: MwSt
table.net-price: Nettopreis
table.net-value: Nettowert
table.vat-tax-value: MwSt
table.gross-value: Bruttobetrag
table.quantity: Menge
bkfpay.notifications: Benachrichtigungen
bkfpay.waiting-invitations-msg: "Sie haben neue Einladungen, klicken Sie in oben Link, um sie zu sehen"
bkfpay.waiting-invitations-you-have-msg: Einladungen warten auf Akzeptanz
email.greetings: Grüße
email.bank-transfer.title: Überweisungstitel
email.team: BKF team
email.welcome: Herzlich willkommen
email.cm-slogan: Technologie mit Leidenschaft geschaffen
email.password-reset-pt-1: Ihr Passwort im System
email.password-reset-pt-2: müssen zurückgesetzt werden.
email.to-reset: "Setzen Sie Ihr Passwort zurück, indem Sie auf den folgenden Link klicken:"
email.reset-button: Zurücksetzen
email.user-create-confirmation: "Kontoerstellungsbestätigung (%url%)"
email.reset-password: Passwort zurücksetzen
invoices.mobile-payment: Online-Zahlung
message.email-not-activ-check-config: E-Mail-Adresse nicht aktiviert. Überprüfen Sie Ihre Kontoeinstellungen.
beloyal-tax-income: Bruttoeinnahmen
email.sorry_no_invoice: Wir sind nicht in der Lage, die Rechnung zu generieren. Bitte kontaktieren Sie unsere Buchhaltung.
invoices.not.issued: Die Rechnung wird nicht ausgegeben
js.ajax.no-company-in-vies-registry: Keine Daten über das Unternehmen im VIES-Register. Vervollständigen Sie den Rest der Daten manuell.
mobile_app_download: Testen Sie unsere App

# Deprecation notice

deprecation:
    title: Einstellungshinweis
    content: Diese Ansicht ist veraltet und wird in Kürze von der Website entfernt. Wir empfehlen Ihnen, die neue Ansicht zu verwenden,
    link: die Sie hier finden.

actions.card-list: Liste der Karten
alarm.details: Alarmen Details
alarms.alarms-history: Alarmen Geschichte
alarms.list: Liste mit Alarmen
alarms.no-alarm-type-defined: Kein Alarmtyp zugewiesen
alarms.no-message-defined: Keine Nachricht definiert
alarms.not-started-carwash: Waschanlage nicht in Betrieb genommen
alert.supported_browsers: Um die App weiter zu benutzen, aktualisieren Sie Ihren Browser oder installieren Sie die neueste Version von Chrome, Firefox, Edge oder Safari.
applicationsettings.application-settings: App Einstellungen
authentication.dealer-accounts-not-supported: Händlerkonto sind nicht unterstützt.
authentication.no-carwash-attached: Keine Waschanlage mit diesem Benutzer verbunden
bkfpay_user_transaction.cant-pay: Keine Waschanlagen oder keine Aufladungen vorhanden
bkfpay_user_transaction.invalid-form-data: Füllen Sie alle Felder des Formulars aus
bkfpay_user_transaction.transaction-created: Kundenkonto wurde aufgeladen
bkfpay_user_transactions.add-transaction: Aufladen
bkfpay_user_transactions.new-transaction: Neue Aufladung
bl-connection-info.mobile-payments-active: Mobile Zahlungen sind verfügbar
bl-connection-info.mobile-payments-not-active: Mobile Zahlungen sind nicht verfügbar
bl-connection-info.no-be-loyal: Keine mobile Zahlungen BE LOYALEN
bl-connection-info.no-info: Keine Informationen über den Status der mobilen Zahlungen
button.agree: Ich stimme zu
button.back: Zurück
button.close: Schließen
button.disagree: Ich stimme nicht zu
button.ok: OK
button.show: Zeigen
cards.error_card_not_exits: 'Validierungsfehler:  Karte {Nummer_Karte} nicht existiert oder ist für einen anderen Kunde vergeben '
cards.error_duplicate: 'Validierungsfehler:  In der Datei gibt es mehrere Aufladungen von {Karte_Nummer}. Doppelte Einträge müssen gelöscht werden.'
cards.error_no_cards: 'Validierungsfehler:  Das Konto hat keine zugeordnete Loyalitätskarten '
cards.error_validate: 'Validierungsfehler:  Dateiformatierung falsch '
cards.error_value: 'Validierungsfehler:  Die Datei hat Aufladungswerte, die gleich Null oder negativ sind'
carwash-manager: Carwash Manager
carwash.choice: Waschanlage auswählen
carwash.date-from: Daten von
carwash.date-to: bis
carwash.date-with: Daten von
carwash.interval: Zeitspanne
carwash.no: Waschanlagenummer
carwash.number-of-available-mobile-payment: Waschanlage mit Mobilezahlungen
carwashmanager.carwashlist: Waschanlage auswählen
carwashmanager.carwashnr: Waschanlagenummer
carwashmanager.data: 'Daten aus: '
carwashpromotionstypes.carwash-plc: PLC der Waschanlage
carwashpromotionstypes.carwashpromotionstypes-add: Promotion für die SB Waschanlage wurde erstellt
carwashpromotionstypes.carwashpromotionstypes-updated: Änderungen wurden gespeichert
carwashpromotionstypes.create: Promotion für SB Waschanlage erstellen
carwashpromotionstypes.delete: Promotion für SB Waschanlage löschen
carwashpromotionstypes.delete-confirmation-text: Möchten Sie wirklich die Promotion für diese SB Waschanlage löschen?
carwashpromotionstypes.details: Promotiondetails für SB Waschanlage
carwashpromotionstypes.edit: Promotion für die SB Waschanlage bearbeiten
carwashpromotionstypes.list: Promotionliste für SB Waschanlage
carwashpromotionstypes.promotion: Promotion
carwashpromotionstypes.promotion-from: Aufladung von
carwashpromotionstypes.promotion-percentage: Prozent
carwashpromotionstypes.promotion-to: Aufladung bis
carwashpromotionstypes.promotion-value: Wert
carwashpromotionstypes.promotiontype-name: Name der Promotion
carwashpromotionstypes.removed: Promotion für SB Waschanlage wurde gelöscht
carwashpromotionstypes.restore: Zurück
carwashpromotionstypes.restored: Promotion für die SB Waschanlage wurde wiederhergestellt
carwashpromotionstypes.value: Wert
clients.card-list: Kundenkarten
clients.invoice-cannot-be-generated: Die Rechnung konnte nicht für diesen Kunde ausgestellt werden
cm-settings.activities: Ereignisse- die Informationen über Ereignissen werden gesendet (einloggen in Gerät, Umsatz löschen, etc.)
cm-settings.alarm: Alarmen- Diagnoseinformationen werden gesendet.
cm-settings.dosage: Dosierungseinstellungen ermöglicht die Änderung der Dosierungschemie im Waschprogrammen.
cm-settings.floor-heating-option-1: Temperatur einschalten- Temperatur außer Container, unter dieser Temperatur wird Bodenheizung eingeschaltet.
cm-settings.floor-heating-option-2: Automatische Temperatur- System wird automatisch die Temperatur halten .
cm-settings.heating-1: Zirkulation- Temperatur der Einschaltung der Zirkulation.
cm-settings.heating-2: Das Aufheizen der Bürste- Temperatur, es wird warmes Wasser für die Bürste zugegeben.
cm-settings.heating-3: Zentralheizung- Temperatur, es wird die Heizung eingeschaltet.
cm-settings.heating-4: Dosierungsintensität- Prozentwert der Menge des warmes Wasser für die Bürste.
cm-settings.light-option-1: AUS- Belechtung ausgeschaltet.
cm-settings.light-option-2: EIN- Beleuchtung durch ganze Zeit eingeschaltet.
cm-settings.light-option-3: Automat – Beleuchtung arbeitet mithilfe Dämmerungsensor.
cm-settings.light-option-4: Zeitlich – Beleuchtung wird eingeschaltet „von” ­ „bis”.
cm-settings.monitor: Bildschirm- es wird die Mitteilungen über SB Waschanlage gesendet (Druck, Temperatur etc.)
cm-settings.osmosis: Osmosen Bypass- Aktivierung, wenn Behälter für Osmosewasser leer ist.
cm-settings.programs: Preiseinstellungen. Es ist möglich verschiedene Preise für jeden Waschprogramm. Wert 0 bedeutet, dass Autowaschen kostenlos ist.
cm-settings.turnover: Umsatz- Informationen über Umsatz der SB Waschanlage und Arbeitszeit
cmmessages.active: Aktiv
cmmessages.cmmessages-add: Mitteilung hinzugefügt
cmmessages.cmmessages-updated: Mitteilung wurde aktualisiert
cmmessages.content: Inhalt
cmmessages.create: Neue Mitteilung
cmmessages.delete: Mitteilungen löschen
cmmessages.delete-confirmation-text: Möchtest du diese Mitteilung löschen?
cmmessages.details: Mitteilung Details
cmmessages.edit: Mitteilung bearbeiten
cmmessages.inactive: Ausgeschaltet
cmmessages.language: Sprache
cmmessages.list: Mitteilungen prüfen
cmmessages.removed: Mitteilung wurde gelöscht
cmmessages.restore: Mitteilung wiederherstellen
cmmessages.restored: Mitteilung wurde wiederherstellt
cmmessages.status: Status
cmmessages.title: Name
cmmessages.type: Typ
cmuser.email_exists: Benutzer E-Mail ist bereits vorhanden
cmuser.role_client_create_invoice: Client Create Invoice
cmuser.role_client_invoices_list: Client Invoices List
cmuser.role_clients: Kunde
cmuser.role_cm: Bedienpult
cmuser.role_cm_alarms: Alarmen Geschichte und Monitor
cmuser.role_cm_alarms_and_technical_data: Alarmen Geschichte
cmuser.role_cm_bkfpay_transactions: BKFPay Transaktionen
cmuser.role_cm_bkfpay_users: Benutzer BKFPay
cmuser.role_cm_clients: Kunden
cmuser.role_cm_costs: Kosten
cmuser.role_cm_demo: Demo
cmuser.role_cm_exchanger: Geldwechsler
cmuser.role_cm_finance: Finanzen
cmuser.role_cm_finance_mobile_payments: Finanzen- mobile Zahlungen
cmuser.role_cm_finance_programsusage_daily: Finanzen- den täglichen Gebrauch
cmuser.role_cm_finance_programsusage_total: Finanzen- Waschprogramme Verwendung
cmuser.role_cm_finance_turnover_daily: Finanzen- Umsatz täglich
cmuser.role_cm_finance_turnover_total: Finanzen- Umsatz gesamt
cmuser.role_cm_instructions: Anleitungen
cmuser.role_cm_loyal_app: Loyalität App
cmuser.role_cm_loyal_app_be_loyal: Loyalität App BE LOYAL
cmuser.role_cm_loyal_app_washstop: Loyalität App Washstop
cmuser.role_cm_loyalsystem_keylist: Schlüssel und Loyalitätskarten- Liste
cmuser.role_cm_loyalsystem_keylist_and_clients: Schlüssel und Loyalitätskarten- Liste
cmuser.role_cm_moneycollect: Inkasso
cmuser.role_cm_moneycollect_exchanger: Inkasso Geldwechsler
cmuser.role_cm_moneycollect_stands: Inkasso Waschboxen
cmuser.role_cm_monitor: Monitor
cmuser.role_cm_owner: Besitzer der Waschanlage
cmuser.role_cm_protect_invoice_data: Rechnungsdaten schützen
cmuser.role_cm_settings: Einstellungen der Waschanlage
cmuser.role_cm_service: Anfragen zur Betreuung
cmuser.role_invoices: Rechnungen
cmuser.role_subscription_basic: BASIC
cmuser.role_subscription_premium: PREMIUM
cmuser.role_superadmin: SUPERADMIN
cmuser.role_user: Benutzer CM
cmuser.username_exists: Benutzername existiert bereits
cost_type.created: Neue Kostenart hinzugefügt
cost_type.duplicated: Der angegebene Kostenart existiert bereits
cost.amount: Betrag
cost.created: Kosten hinzugefügt
cost.type: Kostentyp
dashboard.alarms: Alarmen
dashboard.alarms-not-occured-not-confirmed: Alarmzustand nicht auftritt, nicht bestätigt auf dem Kontroller
dashboard.alarms-occured-confirmed: Alarmzustand nicht auftritt, bestätigt auf dem Kontroller
dashboard.alarms-occured-not-confirmed: Alarmzustand auftritt, nicht bestätigt auf dem Kontroller
dashboard.amount: Betrag
dashboard.carwash-connected: Mit Internet verbunden
dashboard.carwash-counter: Anzahl von SB Waschanlagen
dashboard.carwash-with-alarms: mit Alarmen
dashboard.chemistry-state: Waschmittelstand
dashboard.clients-count: Anzahl von Kunden
dashboard.cost: Kosten
dashboard.customer-distribution: Kundenverteilung
dashboard.details-protocol: Bericht
dashboard.details-protocols: Berichte
dashboard.hours: Stunden
dashboard.hours-tooltip: Blau markiert sind die Stunden, in denen auf der Waschanlage ist die meisten Kunden
dashboard.internet: Internet
dashboard.last-14-days: Letzte 14 Tage
dashboard.last-7-days: Letzte 7 Tage
dashboard.last-counter-reading: Der letzte Zählerstand von Benutzer
dashboard.last-month: Letzter Monat
dashboard.last-update: Letzte Aktualisierung
dashboard.legend: Legende
dashboard.legend-tooltip: Farbe Fliese stellt die Anzahl der Kunden in einer bestimmten Stunde. Je dunkler die Farbe, desto mehr Kunden auf der Waschanlage waren.
dashboard.median-transaction: Beleg Durchschnittswert
dashboard.median-transaction-tooltip: Beleg Durchschnittswert  = Einkommen / Kundenanzahl
dashboard.messages: Benachrichtungen
dashboard.most-clients: Die meisten Kunden
dashboard.most-trafic: Der größte Verkehr
dashboard.most-trafic-tooltip: Der meiste Verkehr basiert auf der Nutzungszeit des Programms
dashboard.name: Name
dashboard.network-details: Netzwerkdetails
dashboard.no-chemistry-state-sensor: Kein Waschmittelsensor
dashboard.percentage: Prozent
dashboard.profit: Gewinn
dashboard.react: Reagieren
dashboard.since_month_start: Seit Monatsbeginn
dashboard.since-last-collection: Seit der letzten Inkassierung
dashboard.status: Status
dashboard.table-details: Details
dashboard.table-details-ip-gateway: IP gateway
dashboard.table-details-ip-in: IP Innen.
dashboard.table-details-ip-out: IP Außen.
dashboard.table-details-last-package: Letzte Paket
dashboard.table-details-mac: MAC
dashboard.table-details-plc: PLC
dashboard.table-details-version: Software Version
dashboard.time: Datum
dashboard.today: Heute
dashboard.trend: Trend
dashboard.yesterday: Gestern
dashboard.your-carwash: Deine SB Waschanlagen
menu.error-report: Fehler melden
date.month.1: Januar
date.month.10: Oktober
date.month.11: November
date.month.12: Dezember
date.month.2: Februar
date.month.3: März
date.month.4: April
date.month.5: Mai
date.month.6: Juni
date.month.7: Juli
date.month.8: August
date.month.9: September
discount-for-carwashes-in-warranty: Rabatt für die Waschanlage während der Garantieleistung
email.cm-account-created: 'Ihr Konto in Carwash Manager wurde erstellt. Bitte melden Sie sich an https: //cm.bkf.pl'
email.report: Bericht
email.report-off: Berichten sind ausgeschaltet
email.report-send-message: Bericht gesendet
email.report-title-moneycollect: Inkassobericht
email.report-title-moneycollect-stands: Waschboxen Inkassobericht %date% von der Waschanlage %sn%
email.report-title-moneycollect-changer: Geldwechsler Inkassobericht %date% von der Waschanlage %sn%
email.report-title-programusage: Bericht von Programmverwendung
email.report-title-turnover: Umsatzbericht
error.error.no-device-assigned-to-car-wash: Kein Gerät, das der Waschanlage zugeordnet ist, wenden Sie sich bitte an den Service.
errors.database-error: Datenbankfehler, Bitte versuchen Sie es später erneut
errors.invalid-hardware-owner: Keine Genehmigung für diese SB Waschanlage
exchanger.current-state: Aktueller Stand
exchanger.exchanger: Geldwechsler
exchanger.last-turnover: Der letzte Umsatz
exchanger.level: Niveu
exchanger.level-empty: leer
exchanger.level-high: Hochniveau
exchanger.level-low: Niedrigniveau
exchanger.level-medium: Mittelniveau
exchanger.message-exchanger-adv: Ausgewählte SB Waschanlage hat kein Wechselgerät. Um das Wechselgerät zu kaufen, setzen Sie sich bitte mit der Handelsabteilung von BKF in Verbindung .
exchanger.message-no-data: Keine Daten angezeigt
exchanger.message-no-exchanger-exists-message: Ausgewählte SB Waschanlage hat kein Geldwechsler
exchanger.moneycollect-history: Inkassogeschichte
exchanger.monitor: Desktop
exchanger.state-for-period: Status für den Zeitraum
exchanger.turnover: Umsatz
exchanger.turnover-with: Umsatz für
exchanger.yeti: YETI
filemanager.ctime: Erstellungszeit
filemanager.delete: Datei löschen
filemanager.delete-confirmation-text: Sind Sie sicher, dass Sie diese Datei löschen wollen?
filemanager.details: Datei Details
filemanager.filemanager-add: Datei wurde hinzugefügt
filemanager.filemanager-generated: Datei wurde generiert
filemanager.list: Dateiliste
filemanager.removed: Datei wurde gelöscht
filemanager.uploaded: Datei wurde heruntergeladen
finance.carwash-turnover: Umsatz der SB Waschanlage
finance.carwashes-payment-types.card: Karte
finance.carwashes-payment-types.cash: Bar
finance.carwashes-payment-types.klu: Schlüssel und Loyalitätskarten
finance.carwashes-payment-types.post: Aufladungen vom Geldwechsler
finance.carwashes-payment-types.skyCash: Mobile Zahlungen
finance.carwashes-payment-types.tok: Jetons
finance.chart: Diagramm
finance.charts-sums: Diagramm mit der Summe
finance.daily-turnover: Tagesumsatz
finance.daily-turnover-data-range: Datenbereich
finance.error-no-carwash-selected: Es wurde keine SB Waschanlage ausgewählt
finance.error-no-data: Keine Daten
finance.error-undefined-summary: Ungültige Arte der Zusammenfassung
finance.message-no-data: Keine Daten weden angezeigt
finance.message-no-data-to-display: Keine Daten in diesem Zeitraum
finance.min: min
finance.mobile-payments: Mobile Zahlungen
finance.only-confirmed: Nur bestätigt
finance.option-day: Tag
finance.option-mobile-payments: Mobile Zahlungen
finance.option-month: Monat
finance.option-paytype.alpha: Barking
finance.option-paytype.rimes: Rimes
finance.option-paytype.autopay: Autopay
finance.option-paytype.autopay_test: Autopay Test
finance.option-paytype.be_loyal: '%app_name%'
finance.option-paytype.bkfpay: '%app_name% Gold'
finance.option-paytype.credit_card: Kreditkarte
finance.option-paytype.gazprom: GAZPROM
finance.option-paytype.leykaclub: Leykaclub
finance.option-paytype.lwo: LWO
finance.option-paytype.pango: Pango
finance.option-paytype.pango_test: Pango Test
finance.option-paytype.payosik: Payosik
finance.option-paytype.polskiportfel: Polski Portfel
finance.option-paytype.posgroup: Pos Group
finance.option-paytype.puto: Puto
finance.option-paytype.r2w: Ready2Wash
finance.option-paytype.scsystems: Smart City Systems
finance.option-paytype.skycash: Sky Cash
finance.option-paytype.udobno: UDOBNO
finance.option-paytype.wwwash: WWWash
finance.option-programsusage-daily: Am Tag verwendet
finance.option-programsusage-total: Programmverwendung
finance.option-turnover-daily: Täglich verwendet
finance.option-turnover-total: Verwendet
finance.option-week: Woche
finance.payment-status-confirmed: Bestätigt
finance.payment-status-initiated: Begonnen
finance.payment-status-timeout: Die Zeit wurde überschritten
finance.payment-status-unknown: Unbekannt
finance.profitability: Rentabilität
finance.program-usage: Programmverwendung
finance.programusage-hourly: Programmnutzung für eine Stunde
finance.report-type: Bericht Typ
finance.summary: Zusammenfassung
finance.table: Tabelle
finance.turnover-from: Umsatz für
finance.portal-programsusage-total: Gesamte Nutzung von Portal-Autowaschprogrammen
finance.portal-programsusage-daily: Tägliche Nutzung von Portalwaschanlagen
form.accept: Bestätigen
form.alarmToService: Alarmensenden an Service
form.all-carwashes: Alle Waschanlagen
form.be-loyal-card-price: Preis der BeLoyal Kundenkarte
form.bkfpayuser_email: BKFPAY Benutzer E-Mail
form.bkfpayuser_identy: Benutzernummer
form.carwash-name: Name der SB Waschanlage
form.currency: Währung
form.dont-send: Nicht senden
form.enableLoyaltyCards: BKF Karten einstellen
form.interval-in-days: Zeitspanne in Tagen
form.invoice-settings: Rechnungseinstellungen
form.off: Aus
form.on: Ein
form.owner: Besitzer
form.owner-ebkf: Besitzer - um den Besitzer zu ändern, erstellen Sie eine Anwendung in JR (EBKF) oder schreiben <NAME_EMAIL>.
form.payment-period: Zahlungsdatum
form.payment-period-type: Zeiteinheit
form.period-count-up-new-loyalty-card-on-first-invoice: Rechnen Sie für die Rechnung die erste Aufladung Preis für Loyalitätskarte zu
form.reports-type: Art von Berichten
form.send: Senden
form.terms_and_conditions: Bestimmungen
form.timezone: Zeitzone
form.user_email: Benutzer E-Mail
form.user_reports_settings_alarms: Informationen über Alarmen an die angegebene E-Mail Adresse senden
form.user_reports_settings_alarms_interval: Zeitspanne in Tagen
form.user_reports_settings_mobile_payments: Mobilezahlungen Bericht
form.user_reports_settings_mobile_payments_interval: Zeitspanne in Tagen
form.user_reports_settings_money_collect: Inkassobericht
form.user_reports_settings_money_collect_interval: Zeitspanne in Tagen
form.user_reports_settings_moneycollect-alarms: Mitteilungen über Inkasierung an die angegebene E-Mail Adresse senden
form.user_reports_settings_programs_usage: Bericht von Programmverwendung
form.user_reports_settings_programs_usage_daily: Bericht im Laufe des Tages
form.user_reports_settings_programs_usage_daily_interval: Zeitspanne in Tagen
form.user_reports_settings_programs_usage_interval: Zeitspanne in Tagen
form.user_reports_settings_share_payments: Bericht von Zahlungsanteil
form.user_reports_settings_share_payments_interval: Zeitspanne in Tagen
form.user_reports_settings_turnover_daily: Tagesumsatzbericht
form.user_reports_settings_turnover_daily_interval: Zeitspanne in Tagen
form.user_reports_settings_turnover_total: Bericht
form.user_reports_settings_turnover_total_interval: Zeitspanne in Tagen
form.user_reports_settings_fiscal_transactions: Bericht über steuerliche Transaktionen
form.user_reports_settings_fiscal_transactions_interval: Zeitspanne in Tagen
form.warning.reload: Nach Taste "speichern" drücken, werden Sie ausgeloggt. Die Änderungen werden gespeichert
form.warning.reload.flag: Um die Sprache zu ändern, werden Sie ausgeloggt.
help.monitor.air-temperature: Außentemperatur der Waschanlage, aufgrund der Anzeigen dieses Sensors werden die Fußbodenheizung, die Frostschutzzirkulation, die Heizung der Hochdruckschläuche und die Heißzirkulation aktiviert.
help.monitor.circulation-pomp: Betriebszustand der Frostschutzpumpe (ein/aus)
help.monitor.circulation-presostat: Status des Druckschalters hinter der Frostschutzpumpe (ein/aus), er dient als Trockenlaufschutz für die Umwälzpumpe, kein Druckschaltersignal bei laufender Pumpe bedeutet einen verstopften Umwälzfilter, Beschädigung der Pumpe oder des Druckschalters
help.monitor.dosage-pomp: Gilt für die VERTICO-Konzept-Waschmaschine, Betriebszustand der Pulverwasserpumpe (ein, aus). Zeigt an, ob aktuell Wasser aus dem Pulverwassertank zu den Programmsammlern der Station geleitet wird.
help.monitor.hot-circulation: Der offene Status des Elektroventils für die Heißzirkulation (offen, geschlossen), zeigt an, ob dem Zirkulationssystem zu irgendeinem Zeitpunkt heißes Wasser vom Kessel zugeführt wird, gilt für die Option Heißzirkulation.
help.monitor.hydrofor: Betriebszustand der Hydrophorpumpe, die den Wasserwäscher speist (ein/aus). Die Pumpe läuft an, wenn der Versorgungsdruck unter das eingestellte Niveau fällt.
help.monitor.inside-temperature: Die Temperatur im Inneren des Technikraums der Waschanlage dient zur Regelung des Betriebs der Innenheizung des Containers, falls die Beheizung über den zentralen Heizkreis des Kessels durchgeführt wird.
help.monitor.kettle-temperature: Temperatur des aus dem Kessel austretenden warmen Nutzwassers, aufgrund der Anzeigen dieses Sensors werden Prozeduren zum Schutz des Kessels gestartet - Umschaltung der Grundreinigung auf kaltes Wasser mit flüssigen Chemikalien, Abschaltung der Fußbodenheizungspumpen, Anzeige von Alarmen.
help.monitor.osmosis-bypass: Öffnungszustand des Elektroventils "Osmose-Bypass". (offen, geschlossen), öffnet das Elektroventil, wenn sich kein Wasser im VE-Wasserbehälter befindet. Dann kommt enthärtetes Wasser in das "Shine"-Programm.
help.monitor.osmosis-conductivity: Der Leitfähigkeitswert des erzeugten deionisierten Wassers. Wird über einen an die Sonde angeschlossenen Leitfähigkeits-Mikrocontroller gesendet.
help.monitor.osmosis-drum-pump: Betriebsstatus der Pumpe zur Erzeugung von deionisiertem Wasser (ein/aus). Die Pumpe wird eingeschaltet, um den Wasserdruck bei der Produktion von deionisiertem Wasser zu erhöhen.
help.monitor.osmosis-high-level: Status des oberen Niveausensors im VE-Wassertank (ein/aus). Dient zum Abschalten des Produktionsprozesses von deionisiertem Wasser.
help.monitor.osmosis-low-level: Status des Bodenniveausensors des VE-Wassertanks (ein/aus). Zeigt an, ob genügend Wasser im Tank vorhanden ist, damit die Pumpe ohne Gefahr des Trockenlaufs laufen kann.
help.monitor.osmosis-medium-level: Status des Füllstandssensors im VE-Wassertank (ein/aus). Mit dem Ausschaltsignal des Sensors wird die Produktion von deionisiertem Wasser gestartet.
help.monitor.osmosis-presostat: Status des Druckschalters der VE-Wasser-Erzeugungsanlage (ein/aus), der zum Schutz der Osmoseerzeugungspumpe vor Trockenlauf dient.
help.monitor.osmosis-valve: Offenes Elektroventil für die Produktion von entmineralisiertem Wasser (offen, geschlossen). Wenn das Elektroventil geöffnet ist, ist die Produktion möglich.
help.monitor.osmosis-water-pump: Betriebszustand der Pumpe, die Wasser aus dem VE-Wassertank ansaugt (ein/aus). Die Pumpe dient der Wasserzufuhr zu den Programmkollektoren der Stationen.
help.monitor.powder-tank: Status der Wasserstandssensoren im Pulverwassertank (niedrig, hoch, Überlauf). Auf deren Basis wird die Befüllung des Tanks gesteuert
help.monitor.smartheating-pomp: Betriebsstatus der Fußbodenheizungspumpen (ein/aus). Die Pumpen sorgen für eine Warmwasserzirkulation mit Glykol in den Leitungen unter dem Boden der Stände.
help.monitor.solenoid: Offener Status der Magnetventile zum Füllen des Pulverwassertanks (offen/geschlossen). Zeigt an, ob der Tank zu einem beliebigen Zeitpunkt nachgefüllt wird.
help.monitor.water-presostat: Der Zustand des Wasserversorgungsdruckschalters der Waschanlage (ein/aus) dient zum Schutz der gesamten Waschanlage vor einem Betrieb unterhalb des Mindestbetriebsdrucks.
help.monitor.water-presure: Wert für den Versorgungswasserdruck. Gesendet von einem analogen Drucktransmitter
help.turnover-since-last-money-collect: Waschanlagenumsatz (letzte Inkasso)
invoice_numerator.invoice_number: Rechnungsnummer
invoice_numerator.invoice_numerator: Rechnungsnummer
invoice_numerator.long_month_format: Monat im Format 01 (für Januar)
invoice_numerator.long_year_format: Jahr im Format 2018
invoice_numerator.next_invoice_number: Nächste Rechnungsnummer
invoice_numerator.short_month_format: Monat im Format 1 (für Januar)
invoice_numerator.short_year_format: Jahr im Format 18
invoice.in-carwash-owner-profile: Waschanlagenbesitzerkonto
invoice.invoice-only-premium: Die Rechnungsstellungsoption ist nur in Premium-Paketen verfügbar.
invoice.you-can-set-invoice-number-and-payment-term-in-owner-profile: Das Format der Rechnungsnummer und des Zahlungsdatums kann in den Einstellungen für die Rechnungsstellung in
invoices.automatic-topup-issuence: Automatische Aufladefakturierung
invoices.invoice-data-set-and-invoicing-is-active: Die Daten für die Rechnung wurde ergänzt. Die Rechnungausstellung ist eingeschaltet
invoices.issuence: Fakturierung
js.april: April
js.august: August
js.cancel: Löschen
js.carwashmanager.period.replaced: Falsche Zeitraum.Datum wurde geändert !
js.choose: Auswählen
js.december: Dezember
js.february: Februar
js.fri: Fr
js.friday: Freitag
js.january: Januar
js.july: Juli
js.june: Juni
js.march: März
js.may: Mai
js.mon: Mo
js.monday: Montag
js.nextmonth: Nächste Monat
js.november: November
js.october: Oktober
js.prevmonth: Letzte Monat
js.sat: Sa
js.saturday: Samstag
js.september: September
js.settings.invalid-dosage-value: Nennen Sie richtige Dosis
js.settings.invalid-period: Nennen Sie entsprechenden Bereich
js.settings.newsettings: Neue Einstellungen werden bald an die SB Waschanlage gesendet
js.settings.reallydisable: Bist Du sicher? Die Änderung der Einstellungen werden nicht möglich auf der Internetseite sein
js.sun: So
js.sunday: Sonntag
js.thu: Do
js.thursday: Donnerstag
js.tue: Di
js.tuesday: Dienstag
js.wed: Mi
js.wednesday: Mittwoch
keyusage: Schlüssel und Loyalitätskarten verwenden
keyusage-monitor: Überwachen Sie die Verwendung von Schlüsseln und Kundenkarten
keyusage.add-key: BKF Key hinzufügen
keyusage.add-reload: Aufladung hinzufügen
keyusage.alert.warning: Bald werden Sie auf eine neue Karte Ansicht surge.
keyusage.alert.warning-description: Es kann mit Blick auf die Treuekarten vorübergehende Probleme. Bitte melden Sie Fehler an <NAME_EMAIL>.
keyusage.alert.warning-link: Loyalitätsschlüssel
keyusage.awaiting-reload-sum: Alle erwartene Aufladungen für BKF Key
keyusage.balance: Saldo
keyusage.balance_adjustment: Ausgleich
keyusage.balance_calculated: Saldo
keyusage.bonus-for-topup: Gutschein für Aufladung
keyusage.card-lock-transaction: Karte blockiert - Rückforderung von Mitteln
keyusage.card-locked: Blockiert
keyusage.carwash-refill: Transaktion von der SB Waschanlage
keyusage.carwash-topups-list: Liste der Wiederaufladen aus dem Autowäsche
keyusage.changer-topup: Aufladung aus dem Geldwechlser
keyusage.creation-hint: Im Fall von Karten und Schlüssel, die mehr als 8 Ziffern haben müssen Sie die ersten 8 alphanumerischen Zeichen umschreiben
keyusage.delete: Löschen
keyusage.delete_hint: Beachten Sie, dass Sie nach dem Entfernen keine Karte mehr hinzufügen können.
keyusage.delete-key: BKF Key löschen
keyusage.delete-key-confirmation: Entfernen der Karte wird es aus der Liste auf dem Bildschirm von Treuekarten verschwinden.
keyusage.delete-key-confirmation-part: Gelöschte Karte kann nicht in das System wieder hinzugefügt werden
keyusage.delete-refill: Aufladung löschen
keyusage.delete-refill-key: wird Aufladung gelöscht
keyusage.delete-refill-key-confirmation: Möchten Sie die Aufladung löschen?
keyusage.difference: Unterschied
keyusage.edit-alias: BKF Key bearbeiten
keyusage.export-csv: Exportieren zu CSV
keyusage.find-key-serial: BKF Key finden (Mindestens 3 Zeichen)
keyusage.hide-empty: Leere ausblenden
keyusage.hide-without-founds: Ohne mittel ausblenden
keyusage.incorect_balance_only: Nur falsche zeigen
keyusage.info-valid-key: Zulässige Kennzeichen sind die Ziffern von "0" bis "9" und Buchstabe von "a" bis "f". Der BKF Key muss aus acht Kennzeichen bestehen
keyusage.internet-topup-form-carwash: Internetaufladung
keyusage.internet-topups-list: Liste der Internet top-ups
keyusage.invalid-dates: Beginndatum darf nicht spätere als Enddatum sein
keyusage.invalid-hardware-id: Falsche SB Waschanlage für eingeloggte Benutzter
keyusage.invalid-key: Falsche Kennzeichen in Seriennummer
keyusage.key-alias-edit-success: BKF Key Name wurde geändert
keyusage.key-already-removed: Beachten Sie, dass eine entfernte Karte nicht erneut hinzugefügt werden kann.
keyusage.key-created: Der BKF Key wurde erstellt
keyusage.key-exists: Der BKF Key existiert
keyusage.key-locked: BKF Key wurde blockiert
keyusage.key-refill-deleted: BKF Key Aufladung wurde gelöscht
keyusage.key-remove-fail: Problem mit dem Schlüssel. Der Schlüssel wurde nicht gelöscht
keyusage.key-remove-success: BKF Key wurde gelöscht
keyusage.key-remove-with-reload-list-message: Der BKF Key hat eine Aufladungsliste. Die BKF Key löschung wird die Aufladungsliste löschen
keyusage.key-restored: Sie haben bereits einen BKF Key mit dieser Nummer. Der BKF Key wurde wiederhergestellt
keyusage.key-unlocked: BKF Schlüssel wurde freigegeben
keyusage.last-read-card-value: Karte
keyusage.last-read-card-value-help: Letzte gesendete Information über das Wert auf BKF Karte oder BKF Schlüssel
keyusage.lock-key: BKF Key blockieren
keyusage.lock-key-confirmation: Möchtest du wirklich den BKF Key blockieren?
keyusage.message-no-data: Keine Daten
keyusage.new-key: Neue BKF Key erstellen
keyusage.no_or_bad_file: Keine oder Falsche Datei
keyusage.not-used: Nicht verwendbar
keyusage.only-blocked: Nur blockiert
keyusage.only-changer-topups: Nur vom Geldwechsler aufgeladen
keyusage.only-refilled: Nur mit Transaktionen
keyusage.owner-is-busy: Aufladung wird gewartet
keyusage.payment: Zahlung
keyusage.payment-list: Zahlungsliste
keyusage.payment-transaction: Zahlung mit Loyalitätskarte
keyusage.refill-wrong-value-message: Aufladunsgwert  muss
keyusage.reload: Aufladung
keyusage.reload-list: Aufladungsliste
keyusage.send: gesendet
keyusage.server-refill: Transaktion vom Server
keyusage.start-balance: Anlaufbilanz
keyusage.tooltip-hide-empty: Verstecken mit einem Nullsaldo
keyusage.tooltip-not-used: Berücksichtigen Sie dabei mit einer Null-Bilanz
keyusage.tooltip-only-blocked: Nur blockiert zeigen
keyusage.tooltip-only-refilled: Nur mit Transaktionen zeigen
keyusage.tooltip-with-names: Nur mit Namen zeigen
keyusage.top-up: Aufladung
keyusage.top-up-transaction: Loyalitätskarte Aufladung
keyusage.topup-form-carwash: Aufladung aus Waschanlage
keyusage.transaction: Transaktion
keyusage.transaction-list: Aufladungsliste
keyusage.unlock-key: BKF Key freigeben
keyusage.unlock-key-confirmation: Möchtest du wirklich den BKF Key freischalten
keyusage.with-names: Nur mit Namen
keyusage.with-transaction: null
keyusage.without-names: Nur ohne Namen
keyusege.no-transactions-in-given-time-range: Keine Transaktionen in diesem Bereich
mail.detailsinmail: Details im Anhang
mail.log-in-to-and-pay-subsctiption-fee: Melden Sie sich unter %app_url% an, und bezahlen Sie ein Abonnement, um die Systemfunktionen in vollem Umfang nutzen zu können.
mail.report_type: Berichtstyp
mail.subscription-ending-title: Carwash Manager Subscription wird beendet
mail.text-message-for-time-period: für den Bereich
mail.your-subscription-will-ended-in: Am %expiration_date% wird Ihr Abonnement Carwash Manager-Anwendung beenden.
menu.alarm-history: Alarmen Geschichte
menu.bkfpay: BKFPay
menu.bkfpay-user-transaction: BKFPay Transaktionen
menu.bkfpay-users: Benutzerkonten verbunden
menu.carwash-instructions: Anleitungen
menu.carwash-promotions: Promotion
menu.carwash-settings: SB Waschanlage Einstellungen
menu.carwash-software: Software
menu.carwash-turnover: Umsatz der SB Waschanlage
menu.cleaner: Staubsuager
menu.clients: Kunden
menu.cmdashboard: Desktop
menu.costs: Kosten
menu.exchanger: Geldwechsler
menu.finance: Finanz
menu.finance-mobile-payments: Mobile Zahlungen
menu.finance-paid: Einkommen
menu.finance-payments-share: Zahlungsanteil
menu.finance-programsusage: Verwendung
menu.finance-programsusage-daily: Programmverwendung im Laufe des Tages
menu.finance-programsusage-total: Programmverwendung
menu.finance-turnover: Umsatz
menu.finance-used: Umsatz
menu.loyal_app_manager: Loyalität App
menu.loyalsystem: Loyalitätssystem
menu.loyalsystem-beta: Kundenkarten
menu.loyalsystem-keylist: Liste der Schlüssel und Treuekarten
menu.loyalsystem-monitor: Monitorschlüssel und Kundenkarten
menu.moneycollect: Inkasso
menu.moneycollect-exchanger: Inkassogeschichte Geldwechsler
menu.moneycollect-yeti: Inkasso YETI
menu.monitor-cm: Bildschirm
menu.program-usage: Programmverwendung
menu.subscription: Abonnement
menu.subscription-to: Abonnement bis
menu.technical-parameters-cm: Technische Parameter
menu.users: Benutzer
menu.finance-fiscal_transactions: Steuerliche Transaktionen
menu.support: Unterstützung
menu.service: Anfragen zur Betreuung
menu.process-data: Prozessdaten
message.cookies-policy: Mitteilung über cookies
moneycollect: Inkassogeschichte
moneycollect-exchanger: Inkassogeschichte Geldwechsler
moneycollect-yeti: YETI Inkassogeschichte
moneycollect.list-period: Inkassoliste für den Zeitraum
moneycollect.list-period-exchanger: Inkassogeschichte für den Zeitraum
moneycollect.list-period-yeti: Inkassogeschichte für den Zeitraum
moneycollect.message-no-data: Keine Daten
monitor: Bildschirm
monitor.carwash-one-day-ping: Keine Verbindung seit über 24 Stunden
monitor.carwash-one-hour-ping: Keine Verbindung seit einer Stunde
monitor.carwash-ping-ok: Verbindung richtig
monitor.cauldron-power: Kessel- Vorlauf
monitor.cauldron-return: Kessel- Rücklauf
monitor.central-heating: Zentralheizung 1
monitor.central-heating2: Zentralheizung 2
monitor.chemistry: Waschmittel
monitor.chemistry-brightener: Glanzspülen
monitor.chemistry-polishing: Glanzspülen
monitor.chemistry-polymer: Wachs
monitor.chemistry-powder: Pulver
monitor.chemistry-spraying: Spray
monitor.chemistry-turbo: Turbo
monitor.chemistry-wax: Wachs
monitor.circulation: Zirkulation
monitor.dosage: Dosierung
monitor.electrovalve: Magnetventil
monitor.hot-circulation: Hot circulation valve 2
monitor.hydrofor: Hydrophore
monitor.message-no-data: Keine Daten
monitor.mixer: Mischer
monitor.mixer-read: Mischer - gelesene Wert
monitor.mixer-set: Mischer - sollwert
monitor.oil-level-sensor: Ölstandsensor
monitor.osmosis: Osmose
monitor.osmosis-bypass: Bypass Osmose
monitor.osmosis-conductivity: Leifähigkeit
monitor.osmosis-drum-pump: Pumpen Osmose-Trommel
monitor.osmosis-valve: Osmoseventil
monitor.osmosis-water-pump: Osmosewasser Pumpe
monitor.powder-tank: Pulverbehälter
monitor.presostat: Druckregler
monitor.pump: Pumpe
monitor.read-timestamp: Datenablesung
monitor.regulator-status: Regulatorstand
monitor.smartheatingA: Smartheating A
monitor.smartheatingB: Smartheating B
monitor.smartheatingC: Smartheating C
monitor.temperature: Temperatur
monitor.temperature-3: Temperatur 3
monitor.temperature-floor: Bodentemperatur
monitor.temperature-heater: Kesseltemperatur
monitor.temperature-in: Temperatur im Container
monitor.temperature-out: Lufttemperatur
monitor.temperature-read: Temperatur abgelesen
monitor.temperature-remote: Fernsteuerung Temperatur
monitor.water-pressure: Wasserdruck
monitor.water-supply: Wasserversorgung
months-short: Monate.
not-supported.your-browser: Ihr Browser ist nicht aktuell
parsley.error-charset: In EU-Ländern sind auf Rechnungen nur lateinische Zeichen zulässig. Entschuldigung für das Problem.
profile.cant-generate-next-invoice-number: Neue Rechnung
profile.changes-not-detected: Keine Änderungen im Profil aufgetreten
profile.password-updated: Passwort wurde aktualisiert
profile.updated: Die Änderungen wurden gespeichert
profile.updated-error: Die Änderungen wurden nicht gespeichert
promotionstypes.create: Erstellen der Förderung Typen
promotionstypes.delete: Typ der Promotion löschen
promotionstypes.delete-confirmation-text: Möchten Sie wirklich Typ der Promotion löschen
promotionstypes.details: Förderung Typen details
promotionstypes.edit: Bearbeitung Förderung Typen
promotionstypes.list: Typ der Promotion Liste
promotionstypes.promotionstypes-add: Typ der Promotion wurde erstellt
promotionstypes.promotionstypes-updated: Typ der Promotion wurde aktualisiert
promotionstypes.removed: Typ der Promotion wurde gelöscht
promotionstypes.restore: Wiederherstellen
promotionstypes.restored: Förderung Typen wurde restauriert
report.amount_sum: Gesamtförderung
report.cash_form_carwash: Bargeld aus der Waschanlage
report.create: Bericht erstellen
report.day: Tag
report.days: Tagen
report.Hopper: Hopper
report.message-no-attach-found: Der Anhang wurde nicht gefunden
report.message-send: Gesendet
report.no-mobile-payments-in-period: Keine mobile Zahlungen während des bestimmten Zeitraums
report.number_of_emergency_discharges_to_safe: Anzahl der Notausgänge zum Tresor
report.period: Zeitspanne
report.report-no-selected: Keine Berichten ausgewählt
report.reports: Berichten
report.sorted_funds: Guthaben sortiert
report.token_amount: Jetonswert
report.total: Gesamt
report.turnover-csv-report: Tagsbericht CSV Umsatz der Waschanlage
report.unrecognized_coins_number: Volumen der nicht erkannten Münzen
reports.email-alarms-title: Alarmen in der Waschanlage
reports.report-send: Bestätigen
reports.text-message-for-time-period: für Periode
reports.text-message-in-attachment: im Anhang
reportssettings: Berichteneinstellung
reportssettings.details: Detalierte Einstellungen von Berichten
reportssettings.report-interval: Zeitspanne
reportssettings.report-moneycollect: Inkassobericht
reportssettings.report-programusage: Bericht von Programmverwendung
reportssettings.report-start-date: Berichten von
reportssettings.report-switcher: Berichten ein
reportssettings.report-turnover: Umsatzbericht
reportssettings.save-report-reports-error: Sie haben kein Bericht ausgewählt
reportssettings.save-report-success: Daten wurde gespeichert
reportssettings.save-switcher-no-user-exists: Es gibt keiner Benutzer
reportssettings.save-switcher-off: Berichten wurden ausgechaltet
reportssettings.save-switcher-on: Berichten wurden eingeschaltet
services.hardware-entity-not-exists: 'Die SB Waschanlage kann nicht für PLC gefunden werden: '
services.input-data-wrong-structure: Falsche Dateistruktur json
services.invalid-request-method: Falsche Methode Datenablesung
services.keyrevaluequeue-entity-not-exists: Keine Aufladungen für BKF Key
services.ok-respone: 'OK für BKF Key: '
settings: SB Waschanlage Einstellungen
settings.Alarms: Alarmen
settings.automatic: Automat
settings.AutomaticTemperature: Automatische Temperatur
settings.Brush: Schaumbürste
settings.BrushHeating: Das Aufeizen der Bürste
settings.cancel: Löschen
settings.CentralHeating: Zentralheizung
settings.Circulation: Zirkulation
settings.CommunicationNetworkManagement: Carwash Network Management
settings.credit: Kredit
settings.Currency: Währung
settings.Dosage: Dosierung
settings.Events: Ereignise
settings.FloorHeating: Bodenheizung
settings.FloorHeatingSwitch: Temperatur einschalten
settings.Heating: Heizung
settings.hh_mm: 'hh: mm'
settings.intensityDispensing: Dosierungsintensität
settings.Lighting: Beleuchtung
settings.LightingMode: Beleuchtung Betriebsmodus
settings.ManualLightingMode: Beleuchtung in Zeitbetrieb
settings.ManualMode: Manuell
settings.Monitor: Bildschirm
settings.off: AUS
settings.on: EIN
settings.Osmosis: Osmosen
settings.OsmosisBypass: Osmose Bypass
settings.Parameters: Parameter
settings.Polishing: Glanzspülen
settings.Powder: Pulver
settings.powder: Pulver
settings.ProgramsRate: Programmsatz
settings.read-timestamp: Datum der lezte Ablesung
settings.Rims: Felgen
settings.save-timestamp: Letzte Speicherdatum
settings.saveSettings: Einstellungen Speichern
settings.timely: Zeitlich
settings.Turbo: Turbo
settings.Turnover: Umsatz
settings.WashingOff: Klarspülen
settings.Waxing: Wachs Lackkonservieren
software.carwash-software-and-instructions: Software und Anleitungen
software.download-license-file: Lizenzdaten herunterladen
software.license-files: Lizenzdaten
software.popular-software: Populär Software
subscription: Abonnement
subscription-end: Die Subskription für Carwash Manager ist abgelaufen.
subscription-end-in: Ihre Subskription für Carwash Manager wird abgelaufen
subscription-end-title: Das Abonnement ist abgelaufen
subscription.click: Wenn Sie Ihr Abonnement verlängern möchten, klicken Sie auf die Schaltfläche unten.
subscription.help-text: 'Bei Fragen wenden Sie sich bitte an die e-mailem: '
subscription.owner-discount: Der Prozentsatz Rabatt Kunde
subscription.payment_redirect: 'Um ein Abonnement zu kaufen, kontaktieren Sie bitte: '
system-name: Carwash Manager
table.distributor: Verteiler
table.added-by: Aufgeladen durch
table.added-to: Aufgeladen für
table.alarm-level: überfüllt
table.alarms: Alarmen
table.alarms-description: Beschreibung
table.alarms-group: Gruppe
table.alarms-no-description: Keine Alarmbeschreibung
table.balance: Bilanz
table.bank_cards: Bankkarten
table.be-loyal-transaction-summary: Be Loyal Zusammenfassung
table.bill: Banknoten
table.brush: Schaumbürste
table.car-wash-service: Waschen in der Waschanlage
table.cards: Karten
table.cards-buy: BKF Card verkaufen
table.carwash: SB Waschanlage
table.carwash-recharge: Autowaschanlage aufladen
table.cash: Bargeld
table.cashBox: Tresor
table.charts: Diagramm
table.charts-details: Detaliertes Diagramm
table.CLEANER: Staubsauger
table.cleaner: Staubsauger
table.client-counter: Benutzerzähler
table.clients: Kunden
table.close: geschlossen
table.coins: Münzen
table.communication: Verbindung
table.communication-off: Keine Verbindung
table.csv-export: Export CSV
table.date: Datum
table.date-from: Vom
table.devices: Geräte
table.dosage: Dosierung
table.down-floatl: unten
table.empty-tank: leer
table.error: Fehler
table.excel-export: Export XLSX
table.export: Export
table.flushing: Klarspülen
table.foam: Schaum
table.from-last-reset: Seit der letzten Nullstellung
table.full-level: Völlig
table.funds_paid_tooltip.cash: Summe des Geldes (Banknoten + Münzen), die für die Autowäsche bezahlt werden PLUS Geldmittel, die im Geldwechsler verwendet werden (Kundenkarten verkaufen + Kundenkarten-Aufladung + Laden der Kundenautos aus Geldwechsler)
table.funds_paid_tooltip.credit_card: Summe der gefundenen Beträge aus den Bezahl- / Abbuchungskarten, die für die Autowäsche bezahlt werden PLUS Summe der gefundenen Beträge aus den Zahlungen / Abbuchungskarten, bezahlt auf dem Geldwechsler
table.funds_paid_tooltip.mobile_payment: Summe der mobilen Zahlungen, die über eine mobile Anwendung getätigt werden, einschließlich Kartenzahlungen über eine mobile Anwendung
table.funds_paid_tooltip.promotion: Betrag, der dem Kunden als Aktion gegeben wird, indem Kundenkarte aufgefüllt und mit Kundenkarte bezahlt wird. Wenn %app_name% promotion auf carwash gesetzt ist, erhält der Nutzer einen zusätzlichen Betrag, indem er seine Kundenkarte auflädt oder mit der Treuekarte pingt (2 verschiedene Werbeaktionen)
table.hardware: Anlage
table.hardware-no: Anlagennummer
table.hopperA: Hopper A
table.hopperB: Hopper B
table.hour: Stunde
table.in-total: Zusammen
table.bkfKeyRecharge: BKF-Kartenaufladung
table.bkfCardSale: BKF-Kartenverkauf
table.carwashRecharge: Stellungen aufladen
table.bkfCardSoldCount: Anzahl verkaufter BKF-Karten
table.income: Einkommen
table.invitation-send-to: Einladung für
table.invitation-to: Einladung für
table.keys: BKF Key und BKF Card
table.keys-recharge: BKF Key und BKF Card Aufladung
table.lack: Keine
table.language: Sprache
table.last-ping-date: Datum des letzte Signals
table.last-refill-date: Datum der letzte Aufladung
table.last-reset: Letzte gelöscht
table.lastLogin: Letzte Anmeldung
table.low-level: Niedrig
table.mid-float: Mittel
table.mid-level: mittlere
table.mobile-payments: Bargeldlose Zahlungen
table.money: Bar
table.more-details: Sehen Sie bitte mehr Details
table.name: Name
table.no-data: Keine Informationen
table.none: Keine
table.nr: Nr
table.offline: Offline
table.ok: Ok
table.online: Online
table.onstart: Zeig beim Start
table.open: geöffnet
table.options: Optionen
table.other-payments: Andere Zahlungen
table.paid: eingezahlt
table.payment-type: Zahlungsart
table.payments_cards: Karten
table.payments-sum: Zahlungssumme
table.paypass: Pay pass
table.pdf-export: Export PDF
table.permissions: Berechtigungen
table.ping: Ping
table.polishing: Glanzspülen
table.POST: Aufladungen vom Geldwechsler
table.powder: Waschen
table.promotion: Promotion
table.promotion-on-loyal-cards-topup: Promotion beim Aufladen von BKF Card
table.promotion-payment-description: Promotion beim Autowäsche. Kunde bekommt
table.promotion-rangefrom: Promotion für die Aufladung von
table.promotion-rangeto: Promotion für die Aufladung bis
table.promotion-top-up-description: Promotion bei der Aufladung. Der Kunde erhält beim Aufladen des Kontos oder der Kundenkarte einen zusätzlichen Betrag.
table.promotion-type: Promotion
table.promotiontype: Promotiontyp
table.rest: Rest
table.serial: Seriennummer
table.serial-key: BKF Key Seriennummer
table.service: Service
table.settings: Einstellungen
table.skycash: Mobil
table.skyCash: Mobile Zahlungen
table.software: Software
table.source: Quelle
table.source-exchanger: Geldwechsler
table.source-manual: Manuell Aufladung
table.spent: Ausgestellt
table.stand: Waschbox
table.STAND: Waschbox
table.status-off: aus
table.status-on: ein
table.sum: Summe
table.summary-export: Export Zusammenfassung
table.table: Tabelle
table.tables: Tabelle
table.tokens: Jetons
table.topup-promotion: Promotion bei der Aufladung
table.transaction-type: Art
table.transactions-export: Transaktionen exporiteren
table.turbo: Vorwäsche
table.up-float: Oben
table.vacuum-cleaner: Staubsauger
table.value: Wert
table.value-after-discount: Wert mit Rabatt
table.waxing: Heißwachs
table.wheels: Felgen
table.whole: Gesamt
table.work-time: Arbeitszeit
table.yeti: Yeti
table.program: Programm
table.terminalDetails: Angaben zum Zahlungsterminal
table.programsSale: Verkauf von Programmen
table.bkfCardPay: BKF-Kartenzahlung
table.terminal: Zahlungsterminal
turnover:
    cash: Barzahlungen
    cashless: Bargeldlose Zahlungen
    prepaid: Prepaid-Zahlungen
    exchangerHoppers: Geldwechsel
    exchangerSale: Geldwechsler verkaufen
technical-parameters: Technische Parameter
terms.cm_title: Car wash manager Bestimmungen
user.details: Benutzer Details
userinvitation.invitation_send_table: Einladungen gesendet
userinvitation.message_invalid_bkfpayuserid: Die Benutzer-ID ungültig
userinvitation.message_invitation_accepted: Die Einladung wurde angenommen
userinvitation.message_invitation_refuse: Die Einladung wurde entfernt
userinvitation.message_no_bkfpayuser_exists: Der Benutzer BKFPay ist nicht bekannt
userinvitation.message_no_invitation_exists: Die Einladung ist nicht bekannt
userinvitation.message_no_user_exists: Der Benutzer ist nicht bekannt
userinvitation.message_unrecoginze_action: Unbekannte Aktion
userinvitation.message_user_already_invited: Dieser Benutzer wurde bereits eingeladen
userinvitation.restore: Wiederherstellen
userinvitations.restored: Die Einladung wurde wiederhergestellt
userprofile.form.language: Sprache
userprofile.invitations: Einladungen
userprofile.message_invitations_has_been_already_accepted: Der Benutzer hat die Einladung angenommen
userprofile.message_invitations_has_been_already_sent: Die Einladung für diesen Benutzer bereits gesendet wurde
userprofile.message_invitations_has_been_sent: Die Einladung wurde gesendet
userprofile.message-position-deleted: Position wurde entfernt
userprofile.primary-information: Basisdaten
userprofile.regional-settings: Regionaleinstellungen
userprofile.repeat-on: Senden in
userprofile.reports-daily-repeat: Senden in (Tage)
userprofile.reports-monthly-repeat: Senden in (Monate)
userprofile.reports-report-settings: Berichtseinstellungen
userprofile.reports-report-type: Art des Berichts
userprofile.reports-report-type-daily: Täglich
userprofile.reports-report-type-monthly: Monatlich
userprofile.reports-report-type-weekly: Wöchentlich
userprofile.reports-send-alarms-on-email: Senden Alarmbenachrichtigung über E-Mail angefordert
userprofile.reports-send-alarms-on-mobile: Alarmbenachrichtigung an mobile App senden
userprofile.reports-send-moneycollect-on-email: Senden Sie Geld sammeln Benachrichtigung über E-Mail angefordert
userprofile.reports-settings: Berichteinstellungen
userprofile.reports-settings-updated: Berichteinstellungen wurden aktualisiert
userprofile.reports-start-date: Senden aus
userprofile.send_invitation_table: Einladungen gesendet
carwash_alarms:
    on_your_carwash_occured_alarms: Auf Ihrem Autow Wash - %carwash_name% - ein neuer Alarm aufgetreten
    actual_alarms_on_this_carwash: Derzeit treten die folgenden Alarme auf dieser Autowäsche auf
    check_current_alarms1: Sie können die aktuellen Alarme im
    check_current_alarms2: CarwashManager
    check_current_alarms3: oder in der mobilen Anwendung überprüfen.
alarm-level: Alarmstufe
alarm-level.information: Information
alarm-level.warning: Warnung
alarm-level.error: Fehler
reporting:
    noreport: Bericht konnte nicht generiert werden
    dateInvalid: Der angegebene Datumsbereich ist ungültig
fakturownia:
    cm-sub-description: 'Carwash Manager (CM) Abonnement für CW '
    month-partial: ' Monat, '
    month: ' Monat, '
    months: ' Monate, '
    carwash: ' Autowäsche.'
    carwashes: ' Autowäsche.'
userprofile.subscription_basic_notice: Verfügbar für Abonnenten des Basic- und Premium-Plans
programs:
    prewash: Vorwäsche
    mainwash: Hauptwäsche
    rinsing: Klarpülen
    wasxing: Wachs
    glossing: Glanzspülen
    rims: Felgen
    brush: Schaumbürste
    foam: Schaumkanone
    degreaser: Entfetter
    water-usage: Gesamtverbrauch Wasser
    water-average: Durchschnittlicher Wasserverbrauch
fiscal:
    isu: ISU
    serial number: Seriennummer
    carwash name: Autowaschanlage
    device: Gerätetyp
    type: Vorgangsart
    location: Standort
    customer: Kunde
    timestamp: Zahlungsdatum
    net: Nettowert
    vat rate: Mehrwertsteuersatz
    vat: Mehrwertsteuerwert
    gross: Bruttowert
    status: Status
    jir: JIR
    fiscal id: Fiscal ID
    zki: ZKI
notifications:
    money_collect:
        stands_collect: Stehen Sie Geldsammlung
        changer_collect: Ladegerät Geldsammlung
menu.company-data: Daten des Unternehmens

invoice_generator:
    exceptions:
        no-user: Keine Rechnung Aussteller
        no-client-assign-to-card: Kein Kunde, der an die Karte angeschlossen ist
        generate-for-promotional-top-up: Eine Rechnung kann nicht für ein Werbe -Top -up ausgestellt werden
        turn-off: Die Ausstellung von Rechnungen für diesen Kunden ist ausgeschlossen
        no-client: Es wurde kein Kartenkunde gefunden
        auto-invoice-turn-off: Die automatische Rechnungsstellung für den Kunden wird ausgeschaltet
        already-issued: Die Rechnung wurde bereits ausgestellt
        owner-no-country: Der Besitzer des Autos wurde das Land nicht eingerichtet
        no-privilege-to-card: Keine Kartenberechtigungen
        aggregate-month: Die Rechnung wird am Ende des Monats erzeugt

topUp:
    date: Top -up -Datum
    exchanger: Durchfall

card_client_report:
    card_invoice_header: Rechnungsbericht
    card_report_email_header: Kundenkarten verwenden Bericht
    cards_transactions: Kartenübertragungen
    card_transactions: Kartenübertragungen -
    transaction_date: Transaktionsdatum
    card: Karte
    last_usage: Letzte Verwendung
    card_funds: Mittel auf der Karte
    generated: Erzeugt bei
    transaction_type: Art der Transaktion
    internetTopUpsSentToCard: Internet -Topup an Karte gesendet
    carwashTopUps: CARWASH -Aufpackungen
    addedInternetTopUps: Internet -Aufladungssumme
    topUpsToSent: Top -ups warten darauf, gesendet zu werden
    report_for_period: Client -Kartennutzung für den Zeitraum von {dateFrom} zu {dateTo}
    stand: Stand
    value_after_transaction: Gleichgewicht nach Transaktion
    transactions:
        history: Verlauf der Transaktionen
        history_for_card: Kartentransaktionsgeschichte für
        payment: Zahlung
        payments: Zahlungen
        topup: Nachfüllen
        topups: Aufladung
        purchase: Kaufen
        internet: Aus dem Internet
        car_wash: Aus Autowaschanlage
        money_changer: Vom Geldwechsler
        internet_topup: Auf dem Internet nachschlagen
        topup_from_carwash: Nachdem Sie die Autowäsche auffüllen
        refill_for: Nachfüllkarte für
        payment_for_wash: Zahlung für Carwash
        export_error: Problem mit der Generation CSV
        topup_from_money_changer: Geldwechsleraufladung
        topup_from_distributor: Vom Distributor auffüllen
        payment_from_distributor: Zahlung für den Händler
        balance_adjustment: Einstellung
        vacuum_cleaner: Staubsauger
        payment_for_vacuum: Staubsaugerzahlung
        distributor: Verteiler
        unknown: Unbekannt
        promotions: Förderung
        topup_history: Aufladungsgeschichte
        topup_card_history: Kartenaufladungshistorie
    summary_report_email_body: Im Anhang lautet der Loyalty -Kartenbericht für den Zeitraum von {from} bis {to}.

cards_topups_report:
    types:
        ADDITION: Nachfüllen
        PROMOTION: Förderung
        SUBTRACTION: Płatność
    sources:
        CAR_WASH: Stellung
        VACUUM_CLEANER: Staubsauger
        DISTRIBUTOR: Zapfsäule
        MONEY_CHANGER: Geldwechsler
        UNKNOWN: Unbekannt
        DEFAULT: Unbekannt
    statuses:
        WAITING: Warten
        NOT_FULLY_REFILLED: Nicht vollständig gesendet
        REFILLED: Gesendet
        DEFAULT: Unbekannt
    topup_value: Toping -Wert
    topUpsToSent: Top -ups warten auf das Senden
    topup-send: Top -ups gesendet
    source: Quelle
    status: Status
    card_number: Kartennummer

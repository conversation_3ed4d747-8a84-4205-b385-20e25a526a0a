<?php

namespace App\Tests\Subscription;

use App\Entity\Enum\SubscriptionPayer;
use I2m\StandardTypes\Enum\CMSubscription;
use App\Repository\Subscription\SubscriptionPackagesRepository;
use App\Repository\UserRepository;
use App\Service\Subscription\OwnerSubscriptionService;
use I2m\StandardTypes\Enum\Currency;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;

class SubscriptionServiceTest extends WebTestCase
{
    public function packagesData()
    {
        return [
            [
                'from' => '2023-01-01',
                "to" => '2023-04-01',
                "currency" => Currency::PLN,
                "package" => CMSubscription::PREMIUM,
                "length" => 3,
                'value' => 164
            ],
            [
                'from' => '2023-01-01',
                "to" => '2023-04-15',
                "currency" => Currency::PLN,
                "package" => CMSubscription::PREMIUM,
                "length" => 3,
                'value' => 164
            ],
            [
                'from' => '2023-01-01',
                "to" => '2024-01-01',
                "currency" => Currency::PLN,
                "package" => CMSubscription::PREMIUM,
                "length" => 12,
                'value' => 154
            ],
            [
                'from' => '2023-01-01',
                "to" => '2025-01-01',
                "currency" => Currency::PLN,
                "package" => CMSubscription::PREMIUM,
                "length" => 12,
                'value' => 154
            ],
            [
                'from' => '2023-01-01',
                "to" => '2025-01-01',
                "currency" => Currency::EUR,
                "package" => CMSubscription::BASIC,
                "length" => 12,
                'value' => 20
            ]

        ];
    }

    public function subscriptionData()
    {
        return [
            [
                'from' => (new \DateTime())->format('Y-m-d'),
                "to" => (new \DateTime())->modify("+98 day")->format('Y-m-d'),
                "currency" => Currency::PLN,
                "package" => CMSubscription::PREMIUM,
                "totalValue" => 1637.61,
                "discountedValue" => null,
                "payerType" => SubscriptionPayer::BKF
            ],
            [
                'from' => (new \DateTime())->format('Y-m-d'),
                "to" => (new \DateTime())->modify("+12 month")->format('Y-m-d'),
                "currency" => Currency::PLN,
                "package" => CMSubscription::PREMIUM,
                "totalValue" => 5544,
                "discountedValue" => 2767.38,
                "payerType" => SubscriptionPayer::CLIENT
            ]
        ];
    }

    /**
     * @dataProvider packagesData
     */
    public function testPackageSelection($from, $to, $currency, $package, $length, $value): void
    {
        $container = static::getContainer();

        /** @var OwnerSubscriptionService $ownerSubscriptionService */
        $ownerSubscriptionService =  $container->get(OwnerSubscriptionService::class);

        $package = $ownerSubscriptionService->getSubscriptionPackageByDates(
            new \DateTime($from),
            new \DateTime($to),
            $currency,
            $package
        );

        $this->assertEquals($length, $package->getMonthsLength());
        $this->assertEquals($value, $package->getValue());
    }

    /**
     * @dataProvider subscriptionData
     */
    public function testSubscriptionAdmin(string $from, string $to, Currency $currency, CMSubscription $package, $totalValue, $discountedValue, SubscriptionPayer $payerType)
    {
        $container = static::getContainer();



        /** @var OwnerSubscriptionService $ownerSubscriptionService */
        $ownerSubscriptionService =  $container->get(OwnerSubscriptionService::class);
        /** @var UserRepository $userRepository */
        $userRepository =  $container->get(UserRepository::class);


        $subscription = $ownerSubscriptionService->generate(
            new \DateTime($from),
            new \DateTime($to),
            $currency,
            $package,
            $userRepository->findOneByEmail('<EMAIL>')->getSubscriber(),
            $userRepository->findOneByEmail('<EMAIL>'),
            $payerType
        );
        $this->assertEqualsWithDelta($totalValue, $subscription->getBaseValue(), $totalValue/100*5); // zakladam ze moga byc roznice na poziomie 5%
        $this->assertEqualsWithDelta($discountedValue, $subscription->getNetValue(),$discountedValue/100*5);
    }

    /**
     * @dataProvider subscriptionData
     */
    public function testSubscriptionClient(string $from, string $to, Currency $currency, CMSubscription $package, $totalValue, $discountedValue, SubscriptionPayer $payerType)
    {
        $this->client = static::createClient();
        $this->client->request('GET', 'https://secure.snd.payu.com/');
        if ($this->client->getResponse()->getStatusCode() !== 200)
        {
            $this->markTestSkipped("sandbox payU nie jest dostępny");
        }

        $container = static::getContainer();

        /** @var OwnerSubscriptionService $ownerSubscriptionService */
        $ownerSubscriptionService =  $container->get(OwnerSubscriptionService::class);
        /** @var UserRepository $userRepository */
        $userRepository =  $container->get(UserRepository::class);

        $package = $ownerSubscriptionService->getSubscriptionPackageByDates(
            new \DateTime($from),
            new \DateTime($to),
            $currency,
            $package
        );

        $subscription = $ownerSubscriptionService->generateForClient(
            $package->getId(),
            $userRepository->findOneByEmail('<EMAIL>'),
            true
        );

        !is_null($discountedValue) ?
            $this->assertEquals($discountedValue, $subscription->getValueAfterDiscount()) :
            null;


    }
}

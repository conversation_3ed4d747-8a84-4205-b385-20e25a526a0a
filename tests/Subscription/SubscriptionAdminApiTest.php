<?php

namespace App\Tests\Subscription;

use App\Entity\Enum\SubscriptionPayer;
use I2m\StandardTypes\Enum\CMSubscription;
use App\Entity\User;
use App\Repository\UserRepository;
use I2m\StandardTypes\Enum\Currency;
use Symfony\Bundle\FrameworkBundle\KernelBrowser;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Component\HttpFoundation\Response;

class SubscriptionAdminApiTest extends WebTestCase
{
    private KernelBrowser $client ;

    public function subscriptionData()
    {
        return [
            [
                'from' => (new \DateTime())->format('Y-m-d'),
                "to" => (new \DateTime())->modify("+98 day")->format('Y-m-d'),
                "currency" => 'PLN',
                "package" => 'premium',
                "totalValue" => 1637.61,
                "discountedValue" => null,
                "payerType" => 'bkf'
            ],
            [
                'from' => (new \DateTime())->format('Y-m-d'),
                "to" => (new \DateTime())->modify("+12 month")->format('Y-m-d'),
                "currency" => 'PLN',
                "package" => 'premium',
                "totalValue" => 5544,
                "discountedValue" => 2772,
                "payerType" => 'client'
            ]
        ];
    }

    protected function setUp(): void
    {
        //self::ensureKernelShutdown();
        $this->client = $this->createClient();
    }


    /**
     * @dataProvider subscriptionData
     */
    public function testCalculateForbiden(string $from, string $to, string $currency, string $package, $totalValue, $discountedValue, string $payerType): void
    {
        $container = static::getContainer();

        /** @var UserRepository $userRepository */
        $userRepository =  $container->get(UserRepository::class);
        $owner = $userRepository->findOneByEmail('<EMAIL>');

        $this->client->loginUser($owner); // <- ten uzytkownik nie ma prawa do dodawania subskrypcji

        $this->client->request(
            'POST',
            "/administration/subscriber/{$owner->getSubscriber()->getId()}/calculate",
            [
                                   'from' => $from,
                                   'to' => $to,
                                   'currency' => $currency,
                                   'package' => $package,
                                   'payer' => $payerType,
                                    'document' => 'none'
                               ]
        );
        $response = $this->client->getResponse();
        $this->assertEquals(Response::HTTP_FORBIDDEN, $response->getStatusCode());
    }

    /**
     * @dataProvider subscriptionData
     */
    public function testCalculateAdmin(string $from, string $to, string $currency, string $package, $totalValue, $discountedValue, string $payerType): void
    {
        $container = static::getContainer();

        /** @var UserRepository $userRepository */
        $userRepository =  $container->get(UserRepository::class);
        $owner = $userRepository->findOneByEmail('<EMAIL>');
        $support = $userRepository->findOneByEmail('<EMAIL>');

        // upewniam się że nie ma żadnej subskrypcji
        $history = $this->request($support, "/administration/subscriber/{$owner->getSubscriber()->getId()}/subscriptions")['items'];
        $this->assertCount(0,$history);

        // robie kalklulacje
        $json = $this->request(
            $support,
            "/administration/subscriber/{$owner->getSubscriber()->getId()}/calculate",
            'POST',
            [
                                                        'from' => $from,
                                                        'to' => $to,
                                                        'currency' => $currency,
                                                        'package' => $package,
                                                        'payer' => $payerType,
                                                        'document' => 'none'
                                                    ]
        );
        $this->assertArrayHasKey("discount", $json);

        $this->assertArrayHasKey("item", $json);
        $this->assertArrayHasKey("position", $json["item"][0]);
        $this->assertArrayHasKey("totalPrice", $json["item"][0]);
        $this->assertArrayHasKey("type", $json["item"][0]);

        $this->assertEqualsWithDelta($totalValue, $json["baseValue"], $totalValue/100*5); // zakladam ze moga byc roznice na poziomie 5%

        // upewniam się że nie ma żadnej subskrypcji
        $history = $this->request($support, "/administration/subscriber/{$owner->getSubscriber()->getId()}/subscriptions")['items'];
        $this->assertCount(0,$history);

    }

    /**
     * @dataProvider subscriptionData
     */
    public function testOrderAdmin(string $from, string $to, string $currency, string $package, $totalValue, $discountedValue, string $payerType): void
    {
        $container = static::getContainer();

        /** @var UserRepository $userRepository */
        $userRepository =  $container->get(UserRepository::class);
        $owner = $userRepository->findOneByEmail('<EMAIL>');
        $support = $userRepository->findOneByEmail('<EMAIL>');


        // upewniam się że nie ma żadnej subskrypcji
        $history = $this->request($support, "/administration/subscriber/{$owner->getSubscriber()->getId()}/subscriptions")['items'];
        $this->assertCount(0,$history);

        // dodaje subskrypcje
        $json = $this->request(
            $support,
            "/administration/subscriber/{$owner->getSubscriber()->getId()}/subscriptions",
            'POST',
            [
                'from' => $from,
                'to' => $to,
                'currency' => $currency,
                'package' => $package,
                'payer' => $payerType,
                'comment' => "subskrypcja dodana przez test",
                'document' => 'none'
            ]
        );
        $this->assertArrayHasKey("discount", $json);

        $this->assertArrayHasKey("item", $json);
        $this->assertArrayHasKey("position", $json["item"][0]);
        $this->assertArrayHasKey("totalPrice", $json["item"][0]);
        $this->assertArrayHasKey("type", $json["item"][0]);

        $this->assertEqualsWithDelta($totalValue, $json["baseValue"], $totalValue/100*5); // zakladam ze moga byc roznice na poziomie 5%


        // sprawdzam czy pojawiła się nowa subskrycpja
        $history = $this->request($support, "/administration/subscriber/{$owner->getSubscriber()->getId()}/subscriptions")['items'];
        $this->assertCount(1,$history);
        $this->assertEquals('paid',$history[0]['status']);

        // na potrzeby listy subksrypcji sprawdzamy czy są wymagane pola
        $this->assertArrayHasKey('id',$history[0]);
        $this->assertArrayHasKey('startDate',$history[0]);
        $this->assertArrayHasKey('endDate',$history[0]);
        $this->assertArrayHasKey('ctime',$history[0]);
        $this->assertArrayHasKey('mtime',$history[0]);
        $this->assertArrayHasKey('comment',$history[0]);
        $this->assertArrayHasKey('type',$history[0]);
        $this->assertArrayHasKey('grossValue',$history[0]);
        $this->assertArrayHasKey('currencyCode',$history[0]);

        //$this->assertArrayHasKey('whoAddedEmail',$history[0]);
        //$this->assertArrayHasKey('ownerEmail',$history[0]);


        $this->assertEquals($package, $history[0]['type']);

    }

    private function request(User $user, string $url, string $method = 'GET', $params = []): array
    {
        $this->client->loginUser($user);
        $this->client->request(
            $method,
            $url,
            $params
        );
        self::assertResponseIsSuccessful();
        return json_decode($this->client->getResponse()->getContent(), true);
    }

}

<?php

namespace App\Tests\Loyalty\v3;

use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Bundle\FrameworkBundle\KernelBrowser;
use App\Repository\UserRepository;

class LoyaltyCardsTest extends WebTestCase
{
    private KernelBrowser $client;

    protected function setUp(): void
    {
        $this->client = static::createClient();
        $container = static::getContainer();
        $userRepository = $container->get(UserRepository::class);

        /** @var \App\Entity\User $user */
        $user = $userRepository->findOneByEmail('<EMAIL>');
        $this->client->loginUser($user);
    }

    /**
     * @test
     */
    public function getCardList()
    {
        $this->client->request('GET', '/api/loyalty/v3/cards');

        $response = $this->client->getResponse();
        $this->assertEquals(200, $response->getStatusCode());
        $this->assertJson($response->getContent());

        $data = json_decode($response->getContent(), true);

        foreach ($data['data'] as $card) {
            $cardKeys = [
                'cardToken',
                'number',
                'alias',
                'email',
                'balance',
                'clientName',
                'status',
                'type',
                'currencySymbol',
                'toSend',
                'payments',
                'topUps',
            ];
            foreach ($cardKeys as $key) {
                $this->assertArrayHasKey($key, $card);
            }
        }
    }

    /**
     * @test
     */
    public function testCardsModule()
    {
        $this->markTestSkipped("Not maintened use api/gateway/bkfpay-owner/cards");
        // Step 1: Add a new client
        $clientData = [
            'companyName' => 'Test Client Inc.',
            'email' => null,
            'phone' => null,
            'taxNumber' => null,
            'regon' => null,
            'firstname' => null,
            'lastname' => null,
            'address' => null,
            'postCode' => null,
            'city' => null,
            'country' => null,
            'invoiceStrategy' => null,
            'discount' => null,
            'paymentTerm' => null,
            'paymentMethod' => null,
            'sendSummaryReport' => null,
        ];
        $this->client->request('POST', '/api/loyalty/v3/clients', content: json_encode($clientData));
        $response = $this->client->getResponse();
        $this->assertEquals(200, $response->getStatusCode(), "Client creation failed");


        $createdClient = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('id', $createdClient, "Client ID is missing in the response");
        $this->assertEquals($clientData['companyName'], $createdClient['companyName'], "Client name mismatch");
        $clientId = $createdClient['id'];

        // Step 2: Verify client was added correctly
        $this->client->request('GET', '/api/loyalty/v3/client/' . $clientId);
        $response = $this->client->getResponse();
        $this->assertEquals(200, $response->getStatusCode(), "Client search failed");

        $foundClient = json_decode($response->getContent(), true);
        $this->assertNotEmpty($foundClient, "Client not found");
        $this->assertArrayHasKey('id', $foundClient, "Client ID is missing in the response");
        $this->assertArrayHasKey('companyName', $foundClient, "Client name is missing in the response");
        $this->assertEquals($clientId, $foundClient['id'], "Client ID mismatch");
        $this->assertEquals($foundClient['companyName'], $clientData['companyName'], "Client name mismatch");

        // Step 3: Add a card assigned to the client
        $cardData = [
            'name' => 'Test Virtual Card',
            'number' => null,
            'clientId' => $clientId,
        ];
        $this->client->request('POST', '/api/loyalty/v3/cards', content: json_encode($cardData));
        $response = $this->client->getResponse();
        $this->assertEquals(201, $response->getStatusCode(), "Card creation failed");

        $createdCard = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('cardToken', $createdCard, "Card token is missing in the response");
        $this->assertArrayHasKey('number', $createdCard, "Card number is missing in the response");
        $this->assertArrayHasKey('type', $createdCard, "Card type is missing in the response");
        $this->assertEquals($createdCard['type'], 'VIRTUAL', "Card type mismatch");
        $this->assertEquals($cardData['clientId'], $createdCard['client']['id'], "Card client id mismatch");

        $cardNumber = $createdCard['number'];
        $cardToken = $createdCard['cardToken'];
        $cardBalance = $createdCard['balance'];


        // // Step 4: Verify the card was added
        $this->client->request('GET', '/api/loyalty/v3/card/' . $cardToken);
        $response = $this->client->getResponse();
        $this->assertEquals(200, $response->getStatusCode(), "Card search failed");

        $foundCard = json_decode($response->getContent(), true);
        $this->assertNotEmpty($foundCard, "Card not found");

        $this->assertArrayHasKey('cardToken', $foundCard, "Card token is missing in the response");
        $this->assertArrayHasKey('number', $foundCard, "Card number is missing in the response");
        $this->assertArrayHasKey('type', $foundCard, "Card type is missing in the response");
        $this->assertArrayHasKey('client', $foundCard, "Card client is missing in the response");

        $this->assertArrayHasKey('id', $foundCard['client'], "Card client ID is missing in the response");

        $this->assertEquals($cardToken, $foundCard['cardToken'], "Card token mismatch");
        $this->assertEquals($cardNumber, $foundCard['number'], "Card number mismatch");
        $this->assertEquals('VIRTUAL', $foundCard['type'], "Card type mismatch");
        $this->assertEquals($clientId, $foundCard['client']['id'], "Card client id mismatch");

        // // Step 5: Top up the created card
        $topUpData = ['value' => 50];
        $this->client->request('POST', '/api/loyalty/v3/card/' . $cardToken . '/top_up', content: json_encode($topUpData));
        $response = $this->client->getResponse();
        $this->assertEquals(200, $response->getStatusCode(), "Card top-up failed");

        $topUpResponse = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('balance', $topUpResponse, "Balance is missing in the top-up response");
        $this->assertEquals($cardBalance + $topUpData['value'], $topUpResponse['balance'], "Top-up value mismatch");

        // // Step 6: Verify the card top-up history
        $this->client->request('GET', '/api/loyalty/v3/top_ups', parameters: ['page' => 1, 'perPage' => 25, 'cardToken' => $cardToken]);
        $response = $this->client->getResponse();
        $this->assertEquals(200, $response->getStatusCode(), "Top-up history search failed");

        $topUpHistory = json_decode($response->getContent(), true)['data'];
        $this->assertGreaterThan(0, count($topUpHistory), "No top-up records found");
        $lastTopUp = $topUpHistory[0];
        $this->assertEquals($topUpData['value'], $lastTopUp['topUpValue'], "Top-up history value mismatch");

        // // Step 7: Delete the card
        $this->client->request('DELETE', '/api/loyalty/v3/card/' . $cardToken);
        $response = $this->client->getResponse();
        $this->assertEquals(200, $response->getStatusCode(), "Card deletion failed");

        // // Step 8: Verify the card is deleted
        $this->client->request('GET', '/api/loyalty/v3/cards', parameters: ['search' => $cardNumber]);
        $response = $this->client->getResponse();
        $this->assertEquals(200, $response->getStatusCode(), "Card search after deletion failed");

        $cardsAfterDeletion = json_decode($response->getContent(), true)['data'];
        $this->assertEmpty($cardsAfterDeletion, "Deleted card still exists in the system");
    }
}

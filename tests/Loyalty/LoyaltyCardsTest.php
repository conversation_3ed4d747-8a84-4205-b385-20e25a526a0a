<?php

namespace App\Tests\Loyalty;

use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Bundle\FrameworkBundle\KernelBrowser;
use App\Repository\UserRepository;

class LoyaltyCardsTest extends WebTestCase
{
    private KernelBrowser $client;
    private string $email = '<EMAIL>';


    protected function setUp(): void
    {
        $this->client = static::createClient();
        $container = static::getContainer();
        $userRepository = $container->get(UserRepository::class);

        /** @var \App\Entity\User $user */
        $user = $userRepository->findOneByEmail($this->email);
        $this->client->loginUser($user);
    }

    /**
     * @test
     */
    public function getCardList()
    {
        $this->client->request('GET', '/api/loyalty/v3/cards');

        $response = $this->client->getResponse();
        $this->assertEquals(200, $response->getStatusCode());
        $this->assertJson($response->getContent());

        $data = json_decode($response->getContent(), true);

        foreach ($data['data'] as $card) {
            $keys = [
                'cardToken',
                'number',
                'alias',
                'balance',
                'clientId',
                'clientName',
                'status',
                'type',
                'currencySymbol',
                'toSend',
                'topUps',
                'payments',
            ];
            foreach ($keys as $key) {
                $this->assertArrayHasKey($key, $card);
            }
        }
    }

    /**
     *
     * @
     */
    public function virtualCardOperationsTest()
    {
        $this->markTestSkipped("na czas przepinki na v3");
        $testCardData = [
            'name' => 'test karty wirtualnej',
            'email' => $this->email,
            'clientId' => null,
            'value' => 5,
        ];

        // dodajemy nową kartę wirtualną
        $this->client->request(
            'POST',
            '/api/loyalty/v3/cards',
            [],
            [],
            [],
            json_encode($testCardData),
        );

        $response = $this->client->getResponse();
        $this->assertEquals(201, $response->getStatusCode());
        $this->assertJson($response->getContent());


        // sprawdzamy, czy karta zawiera poprawne dane
        $card = json_decode($response->getContent(), true);
        $this->assertEquals($testCardData['name'], $card['alias']);
        $this->assertEquals($testCardData['virtualCard'], $card['virtual']);
        $this->assertEquals($testCardData['value'], $card['balance']);
        $this->assertEquals($testCardData['enabled'], $card['enabled']);


        $cardToken = $card['cardToken'];
        $topupValue = 10;

        // doładowujemy kartę
        $this->client->request(
            'POST',
            "/api/loyalty/v3/card/$cardToken/top_up",
            [],
            [],
            [],
            json_encode([
                'value' => $topupValue,
                'bonus' => 0,
                'invoice' => false,
                'paymentMethod' => null,
                'paymentTerm' => null,
                'source' => "INTERNET",
                'type' => "INTERNET",
            ]),
        );

        $response = $this->client->getResponse();
        $this->assertEquals(200, $response->getStatusCode());
        $this->assertJson($response->getContent());


        // sprawdzamy, czy karta została doładowana
        $card = json_decode($response->getContent(), true);
        $this->assertEquals($testCardData['value'] + $topupValue, $card['balance']);

        // edytujemy kartę
        // $editData = [
        //     'alias' => 'Testowa karta zedytowana',
        //     'clientId' => 0,
        //     'email' => '<EMAIL>',
        //     'enabled' => false,
        // ];
        // $this->client->request(
        //     'PATCH',
        //     "/api/loyalty/card/$cardToken",
        //     [],
        //     [],
        //     [],
        //     json_encode($editData),
        // );

        // $editResponse = $this->client->getResponse();
        // $this->assertEquals(200, $editResponse->getStatusCode());
        // $this->assertJson($editResponse->getContent());

        // print_r($editResponse);
        
        // // sprawdzamy, czy karta została zedytowana
        // $editedCard = json_decode($editResponse->getContent(), true);
        // $this->assertEquals($editData['alias'], $editedCard['alias']);
        // $this->assertEquals($editData['email'], $editedCard['email']);
        // $this->assertEquals($editData['enabled'], $editedCard['enabled']);
        

        
        // usuwamy kartę
        $this->client->request('DELETE', "/api/loyalty/v3/card/$cardToken");
        $response = $this->client->getResponse();
        $this->assertEquals(200, $response->getStatusCode());
    }
}
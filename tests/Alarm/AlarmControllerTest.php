<?php

namespace App\Tests\Alarm;

use App\Entity\User;
use App\Repository\UserRepository;
use Symfony\Bundle\FrameworkBundle\KernelBrowser;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;

class AlarmControllerTest extends WebTestCase
{
    private  KernelBrowser $client;

    protected function setUp(): void
    {
        $this->client = static::createClient();
    }


    public function testAlarmHistory()
    {
        $email = '<EMAIL>';

        $container = static::getContainer();
        $userRepository = $container->get(UserRepository::class);
        /** @var User $owner */
        $user = $userRepository->findOneByEmail($email);

        $data = $this->request($user, "/cm/alarm/by_serials/150");

        //print_r($data);

    }

    private function request(User $user, string $url, string $method = 'GET', $params = []): array
    {
        //print_r($url);
        $this->client->loginUser($user);
        $this->client->request(
            $method,
            $url,
            $params
        );
        self::assertResponseIsSuccessful();
        return json_decode($this->client->getResponse()->getContent(), true);
    }


}

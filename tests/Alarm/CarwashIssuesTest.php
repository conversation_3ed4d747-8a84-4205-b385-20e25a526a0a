<?php

namespace App\Tests\Alarm;

use App\Entity\User;
use App\Repository\UserRepository;
use Symfony\Bundle\FrameworkBundle\KernelBrowser;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;

class CarwashIssuesTest extends WebTestCase
{


    private  KernelBrowser $client;

    protected function setUp(): void
    {
        $this->client = static::createClient();
    }


    public function testCarwashIssueController()
    {
        self::markTestSkipped("gdy brak myjni testowych z issue wtedy się wywala");
        $email = '<EMAIL>';

        $container = static::getContainer();
        $userRepository = $container->get(UserRepository::class);

        /** @var User $owner */
        $user = $userRepository->findOneByEmail($email);

        $issues = $this->request($user, '/administration/carwashissues');

        foreach ($issues['data'] as $item) {
            //print_r($item);
            $issue = $this->request($user, "/administration/carwashissue/{$item['issues'][0]['id']}");
            $this->assertArrayHasKey('status',$item['issues'][0]);
            break;
      }


    }


    private function request(User $user, string $url, string $method = 'GET', $params = []): array
    {
        $this->client->loginUser($user);
        $this->client->request(
            $method,
            $url,
            $params
        );
        self::assertResponseIsSuccessful();
        return json_decode($this->client->getResponse()->getContent(), true);
    }

}

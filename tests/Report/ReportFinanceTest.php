<?php

namespace App\Tests\Report;

use App\Entity\User;
use App\Repository\UserRepository;
use Symfony\Bundle\FrameworkBundle\KernelBrowser;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;

class ReportFinanceTest extends WebTestCase
{
    public function smokeTestData()
    {
        return [

            // obrót
            [
                'email' => '<EMAIL>',
                'uri' => '/api/reports/data?report=v2\\FinanceTurnoverTotal&startDate=2024-04-04&endDate=2028-04-10'
            ],
            [
                'email' => '<EMAIL>',
                'uri' => '/api/reports/data?report=v2\\FinanceTurnoverDetailed&startDate=2024-04-04&endDate=2028-04-10&type=monthly'
            ],

            // transakcje fiskalne
            [
                'email' => '<EMAIL>',
                'uri' => '/api/reports/data?report=v2\\FinanceFiscalTransactions&startDate=2024-04-04&endDate=2028-04-10'
            ],
            [
                'email' => '<EMAIL>',
                'uri' => '/api/reports/data?report=v2\\FinanceFiscalSummary&startDate=2024-04-04&endDate=2028-04-10'
            ],
            // mobile
            [
                'email' => '<EMAIL>',
                'uri' => '/api/reports/data?report=v2\\FinanceMobileTransactions&startDate=2024-04-04&endDate=2028-04-10'
            ],

            // inkasacja
            [
                'email' => '<EMAIL>',
                'uri' => '/api/reports/data?report=v2\\FinanceMoneyCollects&page=1&perPage=25'
            ],

        ];
    }

    private  KernelBrowser $client;

    protected function setUp(): void
    {
        $this->client = static::createClient();
    }

    /**
     * @dataProvider smokeTestData
     * @test
     */
    public function testSmokeTest($email,$url )
    {

        $container = static::getContainer();
        $userRepository =  $container->get(UserRepository::class);

        $user = $userRepository->findOneByEmail($email);
        $this->client->loginUser($user);

        // Send a GET request to /list endpoint

        $this->client->request('GET', $url);

        // Assert response status code is 200
        $this->assertEquals(200, $this->client->getResponse()->getStatusCode());

        // Assert response contains JSON data
        $this->assertJson($this->client->getResponse()->getContent());

        //print_r($this->client->getResponse()->getContent());
        // Assert JSON response contains at least one user
        $responseData = json_decode($this->client->getResponse()->getContent(), true);




        //print_r($this->client->getResponse()->getContent());

    }
}

<?php

namespace App\Tests\Administration;

use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Bundle\FrameworkBundle\KernelBrowser;
use App\Repository\UserRepository;

class AdminSmokeTest extends WebTestCase
{
    public function smokeTestData()
    {
        return [

            # użytkownicy
            [
                'email' => '<EMAIL>',
                'uri' => '/administration/users?search=test&isOwner=false&page=1&perPage=10'
            ],

            # myjnie
            [
                'email' => '<EMAIL>',
                'uri' => '/administration/carwashes'
            ],
        ];
    }

    private  KernelBrowser $client;

    protected function setUp(): void
    {
        $this->client = static::createClient();
    }

    /**
     * @dataProvider smokeTestData
     * @test
     */
    public function testSmokeTest($email,$url )
    {

        $container = static::getContainer();
        $userRepository =  $container->get(UserRepository::class);

        /** @var User $owner */

        $user = $userRepository->findOneByEmail($email);
        $this->client->loginUser($user);

        // Send a GET request to /list endpoint

        $this->client->request('GET', $url);

        // Assert response status code is 200
        $this->assertEquals(200, $this->client->getResponse()->getStatusCode());

        // Assert response contains JSON data
        $this->assertJson($this->client->getResponse()->getContent());

        // Assert JSON response contains at least one user
        $responseData = json_decode($this->client->getResponse()->getContent(), true);
        $this->assertGreaterThan(0, count($responseData));

        //print_r($this->client->getResponse()->getContent());

    }
}

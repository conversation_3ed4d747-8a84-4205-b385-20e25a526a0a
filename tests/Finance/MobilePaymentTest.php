<?php

namespace App\Tests\Finance;

use App\Entity\User;
use App\Repository\UserRepository;
use Symfony\Bundle\FrameworkBundle\KernelBrowser;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;

class MobilePaymentTest extends WebTestCase
{
    public function smokeTestData()
    {
        return [

            # lista aplikacji
            [
                'email' => '<EMAIL>',
                'value' => 0.1,
                'standCode' => '00150019'
            ],
        ];
    }

    private  KernelBrowser $client;

    protected function setUp(): void
    {
        $this->client = static::createClient();
    }

    /**
     * @dataProvider smokeTestData
     * @test
     */
    public function testSmokeTest($email,$value, $standCode )
    {

        $container = static::getContainer();
        $userRepository =  $container->get(UserRepository::class);


        /** @var User $user */
        $user = $userRepository->findOneByEmail($email);
        $this->client->loginUser($user);

        // Send a GET request to /list endpoint

        $this->client->request(method: 'POST', uri: '/api/mobile_topup', content: json_encode(['value' => $value, 'standCode' => $standCode]) );

        // Assert response status code is 200
        $this->assertEquals(200, $this->client->getResponse()->getStatusCode());

        // Assert response contains JSON data
        $this->assertJson($this->client->getResponse()->getContent());

        // Assert JSON response contains at least one user
        $responseData = json_decode($this->client->getResponse()->getContent(), true);
        $this->assertGreaterThan(0, count($responseData));

        //print_r($responseData);

    }

    /**
     * @test
     * [{
     * "carwashSn": 46437,
     * "standCode": "46437105",
     * "standType": "MONEY_CHANGER",
     * "bayId": 1,
     * "mobileCode": "46437105", <- depracated
     * "mobileAvailable": false
     * },]
     */
    public function testStandList( )
    {

        $container = static::getContainer();
        $userRepository =  $container->get(UserRepository::class);


        /** @var User $user */
        $user = $userRepository->findOneByEmail('<EMAIL>');
        $this->client->loginUser($user);

        // Send a GET request to /list endpoint

        $this->client->request(method: 'GET', uri: '/api/carwash_stands' );

        // Assert response status code is 200
        $this->assertEquals(200, $this->client->getResponse()->getStatusCode());

        // Assert response contains JSON data
        $this->assertJson($this->client->getResponse()->getContent());

        // Assert JSON response contains at least one user
        $responseData = json_decode($this->client->getResponse()->getContent(), true);
        $this->assertGreaterThan(0, count($responseData));
        $data = $responseData[0];

        $this->assertArrayHasKey('carwashSn', $data);
        $this->assertArrayHasKey('standCode', $data);
        $this->assertArrayHasKey('standType', $data);
        $this->assertArrayHasKey('bayId', $data);
        $this->assertArrayHasKey('mobileAvailable', $data);


    }
}

<?php

namespace App\Tests\Finance;

use App\Repository\UserRepository;
use Symfony\Bundle\FrameworkBundle\KernelBrowser;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;

class MoneyCollectTest extends WebTestCase
{
    public function smokeTestData()
    {
        return [

            # lista aplikacji
            [
                'email' => '<EMAIL>',
                'uri' => '/cm_new/moneycollect/?sn=150&limit=5&sources=CAR_WASH%2CMONEY_CHANGER%2CYETI',
            ],
            [
                'email' => '<EMAIL>',
                'uri' => '/cm_new/moneycollect/?sn=150,144&limit=5&sources=CAR_WASH%2CMONEY_CHANGER%2CYETI',
            ],
            [
                'email' => '<EMAIL>',
                'uri' => '/cm_new/moneycollect/?limit=5&sources=CAR_WASH%2CMONEY_CHANGER%2CYETI',
            ],
        ];
    }

    private  KernelBrowser $client;

    protected function setUp(): void
    {
        $this->client = static::createClient();
    }

    /**
     * @dataProvider smokeTestData
     * @test
     */
    public function testSmokeTest($email,$url )
    {

        $container = static::getContainer();
        $userRepository =  $container->get(UserRepository::class);

        /** @var User $owner */

        $user = $userRepository->findOneByEmail($email);
        $this->client->loginUser($user);

        // Send a GET request to /list endpoint

        $this->client->request('GET', $url);

        // Assert response status code is 200
        $this->assertEquals(200, $this->client->getResponse()->getStatusCode());

        // Assert response contains JSON data
        $this->assertJson($this->client->getResponse()->getContent());

        // Assert JSON response contains at least one user
        $responseData = json_decode($this->client->getResponse()->getContent(), true);
        $this->assertGreaterThan(0, count($responseData));

        //print_r($responseData);

    }
}

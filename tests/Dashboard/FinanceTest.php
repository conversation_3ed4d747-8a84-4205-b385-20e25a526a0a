<?php

namespace App\Tests\Dashboard;

use App\Entity\User;
use App\Repository\UserRepository;
use Symfony\Bundle\FrameworkBundle\KernelBrowser;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;

class FinanceTest extends WebTestCase
{
    private  KernelBrowser $client;


    private function request(User $user, string $url, string $method = 'GET'): array
    {
        $this->client->loginUser($user);
        $this->client->request(
            $method,
            $url
        );
        self::assertResponseIsSuccessful();
        return json_decode($this->client->getResponse()->getContent(), true);
    }

    protected function setUp(): void
    {
        $this->client = static::createClient();
    }

    public function dataToFinanceWidget()
    {
        return [

            # użytkownicy
            [
                'email' => '<EMAIL>',
                'uri' => '/cm/dashboard-widgets/carwashes/turnover?period=now'
            ],
            [
                'email' => '<EMAIL>',
                'uri' => '/cm/dashboard-widgets/carwashes/turnover?period=yesterday'
            ],
        ];
    }

    /**
     * @dataProvider dataToFinanceWidget
     */
    public function testFinanceWidget($email, $url): void
    {
        $container = static::getContainer();
        $userRepository =  $container->get(UserRepository::class);

        /** @var User $owner */

        $user = $userRepository->findOneByEmail($email);

        $data = $this->request($user, $url);

        // dane
        $this->assertArrayHasKey('data', $data);
        $this->assertArrayHasKey(0, $data['data']);
        $this->assertArrayHasKey('name', $data['data'][0]);
        $this->assertArrayHasKey('turnover', $data['data'][0]);

        // sumy
        $this->assertArrayHasKey('sums', $data);
        $this->assertArrayHasKey('all', $data['sums']);


    }

    public function dataToCarwashAndTurnoverWidget()
    {
        return [

            # użytkownicy
            [
                'email' => '<EMAIL>',
                'uri' => '/cm/dashboard-widgets/carwashes?period=now'
            ],
            [
                'email' => '<EMAIL>',
                'uri' => '/cm/dashboard-widgets/carwashes?period=yesterday'
            ],
        ];
    }

    /**
     * @dataProvider dataToCarwashAndTurnoverWidget
     */
    public function testCarwashAndTurnoverFinanceWidget($email, $url): void
    {
        $container = static::getContainer();
        $userRepository =  $container->get(UserRepository::class);

        /** @var User $owner */

        $user = $userRepository->findOneByEmail($email);

        $data = $this->request($user, $url);


        $this->assertArrayHasKey('ownerCountry', $data);
        $this->assertArrayHasKey('turnoverSum', $data);
        $this->assertArrayHasKey('data', $data);

        // Sprawdź elementy wewnętrzne.
        //$this->assertArrayHasKey(0, $data['data']);

        foreach ($data['data'] as $subData)
        {
            $this->assertArrayHasKey('serialNumber', $subData);
            $this->assertArrayHasKey('name', $subData);
            $this->assertArrayHasKey('address', $subData);
            $this->assertArrayHasKey('alarmsInfo', $subData);
            $this->assertArrayHasKey('alarmsStatus', $subData);
            $this->assertArrayHasKey('alarmsHighestLevel', $subData);
            $this->assertArrayHasKey('ip', $subData);
            $this->assertArrayHasKey('software', $subData);
            $this->assertArrayHasKey('lastContact', $subData);
            $this->assertArrayHasKey('turnover', $subData);
        }
    }

    public function dataToCarwashListWidget()
    {
        return [

            # użytkownicy
            [
                'email' => '<EMAIL>',
                'uri' => '/cm/dashboard-widgets/carwashes'
            ],
        ];
    }

    /**
     * @dataProvider dataToCarwashListWidget
     */
    public function testCarwashListWidget($email, $url): void
    {
        $container = static::getContainer();
        $userRepository =  $container->get(UserRepository::class);

        /** @var User $owner */

        $user = $userRepository->findOneByEmail($email);

        $data = $this->request($user, $url)['data'];

        $this->assertGreaterThan(0, count($data));
        foreach ($data as $carwash)
        {
            $this->assertArrayHasKey('name', $carwash);
            $this->assertArrayHasKey('serialNumber', $carwash);
        }

    }

    public function dataToFiscalWidget()
    {
        return [

            [
                'email' => '<EMAIL>',
                'uri' => '/cm/dashboard-widgets/fiscal?period=yesterday'
            ],
        ];
    }

    /**
     * @dataProvider dataToFiscalWidget
     */
    public function testFiscalWidget($email, $url): void
    {
        $container = static::getContainer();
        $userRepository =  $container->get(UserRepository::class);

        /** @var User $owner */

        $user = $userRepository->findOneByEmail($email);

        $data = $this->request($user, $url);

            $this->assertArrayHasKey('sum', $data);

    }
}

<?php

namespace App\Tests\Traits;

use App\Entity\User;

trait MocksUser
{
    private User $user;
    private \Faker\Generator $faker;

    protected function setUpUsers(): void
    {
        $this->faker = \Faker\Factory::create();

        // Set up error report issuer
        $this->user = new User();
        $this->setFakeUserData($this->user);

        // Set up the owner
        $owner = new User();
        $this->user->setOwner($owner);

        // Access owner through user entity to check if they are linked correctly
        /** @var User $ownerEntity */
        $ownerEntity = $this->user->getOwner();

        $this->setFakeUserData($ownerEntity);
    }

    protected function setFakeUserData(User $user): void
    {
        $user->setId($this->faker->randomNumber());
        $user->setEmail($this->faker->email());
    }

}

<?php

namespace App\Tests\LoyaltyApp;

use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Bundle\FrameworkBundle\KernelBrowser;
use App\Repository\UserRepository;

class LoyaltyAppSmokeTest extends WebTestCase
{
    public function smokeTestData()
    {
        return [

            # lista aplikacji
            [
                'email' => '<EMAIL>',
                'uri' => '/api/loyalapp/apps'
            ],
            [
                'email' => '<EMAIL>',
                'uri' => '/api/loyalapp/apps'
            ],
            # użytkownicy
            [
                'email' => '<EMAIL>',
                'uri' => '/api/loyalapp/transactions?pageNumber=1&orderBy=createdAt&orderDescending=1&itemsPerPage=25&userId=7458&status=TOP_UP,PAYMENT&dateFrom=2023-01-01+00:00:00&dateTo=2023-05-10+23:59:59&app=BE_LOYAL'
            ],
            [
                'email' => '<EMAIL>',
                'uri' => '/api/loyalapp/user/9551?app=BE_LOYAL'
            ],
            [
                'email' => '<EMAIL>',
                'uri' => '/api/loyalapp/users?page=2&orderBy=email&orderDescending=0&itemsPerPage=15&dateFrom=2023-05-04+00:00:00&dateTo=2023-05-10+23:59:59&app=BE_LOYAL'
            ],

            # kody promocyjne
            [
                'email' => '<EMAIL>',
                'url' => '/api/loyalapp/promotionalcodes?page=1&orderBy=ctime&orderDescending=1&itemsPerPage=25&fullTextSearch=&dateFrom=2023-05-03&dateTo=2023-05-10&app=BE_LOYAL'
            ],
            [
                'email' => '<EMAIL>',
                'url' => '/api/loyalapp/promotionalcodes?page=1&orderBy=ctime&orderDescending=1&itemsPerPage=25&fullTextSearch=&dateFrom=2023-05-03&dateTo=2023-05-10&app=BE_LOYAL'
            ],

            # lista faktur
//            [
//                'email' => '<EMAIL>',
//                'url' => '/api/loyalapp/invoices?dateFrom=2023-05-03&dateTo=2023-05-10'
//            ],

            # pakiety doladowan
            [
                'email' => '<EMAIL>',
                'url' => '/api/loyalapp/promotionalpackages?search=&app=BE_LOYAL'
            ],

        ];
    }

    private  KernelBrowser $client;

    protected function setUp(): void
    {
        $this->client = static::createClient();
    }

    /**
     * @dataProvider smokeTestData
     * @test
     */
    public function testSmokeTest($email,$url )
    {

        $container = static::getContainer();
        $userRepository =  $container->get(UserRepository::class);

        /** @var User $owner */

        $user = $userRepository->findOneByEmail($email);
        $this->client->loginUser($user);

        // Send a GET request to /list endpoint

        $this->client->request('GET', $url);

        // Assert response status code is 200
        $this->assertEquals(200, $this->client->getResponse()->getStatusCode());

        // Assert response contains JSON data
        $this->assertJson($this->client->getResponse()->getContent());

        // Assert JSON response contains at least one user
        $responseData = json_decode($this->client->getResponse()->getContent(), true);
        $this->assertGreaterThan(0, count($responseData));

        //print_r($responseData);

    }
}

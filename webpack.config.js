const Encore = require('@symfony/webpack-encore');
const path = require("path");

Encore
    .cleanupOutputBeforeBuild()
    .setOutputPath('public/compiled/pdf-reports/')
    .addAliases({
        '/assets': path.resolve(__dirname, './public/assets')
    })
    .setPublicPath('/compiled/pdf-reports')
    .enableSassLoader()
    .addStyleEntry('cm-main-css-pdf',
        [
            './public/assets/i2m/scss/style_cm.scss',
        ])
    .enableSingleRuntimeChunk()
    .enableSourceMaps(false)
    .enableVersioning(false) // turn off - encore can't generate absoute urls to assets
    .autoProvidejQuery()
    .autoProvideVariables({
        $: 'jquery',
        jQuery: 'jquery',
        'window.jQuery': 'jquery',
    });

module.exports = Encore.getWebpackConfig();

<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240122091630 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE subscribers_cm ADD is_dealer BOOLEAN DEFAULT NULL');
        $this->addSql('ALTER TABLE users DROP CONSTRAINT FK_1483A5E9249E6EA1');
        $this->addSql('ALTER TABLE users ADD CONSTRAINT FK_1483A5E9249E6EA1 FOREIGN KEY (dealer_id) REFERENCES subscribers_cm (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_1483A5E97808B1AD ON users (subscriber_id)');
    }

    public function down(Schema $schema): void
    {

    }
}

<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230512095750 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs

        $this->addSql('DROP INDEX uniq_bc370dc8ad288b01');
        $this->addSql('CREATE INDEX IDX_BC370DC8AD288B01 ON owner_subscriptions_payments (vat_tax_id)');
    }

    public function down(Schema $schema): void
    {

    }
}

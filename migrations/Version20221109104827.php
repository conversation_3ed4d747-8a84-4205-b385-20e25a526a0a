<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20221109104827 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'delete invoice_province and invoice_numerator';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('DROP SEQUENCE invoice_numerator_id_seq CASCADE');
        $this->addSql('ALTER TABLE invoice_numerator DROP CONSTRAINT fk_4ef11701164cd246');
        $this->addSql('DROP TABLE invoice_numerator');
        $this->addSql('ALTER TABLE clients DROP invoice_province');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('CREATE SEQUENCE invoice_numerator_id_seq INCREMENT BY 1 MINVALUE 1 START 1');
        $this->addSql('CREATE TABLE invoice_numerator (id INT NOT NULL, invoice_issuer INT DEFAULT NULL, translatedformat VARCHAR(64) NOT NULL, currentvalue INT DEFAULT 0 NOT NULL, current_number VARCHAR(64) NOT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX idx_4ef11701164cd246 ON invoice_numerator (invoice_issuer)');
        $this->addSql('ALTER TABLE invoice_numerator ADD CONSTRAINT fk_4ef11701164cd246 FOREIGN KEY (invoice_issuer) REFERENCES invoice_issuer (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE clients ADD invoice_province VARCHAR(128) DEFAULT NULL');
    }
}

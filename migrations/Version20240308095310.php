<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240308095310 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE SEQUENCE payment_gate_id_seq INCREMENT BY 1 MINVALUE 1 START 1');
        $this->addSql('CREATE TABLE payment_gate (id INT NOT NULL, dealer_id INT DEFAULT NULL, currency_id INT NOT NULL, type VARCHAR(255) NOT NULL, config JSON NOT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_99A80FE0249E6EA1 ON payment_gate (dealer_id)');
        $this->addSql('CREATE INDEX IDX_99A80FE038248176 ON payment_gate (currency_id)');
        $this->addSql('ALTER TABLE payment_gate ADD CONSTRAINT FK_99A80FE0249E6EA1 FOREIGN KEY (dealer_id) REFERENCES subscribers_cm (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE payment_gate ADD CONSTRAINT FK_99A80FE038248176 FOREIGN KEY (currency_id) REFERENCES currency (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
    }

    public function down(Schema $schema): void
    {

    }
}

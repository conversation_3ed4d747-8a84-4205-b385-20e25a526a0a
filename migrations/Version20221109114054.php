<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20221109114054 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'delete subscriber_id form client';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE clients DROP CONSTRAINT fk_c82e747808b1ad');
        $this->addSql('DROP INDEX uniq_c82e747808b1ad');
        $this->addSql('ALTER TABLE clients DROP subscriber_id');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE clients ADD subscriber_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE clients ADD CONSTRAINT fk_c82e747808b1ad FOREIGN KEY (subscriber_id) REFERENCES subscribers_cm (id) ON DELETE CASCADE NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('CREATE UNIQUE INDEX uniq_c82e747808b1ad ON clients (subscriber_id)');
    }
}

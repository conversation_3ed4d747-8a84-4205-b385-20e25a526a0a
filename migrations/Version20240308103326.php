<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240308103326 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('DROP SEQUENCE clients_id_seq CASCADE');
        $this->addSql('DROP SEQUENCE cmusers_id_seq CASCADE');
        $this->addSql('ALTER TABLE clients DROP CONSTRAINT fk_c82e742f6ae345');
        $this->addSql('ALTER TABLE clients DROP CONSTRAINT fk_c82e747e3c61f9');
        $this->addSql('ALTER TABLE clients DROP CONSTRAINT fk_c82e74f34eee');
        $this->addSql('DROP TABLE clients');
        $this->addSql('ALTER TABLE cm_carwashes DROP CONSTRAINT fk_a9a3eb21249e6ea1');
        $this->addSql('ALTER TABLE cm_carwashes DROP CONSTRAINT fk_a9a3eb217e3c61f9');
        $this->addSql('DROP INDEX idx_a9a3eb217e3c61f9');
        $this->addSql('DROP INDEX idx_a9a3eb21249e6ea1');
        $this->addSql('ALTER TABLE cm_carwashes DROP owner_id');
        $this->addSql('ALTER TABLE cm_carwashes DROP dealer_id');
        $this->addSql('ALTER TABLE external_payment ALTER redirect_url DROP DEFAULT');
        $this->addSql('COMMENT ON COLUMN logs.status IS NULL');
        $this->addSql('COMMENT ON COLUMN logs.source IS NULL');
        $this->addSql('COMMENT ON COLUMN logs.message IS NULL');
        $this->addSql('ALTER TABLE owner_subscriptions_payments DROP CONSTRAINT fk_bc370dc8a76ed395');
        $this->addSql('DROP INDEX idx_bc370dc8a76ed395');
        $this->addSql('ALTER TABLE owner_subscriptions_payments DROP user_id');
        $this->addSql('ALTER TABLE owner_subscriptions_payments DROP carwashes');
        $this->addSql('ALTER TABLE users DROP CONSTRAINT fk_1483a5e938248176');
        $this->addSql('DROP INDEX idx_1483a5e938248176');
        $this->addSql('DROP INDEX idx_1483a5e9249e6ea1');
        $this->addSql('ALTER TABLE users DROP currency_id');
        $this->addSql('ALTER TABLE users DROP owner_bkf');
        $this->addSql('ALTER TABLE users DROP subscription_discount');
        $this->addSql('ALTER TABLE users DROP dealer_id');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_1483A5E9DE329E72 ON users (invoice_issuer_id)');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE SCHEMA public');
        $this->addSql('CREATE SEQUENCE clients_id_seq INCREMENT BY 1 MINVALUE 1 START 1');
        $this->addSql('CREATE SEQUENCE cmusers_id_seq INCREMENT BY 1 MINVALUE 1 START 1');
        $this->addSql('CREATE TABLE clients (id INT NOT NULL, invoice_country_id INT DEFAULT NULL, invoice_currency_id INT DEFAULT NULL, owner_id INT NOT NULL, status VARCHAR(1) DEFAULT NULL, mtime TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, ctime TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, email VARCHAR(128) DEFAULT NULL, phone VARCHAR(64) DEFAULT NULL, nip VARCHAR(24) DEFAULT NULL, firstname VARCHAR(24) DEFAULT NULL, lastname VARCHAR(64) DEFAULT NULL, invoice_name VARCHAR(128) DEFAULT NULL, invoice_address VARCHAR(255) DEFAULT NULL, invoice_post_code VARCHAR(12) DEFAULT NULL, invoice_city VARCHAR(128) DEFAULT NULL, invoicestrategy VARCHAR(255) DEFAULT \'block\', discount INT DEFAULT NULL, regon VARCHAR(24) DEFAULT NULL, payment_term VARCHAR(6) DEFAULT NULL, payment_method VARCHAR(12) DEFAULT NULL, send_summary_report BOOLEAN DEFAULT false, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX idx_c82e747e3c61f9 ON clients (owner_id)');
        $this->addSql('CREATE INDEX idx_c82e742f6ae345 ON clients (invoice_currency_id)');
        $this->addSql('CREATE INDEX fki_clients_invoice_country ON clients (invoice_country_id)');
        $this->addSql('ALTER TABLE clients ADD CONSTRAINT fk_c82e742f6ae345 FOREIGN KEY (invoice_currency_id) REFERENCES currency (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE clients ADD CONSTRAINT fk_c82e747e3c61f9 FOREIGN KEY (owner_id) REFERENCES users (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE clients ADD CONSTRAINT fk_c82e74f34eee FOREIGN KEY (invoice_country_id) REFERENCES country (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE owner_subscriptions_payments ADD user_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE owner_subscriptions_payments ADD carwashes TEXT DEFAULT NULL');
        $this->addSql('COMMENT ON COLUMN owner_subscriptions_payments.carwashes IS \'(DC2Type:array)\'');
        $this->addSql('ALTER TABLE owner_subscriptions_payments ADD CONSTRAINT fk_bc370dc8a76ed395 FOREIGN KEY (user_id) REFERENCES users (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('CREATE INDEX idx_bc370dc8a76ed395 ON owner_subscriptions_payments (user_id)');
        $this->addSql('ALTER TABLE cm_carwashes ADD owner_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE cm_carwashes ADD dealer_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE cm_carwashes ADD CONSTRAINT fk_a9a3eb21249e6ea1 FOREIGN KEY (dealer_id) REFERENCES users (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE cm_carwashes ADD CONSTRAINT fk_a9a3eb217e3c61f9 FOREIGN KEY (owner_id) REFERENCES users (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('CREATE INDEX idx_a9a3eb217e3c61f9 ON cm_carwashes (owner_id)');
        $this->addSql('CREATE INDEX idx_a9a3eb21249e6ea1 ON cm_carwashes (dealer_id)');
        $this->addSql('COMMENT ON COLUMN logs.status IS \'status\'');
        $this->addSql('COMMENT ON COLUMN logs.source IS \'zrodlo logu\'');
        $this->addSql('COMMENT ON COLUMN logs.message IS \'wiadomosc\'');
        $this->addSql('DROP INDEX UNIQ_1483A5E9DE329E72');
        $this->addSql('ALTER TABLE "users" ADD currency_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE "users" ADD owner_bkf INT DEFAULT NULL');
        $this->addSql('ALTER TABLE "users" ADD subscription_discount INT DEFAULT NULL');
        $this->addSql('ALTER TABLE "users" ADD dealer_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE "users" ADD CONSTRAINT fk_1483a5e938248176 FOREIGN KEY (currency_id) REFERENCES currency (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('CREATE INDEX idx_1483a5e938248176 ON "users" (currency_id)');
        $this->addSql('CREATE INDEX idx_1483a5e9249e6ea1 ON "users" (dealer_id)');
        $this->addSql('CREATE INDEX IDX_1483A5E9DE329E72 ON "users" (invoice_issuer_id)');
        $this->addSql('ALTER TABLE external_payment ALTER redirect_url SET DEFAULT \'NULL::character varying\'');
    }
}

<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240507124754 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE cm_users_reports ADD loyalty_report VARCHAR(255) DEFAULT NULL');
        $this->addSql('ALTER TABLE currency DROP rate');
        $this->addSql('ALTER TABLE invoice_issuer DROP CONSTRAINT fk_164cd2467e5b41ea');
        $this->addSql('DROP INDEX idx_164cd2467e5b41ea');
        $this->addSql('ALTER TABLE invoice_issuer DROP loyalty_card_price_currency_id');
        $this->addSql('ALTER TABLE invoice_issuer DROP automatic_invoice_new_loyalty_card');
        $this->addSql('ALTER TABLE invoice_issuer DROP loyalty_card_price');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
    }
}

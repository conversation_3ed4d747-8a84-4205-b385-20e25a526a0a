<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230504085104 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs        
        $this->addSql('ALTER TABLE external_payment ADD redirect_url VARCHAR(256) DEFAULT NULL');
        $this->addSql('ALTER TABLE owner_subscriptions_payments ADD discount INT DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE external_payment DROP redirect_url');        
        $this->addSql('ALTER TABLE owner_subscriptions_payments DROP discount');
    }
}

<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20220927123409 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE users ADD subscriber_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE users ADD subscription_discount INT DEFAULT NULL');
        $this->addSql('ALTER TABLE users ADD CONSTRAINT FK_1483A5E97808B1AD FOREIGN KEY (subscriber_id) REFERENCES subscribers_cm (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE users ADD invoice_issuer_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE users ADD is_owner BOOLEAN DEFAULT NULL');
        $this->addSql('ALTER TABLE users ALTER password DROP NOT NULL');
    }

    public function down(Schema $schema): void
    {
    }
}

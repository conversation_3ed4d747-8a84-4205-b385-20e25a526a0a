<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240219064601 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE invoice_issuer ADD subscriber_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE invoice_issuer ADD CONSTRAINT FK_164CD2467808B1AD FOREIGN KEY (subscriber_id) REFERENCES subscribers_cm (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('CREATE INDEX IDX_164CD2467808B1AD ON invoice_issuer (subscriber_id)');
    }

    public function down(Schema $schema): void
    {
    }
}

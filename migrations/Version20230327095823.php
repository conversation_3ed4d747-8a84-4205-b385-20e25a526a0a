<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230327095823 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs

        $this->addSql('ALTER TABLE owner_subscription_carwash ALTER quantity TYPE NUMERIC(10, 2)');
        $this->addSql('ALTER TABLE owner_subscriptions_payments ADD base_value NUMERIC(10, 2) DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE owner_subscriptions_payments DROP base_value');
        $this->addSql('ALTER TABLE owner_subscription_carwash ALTER quantity TYPE INT');

    }
}

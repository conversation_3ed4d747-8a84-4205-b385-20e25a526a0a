<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20221114130342 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Delete File Manager';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('DROP SEQUENCE file_manager_id_seq CASCADE');
        $this->addSql('ALTER TABLE file_manager DROP CONSTRAINT fk_a1429c827e3c61f9');
        $this->addSql('DROP TABLE file_manager');
        $this->addSql('ALTER INDEX idx_c82e74236ecbfc RENAME TO IDX_C82E747E3C61F9');
        $this->addSql('ALTER TABLE external_payment ADD invoice_id INT DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('CREATE SEQUENCE file_manager_id_seq INCREMENT BY 1 MINVALUE 1 START 1');
        $this->addSql('CREATE TABLE file_manager (id INT NOT NULL, owner_id INT DEFAULT NULL, ctime TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, mtime TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, status VARCHAR(1) DEFAULT NULL, entity_name VARCHAR(512) DEFAULT NULL, entity_id VARCHAR(255) DEFAULT NULL, oryginal_file_name VARCHAR(256) DEFAULT NULL, file_path VARCHAR(512) DEFAULT NULL, file_name VARCHAR(256) DEFAULT NULL, file_extension VARCHAR(8) DEFAULT NULL, file_size INT DEFAULT NULL, file_type VARCHAR(128) DEFAULT NULL, file_description TEXT DEFAULT NULL, file_order INT DEFAULT NULL, cloud_storage BOOLEAN DEFAULT NULL, cloud_storage_path TEXT DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_A1429C827E3C61F9 ON file_manager (owner_id)');
        $this->addSql('ALTER TABLE file_manager ADD CONSTRAINT fk_a1429c827e3c61f9 FOREIGN KEY (owner_id) REFERENCES users (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE external_payment DROP invoice_id');
    }
}

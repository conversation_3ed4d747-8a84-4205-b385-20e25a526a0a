<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240304150715 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE owner_subscriptions_payments ADD proforma_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE owner_subscriptions_payments ADD invoice_id INT DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
    }
}

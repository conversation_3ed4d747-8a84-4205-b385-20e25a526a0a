<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20221109095440 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Subscribers clean up';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE subscribers_cm DROP province');
        $this->addSql('ALTER TABLE subscribers_cm DROP name2');
        $this->addSql('ALTER TABLE subscribers_cm DROP data_provider');
        $this->addSql('ALTER TABLE subscribers_cm DROP data_provider_unique_identifier');
        $this->addSql('ALTER TABLE subscribers_cm DROP synchronization_time');
        $this->addSql('ALTER TABLE subscribers_cm DROP logo_file_alternate_base');
        $this->addSql('ALTER TABLE subscribers_cm DROP www');
        $this->addSql('ALTER TABLE subscribers_cm DROP facebook');
        $this->addSql('ALTER TABLE subscribers_cm DROP demo_start_date');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE subscribers_cm ADD province VARCHAR(128) DEFAULT NULL');
        $this->addSql('ALTER TABLE subscribers_cm ADD name2 VARCHAR(128) DEFAULT NULL');
        $this->addSql('ALTER TABLE subscribers_cm ADD data_provider VARCHAR(10) DEFAULT NULL');
        $this->addSql('ALTER TABLE subscribers_cm ADD data_provider_unique_identifier VARCHAR(32) DEFAULT NULL');
        $this->addSql('ALTER TABLE subscribers_cm ADD synchronization_time TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL');
        $this->addSql('ALTER TABLE subscribers_cm ADD logo_file_alternate_base TEXT DEFAULT NULL');
        $this->addSql('ALTER TABLE subscribers_cm ADD www VARCHAR(256) DEFAULT NULL');
        $this->addSql('ALTER TABLE subscribers_cm ADD facebook VARCHAR(256) DEFAULT NULL');
        $this->addSql('ALTER TABLE subscribers_cm ADD demo_start_date TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL');
    }
}

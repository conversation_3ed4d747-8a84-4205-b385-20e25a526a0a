<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240702073332 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE SEQUENCE report_config_id_seq INCREMENT BY 1 MINVALUE 1 START 1');
        $this->addSql('CREATE TABLE report_config (id INT NOT NULL, user_id INT DEFAULT NULL, ctime TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, mtime TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, type VARCHAR(255) NOT NULL, ext VARCHAR(255) NOT NULL, criterias JSON DEFAULT NULL, period VARCHAR(255) NOT NULL, enabled BOOLEAN NOT NULL, email JSON DEFAULT NULL, title VARCHAR(255) DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_FD79FAE3A76ED395 ON report_config (user_id)');
        $this->addSql('ALTER TABLE report_config ADD CONSTRAINT FK_FD79FAE3A76ED395 FOREIGN KEY (user_id) REFERENCES "users" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE SCHEMA public');
        $this->addSql('DROP SEQUENCE report_config_id_seq CASCADE');
        $this->addSql('ALTER TABLE report_config DROP CONSTRAINT FK_FD79FAE3A76ED395');
        $this->addSql('DROP TABLE report_config');
    }
}

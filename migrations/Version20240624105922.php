<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240624105922 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'add email text field to report_file table';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE report_file ADD email_text VARCHAR(255) DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE report_file DROP email_text');
    }
}

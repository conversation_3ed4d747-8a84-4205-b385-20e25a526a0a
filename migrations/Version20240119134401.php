<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240119134401 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE owner_subscription_packages ADD dealer_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE owner_subscription_packages ADD CONSTRAINT FK_B8F47684249E6EA1 FOREIGN KEY (dealer_id) REFERENCES "users" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('CREATE INDEX IDX_B8F47684249E6EA1 ON owner_subscription_packages (dealer_id)');
    }

    public function down(Schema $schema): void
    {

    }
}

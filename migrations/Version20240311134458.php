<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240311134458 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE cm_carwashes DROP self_invoiced');
        $this->addSql('ALTER TABLE users DROP CONSTRAINT fk_1483a5e97e3c61f9');
        $this->addSql('DROP INDEX idx_1483a5e97e3c61f9');
        $this->addSql('ALTER TABLE users DROP owner_id');
        $this->addSql('ALTER TABLE users DROP is_owner');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE SCHEMA public');
        $this->addSql('ALTER TABLE cm_carwashes ADD self_invoiced BOOLEAN DEFAULT false NOT NULL');
        $this->addSql('ALTER TABLE "users" ADD owner_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE "users" ADD is_owner BOOLEAN DEFAULT NULL');
        $this->addSql('ALTER TABLE "users" ADD CONSTRAINT fk_1483a5e97e3c61f9 FOREIGN KEY (owner_id) REFERENCES users (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('CREATE INDEX idx_1483a5e97e3c61f9 ON "users" (owner_id)');
    }
}

<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240202071747 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        /*

            UPDATE users SET subscriber_id=ownersSubscribers.subscriber_id
            FROM (SELECT id as owner, subscriber_id FROM users WHERE is_owner = true and subscriber_id is not null) as ownersSubscribers
            WHERE users.owner_id = ownersSubscribers.owner;

         */
        $this->addSql('DROP INDEX uniq_1483a5e97808b1ad');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('CREATE UNIQUE INDEX uniq_1483a5e97808b1ad ON "users" (subscriber_id)');
    }
}

<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20221201113708 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE clients ADD invoiceStrategy VARCHAR(255) DEFAULT \'block\';');

        $this->addSql("UPDATE clients SET invoiceStrategy = 'auto-after-top-up' WHERE invoiced_after_transaction = true;");

        $this->addSql('ALTER TABLE clients DROP invoiced_after_transaction;');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE clients ADD invoiced_after_transaction BOOLEAN DEFAULT false;');

        $this->addSql("UPDATE clients SET invoiceStrategy= 'auto-after-top-up' WHERE invoiced_after_transaction = true;");

        $this->addSql('ALTER TABLE clients DROP invoiceStrategy;');
    }
}

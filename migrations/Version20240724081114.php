<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240724081114 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add phone and fanpage to subscribers';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE subscribers_cm ADD facebook_fanpage TEXT DEFAULT NULL');
        $this->addSql('ALTER TABLE subscribers_cm ADD phone TEXT DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE subscribers_cm DROP facebook_fanpage');
        $this->addSql('ALTER TABLE subscribers_cm DROP phone');
    }
}

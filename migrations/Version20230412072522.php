<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230412072522 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE owner_subscriptions_payments ADD currency_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE owner_subscriptions_payments ADD CONSTRAINT FK_BC370DC838248176 FOREIGN KEY (currency_id) REFERENCES currency (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('CREATE INDEX IDX_BC370DC838248176 ON owner_subscriptions_payments (currency_id)');
        $this->addSql('UPDATE owner_subscriptions_payments SET currency_id = 2 WHERE currency_id is null');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE owner_subscriptions_payments DROP CONSTRAINT FK_BC370DC838248176');
        $this->addSql('DROP INDEX IDX_BC370DC838248176');
        $this->addSql('ALTER TABLE owner_subscriptions_payments DROP currency_id');
    }
}

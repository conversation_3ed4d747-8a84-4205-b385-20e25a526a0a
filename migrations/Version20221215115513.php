<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20221215115513 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        $this->addSql("INSERT INTO public.languages (id, locale, name, status, ctime, mtime, code) VALUES (10, 'nl', 'hollandsk', 'a', '2022-03-02 12:09:46', null, 'nl_NL');");
        $this->addSql("INSERT INTO public.languages (id, locale, name, status, ctime, mtime, code) VALUES (11, 'fr', 'Français', 'a', '2022-12-15 11:59:39', null, 'fr_FR');");
    }

    public function down(Schema $shema): void
    {
    }
}

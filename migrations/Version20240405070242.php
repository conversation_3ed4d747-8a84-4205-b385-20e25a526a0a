<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240405070242 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE report_file (id INT NOT NULL, subscriber_id INT DEFAULT NULL, user_id INT NOT NULL, cloud_path VARCHAR(255) DEFAULT NULL, ctime TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, etime TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, type VARCHAR(255) NOT NULL, "group" VARCHAR(255) NOT NULL, criterias JSON DEFAULT NULL, mtime TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, status VARCHAR(255) NOT NULL, email JSON DEFAULT NULL, title VARCHAR(255) NOT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_FFB3DBEB7808B1AD ON report_file (subscriber_id)');
        $this->addSql('CREATE INDEX IDX_FFB3DBEBA76ED395 ON report_file (user_id)');
        $this->addSql('ALTER TABLE report_file ADD CONSTRAINT FK_FFB3DBEB7808B1AD FOREIGN KEY (subscriber_id) REFERENCES subscribers_cm (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE report_file ADD CONSTRAINT FK_FFB3DBEBA76ED395 FOREIGN KEY (user_id) REFERENCES "users" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
    }

    public function down(Schema $schema): void
    {
    }
}

<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230512071554 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs


        $this->addSql('ALTER TABLE owner_subscriptions_payments ADD vat_tax_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE owner_subscriptions_payments ADD CONSTRAINT FK_BC370DC8AD288B01 FOREIGN KEY (vat_tax_id) REFERENCES vat_tax (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_BC370DC8AD288B01 ON owner_subscriptions_payments (vat_tax_id)');
    }

    public function down(Schema $schema): void
    {

    }
}

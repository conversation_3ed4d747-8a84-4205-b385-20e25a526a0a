<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20221109091242 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Delete cm_messages';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('DROP TABLE cm_messages');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('CREATE SCHEMA public');
        $this->addSql('CREATE TABLE cm_messages (id INT NOT NULL, lang TEXT NOT NULL, title VARCHAR(255) DEFAULT NULL, content TEXT DEFAULT NULL, ctime TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, mtime TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, on_start BOOLEAN DEFAULT NULL, status VARCHAR(1) DEFAULT NULL, start_time TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, end_time TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, type INT DEFAULT NULL, PRIMARY KEY(id, lang))');
    }
}

<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20220930051933 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE owner_subscriptions_payments DROP CONSTRAINT fk_bc370dc8a76ed395');
        $this->addSql('ALTER TABLE owner_subscriptions_payments DROP CONSTRAINT fk_bc370dc8c05c09e7');
        $this->addSql('ALTER TABLE clients DROP CONSTRAINT fk_c82e74236ecbfc');
        $this->addSql('ALTER TABLE cm_carwashes DROP CONSTRAINT "FK_cm_carwashes_cm_users"');
        $this->addSql('ALTER TABLE cm_carwashes DROP CONSTRAINT fk_a9a3eb21249e6ea1');
        $this->addSql('ALTER TABLE cm_carwashes DROP CONSTRAINT fk_a9a3eb217e3c61f9');
        $this->addSql('ALTER TABLE mobile_token DROP CONSTRAINT fk_11d11077a76ed395');
        $this->addSql('ALTER TABLE cm_users_reports DROP CONSTRAINT fk_cm_user_reports_user');
        $this->addSql('ALTER TABLE file_manager DROP CONSTRAINT fk_a1429c827e3c61f9');
        $this->addSql('DROP SEQUENCE cm_messages_users_id_seq CASCADE');
        $this->addSql('DROP SEQUENCE cm_tokens_id_seq CASCADE');

        $this->addSql('DROP SEQUENCE scheduled_command_id_seq CASCADE');
        $this->addSql('DROP SEQUENCE subscribers_id_seq CASCADE');
        $this->addSql('ALTER TABLE cm_messages_users DROP CONSTRAINT cm_messages_users_users_fkey');
        $this->addSql('ALTER TABLE cm_messages_users DROP CONSTRAINT fk_73b0eb13537a1329989c1fbb');
        $this->addSql('ALTER TABLE cm_users DROP CONSTRAINT "FK_cm_users_cm_users"');
        $this->addSql('ALTER TABLE cm_users DROP CONSTRAINT fk_cm_users_currency');
        $this->addSql('ALTER TABLE cm_users DROP CONSTRAINT fk_cm_users_timezone');
        $this->addSql('ALTER TABLE cm_users DROP CONSTRAINT fk_fe5ebdc25fba3acd');
        $this->addSql('ALTER TABLE cm_users DROP CONSTRAINT fk_fe5ebdc27808b1ad');
        $this->addSql('ALTER TABLE cm_users DROP CONSTRAINT fk_fe5ebdc282f1baf4');
        $this->addSql('ALTER TABLE cm_users DROP CONSTRAINT fk_fe5ebdc2de329e72');
        $this->addSql('DROP TABLE cm_tokens');
        $this->addSql('DROP TABLE cm_messages_users');
        $this->addSql('DROP TABLE scheduled_command');

        $this->addSql('ALTER TABLE clients ADD CONSTRAINT FK_C82E747E3C61F9 FOREIGN KEY (owner_id) REFERENCES "users" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');


        $this->addSql('ALTER TABLE cm_carwashes ADD CONSTRAINT FK_A9A3EB21249E6EA1 FOREIGN KEY (dealer_id) REFERENCES "users" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE cm_carwashes ADD CONSTRAINT FK_A9A3EB217E3C61F9 FOREIGN KEY (owner_id) REFERENCES "users" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE cm_messages ALTER lang TYPE TEXT');
        $this->addSql('DROP INDEX idx_cm_user_reports_user');
        $this->addSql('ALTER TABLE cm_users_reports ADD CONSTRAINT FK_3DF6AFA9A76ED395 FOREIGN KEY (user_id) REFERENCES "users" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');

        $this->addSql('ALTER TABLE file_manager ADD CONSTRAINT FK_A1429C827E3C61F9 FOREIGN KEY (owner_id) REFERENCES "users" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE invoice_issuer ALTER numerator_format SET NOT NULL');


        $this->addSql('ALTER TABLE mobile_token ADD CONSTRAINT FK_11D11077A76ED395 FOREIGN KEY (user_id) REFERENCES "users" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');

        $this->addSql('ALTER TABLE owner_subscriptions_payments ADD CONSTRAINT FK_BC370DC8A76ED395 FOREIGN KEY (user_id) REFERENCES "users" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE owner_subscriptions_payments ADD CONSTRAINT FK_BC370DC8C05C09E7 FOREIGN KEY (who_added_id) REFERENCES "users" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER INDEX uniq_owner_subscription_external_payment RENAME TO UNIQ_BC370DC81E3D2C69');

        $this->addSql('ALTER TABLE users ADD CONSTRAINT FK_1483A5E9DE329E72 FOREIGN KEY (invoice_issuer_id) REFERENCES invoice_issuer (id) NOT DEFERRABLE INITIALLY IMMEDIATE');

    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE SCHEMA public');
        $this->addSql('CREATE SEQUENCE cm_messages_users_id_seq INCREMENT BY 1 MINVALUE 1 START 1');
        $this->addSql('CREATE SEQUENCE cm_tokens_id_seq INCREMENT BY 1 MINVALUE 1 START 1');
        $this->addSql('CREATE SEQUENCE cmusers_id_seq INCREMENT BY 1 MINVALUE 1 START 1');
        $this->addSql('CREATE SEQUENCE scheduled_command_id_seq INCREMENT BY 1 MINVALUE 1 START 1');
        $this->addSql('CREATE SEQUENCE subscribers_id_seq INCREMENT BY 1 MINVALUE 1 START 1');
        $this->addSql('CREATE TABLE cm_tokens (id INT NOT NULL, user_id INT NOT NULL, token VARCHAR(128) DEFAULT NULL, created_date TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE TABLE cm_messages_users (id INT NOT NULL, message_id INT DEFAULT NULL, user_id INT DEFAULT NULL, message_lang TEXT DEFAULT NULL, status VARCHAR(1) DEFAULT NULL, ctime TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, mtime TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, stime TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, rtime TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_73B0EB13A76ED395 ON cm_messages_users (user_id)');
        $this->addSql('CREATE INDEX IDX_73B0EB13537A1329989C1FBB ON cm_messages_users (message_id, message_lang)');
        $this->addSql('CREATE TABLE scheduled_command (id SERIAL NOT NULL, name VARCHAR(100) NOT NULL, command VARCHAR(100) NOT NULL, arguments TEXT DEFAULT NULL, cron_expression VARCHAR(100) DEFAULT NULL, last_execution TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, last_return_code INT DEFAULT NULL, log_file VARCHAR(100) DEFAULT NULL, priority INT NOT NULL, execute_immediately BOOLEAN NOT NULL, disabled BOOLEAN NOT NULL, locked BOOLEAN NOT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE TABLE cm_users (id INT NOT NULL, currency INT DEFAULT NULL, timezone INT DEFAULT NULL, owner_id INT DEFAULT NULL, language_id INT DEFAULT NULL, subscriber_id INT DEFAULT NULL, invoice_issuer_id INT DEFAULT NULL, created_by_cm_user INT DEFAULT NULL, ctime TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, mtime TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, status VARCHAR(1) NOT NULL, username VARCHAR(180) NOT NULL, roles TEXT NOT NULL, firstname VARCHAR(60) DEFAULT NULL, lastname VARCHAR(60) DEFAULT NULL, phone VARCHAR(14) DEFAULT NULL, email VARCHAR(180) NOT NULL, salt VARCHAR(255) DEFAULT NULL, password VARCHAR(255) NOT NULL, last_login TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, delimeter VARCHAR(1) DEFAULT NULL, reporting BOOLEAN DEFAULT NULL, migration TEXT DEFAULT NULL, old_id INT DEFAULT NULL, is_owner BOOLEAN DEFAULT NULL, assigned_carwashes TEXT DEFAULT NULL, username_canonical VARCHAR(180) NOT NULL, email_canonical VARCHAR(180) NOT NULL, enabled BOOLEAN NOT NULL, confirmation_token VARCHAR(180) DEFAULT NULL, password_requested_at TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL, is_privacy_policy_accepted BOOLEAN DEFAULT NULL, api_token TEXT DEFAULT NULL, api_token_life INT DEFAULT NULL, owner_bkf INT DEFAULT NULL, comment VARCHAR(255) DEFAULT NULL, subscription_discount INT DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE UNIQUE INDEX uniq_fe5ebdc2de329e72 ON cm_users (invoice_issuer_id)');
        $this->addSql('CREATE UNIQUE INDEX uniq_fe5ebdc2c05fb297 ON cm_users (confirmation_token)');
        $this->addSql('CREATE UNIQUE INDEX uniq_fe5ebdc2a0d96fbf ON cm_users (email_canonical)');
        $this->addSql('CREATE UNIQUE INDEX uniq_fe5ebdc292fc23a8 ON cm_users (username_canonical)');
        $this->addSql('CREATE UNIQUE INDEX uniq_fe5ebdc27808b1ad ON cm_users (subscriber_id)');
        $this->addSql('CREATE INDEX idx_fe5ebdc282f1baf4 ON cm_users (language_id)');
        $this->addSql('CREATE INDEX idx_fe5ebdc26956883f ON cm_users (currency)');
        $this->addSql('CREATE INDEX idx_fe5ebdc23701b297 ON cm_users (timezone)');
        $this->addSql('CREATE INDEX idx_cm_users_timezone ON cm_users (timezone)');
        $this->addSql('CREATE INDEX idx_cm_users_owner_id ON cm_users (owner_id)');
        $this->addSql('CREATE INDEX idx_cm_users_currency ON cm_users (currency)');
        $this->addSql('CREATE UNIQUE INDEX cm_user_username_key ON cm_users (username)');
        $this->addSql('CREATE UNIQUE INDEX owner_bkf_unique ON cm_users (owner_bkf)');
        $this->addSql('CREATE INDEX IDX_FE5EBDC25FBA3ACD ON cm_users (created_by_cm_user)');
        $this->addSql('COMMENT ON COLUMN cm_users.roles IS \'(DC2Type:array)\'');
        $this->addSql('ALTER TABLE cm_messages_users ADD CONSTRAINT cm_messages_users_users_fkey FOREIGN KEY (user_id) REFERENCES cm_users (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE cm_messages_users ADD CONSTRAINT fk_73b0eb13537a1329989c1fbb FOREIGN KEY (message_id, message_lang) REFERENCES cm_messages (id, lang) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE cm_users ADD CONSTRAINT "FK_cm_users_cm_users" FOREIGN KEY (owner_id) REFERENCES cm_users (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE cm_users ADD CONSTRAINT fk_cm_users_currency FOREIGN KEY (currency) REFERENCES currency (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE cm_users ADD CONSTRAINT fk_cm_users_timezone FOREIGN KEY (timezone) REFERENCES timezones (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE cm_users ADD CONSTRAINT fk_fe5ebdc25fba3acd FOREIGN KEY (created_by_cm_user) REFERENCES cm_users (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE cm_users ADD CONSTRAINT fk_fe5ebdc27808b1ad FOREIGN KEY (subscriber_id) REFERENCES subscribers_cm (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE cm_users ADD CONSTRAINT fk_fe5ebdc282f1baf4 FOREIGN KEY (language_id) REFERENCES languages (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE cm_users ADD CONSTRAINT fk_fe5ebdc2de329e72 FOREIGN KEY (invoice_issuer_id) REFERENCES invoice_issuer (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE owner_subscriptions_payments DROP CONSTRAINT fk_bc370dc8a76ed395');
        $this->addSql('ALTER TABLE owner_subscriptions_payments DROP CONSTRAINT fk_bc370dc8c05c09e7');
        $this->addSql('ALTER TABLE owner_subscriptions_payments ADD CONSTRAINT fk_bc370dc8a76ed395 FOREIGN KEY (user_id) REFERENCES cm_users (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE owner_subscriptions_payments ADD CONSTRAINT fk_bc370dc8c05c09e7 FOREIGN KEY (who_added_id) REFERENCES cm_users (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER INDEX uniq_bc370dc81e3d2c69 RENAME TO uniq_owner_subscription_external_payment');
        $this->addSql('ALTER TABLE cm_messages ALTER lang TYPE VARCHAR(3)');
        $this->addSql('ALTER TABLE clients DROP CONSTRAINT FK_C82E747E3C61F9');
        $this->addSql('ALTER TABLE clients ADD CONSTRAINT fk_c82e74236ecbfc FOREIGN KEY (owner_id) REFERENCES cm_users (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER INDEX idx_c82e747e3c61f9 RENAME TO idx_c82e74236ecbfc');
        $this->addSql('ALTER TABLE "users" DROP CONSTRAINT FK_1483A5E9DE329E72');
        $this->addSql('DROP INDEX UNIQ_1483A5E97808B1AD');
        $this->addSql('DROP INDEX UNIQ_1483A5E9DE329E72');
        $this->addSql('CREATE INDEX IDX_1483A5E97808B1AD ON "users" (subscriber_id)');
        $this->addSql('COMMENT ON COLUMN logs.status IS \'status\'');
        $this->addSql('COMMENT ON COLUMN logs.source IS \'zrodlo logu\'');
        $this->addSql('COMMENT ON COLUMN logs.message IS \'wiadomosc\'');
        $this->addSql('ALTER TABLE cm_carwashes DROP CONSTRAINT fk_a9a3eb217e3c61f9');
        $this->addSql('ALTER TABLE cm_carwashes DROP CONSTRAINT fk_a9a3eb21249e6ea1');
        $this->addSql('ALTER TABLE cm_carwashes ADD CONSTRAINT "FK_cm_carwashes_cm_users" FOREIGN KEY (owner_id) REFERENCES cm_users (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE cm_carwashes ADD CONSTRAINT fk_a9a3eb217e3c61f9 FOREIGN KEY (owner_id) REFERENCES cm_users (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE cm_carwashes ADD CONSTRAINT fk_a9a3eb21249e6ea1 FOREIGN KEY (dealer_id) REFERENCES cm_users (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE mobile_token DROP CONSTRAINT fk_11d11077a76ed395');
        $this->addSql('ALTER TABLE mobile_token ADD CONSTRAINT fk_11d11077a76ed395 FOREIGN KEY (user_id) REFERENCES cm_users (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE cm_users_reports DROP CONSTRAINT FK_3DF6AFA9A76ED395');
        $this->addSql('ALTER TABLE cm_users_reports ADD CONSTRAINT fk_cm_user_reports_user FOREIGN KEY (user_id) REFERENCES cm_users (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('CREATE INDEX idx_cm_user_reports_user ON cm_users_reports (user_id)');
        $this->addSql('ALTER TABLE country ALTER name TYPE VARCHAR(60)');
        $this->addSql('ALTER TABLE file_manager DROP CONSTRAINT fk_a1429c827e3c61f9');
        $this->addSql('ALTER TABLE file_manager ADD CONSTRAINT fk_a1429c827e3c61f9 FOREIGN KEY (owner_id) REFERENCES cm_users (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE invoice_issuer ALTER numerator_format DROP NOT NULL');
    }
}

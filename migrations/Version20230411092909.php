<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230411092909 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add dealer flag to user';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE users ADD is_dealer BOOLEAN DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE "users" DROP is_dealer');
    }
}

<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240219093918 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE subscribers_cm ADD language_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE subscribers_cm ADD CONSTRAINT FK_CC5D3AEF82F1BAF4 FOREIGN KEY (language_id) REFERENCES languages (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('CREATE INDEX IDX_CC5D3AEF82F1BAF4 ON subscribers_cm (language_id)');
    }

    public function down(Schema $schema): void
    {

    }
}

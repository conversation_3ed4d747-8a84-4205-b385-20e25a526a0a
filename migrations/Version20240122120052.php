<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240122120052 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE subscribers_cm ADD dealer_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE subscribers_cm ADD timezone_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE subscribers_cm ADD owner_bkf INT DEFAULT NULL');
        $this->addSql('ALTER TABLE subscribers_cm ADD subscription_discount INT DEFAULT NULL');
        $this->addSql('ALTER TABLE subscribers_cm ADD CONSTRAINT FK_CC5D3AEF249E6EA1 FOREIGN KEY (dealer_id) REFERENCES subscribers_cm (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE subscribers_cm ADD CONSTRAINT FK_CC5D3AEF3FE997DE FOREIGN KEY (timezone_id) REFERENCES timezones (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('CREATE INDEX IDX_CC5D3AEF249E6EA1 ON subscribers_cm (dealer_id)');
        $this->addSql('CREATE INDEX IDX_CC5D3AEF3FE997DE ON subscribers_cm (timezone_id)');
        $this->addSql('ALTER TABLE users DROP CONSTRAINT fk_1483a5e9249e6ea1');
    }

    public function down(Schema $schema): void
    {
    }
}

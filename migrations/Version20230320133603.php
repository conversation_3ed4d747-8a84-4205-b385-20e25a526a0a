<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230320133603 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE SEQUENCE owner_subscription_carwash_id_seq INCREMENT BY 1 MINVALUE 1 START 1');
        $this->addSql('CREATE TABLE owner_subscription_carwash (id INT NOT NULL, subscription_id INT NOT NULL, carwash_id INT NOT NULL, currency_id INT NOT NULL, ctime TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL, type VARCHAR(32) NOT NULL, start_time DATE NOT NULL, end_time DATE NOT NULL, code VARCHAR(32) NOT NULL, price NUMERIC(10, 2) NOT NULL, quantity INT NOT NULL, total_price NUMERIC(10, 2) NOT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_7DF80CC9A1887DC ON owner_subscription_carwash (subscription_id)');
        $this->addSql('CREATE INDEX IDX_7DF80CC4FB0BF84 ON owner_subscription_carwash (carwash_id)');
        $this->addSql('CREATE INDEX IDX_7DF80CC38248176 ON owner_subscription_carwash (currency_id)');
        $this->addSql('ALTER TABLE owner_subscription_carwash ADD CONSTRAINT FK_7DF80CC9A1887DC FOREIGN KEY (subscription_id) REFERENCES owner_subscriptions_payments (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE owner_subscription_carwash ADD CONSTRAINT FK_7DF80CC4FB0BF84 FOREIGN KEY (carwash_id) REFERENCES cm_carwashes (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE owner_subscription_carwash ADD CONSTRAINT FK_7DF80CC38248176 FOREIGN KEY (currency_id) REFERENCES currency (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
    }

    public function down(Schema $schema): void
    {

    }
}

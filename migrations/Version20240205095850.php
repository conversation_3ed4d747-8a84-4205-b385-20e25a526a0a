<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240205095850 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE cm_carwashes ADD subscriber_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE cm_carwashes ADD CONSTRAINT FK_A9A3EB217808B1AD FOREIGN KEY (subscriber_id) REFERENCES subscribers_cm (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('CREATE INDEX IDX_A9A3EB217808B1AD ON cm_carwashes (subscriber_id)');
        $this->addSql('ALTER TABLE owner_subscriptions_payments ADD subscriber_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE owner_subscriptions_payments ADD CONSTRAINT FK_BC370DC87808B1AD FOREIGN KEY (subscriber_id) REFERENCES subscribers_cm (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('CREATE INDEX IDX_BC370DC87808B1AD ON owner_subscriptions_payments (subscriber_id)');
    }

    public function down(Schema $schema): void
    {

    }
}

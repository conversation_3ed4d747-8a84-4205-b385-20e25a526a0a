<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20230327124228 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE owner_subscription_carwash ADD who_pay_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE owner_subscription_carwash ADD CONSTRAINT FK_7DF80CCECB55E6A FOREIGN KEY (who_pay_id) REFERENCES "users" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('CREATE INDEX IDX_7DF80CCECB55E6A ON owner_subscription_carwash (who_pay_id)');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE owner_subscription_carwash DROP CONSTRAINT FK_7DF80CCECB55E6A');
        $this->addSql('DROP INDEX IDX_7DF80CCECB55E6A');
        $this->addSql('ALTER TABLE owner_subscription_carwash DROP who_pay_id');
    }
}

<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240122094535 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE owner_subscription_packages DROP CONSTRAINT FK_B8F47684249E6EA1');
        $this->addSql('ALTER TABLE owner_subscription_packages ADD CONSTRAINT FK_B8F47684249E6EA1 FOREIGN KEY (dealer_id) REFERENCES subscribers_cm (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
    }

    public function down(Schema $schema): void
    {

    }
}

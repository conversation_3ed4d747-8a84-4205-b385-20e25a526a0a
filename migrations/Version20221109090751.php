<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20221109090751 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Delete user last_login';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE users DROP last_login');

    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE "users" ADD last_login TIMESTAMP(0) WITHOUT TIME ZONE DEFAULT NULL');
        $this->addSql('COMMENT ON COLUMN "users".last_login IS \'(DC2Type:datetime_immutable)\'');
    }
}

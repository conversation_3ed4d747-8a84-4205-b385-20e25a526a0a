<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240422092736 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE SEQUENCE logs_id_seq INCREMENT BY 1 MINVALUE 1 START 1');
        $this->addSql('CREATE TABLE logs (id INT NOT NULL, user_id INT DEFAULT NULL, subscriber_id INT DEFAULT NULL, carwash_id INT DEFAULT NULL, ctime VARCHAR(255) NOT NULL, source VARCHAR(255) NOT NULL, level VARCHAR(16) NOT NULL, message TEXT DEFAULT NULL, PRIMARY KEY(id))');
        $this->addSql('CREATE INDEX IDX_F08FC65CA76ED395 ON logs (user_id)');
        $this->addSql('CREATE INDEX IDX_F08FC65C7808B1AD ON logs (subscriber_id)');
        $this->addSql('CREATE INDEX IDX_F08FC65C4FB0BF84 ON logs (carwash_id)');
        $this->addSql('ALTER TABLE logs ADD CONSTRAINT FK_F08FC65CA76ED395 FOREIGN KEY (user_id) REFERENCES "users" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE logs ADD CONSTRAINT FK_F08FC65C7808B1AD FOREIGN KEY (subscriber_id) REFERENCES subscribers_cm (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE logs ADD CONSTRAINT FK_F08FC65C4FB0BF84 FOREIGN KEY (carwash_id) REFERENCES cm_carwashes (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
    }
}

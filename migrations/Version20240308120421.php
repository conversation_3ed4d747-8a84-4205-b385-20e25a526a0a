<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240308120421 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE external_payment ADD payment_gate_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE external_payment ADD user_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE external_payment ADD comment VARCHAR(255) DEFAULT NULL');
        $this->addSql('ALTER TABLE external_payment ADD CONSTRAINT FK_B48A5D4E9036221C FOREIGN KEY (payment_gate_id) REFERENCES payment_gate (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('ALTER TABLE external_payment ADD CONSTRAINT FK_B48A5D4EA76ED395 FOREIGN KEY (user_id) REFERENCES "users" (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('CREATE INDEX IDX_B48A5D4E9036221C ON external_payment (payment_gate_id)');
        $this->addSql('CREATE INDEX IDX_B48A5D4EA76ED395 ON external_payment (user_id)');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE SCHEMA public');
        $this->addSql('ALTER TABLE external_payment DROP CONSTRAINT FK_B48A5D4E9036221C');
        $this->addSql('ALTER TABLE external_payment DROP CONSTRAINT FK_B48A5D4EA76ED395');
        $this->addSql('DROP INDEX IDX_B48A5D4E9036221C');
        $this->addSql('DROP INDEX IDX_B48A5D4EA76ED395');
        $this->addSql('ALTER TABLE external_payment DROP payment_gate_id');
        $this->addSql('ALTER TABLE external_payment DROP user_id');
        $this->addSql('ALTER TABLE external_payment DROP comment');
    }
}

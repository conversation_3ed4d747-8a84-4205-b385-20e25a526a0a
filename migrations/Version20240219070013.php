<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240219070013 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('DROP INDEX idx_164cd2467808b1ad');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_164CD2467808B1AD ON invoice_issuer (subscriber_id)');
    }

    public function down(Schema $schema): void
    {

    }
}

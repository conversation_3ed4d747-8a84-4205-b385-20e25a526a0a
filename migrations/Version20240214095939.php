<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20240214095939 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE owner_subscription_carwash ADD dealer_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE owner_subscription_carwash ADD invoice INT DEFAULT NULL');
        $this->addSql('ALTER TABLE owner_subscription_carwash ADD CONSTRAINT FK_7DF80CC249E6EA1 FOREIGN KEY (dealer_id) REFERENCES subscribers_cm (id) NOT DEFERRABLE INITIALLY IMMEDIATE');
        $this->addSql('CREATE INDEX IDX_7DF80CC249E6EA1 ON owner_subscription_carwash (dealer_id)');
    }

    public function down(Schema $schema): void
    {

    }
}

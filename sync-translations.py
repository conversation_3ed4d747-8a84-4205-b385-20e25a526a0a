#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import gspread
from ruamel.yaml import YAML
from oauth2client.service_account import ServiceAccountCredentials

# Define the scope of the Google Sheets API
scope = [
    'https://spreadsheets.google.com/feeds',
    'https://www.googleapis.com/auth/drive'
]

# Path to the credentials JSON file downloaded from Google Cloud Console
creds = ServiceAccountCredentials.from_json_keyfile_name('cm-bkf-pl-170e3bd4338b.json', scope)

# Authorize the client using the credentials
client = gspread.authorize(creds)

# Open the Google Sheets spreadsheet by its title
spreadsheet = client.open('cm translations')

# Select a specific worksheet within the spreadsheet
worksheet = spreadsheet.worksheet('cm')

# Get all values from the worksheet as a list of lists
data = worksheet.get_all_values()

langMap = {lang: idx for idx, lang in enumerate(data[0]) if lang}

environmentsMap = {env: idx for idx, env in enumerate(data[1]) if env}

langsData = {lang: {} for idx, lang in enumerate(data[0]) if lang}

saveConfig = {
    'frontend': { 'path': './src/i18n/', 'ext': 'yaml' },
    'backend': { 'path' :'./translations/messages.', 'ext': 'yml' },
    'mobile': { 'path' :'./lib/l10n/app_', 'ext': 'arb' },
}

langs = ['pl', 'en', 'ru', 'cs']
source = 'backend'

yaml = YAML()
# number of characters after with wrapping occures
yaml.width = 800
yaml.preserve_quotes = True

# #Print the downloaded data
for row in data[3:]:
    if row[environmentsMap[source]] == 'TRUE':
        for lang in langs:
            langsData[lang].update({row[3]: row[langMap[lang]]})

for lang in langs:
    # clear keys with empty taranslations
    empty_keys = [k for k,v in langsData[lang].items() if not v]
    for k in empty_keys:
        del langsData[lang][k]

    # save translations
    path = saveConfig[source]['path'] + lang + '.' + saveConfig[source]['ext']
    with open(path, 'w', encoding="utf-8") as outfile:
        yaml.dump(langsData[lang], outfile)

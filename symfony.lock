{"bkf/connector": {"version": "dev-main"}, "dama/doctrine-test-bundle": {"version": "7.3", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "7.2", "ref": "896306d79d4ee143af9eadf9b09fd34a8c391b70"}, "files": ["./config/packages/dama_doctrine_test_bundle.yaml"]}, "doctrine/deprecations": {"version": "1.1", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "1.0", "ref": "87424683adc81d7dc305eefec1fced883084aab9"}}, "doctrine/doctrine-bundle": {"version": "2.14", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "2.10", "ref": "d1778a69711a9b06bb4e202977ca6c4a0d16933d"}, "files": ["config/packages/doctrine.yaml", "src/Entity/.gitignore", "src/Repository/.gitignore"]}, "doctrine/doctrine-fixtures-bundle": {"version": "3.7", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "3.0", "ref": "1f5514cfa15b947298df4d771e694e578d4c204d"}, "files": ["./src/DataFixtures/AppFixtures.php"]}, "doctrine/doctrine-migrations-bundle": {"version": "3.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "3.1", "ref": "1d01ec03c6ecbd67c3375c5478c9a423ae5d6a33"}, "files": ["./config/packages/doctrine_migrations.yaml", "./migrations/.gitignore"]}, "i2m/connectors": {"version": "dev-master"}, "i2m/iiot": {"version": "dev-master"}, "i2m/invoices": {"version": "dev-main"}, "i2m/payment": {"version": "dev-main"}, "i2m/reports": {"version": "dev-main"}, "i2m/storage": {"version": "dev-main"}, "knplabs/knp-snappy-bundle": {"version": "1.10", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "1.5", "ref": "c81bdcf4a9d4e7b1959071457f9608631865d381"}, "files": ["./config/packages/knp_snappy.yaml"]}, "kreait/firebase-bundle": {"version": "5.6.0"}, "nelmio/alice": {"version": "3.12", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "3.3", "ref": "42b52d2065dc3fde27912d502c18ca1926e35ae2"}, "files": ["./config/packages/nelmio_alice.yaml"]}, "nelmio/api-doc-bundle": {"version": "4.38", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "3.0", "ref": "c8e0c38e1a280ab9e37587a8fa32b251d5bc1c94"}, "files": ["./config/packages/nelmio_api_doc.yaml", "./config/routes/nelmio_api_doc.yaml"]}, "nelmio/cors-bundle": {"version": "2.5", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "1.5", "ref": "6bea22e6c564fba3a1391615cada1437d0bde39c"}, "files": ["./config/packages/nelmio_cors.yaml"]}, "php-amqplib/rabbitmq-bundle": {"version": "2.17", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "1.12", "ref": "ce3ca2e4577270c4f518539aa6b9e878998fcfef"}, "files": ["./config/packages/old_sound_rabbit_mq.yaml", "./src/Consumer/.gitignore"]}, "phpstan/phpstan": {"version": "2.1", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "1.0", "ref": "5e490cc197fb6bb1ae22e5abbc531ddc633b6767"}, "files": ["./phpstan.dist.neon"]}, "phpunit/phpunit": {"version": "9.6", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "9.6", "ref": "6a9341aa97d441627f8bd424ae85dc04c944f8b4"}, "files": ["./.env.test", "./phpunit.xml.dist", "./tests/bootstrap.php"]}, "sentry/sentry-symfony": {"version": "5.2", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "5.0", "ref": "f26c577142172082bb3aeef519af6b5944b86ac2"}, "files": ["./config/packages/sentry.yaml"]}, "squizlabs/php_codesniffer": {"version": "3.13", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "3.6", "ref": "1019e5c08d4821cb9b77f4891f8e9c31ff20ac6f"}, "files": ["./phpcs.xml.dist"]}, "symfony/console": {"version": "5.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "5.3", "ref": "1781ff40d8a17d87cf53f8d4cf0c8346ed2bb461"}, "files": ["./bin/console"]}, "symfony/debug-bundle": {"version": "5.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "5.3", "ref": "5aa8aa48234c8eb6dbdd7b3cd5d791485d2cec4b"}, "files": ["./config/packages/debug.yaml"]}, "symfony/flex": {"version": "2.7", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "2.4", "ref": "52e9754527a15e2b79d9a610f98185a1fe46622a"}, "files": [".env", ".env.dev"]}, "symfony/framework-bundle": {"version": "6.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.4", "ref": "32126346f25e1cee607cc4aa6783d46034920554"}, "files": ["config/packages/cache.yaml", "config/packages/framework.yaml", "config/preload.php", "config/routes/framework.yaml", "config/services.yaml", "public/index.php", "src/Controller/.gitignore", "src/Kernel.php"]}, "symfony/lock": {"version": "5.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "5.2", "ref": "8e937ff2b4735d110af1770f242c1107fdab4c8e"}, "files": ["./config/packages/lock.yaml"]}, "symfony/mailer": {"version": "5.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "4.3", "ref": "09051cfde49476e3c12cd3a0e44289ace1c75a4f"}, "files": ["./config/packages/mailer.yaml"]}, "symfony/maker-bundle": {"version": "1.50", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "1.0", "ref": "fadbfe33303a76e25cb63401050439aa9b1a9c7f"}}, "symfony/messenger": {"version": "6.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.0", "ref": "ba1ac4e919baba5644d31b57a3284d6ba12d52ee"}, "files": ["config/packages/messenger.yaml"]}, "symfony/monolog-bundle": {"version": "3.10", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "3.7", "ref": "aff23899c4440dd995907613c1dd709b6f59503f"}, "files": ["./config/packages/monolog.yaml"]}, "symfony/phpunit-bridge": {"version": "6.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.3", "ref": "a411a0480041243d97382cac7984f7dce7813c08"}, "files": ["./.env.test", "./bin/phpunit", "./phpunit.xml.dist", "./tests/bootstrap.php"]}, "symfony/routing": {"version": "6.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.2", "ref": "e0a11b4ccb8c9e70b574ff5ad3dfdcd41dec5aa6"}, "files": ["config/packages/routing.yaml", "config/routes.yaml"]}, "symfony/security-bundle": {"version": "6.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.4", "ref": "2ae08430db28c8eb4476605894296c82a642028f"}, "files": ["config/packages/security.yaml", "config/routes/security.yaml"]}, "symfony/translation": {"version": "6.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.3", "ref": "620a1b84865ceb2ba304c8f8bf2a185fbf32a843"}, "files": ["config/packages/translation.yaml", "translations/.gitignore"]}, "symfony/twig-bundle": {"version": "6.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.4", "ref": "cab5fd2a13a45c266d45a7d9337e28dee6272877"}, "files": ["config/packages/twig.yaml", "templates/base.html.twig"]}, "symfony/validator": {"version": "5.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "5.3", "ref": "c32cfd98f714894c4f128bb99aa2530c1227603c"}, "files": ["./config/packages/validator.yaml"]}, "symfony/web-profiler-bundle": {"version": "6.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.1", "ref": "8b51135b84f4266e3b4c8a6dc23c9d1e32e543b7"}, "files": ["config/packages/web_profiler.yaml", "config/routes/web_profiler.yaml"]}, "symfony/webpack-encore-bundle": {"version": "1.17", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "1.10", "ref": "6642c5eeebbd06268826e7f9fbee721cba1d7d77"}, "files": ["./assets/app.js", "./assets/bootstrap.js", "./assets/controllers.json", "./assets/controllers/hello_controller.js", "./assets/styles/app.css", "./config/packages/webpack_encore.yaml", "./package.json", "./webpack.config.js"]}, "twig/extra-bundle": {"version": "v3.21.0"}}
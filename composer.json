{"type": "project", "license": "proprietary", "minimum-stability": "dev", "prefer-stable": true, "require": {"php": ">=8.2", "ext-ctype": "*", "ext-fileinfo": "*", "ext-iconv": "*", "ext-intl": "*", "ext-json": "*", "ext-zip": "*", "bkf/connector": "dev-main", "chillerlan/php-qrcode": "^4", "doctrine/doctrine-bundle": "^2.7", "doctrine/doctrine-migrations-bundle": "^3.2", "doctrine/orm": "^2.13", "i2m/connectors": "dev-master", "i2m/iiot": "dev-master", "i2m/invoices": "dev-main", "i2m/payment": "dev-main", "i2m/reports": "dev-main", "knplabs/knp-snappy-bundle": "^1.9", "kreait/firebase-bundle": "^5.6", "nelmio/api-doc-bundle": "^4.9", "nelmio/cors-bundle": "^2", "nesbot/carbon": "^3.0", "openpayu/openpayu": "^2.2.13", "php-amqplib/rabbitmq-bundle": "^2", "phpoffice/phpspreadsheet": "^1.10", "predis/predis": "^2.2", "sentry/sentry-symfony": "*", "symfony/asset": "6.4.*", "symfony/cache": "6.4.*", "symfony/console": "6.4.*", "symfony/doctrine-messenger": "6.4.*", "symfony/dotenv": "6.4.*", "symfony/expression-language": "6.4.*", "symfony/flex": "^2.7", "symfony/framework-bundle": "6.4.*", "symfony/http-client": "6.4.*", "symfony/lock": "6.4.*", "symfony/mailer": "6.4.*", "symfony/messenger": "6.4.*", "symfony/monolog-bundle": "^3.4", "symfony/proxy-manager-bridge": "6.4.*", "symfony/runtime": "6.4.*", "symfony/security-bundle": "6.4.*", "symfony/serializer": "6.4.*", "symfony/templating": "6.4.*", "symfony/translation": "6.4.*", "symfony/twig-bundle": "6.4.*", "symfony/validator": "6.4.*", "symfony/webpack-encore-bundle": "^1.15", "symfony/yaml": "6.4.*", "twig/extra-bundle": "^2.12|^3.0", "twig/twig": "^2.12|^3.0"}, "require-dev": {"dama/doctrine-test-bundle": "^7.2", "doctrine/doctrine-fixtures-bundle": "^4.1", "nelmio/alice": "^3.5", "phpstan/phpstan": "*", "phpunit/phpunit": "^9.5", "rector/rector": "*", "squizlabs/php_codesniffer": "^3.6", "symfony/browser-kit": "6.4.*", "symfony/css-selector": "6.4.*", "symfony/debug-bundle": "6.4.*", "symfony/maker-bundle": "^1.46", "symfony/phpunit-bridge": "^7.3", "symfony/web-profiler-bundle": "6.4.*"}, "config": {"allow-plugins": {"php-http/discovery": true, "symfony/flex": true, "symfony/runtime": true}, "preferred-install": {"*": "dist"}, "sort-packages": true}, "autoload": {"psr-4": {"App\\": "src/"}}, "autoload-dev": {"psr-4": {"App\\Tests\\": "tests/"}}, "replace": {"paragonie/random_compat": "2.*", "symfony/polyfill-ctype": "*", "symfony/polyfill-iconv": "*", "symfony/polyfill-php71": "*", "symfony/polyfill-php70": "*", "symfony/polyfill-php56": "*"}, "scripts": {"auto-scripts": {"cache:clear": "symfony-cmd", "assets:install %PUBLIC_DIR%": "symfony-cmd"}, "post-install-cmd": ["@auto-scripts"], "post-update-cmd": ["@auto-scripts"]}, "conflict": {"symfony/symfony": "*"}, "extra": {"symfony": {"allow-contrib": "true", "require": "6.4.*", "docker": false}}, "repositories": {"gitlab.bkf.pl/653": {"type": "composer", "url": "https://gitlab.bkf.pl/api/v4/group/653/-/packages/composer/packages.json"}, "gitlab.bkf.pl/166": {"type": "composer", "url": "https://gitlab.bkf.pl/api/v4/group/166/-/packages/composer/packages.json"}}}